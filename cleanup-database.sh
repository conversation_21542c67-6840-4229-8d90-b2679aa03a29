#!/bin/bash

# Stop all containers
echo "Stopping all containers..."
docker-compose down

# Start only the database container
echo "Starting only the database container..."
docker-compose up -d database18-stack

# Wait for the database to be ready
echo "Waiting for database to be ready..."
sleep 20

# Connect to PostgreSQL and terminate all connections to the llmdb18 database
echo "Terminating all connections to the llmdb18 database..."
docker-compose exec database18-stack psql -U odoo -c "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'llmdb18' AND pid <> pg_backend_pid();"

# Drop the database if it exists
echo "Dropping the llmdb18 database if it exists..."
docker-compose exec database18-stack dropdb -U odoo --if-exists llmdb18

# Create a fresh database
echo "Creating a fresh llmdb18 database..."
docker-compose exec database18-stack createdb -U odoo llmdb18

# Stop the database container
echo "Stopping the database container..."
docker-compose down

echo "Database cleanup complete. You can now run ./initialize-odoo.sh to initialize Odoo."
