# Odoo 18 Importable Modules (Data Modules) Development Guide

## Overview
This guide provides comprehensive instructions for developing **importable modules** (also called **data modules**) in Odoo 18. These are special modules that define models, fields, and logic entirely in XML data files without any Python code, making them suitable for managed hosting environments that don't allow custom Python deployment.

## Key Characteristics of Importable Modules
- **XML-only**: Use only XML files for models, fields, and logic - NO Python code allowed
- **Frontend-compatible**: Can be imported via Odoo UI without server restart
- **Managed hosting friendly**: Work on platforms like Odoo.com that restrict Python code
- **All models/fields must be prefixed with `x_`**: Mandatory prefix to differentiate from Python-defined models
- **No glob expansion**: Must list each file explicitly in manifest

## Critical Limitations
⚠️ **IMPORTANT**: Importable modules **CANNOT** include:
- Python model files (`.py` files with model definitions)
- Custom Python business logic
- Python computed field methods
- Python constraints and validations
- Custom Python controllers (except via server actions)

## Module Structure

### Directory Structure (XML-only)
```
your_module/
├── __manifest__.py          # Only Python file (required)
├── __init__.py             # Empty file (required but empty)
├── models/
│   └── *.xml              # Model definitions in XML
├── views/
│   └── *.xml              # View definitions
├── actions/
│   └── *.xml              # Action definitions
├── security/
│   ├── ir.model.access.csv # Access rights
│   └── *.xml              # Security rules
├── data/
│   └── *.xml              # Data files
└── static/
    └── src/
        └── js/
            └── *.js       # JavaScript files (allowed)
```

### Manifest File (__manifest__.py)
```python
{
    'name': 'Your Importable Module',
    'version': '********.0',
    'category': 'Custom',
    'summary': 'XML-only importable module',
    'description': """
        This is an importable module that uses only XML files
        for model and field definitions.
    """,
    'author': 'Your Name',
    'website': 'https://yourwebsite.com',
    'depends': ['base', 'mail'],  # base_import_module needed for import
    'data': [
        'models/your_model.xml',    # Models first
        'security/ir.model.access.csv',
        'security/security.xml',
        'views/your_views.xml',
        'actions/your_actions.xml',
        'data/data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'your_module/static/src/js/tour.js',
        ],
    },
    'installable': True,
    'auto_install': False,
    'application': False,
}
```

### Empty __init__.py File
```python
# This file must exist but should be empty
# Required for Odoo to recognize the module structure
```

## Models Definition in XML

### Basic Model Structure
```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Define the model -->
    <record id="model_your_custom_model" model="ir.model">
        <field name="name">Your Custom Model</field>
        <field name="model">x_your.custom.model</field>
        <field name="order">x_name</field>
    </record>
</odoo>
```

### Field Definitions in XML
```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Basic Char Field -->
    <record id="field_your_model_name" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_name</field>
        <field name="field_description">Name</field>
        <field name="ttype">char</field>
        <field name="required">True</field>
    </record>

    <!-- Float Field -->
    <record id="field_your_model_price" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_price</field>
        <field name="field_description">Price</field>
        <field name="ttype">float</field>
        <field name="digits">[16, 2]</field>
    </record>

    <!-- Integer Field -->
    <record id="field_your_model_quantity" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_quantity</field>
        <field name="field_description">Quantity</field>
        <field name="ttype">integer</field>
    </record>

    <!-- Boolean Field -->
    <record id="field_your_model_active" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_active</field>
        <field name="field_description">Active</field>
        <field name="ttype">boolean</field>
    </record>

    <!-- Date Field -->
    <record id="field_your_model_date" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_date</field>
        <field name="field_description">Date</field>
        <field name="ttype">date</field>
    </record>

    <!-- Datetime Field -->
    <record id="field_your_model_datetime" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_datetime</field>
        <field name="field_description">Date Time</field>
        <field name="ttype">datetime</field>
    </record>

    <!-- Text Field -->
    <record id="field_your_model_description" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_description</field>
        <field name="field_description">Description</field>
        <field name="ttype">text</field>
    </record>

    <!-- HTML Field -->
    <record id="field_your_model_notes" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_notes</field>
        <field name="field_description">Notes</field>
        <field name="ttype">html</field>
    </record>

    <!-- Selection Field -->
    <record id="field_your_model_state" model="ir.model.fields">
        <field name="model_id" ref="model_your_custom_model"/>
        <field name="name">x_state</field>
        <field name="field_description">State</field>
        <field name="ttype">selection</field>
    </record>

    <!-- Selection Options -->
    <record id="selection_state_draft" model="ir.model.fields.selection">
        <field name="field_id" ref="field_your_model_state"/>
        <field name="value">draft</field>
        <field name="name">Draft</field>
        <field name="sequence">1</field>
    </record>
    <record id="selection_state_confirmed" model="ir.model.fields.selection">
        <field name="field_id" ref="field_your_model_state"/>
        <field name="value">confirmed</field>
        <field name="name">Confirmed</field>
        <field name="sequence">2</field>
    </record>
    <record id="selection_state_done" model="ir.model.fields.selection">
        <field name="field_id" ref="field_your_model_state"/>
        <field name="value">done</field>
        <field name="name">Done</field>
        <field name="sequence">3</field>
    </record>
</odoo>
```

## Field Attributes Reference

### Common Field Attributes
- `name`: Technical name (must start with `x_`)
- `field_description`: Label shown in UI
- `help`: Help text for the field
- `ttype`: Field type (`char`, `integer`, `float`, `boolean`, `date`, `datetime`, `text`, `html`, `selection`, `many2one`, `one2many`, `many2many`)
- `required`: Whether field is required (`True`/`False`)
- `readonly`: Whether field is read-only (`True`/`False`)
- `index`: Whether field is indexed (`True`/`False`)
- `copied`: Whether field is copied on duplication (`True`/`False`)
- `translate`: Whether field is translatable (`True`/`False`)

### Field Type Specific Attributes
- **Float fields**: `digits` - `[precision, scale]` e.g., `[16, 2]`
- **Char fields**: `size` - Maximum character length
- **Selection fields**: Requires `ir.model.fields.selection` records
- **Relational fields**: `relation` - Target model name

## Relations in XML

### Many-to-one Relationship
```xml
<record id="field_your_model_partner_id" model="ir.model.fields">
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="name">x_partner_id</field>
    <field name="field_description">Customer</field>
    <field name="ttype">many2one</field>
    <field name="relation">res.partner</field>
</record>
```

### One-to-many Relationship
```xml
<!-- First create the child model with many2one back-reference -->
<record id="field_child_model_parent_id" model="ir.model.fields">
    <field name="model_id" ref="model_child_model"/>
    <field name="name">x_parent_id</field>
    <field name="field_description">Parent</field>
    <field name="ttype">many2one</field>
    <field name="relation">x_your.custom.model</field>
</record>

<!-- Then create the one2many field -->
<record id="field_your_model_child_ids" model="ir.model.fields">
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="name">x_child_ids</field>
    <field name="field_description">Children</field>
    <field name="ttype">one2many</field>
    <field name="relation">x_child.model</field>
    <field name="relation_field">x_parent_id</field>
</record>
```

### Many-to-many Relationship
```xml
<record id="field_your_model_tag_ids" model="ir.model.fields">
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="name">x_tag_ids</field>
    <field name="field_description">Tags</field>
    <field name="ttype">many2many</field>
    <field name="relation">x_tag.model</field>
    <field name="relation_table">x_your_model_tag_rel</field>
    <field name="column1">your_model_id</field>
    <field name="column2">tag_id</field>
</record>
```

## Default Values in XML

### Setting Default Values
```xml
<record id="default_your_model_state" model="ir.default">
    <field name="field_id" ref="field_your_model_state"/>
    <field name="json_value">"draft"</field>
</record>

<record id="default_your_model_active" model="ir.default">
    <field name="field_id" ref="field_your_model_active"/>
    <field name="json_value">true</field>
</record>

<record id="default_your_model_quantity" model="ir.default">
    <field name="field_id" ref="field_your_model_quantity"/>
    <field name="json_value">1</field>
</record>
```

## Computed Fields in XML

### Basic Computed Field
```xml
<record id="field_your_model_total" model="ir.model.fields">
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="name">x_total</field>
    <field name="field_description">Total</field>
    <field name="ttype">float</field>
    <field name="compute">_compute_total</field>
    <field name="depends">x_quantity,x_price</field>
    <field name="store">True</field>
    <field name="readonly">True</field>
</record>

<!-- Server action for computation -->
<record id="server_action_compute_total" model="ir.actions.server">
    <field name="name">Compute Total</field>
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="binding_model_id" ref="model_your_custom_model"/>
    <field name="state">code</field>
    <field name="code"><![CDATA[
for record in records:
    record.x_total = record.x_quantity * record.x_price
    ]]></field>
</record>
```

### Related Fields
```xml
<record id="field_your_model_partner_name" model="ir.model.fields">
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="name">x_partner_name</field>
    <field name="field_description">Customer Name</field>
    <field name="ttype">char</field>
    <field name="related">x_partner_id.name</field>
    <field name="store">True</field>
    <field name="readonly">True</field>
</record>
```

## Security in XML

### Access Rights (ir.model.access.csv)
```csv
id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_x_your_custom_model_user,x_your.custom.model.user,model_your_custom_model,base.group_user,1,1,1,0
access_x_your_custom_model_manager,x_your.custom.model.manager,model_your_custom_model,base.group_system,1,1,1,1
```

### Record Rules (security.xml)
```xml
<odoo>
    <data noupdate="1">
        <!-- Multi-company rule -->
        <record id="your_model_company_rule" model="ir.rule">
            <field name="name">Your Model: multi-company</field>
            <field name="model_id" ref="model_your_custom_model"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>
        
        <!-- User access rule -->
        <record id="your_model_user_rule" model="ir.rule">
            <field name="name">Your Model: user access</field>
            <field name="model_id" ref="model_your_custom_model"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
        </record>
    </data>
</odoo>
```

## Views in XML (Following Odoo 18 Standards)

⚠️ **Important Odoo 18 Changes:**
- Use `<list>` instead of `<tree>` for list views
- Use direct attributes instead of `attrs` dictionary
- Use simplified `invisible` syntax: `invisible="field == value"`
- Use `<chatter/>` tag for chatter functionality
- Use `widget="text"` instead of `widget="ace"`
- Use `readonly="True"` instead of `attrs` for readonly fields

### Form View (Odoo 18 Standard)
```xml
<record id="view_your_model_form" model="ir.ui.view">
    <field name="name">x_your.custom.model.form</field>
    <field name="model">x_your.custom.model</field>
    <field name="arch" type="xml">
        <form>
            <header>
                <button name="action_confirm" string="Confirm" type="object" 
                        class="btn-primary" invisible="x_state != 'draft'"/>
                <field name="x_state" widget="statusbar" 
                       statusbar_visible="draft,confirmed,done"/>
            </header>
            <sheet>
                <div class="oe_title">
                    <h1>
                        <field name="x_name" placeholder="Name..."/>
                    </h1>
                </div>
                <group>
                    <group>
                        <field name="x_partner_id"/>
                        <field name="x_date"/>
                        <field name="x_price"/>
                    </group>
                    <group>
                        <field name="x_quantity"/>
                        <field name="x_total" readonly="True"/>
                        <field name="x_active"/>
                    </group>
                </group>
                <notebook>
                    <page string="Description">
                        <field name="x_description" widget="text" placeholder="Enter description here..."/>
                    </page>
                    <page string="Notes">
                        <field name="x_notes" widget="html"/>
                    </page>
                </notebook>
            </sheet>
            <chatter/>
        </form>
    </field>
</record>
```

### List View (Odoo 18 Standard)
```xml
<record id="view_your_model_tree" model="ir.ui.view">
    <field name="name">x_your.custom.model.tree</field>
    <field name="model">x_your.custom.model</field>
    <field name="arch" type="xml">
        <list>
            <field name="x_name"/>
            <field name="x_partner_id"/>
            <field name="x_date"/>
            <field name="x_quantity"/>
            <field name="x_price"/>
            <field name="x_total" sum="Total"/>
            <field name="x_state" decoration-success="x_state=='done'" 
                   decoration-info="x_state=='confirmed'"/>
        </list>
    </field>
</record>
```

### Search View (Odoo 18 Standard)
```xml
<record id="view_your_model_search" model="ir.ui.view">
    <field name="name">x_your.custom.model.search</field>
    <field name="model">x_your.custom.model</field>
    <field name="arch" type="xml">
        <search>
            <field name="x_name"/>
            <field name="x_partner_id"/>
            <filter string="Draft" name="draft" domain="[('x_state','=','draft')]"/>
            <filter string="Confirmed" name="confirmed" domain="[('x_state','=','confirmed')]"/>
            <separator/>
            <filter string="Active" name="active" domain="[('x_active','=',True)]"/>
            <group expand="0" string="Group By">
                <filter string="Customer" name="group_partner" 
                        context="{'group_by':'x_partner_id'}"/>
                <filter string="State" name="group_state" 
                        context="{'group_by':'x_state'}"/>
                <filter string="Date" name="group_date" 
                        context="{'group_by':'x_date'}"/>
            </group>
        </search>
    </field>
</record>
```

## Actions and Menus

### Window Action
```xml
<record id="action_your_model" model="ir.actions.act_window">
    <field name="name">Your Models</field>
    <field name="res_model">x_your.custom.model</field>
    <field name="view_mode">tree,form</field>
    <field name="context">{}</field>
    <field name="domain">[]</field>
    <field name="help" type="html">
        <p class="o_view_nocontent_smiling_face">
            Create your first record!
        </p>
    </field>
</record>
```

### Menu Items
```xml
<menuitem id="menu_your_module_root" name="Your Module" sequence="10"/>
<menuitem id="menu_your_model" name="Your Models" 
          parent="menu_your_module_root" 
          action="action_your_model" sequence="10"/>
```

## Server Actions for Business Logic

### Basic Server Action
```xml
<record id="server_action_confirm" model="ir.actions.server">
    <field name="name">Confirm Records</field>
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="binding_model_id" ref="model_your_custom_model"/>
    <field name="state">code</field>
    <field name="code"><![CDATA[
for record in records:
    if record.x_state == 'draft':
        record.x_state = 'confirmed'
    ]]></field>
</record>
```

### Server Action with Email
```xml
<record id="server_action_send_email" model="ir.actions.server">
    <field name="name">Send Notification Email</field>
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="state">code</field>
    <field name="code"><![CDATA[
for record in records:
    if record.x_partner_id and record.x_partner_id.email:
        env['mail.mail'].create({
            'subject': 'Record Confirmed: %s' % record.x_name,
            'body_html': '<p>Your record has been confirmed.</p>',
            'email_to': record.x_partner_id.email,
        }).send()
    ]]></field>
</record>
```

## Automated Actions

### Trigger on Create/Write
```xml
<record id="automated_action_your_model" model="base.automation">
    <field name="name">Auto Process</field>
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="trigger">on_create_or_write</field>
    <field name="filter_domain">[('x_price', '>', 1000)]</field>
    <field name="code">
        records.write({'x_state': 'confirmed'})
    </field>
</record>
```

### Time-based Trigger
```xml
<record id="automated_action_daily" model="base.automation">
    <field name="name">Daily Processing</field>
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="trigger">on_time</field>
    <field name="interval_number">1</field>
    <field name="interval_type">days</field>
    <field name="code">
        records = model.search([('x_state', '=', 'draft')])
        records.write({'x_state': 'confirmed'})
    </field>
</record>
```

## Website Integration

### Website Controller via Server Action
```xml
<record id="server_action_website_api" model="ir.actions.server">
    <field name="name">Website API</field>
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="state">code</field>
    <field name="code"><![CDATA[
import json
from odoo.http import request

# Get published records
records = env['x_your.custom.model'].search([
    ('x_active', '=', True)
])

# Prepare response data
data = []
for record in records:
    data.append({
        'id': record.id,
        'name': record.x_name,
        'price': record.x_price,
        'date': record.x_date.isoformat() if record.x_date else None,
    })

# Return JSON response
action = {
    'type': 'ir.actions.act_url',
    'url': '/web/content/?model=ir.attachment&field=datas&filename=api_response.json',
    'target': 'new',
}
    ]]></field>
</record>
```

## JavaScript Integration

### Basic Tour (static/src/js/tour.js) - Odoo 18 Standard
```javascript
/** @odoo-module **/

import { registry } from "@web/core/registry";

registry.category("web_tour.tours").add('your_module_tour', {
    url: "/web",
    steps: () => [{
        trigger: '.o_app[data-menu-xmlid="your_module.menu_your_module_root"]',
        content: 'Welcome to your module!',
    }],
});
```

### Tour Record in XML
```xml
<record id="your_module_tour" model="web_tour.tour">
    <field name="name">your_module_tour</field>
    <field name="sequence">10</field>
    <field name="rainbow_man_message">Welcome! Happy exploring.</field>
</record>
```

## Data Files

### Demo Data (Odoo 18 Standard)
```xml
<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="demo_record_1" model="x_your.custom.model">
            <field name="x_name">Demo Record 1</field>
            <field name="x_price">100.0</field>
            <field name="x_quantity">5</field>
            <field name="x_state">draft</field>
            <field name="x_active">True</field>
        </record>
        
        <record id="demo_record_2" model="x_your.custom.model">
            <field name="x_name">Demo Record 2</field>
            <field name="x_price">200.0</field>
            <field name="x_quantity">3</field>
            <field name="x_state">confirmed</field>
            <field name="x_active">True</field>
        </record>
    </data>
</odoo>
```

## Email Templates

### Email Template Definition
```xml
<record id="email_template_your_model" model="mail.template">
    <field name="name">Your Model Notification</field>
    <field name="model_id" ref="model_your_custom_model"/>
    <field name="subject">{{ object.x_name }} - Status Update</field>
    <field name="body_html" type="html">
        <p>Hello,</p>
        <p>Your record <strong>{{ object.x_name }}</strong> has been updated.</p>
        <p>Current status: {{ object.x_state }}</p>
        <p>Price: {{ object.x_price }}</p>
        <p>Quantity: {{ object.x_quantity }}</p>
        <p>Total: {{ object.x_total }}</p>
    </field>
</record>
```

## Deployment

### Creating and Uploading Module
1. **Prepare module structure** with only XML files and empty `__init__.py`
2. **Create ZIP file** of the entire module directory
3. **Install base_import_module** on your Odoo instance
4. **Enable developer mode** in Odoo
5. **Go to Apps → Import Module**
6. **Upload ZIP file** and install

### Update Process
1. **Modify XML files** as needed
2. **Increment version** in `__manifest__.py`
3. **Create new ZIP file**
4. **Upload again** (will update existing module)
5. **Use "Force init"** option if needed

### Command Line Deployment
```bash
odoo-bin deploy <path_to_module> https://<your_instance> \
  --login <login> --password <password> --force
```

## Best Practices

### Naming Conventions
- **All models**: Must start with `x_` (e.g., `x_your.model`)
- **All fields**: Must start with `x_` (e.g., `x_name`, `x_price`)
- **Record IDs**: Use descriptive names (e.g., `model_your_custom_model`)
- **File organization**: Group related records in same XML file

### Performance Considerations
- **Use indexes** on frequently searched fields: `<field name="index">True</field>`
- **Store computed fields** when appropriate: `<field name="store">True</field>`
- **Limit related fields** to avoid N+1 queries
- **Use proper field types** (integer vs float vs char)

### Security Best Practices
- **Always define access rights** in `ir.model.access.csv`
- **Use record rules** for data isolation
- **Validate user inputs** in server actions
- **Be careful with sudo()** in server actions

### Maintenance Tips
- **Document your XML** with comments
- **Use consistent naming** throughout the module
- **Test thoroughly** before deployment
- **Keep backups** before major updates
- **Version control** your XML files

## Troubleshooting

### Common Issues
1. **Missing `x_` prefix**: All custom models/fields must have `x_` prefix
2. **Wrong field order**: Define models before fields, fields before views
3. **Invalid XML syntax**: Use XML validators to check syntax
4. **Missing dependencies**: Ensure all referenced models exist
5. **Access rights errors**: Check `ir.model.access.csv` configuration

### Debugging Tips
- **Check Odoo logs** for detailed error messages
- **Use developer mode** for better error visibility
- **Validate XML syntax** before uploading
- **Test with minimal data** first
- **Use server actions** for debugging logic

### Limitations to Remember
- **No Python code** in model definitions
- **No custom Python methods** for computed fields
- **No Python constraints** - use server actions instead
- **No custom Python controllers** - use server actions for web endpoints
- **Static default values only** - no dynamic defaults like "today"
- **Limited computed field capabilities** compared to Python

## Migration from Python Modules

### Converting Python Models to XML
1. **Extract model definition** → Create `ir.model` record
2. **Convert field definitions** → Create `ir.model.fields` records
3. **Replace Python methods** → Use server actions
4. **Convert constraints** → Use automated actions
5. **Replace computed fields** → Use server actions with compute attribute

### Example Conversion
**Python Code:**
```python
class YourModel(models.Model):
    _name = 'your.model'
    name = fields.Char('Name', required=True)
    price = fields.Float('Price')
    
    @api.depends('price')
    def _compute_total(self):
        for record in self:
            record.total = record.price * 1.2
```

**XML Equivalent:**
```xml
<!-- Model -->
<record id="model_your_model" model="ir.model">
    <field name="name">Your Model</field>
    <field name="model">x_your.model</field>
</record>

<!-- Fields -->
<record id="field_name" model="ir.model.fields">
    <field name="model_id" ref="model_your_model"/>
    <field name="name">x_name</field>
    <field name="field_description">Name</field>
    <field name="ttype">char</field>
    <field name="required">True</field>
</record>

<record id="field_price" model="ir.model.fields">
    <field name="model_id" ref="model_your_model"/>
    <field name="name">x_price</field>
    <field name="field_description">Price</field>
    <field name="ttype">float</field>
</record>

<record id="field_total" model="ir.model.fields">
    <field name="model_id" ref="model_your_model"/>
    <field name="name">x_total</field>
    <field name="field_description">Total</field>
    <field name="ttype">float</field>
    <field name="compute">_compute_total</field>
    <field name="depends">x_price</field>
    <field name="store">True</field>
</record>

<!-- Server Action for Computation -->
<record id="server_action_compute_total" model="ir.actions.server">
    <field name="name">Compute Total</field>
    <field name="model_id" ref="model_your_model"/>
    <field name="state">code</field>
    <field name="code"><![CDATA[
for record in records:
    record.x_total = record.x_price * 1.2
    ]]></field>
</record>
```

This guide provides a comprehensive foundation for developing XML-only importable modules in Odoo 18. These modules are perfect for managed hosting environments and provide a way to create sophisticated business applications without requiring Python code deployment.

## Odoo 18 Coding Standards Compliance

### Mandatory Standards for All XML Files

#### 1. View Tag Changes
- ✅ **Use `<list>` instead of `<tree>`**: All list views must use `<list>` tag
- ✅ **Use `<chatter/>` tag**: Replace chatter implementation with single `<chatter/>` tag
- ✅ **No `attrs` usage**: Use direct attributes instead of `attrs` dictionary
- ✅ **Simplified `invisible` syntax**: Use `invisible="field == value"` instead of `invisible="[('field', '=', value)]"`
- ✅ **Use `readonly="True"`**: Instead of `attrs` for readonly fields
- ✅ **Widget updates**: Use `widget="text"` instead of `widget="ace"`

#### 2. XML Structure Standards
```xml
<!-- ❌ OLD WAY (Odoo 17 and below) -->
<tree string="Records">
    <field name="name" attrs="{'invisible': [('state', '=', 'draft')]}"/>
    <field name="description" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
</tree>

<group>
    <group>
        <field name="message_follower_ids" widget="mail_followers"/>
        <field name="activity_ids" widget="mail_activity"/>
    </group>
    <group>
        <field name="message_ids" widget="mail_thread"/>
    </group>
</group>

<field name="parameters" widget="ace" options="{'mode': 'json'}"/>

<!-- ✅ NEW WAY (Odoo 18) -->
<list string="Records">
    <field name="name" invisible="state == 'draft'"/>
    <field name="description" readonly="state != 'draft'"/>
</list>

<chatter/>

<field name="parameters" widget="text" placeholder="Enter JSON parameters here..."/>
```

#### 3. JavaScript Standards
```javascript
/** @odoo-module **/
// Always include module declaration at the top

import { registry } from "@web/core/registry";
// Use proper ES6+ imports

// Follow proper module structure
registry.category("web_tour.tours").add('module_tour', {
    // Implementation
});
```

#### 4. XML Validation Requirements
- **XML Declaration**: Always include `<?xml version="1.0" encoding="utf-8"?>`
- **Data Wrapper**: Use proper `<data>` wrapper when needed
- **Schema Compliance**: Validate against Odoo 18 RelaxNG schema
- **Icon Attributes**: All FontAwesome icons must have `title` attributes

### Updated Examples Following Odoo 18 Standards

#### Conditional Field Display
```xml
<!-- ❌ OLD -->
<field name="field1" attrs="{'invisible': [('state', '=', 'draft')]}"/>
<field name="field2" attrs="{'readonly': [('state', '!=', 'draft')]}"/>
<field name="field3" attrs="{'required': [('type', '=', 'special')]}"/>

<!-- ✅ NEW -->
<field name="field1" invisible="state == 'draft'"/>
<field name="field2" readonly="state != 'draft'"/>
<field name="field3" required="type == 'special'"/>
```

#### Button Visibility
```xml
<!-- ❌ OLD -->
<button name="action_confirm" string="Confirm" type="object" 
        attrs="{'invisible': [('state', '!=', 'draft')]}"/>

<!-- ✅ NEW -->
<button name="action_confirm" string="Confirm" type="object" 
        invisible="state != 'draft'"/>
```

#### Complex Conditions
```xml
<!-- ❌ OLD -->
<field name="field1" attrs="{'invisible': ['|', ('state', '=', 'done'), ('active', '=', False)]}"/>

<!-- ✅ NEW -->
<field name="field1" invisible="state == 'done' or not active"/>
```

#### List View with Decorations
```xml
<record id="view_model_tree" model="ir.ui.view">
    <field name="name">model.tree</field>
    <field name="model">x_your.model</field>
    <field name="arch" type="xml">
        <list>
            <field name="x_name"/>
            <field name="x_state" 
                   decoration-success="x_state == 'done'" 
                   decoration-warning="x_state == 'draft'"
                   decoration-danger="x_state == 'failed'"/>
            <field name="x_priority" invisible="x_state == 'done'"/>
        </list>
    </field>
</record>
```

### Pre-Deployment Checklist

Before deploying any importable module, ensure:

1. ✅ **No `<tree>` tags** - All replaced with `<list>`
2. ✅ **No `attrs` usage** - All replaced with direct attributes
3. ✅ **Simplified conditions** - Use `field == value` syntax
4. ✅ **Proper chatter** - Use `<chatter/>` tag
5. ✅ **Widget compliance** - No deprecated widgets
6. ✅ **XML validation** - Validate against Odoo 18 schema
7. ✅ **JavaScript modules** - Include `/** @odoo-module **/`
8. ✅ **Proper imports** - Use ES6+ import syntax
9. ✅ **Icon attributes** - All icons have `title` attributes
10. ✅ **Data wrappers** - Proper XML structure

### XML Schema Validation

Use online RelaxNG validator with Odoo 18 schema:
- **Validator**: https://www.liquid-technologies.com/online-relaxng-validator
- **Schema**: https://github.com/odoo/odoo/blob/18.0/odoo/import_xml.rng

### Common Migration Issues

#### Issue 1: "Element odoo has extra content"
```xml
<!-- ❌ WRONG -->
<odoo>
    <record id="..." model="...">
        <!-- content -->
    </record>
</odoo>

<!-- ✅ CORRECT -->
<odoo>
    <data>
        <record id="..." model="...">
            <!-- content -->
        </record>
    </data>
</odoo>
```

#### Issue 2: Deprecated Widget Usage
```xml
<!-- ❌ WRONG -->
<field name="code" widget="ace" options="{'mode': 'python'}"/>

<!-- ✅ CORRECT -->
<field name="code" widget="text" placeholder="Enter Python code here..."/>
```

#### Issue 3: Complex Attrs Migration
```xml
<!-- ❌ OLD COMPLEX ATTRS -->
<field name="field1" attrs="{
    'invisible': [('type', '!=', 'special')],
    'required': [('state', 'in', ['draft', 'confirm'])],
    'readonly': [('state', '=', 'done')]
}"/>

<!-- ✅ NEW DIRECT ATTRIBUTES -->
<field name="field1" 
       invisible="type != 'special'"
       required="state in ['draft', 'confirm']"
       readonly="state == 'done'"/>
```

This ensures your importable modules are fully compliant with Odoo 18 standards and will work correctly in the latest version.