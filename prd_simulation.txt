# Odoo TOC Simulation & Advisor Module Product Requirements Document (PRD) (Version 1.2)

## **1. Goal, Objective, and Context**

* **Primary Goal:** To create a simulation tool within Odoo that automatically generates transactional data (Sales, Manufacturing, Purchase Orders) based on user-defined parameters.
* **Core Objective:** To allow users to visualize the real-time impact of varying production loads on the Theory of Constraints (TOC) dashboard and to receive AI-driven advice on how to resolve system bottlenecks based on TOC principles.
* **Business Value:** Enable proactive capacity planning, bottleneck prediction, and system optimization by simulating future production scenarios without affecting live data.
* **Context:** This module is an extension of the "Odoo TOC Integration" module and leverages its dashboard and core metric calculations.

## **2. Functional Requirements (MVP)**

1.  **Dummy Data Generation:** Users must have an option to generate a baseline set of dummy master data (products with BoMs/routings, customers, vendors) to populate a sparse test environment.
2.  **Simulation Configuration:** A dedicated UI must exist for users to define and save simulation scenarios, including parameters for **date range**, number of orders, and selection of master data (either existing or dummy).
3.  **Automated Data Generation Engine:** The system must automatically generate all necessary `sale.order`, `mrp.production`, and `purchase.order` records based on the configured scenario.
4.  **Simulation Execution & Monitoring:** Users must be able to "run" a simulation, which processes the generated orders and provides real-time updates to the TOC Dashboard, including metric calculations and color-coded signals.
5.  **TOC Bottleneck Advisor:** When a bottleneck is detected during a simulation, the system must analyze it and suggest actionable solutions to the user based on TOC principles.
6.  **Simulation Scenario Management & Reset:** The system must provide functions to save/load scenarios and to safely delete all data generated during a simulation run to reset the environment.

## **3. Non-Functional Requirements (MVP)**

1.  **Performance:** The simulation process should not cause significant performance degradation to the Odoo instance.
2.  **Usability:** The configuration and advisory interfaces should be intuitive for a user familiar with Odoo's manufacturing module.
3.  **Data Integrity:** All generated data must be valid within Odoo's data model to ensure simulations run without data-related errors.

## **4. User Interaction and Design Goals**

* The simulation configuration screen should be a clean, wizard-like interface that guides the user through setup.
* During simulation execution, a simple, non-intrusive progress indicator should be visible.
* The "Advisor" panel for TOC suggestions should be presented clearly and contextually when a bottleneck is detected, without overwhelming the user.

## **5. Technical Assumptions**

* **Deployment Environment:** This module will be deployed and operated **exclusively** on a non-production (staging/test) Odoo database. This simplifies data handling, as there is no need to isolate simulation data from live production data.
* **Parent Module Dependency:** This new module (technical name: `toc_simulation_advisor`) **must** declare a direct dependency on the base module `odoo_toc_integration`. The base module provides the necessary dashboard views, data models, and metric calculation fields that this simulation module extends and utilizes.

## **6. Epic Overview**

### **Epic 1: Foundational Dummy Data Generation**
* **Goal:** To enable a user to populate a sparse test database with a single action, creating a viable set of baseline data required to run a manufacturing simulation.
* **Stories:**
    * **1.1: Dummy Data Generation UI:** As a setup user, I want a simple interface (e.g., a button in the simulation settings) to trigger the creation of a standard set of dummy data, so that I can easily populate an empty test environment.
    * **1.2: Baseline Data Creation:** As the system, I want to generate a predefined number of records (e.g., 20 products, 10 customers, 10 vendors) when the dummy data generation is triggered, so that a baseline of master data is available for simulations.
    * **1.3: Create Manufacturable Dummy Products:** As the system, I want to automatically create a simple Bill of Materials (BoM) and a basic two-step Routing for each dummy product generated, so that they are immediately usable in the manufacturing simulation process.

### **Epic 2: Simulation Configuration UI**
* **Goal:** To build an intuitive user interface where a manager can define all the parameters for a specific simulation run, save those settings for future use, and launch the simulation.
* **Stories:**
    * **2.1: Main Configuration Screen:** As a simulation user, I want a dedicated and clearly organized configuration screen, so that I have a central place to create and manage my simulation scenarios.
    * **2.2: Set Simulation Parameters:** As a simulation user, I want input fields on the configuration screen to set the simulation's core parameters, so that I can control the timeline and volume of the simulation.
    * **2.3: Select Data Sources:** As a simulation user, I want to select the specific master data records to be used in the simulation, so that I can run targeted scenarios.
    * **2.4: Save and Load Scenarios:** As a simulation user, I want to be able to save a completed simulation configuration as a named scenario and load it back later, so that I don't have to re-enter the same parameters for repeated tests.

### **Epic 3: Automated Order Generation Engine**
* **Goal:** To create the core backend logic that receives a configuration from the UI and generates a complete and valid set of sales, manufacturing, and purchase orders to simulate a full business workflow.
* **Stories:**
    * **3.1: Receive and Process Configuration:** As the system, I want to receive the complete simulation configuration when a user clicks "Run Simulation," so that I have all the necessary inputs to begin the data generation process.
    * **3.2: Generate Sales Orders:** As the system, I want to create the specified number of `sale.order` records using the selected customers and products, so that a realistic sales demand is simulated.
    * **3.3: Trigger Manufacturing Orders:** As the system, I want to automatically trigger the creation of `mrp.production` records based on the generated sales demand, so that the load on the manufacturing floor is accurately simulated.
    * **3.4: Trigger Purchase Orders:** As the system, I want to automatically trigger the creation of `purchase.order` records based on the material needs of the new manufacturing orders, so that the entire supply chain is simulated.

### **Epic 4: Simulation Execution & TOC Dashboard Integration**
* **Goal:** To process the generated transactional orders over the simulated time period and provide real-time visualization of the system's performance on the TOC Dashboard.
* **Stories:**
    * **4.1: Process Simulated Orders:** As the system, I want to process the generated orders in a logical, time-ordered sequence when a simulation is run, so that the flow of work through the system is realistically simulated.
    * **4.2: Live Dashboard Updates:** As a simulation user, I want the key metrics on the TOC Dashboard to update in near real-time as the simulation progresses, so that I can see the impact of the production load on the system's performance.
    * **4.3: Dynamic Bottleneck Visualization:** As a simulation user, I want to see the color-coded signals (Red/Yellow/Green) on the TOC Dashboard change dynamically, so that I can immediately identify when and where bottlenecks form under the simulated load.

### **Epic 5: Simulation Management & Reset**
* **Goal:** To provide the user with the tools to review past simulation scenarios and, most importantly, to clean up all generated data, allowing for repeatable, clean-slate testing.
* **Stories:**
    * **5.1: View Simulation Scenarios:** As a simulation user, I want a list of my saved simulation scenarios, so that I can see the different configurations I have created and track my tests.
    * **5.2: Reset Transactional Data:** As a simulation user, I want a clear and simple function to delete all transactional data generated by a simulation run, so that I can easily reset my test environment.
    * **5.3: Reset Foundational Dummy Data:** As a simulation user, I want a separate option to delete the foundational dummy master data, so that I can return my database to a completely empty state if needed.

### **Epic 6: TOC Bottleneck Advisor**
* **Goal:** To automatically analyze an identified bottleneck during a simulation and present the user with a ranked list of actionable improvement suggestions based on the Theory of Constraints' Five Focusing Steps.
* **Stories:**
    * **6.1: Bottleneck Analysis Trigger:** As the system, I want to trigger an analysis and advisory process the moment a Work Center's status changes to 'Red' (bottleneck), so that I can prepare timely and contextual recommendations for the user.
    * **6.2: Display Advisory Panel:** As a simulation user, I want an 'Advisor' panel or pop-up to appear when a bottleneck is detected, so that I am immediately notified and can see the suggested solutions.
    * **6.3: Generate TOC-Based Suggestions:** As the system, I want to generate a contextual list of suggestions based on TOC's Five Focusing Steps, so that the user is guided on how to resolve the bottleneck.

## **7. Out of Scope Ideas Post MVP**
* All discussed functionality was included in the MVP scope.

## **Initial Architect Prompt**

This prompt is for the next agent in the BMAD process.

**To the Architect Agent:**
This PRD outlines a new **TOC Simulation & Advisor Module** for Odoo 18. Please review it thoroughly to begin creating the technical architecture.

Key technical guidance and constraints from this PRD:
* The module's primary purpose is to simulate production load and provide TOC-based advice.
* It builds upon an existing "Odoo TOC Integration" module.
* **Crucially, the module will only be used on non-production (staging/test) databases.**
* **This module must have a direct dependency on the base `odoo_toc_integration` module.**
* The architecture must account for a UI for configuration, a backend data generation engine, simulation processing logic, and a new "advisor" feature that analyzes and presents solutions.