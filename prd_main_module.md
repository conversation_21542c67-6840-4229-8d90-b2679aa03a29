**Product Requirements Document (PRD) for Odoo TOC Integration**

Document Version: 1.0  
Date: June 2, 2025  
Author: <PERSON> (Product Manager)  
Project: Odoo Theory of Constraints (TOC) Integration for Manufacturing

## **1\. Introduction**

This Product Requirements Document (PRD) outlines the features and functionalities required to integrate the principles of Eliyahu M. <PERSON>ratt's "The Goal" (Theory of Constraints \- TOC) into Odoo 18 Community Edition's manufacturing module. The primary goal is to provide manufacturing managers with real-time visibility into system bottlenecks, allowing for improved throughput, reduced inventory, and optimized operational expenses.

## **2\. Business Goals**

The primary business objectives for this project are:

* **Increase Throughput:** By identifying and exploiting bottlenecks, ensure the manufacturing system generates more money through sales.  
* **Decrease Inventory:** By subordinating non-bottlenecks to the bottleneck, reduce Work-In-Progress (WIP) and overall inventory levels.  
* **Decrease Operational Expense:** By focusing improvement efforts on the constraint, optimize overall system efficiency.  
* **Enhance Decision Making:** Provide real-time, actionable insights into production flow and potential constraints.

## **3\. Scope**

This project focuses on enhancing Odoo 18 Community Edition's Manufacturing (MRP) module to support TOC principles. It involves custom backend calculations, new data fields, and a dynamic graphical dashboard for visualization.

## **4\. Key Definitions (from "The Goal")**

* **Throughput (T):** The rate at which the system generates money through sales.  
  * **Odoo Definition:** Occurs when **payment is received**, originating from validated sales invoices that are generated upon delivery order completion (ship status).  
* **Inventory (I):** All the money the system has invested in purchasing things it intends to sell.  
  * **Odoo Definition:** The **valuation of all stock** (raw materials, WIP, finished goods) as reported by Odoo's inventory valuation reports, using standard costing methods.  
* **Operational Expense (OE):** All the money the system spent to turn inventory into throughput.  
  * **Odoo Definition:** Tracked through **vendor bills, general expenses, and payroll/HR expense entries** in Odoo's accounting module.  
* **Bottleneck:** Any resource whose capacity is less than or equal to the demand placed upon it.  
* **Non-Bottleneck:** Any resource whose capacity is greater than the demand placed upon it.

## **5\. User Stories**

The following user stories define the desired features:

### **Category: Core TOC Metric Calculations (Backend)**

**User Story 1: As a Production Manager, I want to see the real-time queue length at each Work Center, so I can identify potential bottlenecks before they impact throughput.**

* **Description:** This story covers the calculation of "Work Order Queue Length/Size".  
* **Acceptance Criteria:**  
  * The system accurately calculates the sum of estimated\_duration for all mrp.workorder records in 'waiting' or 'ready' states assigned to a specific mrp.workcenter.  
  * The calculated queue time is available as a functional field on the mrp.workcenter model.


**User Story 2: As a Production Manager, I want to see the actual waiting time for work orders before they start at each Work Center, so I can measure the impact of queues and understand hidden delays.**

* **Description:** This story covers the calculation of "Work Center Waiting Time".  
* **Acceptance Criteria:**  
  * The system records the x\_end\_time\_at\_previous\_wc timestamp when a work order completes at a prior work center.  
  * The system records the x\_start\_time\_at\_current\_wc timestamp when a work order transitions to 'progress' or 'start' at its assigned work center.  
  * The system calculates the x\_waiting\_duration\_minutes (difference between start at current WC and end at previous WC) for each work order.  
  * This waiting duration is available as a functional field on the mrp.workorder model.

**User Story 3: As a Production Manager, I want to see the actual utilization percentage for each Work Center, so I can understand if a work center is overloaded and becoming a constraint.**

* **Description:** This story covers the calculation of "Work Center Utilization / Load".  
* **Acceptance Criteria:**  
  * The system calculates the x\_actual\_load\_hours\_daily by summing the actual duration of all mrp.workorder records processed (progress or done) at a specific mrp.workcenter within a defined period (e.g., daily shift, configured by user).  
  * The system calculates x\_utilization\_percentage by comparing x\_actual\_load\_hours\_daily against the mrp.workcenter's defined capacity field.  
  * These metrics are available as functional fields on the mrp.workcenter model.


**User Story 4: As a Production Manager, I want to see the quantity and value of Work-In-Progress (WIP) at specific stages or Work Centers, so I can identify where inventory is accumulating.**

* **Description:** This story covers the tracking of "Work-In-Progress (WIP)".  
* **Acceptance Criteria:**  
  * The system aggregates the quantity and value of stock.move.line records (or similar) representing materials/semi-finished goods waiting at designated WIP locations, or associated with mrp.workorder records in 'ready', 'waiting', or 'progress' states.  
  * The aggregated x\_wip\_value is available for display, linked to relevant work centers or production orders.

**User Story 5: As a Production Manager, I want to track quality issues and scrap rates by Work Center, so I can identify if quality problems are contributing to bottlenecks or reducing effective capacity.**

* **Description:** This story covers "Quality/Scrap Rates".  
* **Acceptance Criteria:**  
  * The system allows explicit association of quality alerts, defects, or scrap quantities with the mrp.workcenter where they occurred.  
  * The system calculates x\_scrap\_rate\_percentage for each work center based on scrapped quantity vs. total produced quantity.  
  * These metrics are available for reporting and display.

### **Category: Graphical Process Flow Dashboard (Frontend & Backend Integration)**

**User Story 6: As a Production Manager, I want a dynamic graphical representation of the manufacturing process flow, so I can quickly visualize the entire value stream from Sales Order to Customer Invoice.**

* **Description:** This is the core dashboard visualization.  
* **Acceptance Criteria:**  
  * The dashboard displays interactive nodes for SO, PO, MO, individual WC/Operations, DO, and INV.  
  * Directional arrows correctly depict the relationships and flow based on product routing (buy/manufacture/MTO).

**User Story 7: As a Production Manager, I want Work Center nodes on the graphical dashboard to be color-coded (Red/Yellow/Green) based on their utilization and queue length, so I can immediately spot bottlenecks.**

* **Description:** Color-coding for WCs.  
* **Acceptance Criteria:**  
  * Work Center nodes are colored Red if x\_utilization\_percentage \>= 90% OR x\_waiting\_duration\_minutes \>= 2 \* \[WC Average Processing Time\].  
  * Work Center nodes are colored Yellow if x\_utilization\_percentage \>= 70% and \< 90% OR x\_waiting\_duration\_minutes \>= 1 \* \[WC Average Processing Time\] and \< 2 \* \[WC Average Processing Time\].  
  * Work Center nodes are colored Green otherwise.  
  * Average Processing Time for a WC is calculated dynamically based on historical mrp.workorder duration data.

**User Story 8: As a Production Manager, I want key TOC metrics (Utilization, Queue Time, WIP Value) to be displayed concisely on relevant nodes within the graphical dashboard, so I have immediate context.**

* **Description:** Metric display on nodes.  
* **Acceptance Criteria:**  
  * WC nodes display x\_utilization\_percentage, x\_waiting\_duration\_minutes, and x\_wip\_value.  
  * INV nodes display the total\_amount\_paid.  
  * SO nodes display the total\_amount\_invoiced or total\_amount.

**User Story 9: As a Production Manager, I want to click on any node in the graph and see its detailed Odoo record, along with its upstream and downstream dependencies, so I can quickly investigate issues and their impact.**

* **Description:** Drill-down and dependency visualization.  
* **Acceptance Criteria:**  
  * Clicking a node opens a side panel/modal.  
  * The panel displays the standard Odoo record view.  
  * The panel includes dedicated sections for "Upstream Dependencies" and "Downstream Dependencies."  
  * Dependencies are listed with their name, status, and relevant color code (if applicable).  
  * Clicking a dependency in the panel navigates to that record or highlights it on the main graph.

**User Story 10: As a Production Manager, I want a corresponding list view that dynamically updates alongside the graphical dashboard, displaying key TOC metrics and their color codes, so I can review details in a tabular format.**

* **Description:** Companion List View.  
* **Acceptance Criteria:**  
  * A configurable list view (e.g., for mrp.workorder or mrp.production) is available.  
  * This list view includes columns for x\_utilization\_percentage, x\_waiting\_duration\_minutes, x\_wip\_value, x\_scrap\_rate\_percentage, and their corresponding color indicators (e.g., a colored cell or icon).  
  * Clicking an item in the list highlights it on the graph (if visible).

## **6\. Odoo 18 Model Structure Validation**

A direct check using the Odoo 18 MCP tool confirms the following:

- The following models exist and are accessible: `mrp.workorder`, `mrp.workcenter`, `mrp.production`, and `stock.move.line`.
- Key fields and relationships (such as workcenter, production, product, date fields, etc.) are present in each model, matching the requirements for TOC metric extensions.
- No records were found in `mrp.workorder` (the database may be empty or demo data not loaded), but the model and its fields are available for customization.
- The MCP tool was used to verify model existence and field structure, ensuring a solid foundation for planned customizations.

This validation ensures that the planned TOC features can be implemented as extensions to the existing Odoo models, with no structural blockers identified.

## **6\. Assumptions & Constraints**

* **Odoo Edition:** Odoo 18 Community Edition. No Enterprise-specific features will be used unless explicitly stated as requiring a paid upgrade.  
* **Manufacturing Process:** Assumes a clearly defined manufacturing process modeled in Odoo via Routings and Work Centers.  
* **Data Accuracy:** Accuracy of inputs (e.g., actual work order durations, BOMs, capacities) is critical for accurate TOC metrics.  
* **Dependencies:** Custom module development will be required.  
* **Performance:** Initial performance will be monitored and optimized as necessary. Complex graphing libraries will be avoided in favor of OWL's native SVG capabilities where possible.