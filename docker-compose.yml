version: '3.8'

services:
  database18-stack:
    container_name: database18-stack
    image: postgres:15-alpine
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=odoo
      - POSTGRES_PASSWORD=odoo
      - PGDATA=/var/lib/postgresql/data/pgdata
    volumes:
      - odoo-db-data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    restart: unless-stopped
    command: >
      postgres
        -c max_connections=50
        -c shared_buffers=128MB
        -c work_mem=4MB
        -c maintenance_work_mem=64MB
        -c effective_cache_size=512MB
        -c max_locks_per_transaction=128
        -c deadlock_timeout=10s
        -c statement_timeout=120000
        -c idle_in_transaction_session_timeout=120000
        -c lock_timeout=10000
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U odoo"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    shm_size: 1gb

  odoo18-stack:
    container_name: odoo18-stack
    build:
      context: .
      dockerfile: Dockerfile
    depends_on:
      database18-stack:
        condition: service_healthy
    ports:
      - "8069:8069"
      - "8072:8072"
      - "8082:8082"  # WhatsApp Bridge
      - "8081:8081"  # MCP Server
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./custom-addons:/mnt/extra-addons
      - ./logs:/var/log/odoo
    environment:
      - DB_HOST=database18-stack
      - DB_PORT=5432
      - DB_USER=odoo
      - DB_PASSWORD=odoo
      - DB_NAME=postgres
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8069/web/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    ulimits:
      nproc: 65535
      nofile:
        soft: 65535
        hard: 65535

volumes:
  odoo-web-data:
  odoo-db-data: