#!/bin/bash

# Odoo 18 Docker Development Automation Scripts
# Usage: Source this file or add to your .bashrc/.zshrc

# Configuration
CONTAINER_NAME="odoo18-stack"
DB_CONTAINER_NAME="database18-stack"
ODOO_CONFIG="/etc/odoo/odoo.conf"
DATABASE_NAME="llmdb18"
LOG_FILE="/var/log/odoo/odoo.log"
ODOO_BIN="/usr/src/odoo/odoo-bin"
HEALTH_CHECK_URL="http://localhost:8069/web/health"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
log_info() { echo -e "${BLUE}[INFO]${NC} $1"; }
log_success() { echo -e "${GREEN}[SUCCESS]${NC} $1"; }
log_warning() { echo -e "${YELLOW}[WARNING]${NC} $1"; }
log_error() { echo -e "${RED}[ERROR]${NC} $1"; }

# Prune Docker system
odoo18_prune() {
   log_info "Pruning Docker system..."
   docker system prune -f
}

# Step 1: Restart Docker Container
odoo18_restart() {
    log_info "Step 1: Restarting Odoo 18 Docker container..."
    docker restart $CONTAINER_NAME
    
    if [ $? -eq 0 ]; then
        log_success "Container restarted successfully"
        sleep 5  # Wait for container to fully start
    else
        log_error "Failed to restart container"
        return 1
    fi
}

# Step 2: Monitor logs in real-time
odoo18_logs() {
    log_info "Step 2: Monitoring Odoo logs..."
    echo "Press Ctrl+C to stop log monitoring"
    docker exec -it $CONTAINER_NAME tail -f $LOG_FILE
}

# Step 3: Update specific module
odoo18_update() {
    local module_name=$1
    
    if [ -z "$module_name" ]; then
        log_error "Module name is required"
        echo "Usage: odoo18_update <module_name>"
        return 1
    fi
    
    log_info "Step 3: Updating module '$module_name'..."
    docker exec -it $CONTAINER_NAME $ODOO_BIN -c $ODOO_CONFIG -d $DATABASE_NAME -u $module_name --no-xmlrpc --stop-after-init
    
    if [ $? -eq 0 ]; then
        log_success "Module '$module_name' updated successfully"
    else
        log_error "Failed to update module '$module_name'"
        return 1
    fi
}

# Step 4: Install new module
odoo18_install() {
    local module_name=$1
    
    if [ -z "$module_name" ]; then
        log_error "Module name is required"
        echo "Usage: odoo18_install <module_name>"
        return 1
    fi
    
    log_info "Installing module '$module_name'..."
    docker exec -it $CONTAINER_NAME $ODOO_BIN -c $ODOO_CONFIG -d $DATABASE_NAME -i $module_name --no-xmlrpc --stop-after-init
    
    if [ $? -eq 0 ]; then
        log_success "Module '$module_name' installed successfully"
    else
        log_error "Failed to install module '$module_name'"
        return 1
    fi
}

# Combined function: Complete restart and update workflow
odoo18_restart_and_update() {
    local module_name=$1
    
    if [ -z "$module_name" ]; then
        log_error "Module name is required"
        echo "Usage: odoo18_restart_and_update <module_name>"
        return 1
    fi
    
    log_info "Starting complete Odoo 18 restart and update workflow for module '$module_name'"
    
    # Prune and restart
    odoo18_prune
    odoo18_restart
    if [ $? -ne 0 ]; then
        return 1
    fi
    
    # Step 2: Brief log check
    log_info "Checking initial logs..."
    docker exec $CONTAINER_NAME tail -n 20 $LOG_FILE
    
    # Step 3: Update module
    odoo18_update $module_name
    local update_result=$?
    
    # Step 4: Check logs for errors
    log_info "Step 4: Checking logs for errors..."
    local recent_logs=$(docker exec $CONTAINER_NAME tail -n 50 $LOG_FILE)
    
    if echo "$recent_logs" | grep -qi "error\|exception\|traceback"; then
        log_error "Errors found in logs:"
        echo "$recent_logs" | grep -i "error\|exception\|traceback" | tail -10
        return 1
    elif [ $update_result -eq 0 ]; then
        log_success "Module update completed successfully with no errors"
        return 0
    else
        log_error "Module update failed"
        return 1
    fi
}

# Auto-retry function with error analysis
odoo18_auto_test() {
    local module_name=$1
    local max_retries=${2:-3}
    local retry_count=0
    
    if [ -z "$module_name" ]; then
        log_error "Module name is required"
        echo "Usage: odoo18_auto_test <module_name> [max_retries]"
        return 1
    fi
    
    log_info "Starting automated testing for module '$module_name' (max retries: $max_retries)"
    
    while [ $retry_count -lt $max_retries ]; do
        log_info "Attempt $((retry_count + 1)) of $max_retries"
        
        if odoo18_restart_and_update $module_name; then
            log_success "Module '$module_name' updated successfully!"
            return 0
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                log_warning "Attempt failed, retrying in 10 seconds..."
                sleep 10
            fi
        fi
    done
    
    log_error "All attempts failed for module '$module_name'"
    log_info "Recent error logs:"
    docker exec $CONTAINER_NAME tail -n 30 $LOG_FILE | grep -i "error\|exception\|traceback"
    return 1
}

# Run tests for a specific module
odoo18_test() {
    local module_name=$1
    local test_tags=$2
    
    if [ -z "$module_name" ]; then
        log_error "Module name is required"
        echo "Usage: odoo18_test <module_name> [test_tags]"
        return 1
    fi
    
   odoo18_stop_server
   if [ -n "$test_tags" ]; then
       log_info "Running tests for module '$module_name' with tags '$test_tags'..."
       docker exec -it $CONTAINER_NAME $ODOO_BIN -c $ODOO_CONFIG -d $DATABASE_NAME --test-enable --stop-after-init -u $module_name --no-xmlrpc --test-tags=$test_tags
   else
       log_info "Running all tests for module '$module_name'..."
       docker exec -it $CONTAINER_NAME $ODOO_BIN -c $ODOO_CONFIG -d $DATABASE_NAME --test-enable --stop-after-init -u $module_name --no-xmlrpc
   fi
   odoo18_start_server
}

# Stop Odoo server
odoo18_stop_server() {
   log_info "Stopping Odoo server..."
   docker exec $CONTAINER_NAME pkill -f odoo-bin
}

# Start Odoo server
odoo18_start_server() {
   log_info "Starting Odoo server..."
   docker exec -d $CONTAINER_NAME odoo-bin -c $ODOO_CONFIG
}

# Enter container bash mode
odoo18_bash() {
    log_info "Entering Odoo 18 container bash mode..."
    docker exec -it $CONTAINER_NAME bash
}

# Check container status
odoo18_status() {
    log_info "Checking Odoo 18 container status..."
    docker ps | grep $CONTAINER_NAME
    
    if docker ps | grep -q $CONTAINER_NAME; then
        log_success "Container is running"
        
        # Check if Odoo is responding
        if docker exec $CONTAINER_NAME ps aux | grep -q "odoo"; then
            log_success "Odoo process is running"
        else
            log_warning "Container is running but Odoo process may not be active"
        fi
    else
        log_error "Container is not running"
        return 1
    fi
}

# Show recent error logs
odoo18_errors() {
    log_info "Showing recent error logs..."
    docker exec $CONTAINER_NAME tail -n 100 $LOG_FILE | grep -i "error\|exception\|traceback\|failed" | tail -20
}

# Database operations
odoo18_db_backup() {
    local backup_name=${1:-"backup_$(date +%Y%m%d_%H%M%S)"}
    log_info "Creating database backup: $backup_name"
    docker exec $CONTAINER_NAME pg_dump -U odoo $DATABASE_NAME > "${backup_name}.sql"
    log_success "Database backup created: ${backup_name}.sql"
}

# Help function
odoo18_help() {
    echo "Odoo 18 Docker Development Commands:"
    echo "  odoo18_restart                    - Restart the Odoo container"
    echo "  odoo18_logs                       - Monitor real-time logs"
    echo "  odoo18_update <module>            - Update a specific module"
    echo "  odoo18_install <module>           - Install a new module"
    echo "  odoo18_restart_and_update <module> - Complete restart and update workflow"
    echo "  odoo18_auto_test <module> [retries] - Automated testing with retries"
    echo "  odoo18_test <module>              - Run tests for a module"
    echo "  odoo18_bash                       - Enter container bash mode"
    echo "  odoo18_status                     - Check container and Odoo status"
    echo "  odoo18_errors                     - Show recent error logs"
    echo "  odoo18_db_backup [name]           - Create database backup"
    echo "  odoo18_help                       - Show this help message"
}

# Health check function
odoo18_health() {
    log_info "Checking Odoo 18 health status..."
    
    # Check if container is running
    if ! docker ps | grep -q $CONTAINER_NAME; then
        log_error "Container is not running"
        return 1
    fi
    
    # Check web health endpoint
    if curl -sf $HEALTH_CHECK_URL > /dev/null 2>&1; then
        log_success "Odoo web service is healthy"
    else
        log_error "Odoo web service is not responding"
        return 1
    fi
    
    # Check database connection
    if docker exec $DB_CONTAINER_NAME pg_isready -U odoo > /dev/null 2>&1; then
        log_success "Database connection is healthy"
    else
        log_error "Database connection failed"
        return 1
    fi
    
    return 0
}

# Export functions for use in other scripts
export -f odoo18_restart odoo18_logs odoo18_update odoo18_install
export -f odoo18_restart_and_update odoo18_auto_test odoo18_test
export -f odoo18_bash odoo18_status odoo18_errors odoo18_db_backup odoo18_help

# Create aliases for common commands
alias odoo18-restart='odoo18_restart'
alias odoo18-logs='odoo18_logs'
alias odoo18-update='odoo18_update'
alias odoo18-install='odoo18_install'
alias odoo18-restart-and-update='odoo18_restart_and_update'
alias odoo18-test='odoo18_test'
alias odoo18-bash='odoo18_bash'
alias odoo18-status='odoo18_status'
alias odoo18-errors='odoo18_errors'
alias odoo18-help='odoo18_help'

log_success "Odoo 18 Docker automation scripts loaded successfully!"
log_info "Type 'odoo18-help' for available commands"

# Main execution block
if [ -n "$1" ]; then
   command=$1
   shift
   if type "$command" &>/dev/null; then
       "$command" "$@"
   else
       log_error "Unknown command: $command"
       odoo18_help
       exit 1
   fi
fi