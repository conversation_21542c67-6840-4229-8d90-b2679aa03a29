# AI Blog Post Generator Product Requirements Document (PRD)

## 1. Goal, Objective, and Context

* **Goal:** To develop a custom Odoo 18 module that automates the creation of website blog posts from various external and internal sources.
* **Objectives:**
    * To significantly reduce the manual effort and time required to write and format technical blog content.
    * To enable content creation from multiple sources, including YouTube video transcripts, user-defined topics, and the contents of a GitHub repository.
    * To leverage external AI models (specifically Google's Gemini models and optionally free models via OpenRouter) to generate high-quality, relevant blog text.
    * To create a seamless workflow within Odoo where a user can initiate a content request and receive a draft `blog.post` record, complete with a generated cover image, ready for review and publication.
* **Context:** This project is initiated because there is no standard application or module in the Odoo 18 ecosystem that provides this specific AI-driven content generation capability. The aim is to build a custom solution that directly integrates with the Odoo Website Blog functionality, providing a significant competitive advantage and content marketing efficiency.

## 2. Functional Requirements (MVP)

1.  **Source Selection:** The user must be able to select one of three content sources: YouTube URL, GitHub Repository Path, or Topic Description (Text Input).
2.  **Input & Configuration:** The system must provide an interface for the user to input the data for the selected source, enter necessary API keys (managed by the provider module), and select their preferred Gemini model.
3.  **Content Generation:** The user must be able to trigger the content generation process with a single action. The system shall process the input and call the selected external AI model to generate the main blog post content in HTML format.
4.  **Image Generation:** The system shall automatically generate a relevant cover image based on the blog post's content using an external image generation service.
5.  **Odoo Blog Post Creation:** Upon successful generation, the system must automatically create a new `blog.post` record in a **draft** state, populating the title, content, and cover image.

## 3. Non-Functional Requirements (MVP)

1.  **Usability:** The interface for generating a blog post within Odoo must be intuitive. Error messages returned from the LLM Provider module must be clearly displayed to the user.
2.  **Performance:** The call to the LLM Provider module must be executed as a **background job** to prevent freezing the user's screen. The system should provide a clear status indicator on the request record.
3.  **Security & Dependencies:** The custom module **must** declare a direct dependency on `vpcs_llm_provider`. All communication with LLMs **must** be channeled exclusively through this provider module.
4.  **Reliability:** The background job must gracefully handle any errors or exceptions returned from the `vpcs_llm_provider` module and update the request status accordingly.

## 4. User Interaction and Design Goals

* **Overall Vision & Experience:** The user experience will be a seamless, two-stage process. The first stage (content generation) will be managed through a simple backend interface. The second stage (refinement) will transition the user into Odoo's familiar website builder.
* **Key Interaction Paradigms:** A request-based system managed via Kanban, List, and Form views. All post-generation editing will leverage the standard Odoo Website Builder.
* **Core Screens/Views:**
    * A new `blog.generation.request` model with Kanban, List, and Form views.
    * Users will leverage the standard `blog.post` form and its "Preview on Website" functionality for editing and publishing.

## 5. Technical Assumptions

* **Core Architecture:** The solution will be a native **Odoo 18 custom module**.
* **Repository Structure:** This module will be developed and maintained in its own **dedicated, private Git repository**.
* **Key Dependency:** The module will have a mandatory dependency on the **`vpcs_llm_provider`** (v18.0) module from the Odoo App Store.
* **External Services & Libraries:** The project will indirectly use Google Gemini, Hugging Face API (via the provider module), and the `pytubefix` Python library.

## 6. Epic Overview

### Epic 1: Foundational Module & UI Scaffolding

* **Goal:** To create a basic, installable Odoo module with the necessary data model (`blog.generation.request`) and a functional, but not yet automated, user interface.
* **Stories:**
    * **1.1: Module Initialization:** Create the installable `website_blog_ai_generator` module with correct dependencies.
    * **1.2: Data Model & Security:** Create the `blog.generation.request` model with all necessary fields and access rights.
    * **1.3: User Interface Views:** Create the Form, List, and Kanban views, and the menu item to access them.

### Epic 2: Core Content Generation Engine

* **Goal:** To build the backend "engine" that does the actual work of processing inputs and communicating with external AI services.
* **Stories:**
    * **2.1: GitHub Repository Scanner:** Create the logic to process a local GitHub repository and consolidate its content.
    * **2.2: YouTube Transcript Processor:** Create the logic to extract and clean a transcript from a YouTube URL.
    * **2.3: Content Generation Service:** Create the central service that uses the `vpcs_llm_provider` to generate HTML blog content.
    * **2.4: Image Generation Service:** Create the service to generate a cover image based on the blog content.

### Epic 3: Workflow Automation & Integration

* **Goal:** To connect the UI built in Epic 1 with the engine from Epic 2, creating the final, functional workflow.
* **Stories:**
    * **3.1: Trigger Background Generation:** Make the "Generate" button trigger a background job and update the request status.
    * **3.2: Execute End-to-End Generation in Job:** Create the background job that calls all the engine services in the correct sequence.
    * **3.3: Finalize Blog Post Creation:** Upon job success, create the draft `blog.post` in Odoo and update the request status. Handle failures gracefully.

## 7. Change Log

| Change               | Date         | Version | Description      | Author |
| -------------------- | ------------ | ------- | ---------------- | ------ |
| Initial PRD Creation | 2025-06-15   | 1.0     | First full draft | John   |

---
## Checklist Results Report

I have validated this PRD against the standard **Product Manager (PM) Requirements Checklist**. All sections have passed, confirming that the document is comprehensive, well-structured, and ready for the next phase.

| Category                          | Status | Critical Issues |
| --------------------------------- | ------ | --------------- |
| 1. Problem Definition & Context   | PASS   | None            |
| 2. MVP Scope Definition           | PASS   | None            |
| 3. User Experience Requirements   | PASS   | None            |
| 4. Functional Requirements        | PASS   | None            |
| 5. Non-Functional Requirements    | PASS   | None            |
| 6. Epic & Story Structure         | PASS   | None            |
| 7. Technical Guidance             | PASS   | None            |
| 8. Cross-Functional Requirements  | PASS   | None            |
| 9. Clarity & Communication        | PASS   | None            |

**Final Decision:** **READY FOR ARCHITECT**

---
## Initial Architect Prompt

This PRD is now complete. The next step is to engage the **Architect** to create the detailed technical design. Here is the prompt for them:

**Objective:** Please review the PRD for the "AI Blog Post Generator" module. Your task is to create the technical architecture document.

**Key Technical Guidance from PRD:**

* **Architecture:** This is a native Odoo 18 custom module.
* **Repository:** It will be developed in its own dedicated Git repository.
* **Core Dependency:** The architecture must be designed around the `vpcs_llm_provider` (v18.0) Odoo module, which will handle all LLM and image generation API calls. Our custom module will be a consumer of this provider module.
* **Workflow:** The system must use a background job to process generation requests asynchronously to keep the UI responsive.
* **Models:** A new model, `blog.generation.request`, will be required to manage the workflow.

Please proceed with the `Create Architecture` task to define the specific Python classes, methods, and file structures required to implement the epics and stories outlined in this PRD.
