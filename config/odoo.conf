[options]
test_enable = True
; Basic database settings
db_host = database18-stack
db_port = 5432
db_user = odoo
db_password = odoo
db_template = template0
db_sslmode = prefer

; Paths
addons_path = /mnt/extra-addons,/usr/src/odoo/addons
data_dir = /var/lib/odoo

; Performance settings
db_maxconn = 32
limit_memory_soft = 2147483648 
limit_memory_hard = 4294967296
limit_time_cpu = 600
limit_time_real = 1200
limit_request = 8192
max_cron_threads = 2
workers = 0

; Logging
log_level = info
log_handler = [':INFO']
logfile = /var/log/odoo/odoo.log
log_db = False
log_db_level = warning
logrotate = True

; Security
admin_passwd = 240502
list_db = True
proxy_mode = True

; Test configuration
[test]
; Enable test mode
test_enable = True
; Enable file storage for tests
test_file = /var/lib/odoo/test-results.xml
; Enable test tags (comma-separated)
test_tags = /website_blog_ai_generator,/odoo_toc_integration

; Worker configuration for tests
[workers]
workers = 4
limit_memory_soft = 1073741824
limit_memory_hard = 1288490189
limit_time_cpu = 300
limit_time_real = 600

; Database settings for tests
[db_filter]
db_filter = .*

; Performance optimization for tests
[runbot]
max_cron_threads = 1

; Disable demo data for tests
without_demo = all
osv_memory_count_limit = 0
osv_memory_age_limit = 0

; Disable update modules list on startup
update = {}

; Disable auto-reload on Python files
dev = None

; Disable web tour
server_wide_modules = web,web_tour_disable,queue_job
channels = root:1

; Disable gevent for tests
gevent_port = False

; Disable websocket for tests
websocket = False

