#!/bin/bash

# Stop any running containers
echo "Stopping any running containers..."
docker-compose down

# Remove volumes to start fresh
echo "Removing volumes to start fresh..."
docker-compose down -v

# Start the database container only
echo "Starting the database container..."
docker-compose up -d database18-stack

# Wait for the database to be ready (increased wait time)
echo "Waiting for database to be ready..."
sleep 30

# Check if PostgreSQL is ready
echo "Checking if PostgreSQL is ready..."
docker-compose exec database18-stack pg_isready -U odoo

# Initialize the database with the base module
echo "Initializing the database 'llmdb18' with base module..."
docker-compose run --rm odoo18-stack odoo --stop-after-init -i base -d llmdb18 --no-http --without-demo=all

# Wait for initialization to complete
echo "Waiting for initialization to complete..."
sleep 10

# Start all services
echo "Starting all services..."
docker-compose up -d

echo "Database initialization complete. Odoo should now be accessible at http://localhost:8069"
echo "Wait a few moments for Odoo to fully start, then you can install your custom module."
echo ""
echo "To install your custom module, run:"
echo "docker-compose exec odoo18-stack odoo -i odoo_law_agents -d llmdb18"
