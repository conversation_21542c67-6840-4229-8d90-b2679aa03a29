#!/bin/bash

# Stop any running containers
docker-compose down

# Start the database container only
docker-compose up -d database18-stack

# Wait for the database to be ready
echo "Waiting for database to be ready..."
sleep 10

# Run Odoo with the init parameter to initialize the database with custom name
echo "Initializing the database 'llmdb18' with base module..."
docker-compose run --rm odoo18-stack odoo --stop-after-init -i base -d llmdb18

# If successful, update the docker-compose.yml to remove the init parameter
sed -i '' 's/command: odoo --stop-after-init -i base/command: odoo/g' docker-compose.yml

# Start all services
echo "Starting all services..."
docker-compose up -d

echo "Database initialization complete. Odoo should now be accessible at http://localhost:8069"
