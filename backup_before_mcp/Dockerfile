# Use a specific Python version for stability
FROM python:3.11-slim-bullseye

# Set metadata labels
LABEL maintainer="<PERSON><PERSON> <<EMAIL>>"
LABEL description="Odoo 18 development environment with custom addons."

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONIOENCODING=utf-8 \
    PYTHONDONTWRITEBYTECODE=1 \
    LANG=en_US.UTF-8 \
    LC_ALL=en_US.UTF-8 \
    LANGUAGE=en_US.UTF-8 \
    ODOO_VERSION=18.0 \
    ODOO_HOME=/usr/src/odoo \
    ODOO_VENV=/opt/odoo-venv

ENV PATH="$ODOO_VENV/bin:$ODOO_HOME:$PATH"

# Install system dependencies required for Odoo
RUN set -x; \
    apt-get update \
    && apt-get install -y --no-install-recommends \
        build-essential \
        curl \
        git \
        gosu \
        libffi-dev \
        libldap2-dev \
        libsasl2-dev \
        libpq-dev \
        libssl-dev \
        libxml2-dev \
        libxslt1-dev \
        locales \
        postgresql-client \
        wget \
        zlib1g-dev \
        # Install Rust
        && curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y \
        && . $HOME/.cargo/env \
    # Configure locales
    && sed -i -e 's/# en_US.UTF-8 UTF-8/en_US.UTF-8 UTF-8/' /etc/locale.gen \
    && dpkg-reconfigure --frontend=noninteractive locales \
    && update-locale LANG=en_US.UTF-8 LC_ALL=en_US.UTF-8 \
    # Clean up
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js and frontend dependencies
RUN apt-get update && apt-get install -y curl gnupg \
    && curl -fsSL https://deb.nodesource.com/setup_20.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g less less-plugin-clean-css \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Install wkhtmltopdf
RUN wget https://github.com/wkhtmltopdf/packaging/releases/download/0.12.6.1-3/wkhtmltox_0.12.6.1-3.bullseye_amd64.deb \
    && apt-get update \
    && apt-get install -y --no-install-recommends ./wkhtmltox_0.12.6.1-3.bullseye_amd64.deb \
    && rm ./wkhtmltox_0.12.6.1-3.bullseye_amd64.deb \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Create a non-root user to run Odoo
RUN useradd -m -d /home/<USER>/bin/bash odoo

# Clone Odoo source from GitHub
RUN git clone --depth 1 --branch ${ODOO_VERSION} https://github.com/odoo/odoo.git ${ODOO_HOME}

# Create Python virtual environment
RUN python3 -m venv ${ODOO_VENV}

# Install Python dependencies for Odoo
RUN ${ODOO_VENV}/bin/pip install --no-cache-dir --upgrade pip \
    && ${ODOO_VENV}/bin/pip install --no-cache-dir -r ${ODOO_HOME}/requirements.txt

# Copy and install custom Python dependencies
COPY requirements.txt /tmp/requirements.txt
RUN ${ODOO_VENV}/bin/pip install --no-cache-dir -r /tmp/requirements.txt \
    && rm -f /tmp/requirements.txt

# Copy custom addons
COPY ./custom-addons /mnt/extra-addons/

# Copy configuration and entrypoint
COPY config/odoo.conf /etc/odoo/odoo.conf
COPY docker-entrypoint.sh /docker-entrypoint.sh

# Create log directory
RUN mkdir -p /var/log/odoo

# Set permissions
RUN chown -R odoo:odoo ${ODOO_HOME} \
    && chown -R odoo:odoo ${ODOO_VENV} \
    && chown -R odoo:odoo /mnt/extra-addons \
    && chown -R odoo:odoo /var/log/odoo \
    && chown odoo:odoo /etc/odoo/odoo.conf \
    && chmod 0640 /etc/odoo/odoo.conf \
    && chmod +x /docker-entrypoint.sh \
    && chown odoo:odoo /docker-entrypoint.sh

# Expose standard Odoo ports
EXPOSE 8069 8072

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8069/web/health || exit 1

# Switch to the odoo user
# USER odoo

# Set working directory
WORKDIR ${ODOO_HOME}

# Set entrypoint and default command
ENTRYPOINT ["/docker-entrypoint.sh"]
CMD ["odoo-bin"]