# AI and Machine Learning Dependencies
openai>=1.0.0
anthropic>=0.34.0
groq>=0.4.0
google-generativeai>=0.3.0
google-genai>=0.1.0
pydantic-ai>=0.1.0
huggingface-hub>=0.20.0

# YouTube processing
pytubefix>=6.0.0

#python-dotenv is used for environment variable management
python-dotenv>=1.0.0
# Additional dependencies that might be needed for AI libraries
httpx>=0.24.0
httpcore>=0.17.0
typing-extensions>=4.5.0
pydantic>=2.0.0
tiktoken>=0.5.0
tenacity>=8.0.0

# Optional: For enhanced AI functionality
numpy>=1.21.0
pandas>=1.3.0

# For async operations that AI libraries might use
aiohttp>=3.8.0
asyncio-throttle>=1.0.0

# Security and encoding support
cryptography>=3.4.8
charset-normalizer>=3.0.0

#pydantic is used for data validation and settings management in many AI libraries
pydantic>=2.0.0

# MCP support with Gradio interface on Hugging Face
gradio>=4.0.0
# Gradio client for interacting with Gradio apps
gradio_client>=0.6.0

# Database and SQL
sqlalchemy>=1.4.0
sqlparse
psycopg2-binary
pgvector
# LlamaIndex core and extensions
llama-index-core>=0.9.48
llama-index-vector-stores-postgres>=0.1.2
llama-index-embeddings-openai>=0.1.3

# For Odoo view related clarity
jingtrang

# WhatsApp MCP Integration Dependencies
requests>=2.28.0
fastmcp>=0.1.0
websockets>=11.0.0
# For audio processing in WhatsApp bridge
ffmpeg-python>=0.2.0
qrcode[pil]==7.4.2 
Pillow==10.0.1

# For PDF processing in WhatsApp bridge
pdfminer.six