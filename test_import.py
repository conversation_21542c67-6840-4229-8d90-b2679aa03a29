#!/usr/bin/env python3
"""
Test script to verify the imports from simple_tracing.py
"""

import sys
import os

# Add the current directory to the Python path
sys.path.insert(0, os.path.abspath('.'))

try:
    # Try to import the module
    from custom_addons.odoo_law_agents.tools.simple_tracing import Mo<PERSON><PERSON>race<PERSON>, get_tracing_config, setup_opik_tracing
    print("Import successful!")

    # Create a mock tracer instance
    tracer = MockTracer(project_name="test_project")
    print(f"Created MockTracer with project: {tracer.project_name}")

    print("All tests passed!")
except ImportError as e:
    print(f"Import error: {e}")
except Exception as e:
    print(f"Error: {e}")
