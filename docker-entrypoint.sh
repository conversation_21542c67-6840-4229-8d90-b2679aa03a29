#!/bin/bash

set -e

# Ensure odoo user owns the data directory
if [ "$(stat -c %U /var/lib/odoo)" != "odoo" ]; then
    echo "Fixing permissions for /var/lib/odoo..."
    chown -R odoo:odoo /var/lib/odoo
fi

ODOO_RC=${ODOO_RC:-"/etc/odoo/odoo.conf"}

# Activate the virtual environment
if [ -f "/opt/odoo-venv/bin/activate" ]; then
    . /opt/odoo-venv/bin/activate
    echo "Python virtual environment activated."
else
    echo "Warning: Virtual environment not found at /opt/odoo-venv."
fi

# Set the postgres database host, port, user and password according to the environment
: ${HOST:=${DB_HOST:='database18-stack'}}
: ${PORT:=${DB_PORT:=5432}}
: ${USER:=${DB_USER:=${POSTGRES_USER:='odoo'}}}
: ${PASSWORD:=${DB_PASSWORD:=${POSTGRES_PASSWORD:='odoo'}}}
: ${DB_NAME:=${POSTGRES_DB:='postgres'}}

# Export PostgreSQL environment variables for pg_dump
export PGHOST="$HOST"
export PGPORT="$PORT"
export PGUSER="$USER"
export PGPASSWORD="$PASSWORD"

# Fix PostgreSQL version mismatch issue
# Force pg_dump to ignore version mismatch warnings
export PGDUMP_BINARY="/usr/lib/postgresql/13/bin/pg_dump"
if [ -f "$PGDUMP_BINARY" ]; then
    ln -sf "$PGDUMP_BINARY" /usr/bin/pg_dump_fixed
    # Create wrapper that ignores version warnings
    cat > /usr/bin/pg_dump_wrapper << 'WRAPPER_EOF'
#!/bin/bash
# PostgreSQL version compatibility wrapper
export PGPASSWORD="${PGPASSWORD:-odoo}"
/usr/lib/postgresql/13/bin/pg_dump --no-password "$@" 2>/dev/null || /usr/lib/postgresql/13/bin/pg_dump "$@"
WRAPPER_EOF
    chmod +x /usr/bin/pg_dump_wrapper
    ln -sf /usr/bin/pg_dump_wrapper /usr/bin/pg_dump
fi

# Verify Python environment and AI dependencies
echo "Using Python: $(which python3)"
echo "Python version: $(python3 --version)"
echo "Checking AI dependencies..."

# Check if AI libraries are properly installed
python3 -c "
try:
    import openai
    print('✓ OpenAI library available')
except ImportError:
    print('✗ OpenAI library not found')

try:
    import anthropic
    print('✓ Anthropic library available')
except ImportError:
    print('✗ Anthropic library not found')

try:
    import groq
    print('✓ Groq library available')
except ImportError:
    print('✗ Groq library not found')

try:
    import google.generativeai
    print('✓ Google Generative AI library available')
except ImportError:
    print('✗ Google Generative AI library not found')

try:
    import pytubefix
    print('✓ PyTubeFix library available')
except ImportError:
    print('✗ PyTubeFix library not found')
"

# Function to check if a parameter exists in Odoo config
DB_ARGS=()
function check_config() {
    param="$1"
    value="$2"
    if ! grep -q -E "^\s*\b${param}\b\s*=" "$ODOO_RC" ; then
        DB_ARGS+=("--${param}")
        DB_ARGS+=("${value}")
    fi
}

# Check if the first argument is a flag
if [ "${1:0:1}" = '-' ]; then
    set -- odoo-bin "$@"
fi

# Wait for PostgreSQL with timeout
echo "Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if PGPASSWORD=$PASSWORD psql -h "$HOST" -U "$USER" -d postgres -c '\q' 2>/dev/null; then
        echo "PostgreSQL is ready!"
        break
    fi
    echo "Waiting for PostgreSQL... ($i/30)"
    sleep 2
    if [ $i -eq 30 ]; then
        echo "Error: PostgreSQL did not become ready in time."
        exit 1
    fi
done

# If odoo command is used
if [ "$1" = 'odoo-bin' ]; then
    # Add database connection parameters if not in config
    check_config "db_host" "$HOST"
    check_config "db_port" "$PORT"
    check_config "db_user" "$USER"
    check_config "db_password" "$PASSWORD"

    # Add additional parameters for better performance
    # Adjust these based on your container resources
    DB_ARGS+=("--max-cron-threads=1")
    DB_ARGS+=("--workers=0")
    DB_ARGS+=("--limit-memory-hard=4294967296")
    DB_ARGS+=("--limit-memory-soft=2147483648")
    DB_ARGS+=("--db-template=template0")

    # Add proxy mode if behind reverse proxy
    if [ "${PROXY_MODE:-false}" = "true" ]; then
        DB_ARGS+=("--proxy-mode")
    fi

    # Get the database name from the config file
    ODOO_DB_NAME=$(grep -E "^\s*db_name\s*=" "$ODOO_RC" | sed -e 's/.*=\s*//' | tr -d '[:space:]')

    # Check if the application database exists and create it if not
    if [ -n "$ODOO_DB_NAME" ] && ! PGPASSWORD=$PASSWORD psql -h "$HOST" -U "$USER" -d postgres -lqt | cut -d \| -f 1 | grep -qw "$ODOO_DB_NAME"; then
        echo "Database '$ODOO_DB_NAME' does not exist. Creating it..."
        PGPASSWORD=$PASSWORD createdb -h "$HOST" -U "$USER" -T template0 -E UTF8 "$ODOO_DB_NAME"
        echo "Database '$ODOO_DB_NAME' created. Initializing with 'base' module..."
        DB_ARGS+=("-i" "base")
        DB_ARGS+=("--stop-after-init")
    elif [ -n "$ODOO_DB_NAME" ]; then
        echo "Database '$ODOO_DB_NAME' already exists."
    fi

    # Correctly set the addons path, overriding the one in the config if necessary
    DB_ARGS+=("--addons-path=${ODOO_HOME}/addons,/mnt/extra-addons")
fi

# Start WhatsApp Bridge on port 8082
echo "Starting WhatsApp Bridge..."
BRIDGE_DIR="/mnt/extra-addons/odoo_whatsapp_mcp/bridge"
if [ -f "$BRIDGE_DIR/production_bridge.go" ]; then
    cd "$BRIDGE_DIR"
    
    # Build bridge if executable doesn't exist or is older than source
    if [ ! -f "bridge" ] || [ "production_bridge.go" -nt "bridge" ]; then
        echo "Building WhatsApp Bridge..."
        rm -f bridge
        go build -o bridge production_bridge.go
        chown odoo:odoo bridge
    fi
    
    # Start bridge
    chmod +x bridge
    gosu odoo nohup ./bridge > /var/log/whatsapp_bridge.log 2>&1 &
    BRIDGE_PID=$!
    echo $BRIDGE_PID > /tmp/bridge.pid
    echo "WhatsApp Bridge started with PID: $BRIDGE_PID on port 8082"
    
    # Wait and verify bridge is running
    sleep 3
    if kill -0 $BRIDGE_PID 2>/dev/null; then
        echo "✅ WhatsApp Bridge is running successfully"
    else
        echo "❌ WhatsApp Bridge failed to start - check logs at /var/log/whatsapp_bridge.log"
    fi
else
    echo "WhatsApp Bridge source not found at $BRIDGE_DIR/production_bridge.go"
fi

# Start HuggingFace MCP server in the background if HF_API_KEY is set
if [ -n "$HF_API_KEY" ] && [ "$HF_API_KEY" != "REPLACE_WITH_YOUR_HUGGINGFACE_API_KEY" ] && [ "$HF_API_KEY" != "your_huggingface_api_key_here" ]; then
    echo "Starting HuggingFace MCP server (@llmindset/mcp-hfspace)..."
    gosu odoo npx -y @llmindset/mcp-hfspace --work-dir=/tmp/mcp-files &
    MCP_PID=$!
    echo "MCP server started with PID: $MCP_PID"
    
    # Wait for MCP server to start
    sleep 5
    
    # Check if MCP server is running
    if kill -0 $MCP_PID 2>/dev/null; then
        echo "HuggingFace MCP server is running successfully on port 8081"
    else
        echo "Warning: HuggingFace MCP server failed to start - using LLM fallback"
    fi
else
    echo "HF_API_KEY not configured - using LLM fallback for AI tasks"
fi

echo "Starting Odoo with arguments: $@ ${DB_ARGS[@]}"
exec gosu odoo "$@" --config "${ODOO_RC}" "${DB_ARGS[@]}"