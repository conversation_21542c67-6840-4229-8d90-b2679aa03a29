#!/bin/bash
# Fix pg_dump version mismatch in Docker container
# Based on Odoo forum solution

echo "Applying pg_dump version mismatch fix..."

# Create a pg_dump wrapper that uses database container
docker exec odoo18-stack bash -c 'cat > /usr/bin/pg_dump << "EOF"
#!/bin/bash
# pg_dump wrapper for Odoo Docker environment
# Executes pg_dump in database container to avoid version mismatch

# Parse arguments to extract database connection info
ARGS=()
DB_NAME=""
HOST=""
USER=""

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--host)
            HOST="$2"
            shift 2
            ;;
        -U|--username)
            USER="$2"
            shift 2
            ;;
        -d|--dbname)
            DB_NAME="$2"
            shift 2
            ;;
        *)
            ARGS+=("$1")
            shift
            ;;
    esac
done

# If no database specified, use last argument as database name
if [ -z "$DB_NAME" ] && [ ${#ARGS[@]} -gt 0 ]; then
    DB_NAME="${ARGS[-1]}"
    unset "ARGS[-1]"
fi

# Execute pg_dump in database container
docker exec -e PGPASSWORD="${PGPASSWORD:-odoo}" database18-stack \
    pg_dump -U "${USER:-odoo}" -d "${DB_NAME}" "${ARGS[@]}"
EOF

chmod +x /usr/bin/pg_dump'

echo "✅ pg_dump wrapper created successfully"
echo "Now testing the fix..."

# Test the fix
docker exec odoo18-stack bash -c "PGPASSWORD=odoo pg_dump -h database18-stack -U odoo --no-owner --schema-only llmdb18 | head -3"

if [ $? -eq 0 ]; then
    echo "✅ pg_dump fix applied successfully!"
else
    echo "❌ Fix failed, reverting..."
    docker exec odoo18-stack bash -c "rm -f /usr/bin/pg_dump && ln -sf ../share/postgresql-common/pg_wrapper /usr/bin/pg_dump"
fi