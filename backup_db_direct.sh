#!/bin/bash
# Direct database backup using the database container

DB_NAME=${1:-llmdb18}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${DB_NAME}_${TIMESTAMP}.sql"

echo "Creating backup of database: $DB_NAME using database container"

# Use the database container's pg_dump (same version as server)
docker exec database18-stack pg_dump -U odoo -d $DB_NAME --no-owner --no-privileges --clean --if-exists > "$BACKUP_FILE"

if [ $? -eq 0 ]; then
    echo "✅ Backup created successfully: $BACKUP_FILE"
    echo "File size: $(ls -lh $BACKUP_FILE | awk '{print $5}')"
    echo "To restore: docker exec -i database18-stack psql -U odoo -d $DB_NAME < $BACKUP_FILE"
else
    echo "❌ Backup failed"
    exit 1
fi