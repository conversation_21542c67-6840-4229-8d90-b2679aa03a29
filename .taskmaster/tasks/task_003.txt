# Task ID: 3
# Title: Develop WhatsApp Configuration UI in `odoo_whatsapp_mcp`
# Status: deferred
# Dependencies: None
# Priority: medium
# Description: Develop the `odoo_whatsapp_mcp` module, creating a dedicated Odoo app menu and forms for configuring the Go WhatsApp Bridge connection details and initiating QR code display for authentication.
# Details:


# Test Strategy:


# Subtasks:
## 1. Integrate Go WhatsApp Bridge into odoo_whatsapp_mcp [done]
### Dependencies: None
### Description: Copied the Go WhatsApp Bridge code into the module and created a helper script to run it as a subprocess. Modified the WhatsApp configuration to call this script to start the bridge and get the QR code.
### Details:


