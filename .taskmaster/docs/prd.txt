# Odoo MCP & AI Integration Product Requirements Document (PRD)

## Goal, Objective and Context

The primary goal of this project is to seamlessly integrate powerful communication and AI capabilities directly into the Odoo ecosystem. This will be achieved by leveraging Multi-Cloud Platform (MCP) server tools, specifically focusing on WhatsApp messaging and Hugging Face AI models.

The core objectives are:
* To enable direct, out-of-the-box WhatsApp messaging functionality within Odoo, simplifying the setup process by utilizing QR code authentication and minimizing complex API configurations and multiple approval steps.
* To integrate the versatile capabilities of Hugging Face models and Spaces directly within the Odoo ecosystem, allowing for dynamic integration across any existing or new Odoo application. This will empower Odoo with AI functionalities such as image generation, translation, sentiment analysis, and general model inference, accessible in a streamlined manner.

This initiative aims to enhance Odoo's value proposition by providing users with immediate access to advanced messaging and AI processing, fostering a more dynamic and intelligent business environment.

## Functional Requirements (MVP)

The Minimum Viable Product (MVP) will focus on establishing foundational integrations with WhatsApp and Hugging Face, enabling core communication and AI capabilities within Odoo.

* **WhatsApp Integration:**
    * Enable direct sending of WhatsApp messages from any existing or new Odoo record.
    * This functionality will be integrated primarily via **Odoo's chatter mechanism**, allowing for broad applicability across various Odoo models.
    * The integration will leverage the `whatsmeow` library, supporting key features such as:
        * Sending text and media messages to private chats and groups.
        * Receiving all incoming messages.
        * Managing groups and receiving group change events.
        * Joining groups via invite messages, and creating/using invite links.
        * Sending and receiving typing notifications, delivery, and read receipts.
        * Reading and writing WhatsApp app state (e.g., contact list, chat pin/mute status).
        * Handling retry receipts for message decryption failures.
        * Experimental support for sending status messages.
* **Hugging Face Integration:**
    * Integrate the `mcp-hfspace` tool to enable direct Hugging Face functionalities within Odoo.
    * Initial AI capabilities to be supported include:
        * Image generation.
        * Translation.
        * Sentiment analysis.
        * General model inference.
    * The Hugging Face API connectivity will be achieved through updates to the existing **LLM Provider parent model**, enhancing its base functionality to support these new AI capabilities.

## Non Functional Requirements (MVP)

* **Performance:**
    * **Hugging Face AI Inferences (Image Generation, Translation, Sentiment Analysis, Model Inference):** A standard **2-minute timeout limit per request** will be implemented for Hugging Face AI inferences. This limit will be configurable in Odoo settings to allow for adjustments as per needs.
    * **WhatsApp Messaging:** WhatsApp messages sent through Odoo are expected to be **immediate**.
* **Security:**
    * The custom application will adhere to **Odoo's standard security parameters and related timeout configurations**, which can be managed through the Odoo config file.
    * API keys/credentials for external AI services must be stored securely (e.g., encrypted via an existing Odoo module for API key storage with Odoo password security configuration). Hardcoding credentials is strictly forbidden.
* **Usability:**
    * The QR code linking and authentication process for WhatsApp will replicate the existing Odoo frontend configuration prompt style from the Odoo platform to maintain a consistent user experience.
    * The WhatsApp chatter integration should be seamless and intuitive for Odoo users.
    * AI results from Hugging Face (e.g., image generation, translation) should be presented effectively to the user (e.g., direct field updates, pop-up notifications with results, or attached documents).
* **Scalability:**
    * Specific anticipated volumes for daily/hourly WhatsApp messages or frequency/volume of Hugging Face AI requests, and the number of concurrent users, are currently not defined but will be a consideration for future scaling.
* **Reliability:**
    * Acceptable downtime or failure rates for WhatsApp messaging and Hugging Face AI features are currently not explicitly defined.
    * The system should consider appropriate behavior in Odoo if the Go WhatsApp Bridge or Hugging Face MCP server is temporarily unavailable (e.g., implementing retry mechanisms, providing user notifications).
    * Authentication challenges with WhatsApp, such as QR code not displaying, device limits, message history loading delays, and out-of-sync issues, are acknowledged and require built-in resolution mechanisms (e.g., restarting authentication script, guiding users to remove linked devices, or providing instructions to reset database files when WhatsApp is out of sync).

## User Interaction and Design Goals

The user experience for the integrated WhatsApp and Hugging Face functionalities will prioritize Odoo's native design principles, focusing on intuitiveness, seamless integration, and efficiency.

* **Overall Vision & Experience:** The desired experience is one of enhanced communication and intelligent automation directly within Odoo's familiar interface. Users should feel empowered to leverage advanced AI and messaging with minimal friction.
* **Key Interaction Paradigms:**
    * **WhatsApp Messaging:** Will be primarily integrated through the **Odoo chatter mechanism**, making it a familiar and consistent experience across any Odoo model that utilizes the mail thread model. This enables immediate messaging from various Odoo records.
    * **WhatsApp Configuration:** A dedicated **separate app menu with menu items and forms** will be created for WhatsApp configuration. This will guide users through all necessary steps to achieve a "green" (configured and ready) status for WhatsApp integration, likely involving QR code linking.
    * **Hugging Face AI:** For tasks involving model inference, the system will propose **top 2-3 best available model results** for the user to choose from, if multiple options are applicable.
    * **AI Command Integration:** Hugging Face AI functionalities will be directly accessible within the Odoo chatter via a **"/" command**. This allows for dynamic integration with any Odoo model supporting chatter.
    * **Website Integration:** A **website snippet** will be prepared to allow direct usage of related AI tasks within the website DOM, with appropriate authentication measures in place.
    * **Backend Configuration:** Specific **Odoo backend configuration forms** also need to be created for more detailed and applicable settings related to both WhatsApp and Hugging Face integrations.
* **Core Screens/Views (Conceptual):**
    * WhatsApp Configuration forms (within a dedicated app menu).
    * Existing Odoo records (e.g., Contacts, Sales Orders, Leads) showcasing WhatsApp chatter integration.
    * Chatter views with the new "/" command interface for AI interactions.
    * AI model selection/results display within Odoo.
    * Website pages utilizing the new AI snippets.
    * Hugging Face specific backend configuration forms.
* **Accessibility Aspirations:** While not explicitly detailed, the aim will be to maintain Odoo's inherent accessibility standards, ensuring features are usable by diverse user groups.
* **Branding Considerations:** The integration will adhere to Odoo's standard look and feel, ensuring a cohesive user experience without introducing conflicting branding elements.
* **Target Devices/Platforms:** Primarily web desktop, consistent with Odoo's main interface.

## Technical Assumptions

This section outlines the foundational technical decisions and assumptions guiding the development of the Odoo custom application.

* **Repository & Service Architecture:** The custom application will be developed as **separate, distinct Odoo custom modules**:
    * `odoo_whatsapp_mcp`: For WhatsApp integration via the Go WhatsApp Bridge.
    * `odoo_huggingface_mcp`: For Hugging Face integration.
    * An **enhancement to the existing `vpcs_llm_provider_extension` module** to incorporate Pydantic AI and Hugging Face model interaction.
* **AI Agent and MCP Capability Framework:** **Pydantic AI** (`https://ai.pydantic.dev/llms-full.txt`) will be the primary library/framework used for interacting with AI agents and leveraging MCP capabilities. This will simplify connectivity and interaction with various AI models.
* **WhatsApp Integration:** Relies on the Go WhatsApp Bridge (`whatsapp-bridge`) and the `whatsmeow` library for WhatsApp Web API connectivity.
    * **Resource URL:** `https://github.com/lharries/whatsapp-mcp`
* **Hugging Face Integration:** Relies on the `mcp-hfspace` tool for direct Hugging Face functionalities.
    * **Resource URL:** `https://github.com/evalstate/mcp-hfspace`
* **LLM Provider Enhancement:** The existing `vpcs_llm_provider_extension` parent module will be enhanced to support Hugging Face functionalities and Pydantic AI integration.
* **Frontend Framework:** Odoo's existing frontend framework (OWL, QWeb, native Odoo views) will be used for all UI components. No external frontend frameworks will be introduced.
* **Backend Language:** Python (Odoo's native backend language).
* **Database:** PostgreSQL (Odoo's native database). Specific integration strategy for WhatsApp's SQLite history will need to be defined (e.g., migration, synchronization, or kept separate but accessible).
* **Authentication for WhatsApp:** QR code linking and authentication will be managed from the Odoo frontend configuration, replicating existing Odoo configuration prompt styles.
* **Communication with External Tools:** MCP server tools will be used as the bridge to external services (WhatsApp, Hugging Face).
* **Timeout Configuration:** A configurable 2-minute timeout for Hugging Face requests will be implemented.
* **Security & Configuration:** Adherence to Odoo's standard security parameters and timeout configurations via the Odoo config file.

### Testing requirements

We will primarily leverage the **Odoo 18 existing unit testing environment** for validating the functionality of the custom modules. This will include:

* **Unit Tests:** Focused on individual functions, methods, and logic within each module (`odoo_whatsapp_mcp`, `odoo_huggingface_mcp`, `vpcs_llm_provider_extension`).
* **Integration Tests:** To validate the interactions between the custom Odoo modules and the external MCP tools (Go WhatsApp Bridge, `mcp-hfspace`), as well as the integration with Odoo's core functionalities (e.g., chatter, mail thread).
* **Manual Testing:** To ensure a seamless user experience, particularly for the WhatsApp chatter integration, QR code linking process, and the display of Hugging Face AI results.
* **End-to-End (E2E) Testing (Future Consideration):** While not a primary focus for the MVP, E2E testing could be considered in later phases to validate complete user journeys involving both Odoo and external services.

## Epic Overview

* **Epic 1: Foundational MCP & AI Provider Setup**
    * **Goal:** To establish the core technical infrastructure for integrating MCP capabilities and Pydantic AI into Odoo, ensuring basic connectivity and the readiness of the `vpcs_llm_provider_extension` module.
    * **Story 1.1:** As an Odoo Administrator, I want to install the `vpcs_llm_provider_extension` module so that the foundational AI agent capabilities using Pydantic AI are available within Odoo.
        * **Acceptance Criteria:**
            * The `vpcs_llm_provider_extension` module can be successfully installed in Odoo 18.
            * The module's manifest correctly declares its name as `vpcs_llm_provider_extension` and any necessary dependencies.
            * Basic Pydantic AI integration components are available in the module.
            * The module does not cause conflicts with existing Odoo modules.
    * **Story 1.2:** As an Odoo Administrator, I want to configure the `vpcs_llm_provider_extension` module so that it can securely manage API keys for external AI services.
        * **Acceptance Criteria:**
            * The existing Odoo module for API key storage with Odoo password security configuration is leveraged for securely storing and accessing API keys.
            * API keys are not hardcoded within the module.
            * The configuration process aligns with Odoo's standard security parameters.
    * **Story 1.3:** As an Odoo Developer, I want to verify that Pydantic AI is correctly integrated into `vpcs_llm_provider_extension` so that I can begin building AI agent interactions.
        * **Acceptance Criteria:**
            * A basic Pydantic AI component or function can be invoked within the module.
            * No errors occur during the basic invocation of Pydantic AI functionalities.
            * The module can successfully interact with the Pydantic AI library.
    * **Story 1.4:** As an Odoo Administrator, I want to ensure the Go WhatsApp Bridge can run independently and is accessible to Odoo so that it can serve as the communication bridge for WhatsApp messages.
        * **Acceptance Criteria:**
            * The Go WhatsApp Bridge application can be deployed and started successfully in its environment.
            * The Go WhatsApp Bridge is configured to handle QR code authentication for WhatsApp Web.
            * The Go WhatsApp Bridge can store message history in SQLite.
            * The Odoo environment can establish basic network connectivity with the Go WhatsApp Bridge.
* **Epic 2: WhatsApp Messaging Integration**
    * **Goal:** To enable basic WhatsApp messaging functionality directly within Odoo's chatter, including initial configuration and message sending.
    * **Story 2.1:** As an Odoo Administrator, I want to configure WhatsApp integration through a dedicated app menu so that I can manage the connection to the Go WhatsApp Bridge.
        * **Acceptance Criteria:**
            * A new top-level Odoo app menu item is available for WhatsApp configuration.
            * This menu item leads to a form where connection details for the Go WhatsApp Bridge can be entered (e.g., bridge URL/IP, port).
            * The form includes a mechanism for initiating QR code display for WhatsApp authentication.
            * The configuration process provides feedback on the connection status to the Go WhatsApp Bridge.
    * **Story 2.2:** As an Odoo User, I want to send a WhatsApp message from an Odoo record's chatter so that I can communicate with contacts directly within the Odoo workflow.
        * **Acceptance Criteria:**
            * A new option to send a WhatsApp message is available within the chatter of any Odoo model utilizing the mail thread.
            * Upon selecting the WhatsApp option, a compose window or interface appears.
            * The system allows entering a recipient's WhatsApp number (or selecting from a related contact).
            * The system allows composing and sending a text message.
            * Sent messages appear in the chatter thread of the Odoo record.
            * The Odoo user receives immediate feedback on message delivery status (e.g., sent, failed).
    * **Story 2.3:** As an Odoo User, I want to receive incoming WhatsApp messages and see them linked to relevant Odoo records so that I have a unified communication history.
        * **Acceptance Criteria:**
            * Incoming WhatsApp messages are automatically received by the Odoo system via the Go WhatsApp Bridge.
            * If the sender's WhatsApp number is linked to an existing Odoo contact, the message is displayed in the chatter of that contact's record (and potentially related records like Leads/Sales Orders).
            * If the sender's WhatsApp number is not linked to an existing contact, the message can either create a new communication entry (e.g., a new activity on a generic contact) or provide an option to create a new contact/lead.
            * Received messages are displayed accurately in the Odoo chatter interface.
* **Epic 3: Hugging Face AI Capabilities Integration**
    * **Goal:** To integrate initial Hugging Face AI functionalities (image generation, translation, sentiment analysis, model inference) into Odoo, accessible via commands or dedicated UI elements.
    * **Story 3.1:** As an Odoo Administrator, I want to configure the Hugging Face MCP server connection within Odoo so that Hugging Face AI capabilities can be accessed.
        * **Acceptance Criteria:**
            * A new Odoo configuration interface (form) is available for Hugging Face MCP server details (e.g., endpoint URL).
            * The configuration allows for testing connectivity to the `mcp-hfspace` server.
            * Authentication details (if any beyond the LLM Provider) can be securely stored.
    * **Story 3.2:** As an Odoo User, I want to perform sentiment analysis on text from any Odoo record's chatter using a command so that I can quickly gauge the emotional tone of communications.
        * **Acceptance Criteria:**
            * A new "/" command (e.g., `/sentiment`) is available in the chatter of Odoo records.
            * When invoked, this command sends the selected or specified chatter text to the Hugging Face MCP server via `odoo_huggingface_mcp` and `vpcs_llm_provider_extension`.
            * The sentiment analysis result (e.g., Positive, Negative, Neutral, with a confidence score) is displayed back in the chatter thread or in a relevant field on the Odoo record.
            * The process respects the configurable 2-minute timeout limit for AI requests.
    * **Story 3.3:** As an Odoo User, I want to translate text from an Odoo record's chatter into a target language using a command so that I can understand communications in different languages.
        * **Acceptance Criteria:**
            * A new "/" command (e.g., `/translate`) is available in the chatter of Odoo records.
            * The command allows specifying a target language (e.g., `/translate en`).
            * The selected or specified chatter text is sent to the Hugging Face MCP server for translation.
            * The translated text is displayed back in the chatter thread or in a relevant field.
            * The process respects the configurable 2-minute timeout limit.
    * **Story 3.4:** As an Odoo User, I want to generate an image based on a text prompt from an Odoo record so that I can create visual content related to my Odoo data.
        * **Acceptance Criteria:**
            * A mechanism (e.g., a "/" command in chatter or a dedicated button on a relevant form) exists to trigger image generation.
            * The user can provide a text prompt for image generation.
            * The text prompt is sent to the Hugging Face MCP server for image generation.
            * The generated image is returned and attached to the Odoo record or displayed in a designated area.
            * The process respects the configurable 2-minute timeout limit.
    * **Story 3.5:** As an Odoo Developer, I want to be able to select from a top 2-3 list of available Hugging Face models for inference within `vpcs_llm_provider_extension` so that I can utilize the best model for specific tasks.
        * **Acceptance Criteria:**
            * The `vpcs_llm_provider_extension` module provides a mechanism (e.g., a Python method or configuration setting) to query and receive a prioritized list of 2-3 suitable Hugging Face models for a given inference task type.
            * The `odoo_huggingface_mcp` module can dynamically use the selected model for its inference requests.
            * The underlying Pydantic AI integration supports switching between these models.

## Key Reference Documents

## Out of Scope Ideas Post MVP

## [OPTIONAL: For Simplified PM-to-Development Workflow Only] Core Technical Decisions & Application Structure

#### Technology Stack Selections

* **Primary Backend Language/Framework:** Python (Odoo's native framework).
* **Primary Frontend Language/Framework:** Odoo's native frontend framework (OWL, JavaScript, QWeb).
* **Database:** PostgreSQL (Odoo's primary database).
* **AI Agent & MCP Capability Framework:** Pydantic AI (`https://ai.pydantic.dev/llms-full.txt`).
* **WhatsApp Bridge:** Go `whatsmeow` library for bridging to WhatsApp, with custom Python code for Odoo connectivity.
* **Hugging Face Access:** Direct access via its dedicated MCP server (`mcp-hfspace`). No additional Python library is needed within Odoo for direct Hugging Face access, as the MCP server handles this.

#### Proposed Application Structure

Given the decision for separate Odoo modules, the high-level structure will involve:

* **`vpcs_llm_provider_extension` (Enhanced):** This existing module will be enhanced to incorporate Pydantic AI for general AI agent capabilities and to facilitate Hugging Face model interaction.
    * This module will house the core logic for Pydantic AI integration and act as the central point for AI model selection and inference, serving as the "parent model" for AI access.
* **`odoo_whatsapp_mcp`:** This new module will contain all Odoo-specific logic for WhatsApp integration.
    * It will handle the communication with the Go WhatsApp Bridge.
    * It will implement the Chatter integration for sending/receiving messages.
    * It will include Odoo forms and views for WhatsApp configuration (e.g., QR code linking).
    * It will manage the storage and display of WhatsApp messages within Odoo models.
* **`odoo_huggingface_mcp`:** This new module will house all Odoo-specific logic for Hugging Face integration.
    * It will contain Python code to interact with the `mcp-hfspace` server for image generation, translation, sentiment analysis, and model inference.
    * It will provide the Odoo UI elements for triggering these AI tasks (e.g., chatter commands) and displaying results.
    * It will interact with the enhanced `vpcs_llm_provider_extension` for model selection and invocation.

**Key Modules/Components and Responsibilities:**

* **`vpcs_llm_provider_extension` (Enhanced):** Provides the foundational AI framework using Pydantic AI. It manages LLM provider configurations and acts as the central orchestrator for AI model interactions, including those with Hugging Face.
* **`odoo_whatsapp_mcp`:** Manages all WhatsApp communication features within Odoo, including sending and receiving messages, QR code authentication, and chatter integration. It acts as the Odoo-side client for the Go WhatsApp Bridge.
* **`odoo_huggingface_mcp`:** Facilitates direct access to Hugging Face models (image generation, translation, sentiment analysis, inference) via the `mcp-hfspace` server and integrates these capabilities into Odoo's UI (e.g., via chatter commands). It leverages `vpcs_llm_provider_extension` for AI model selection and execution.

**Data Flow Overview (Conceptual):**

* **WhatsApp:** User action in Odoo -> `odoo_whatsapp_mcp` -> Go WhatsApp Bridge -> WhatsApp API. Incoming messages: WhatsApp API -> Go WhatsApp Bridge -> `odoo_whatsapp_mcp` -> Odoo (chatter, record updates).
* **Hugging Face:** User action in Odoo (e.g., chatter command) -> `odoo_huggingface_mcp` -> `vpcs_llm_provider_extension` (model selection/invocation) -> `mcp-hfspace` server -> Hugging Face models. Results flow back in reverse order to Odoo.

## Change Log

| Change | Date | Version | Description | Author |
| ------ | ---- | ------- | ----------- | ------ |

--- Below, Prompt for Design Architect (If Project has UI) To Produce Front End Architecture ----

## Prompt for Design Architect (UI/UX Specification Mode)

**Objective:** Elaborate on the UI/UX aspects of the product defined in this PRD.
**Mode:** UI/UX Specification Mode
**Input:** This completed PRD document.
**Key Tasks:**

1.  Review the product goals, user stories, and any UI-related notes herein.
2.  Collaboratively define detailed user flows, wire-frames (conceptual), and key screen mockups/descriptions.
3.  Specify usability requirements and accessibility considerations.
4.  Populate or create the `front-end-spec-tmpl` document.
5.  Ensure that this PRD is updated or clearly references the detailed UI/UX specifications derived from your work, so that it provides a comprehensive foundation for subsequent architecture and development phases.

Please guide the user through this process to enrich the PRD with detailed UI/UX specifications.

--- End of PRD Content ---