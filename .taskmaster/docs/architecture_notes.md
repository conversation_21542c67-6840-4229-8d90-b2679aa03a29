# Odoo Hugging Face Integration Architecture

This document outlines the architecture for the Hugging Face integration in this project.

There are two primary ways to connect to Hugging Face:

1.  **Direct API Connection (via `vpcs_llm_provider_huggingface`)**
    *   **Purpose:** Provides a direct connection to the Hugging Face Inference API.
    *   **Configuration:** This is configured in the **Settings -> LLM Providers** menu. Create a new provider, select "Hugging Face" as the type, and enter the direct model inference endpoint in the "Base URL" field (e.g., `https://api-inference.huggingface.co/models/distilbert-base-uncased-finetuned-sst-2-english`).
    *   **Authentication:** The Hugging Face API key is stored in the `api_key` field of the `llm.provider` record.

2.  **MCP Server Connection (via `odoo_huggingface_mcp`)**
    *   **Purpose:** Connects to the `hf-mcp-server`, which acts as a proxy and tool server for Hugging Face.
    *   **Configuration:** This is configured in the **Settings -> Hugging Face -> Configuration** menu.
        *   **MCP Server URL/Port:** The connection details for the `hf-mcp-server`.
        *   **Hugging Face API Key:** The API key for the `hf-mcp-server`.
        *   **LLM Provider:** A separate `llm.provider` record (e.g., OpenAI, Anthropic) that the Pydantic AI agent will use for its reasoning and tool-calling capabilities.
    *   **Authentication:** The Hugging Face API key is stored in the `huggingface_api_key` field of the `huggingface.config` record. This key is used by the Pydantic AI agent to authenticate with the `hf-mcp-server`.

This dual architecture provides maximum flexibility, allowing for both direct API access and the use of the more powerful, tool-based MCP server, which can be orchestrated by any configured LLM provider.