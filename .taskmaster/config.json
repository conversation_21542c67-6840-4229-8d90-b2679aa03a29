{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro-preview-03-25", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "google", "modelId": "gemini-2.5-pro-preview-03-25", "maxTokens": 120000, "temperature": 0.2}, "fallback": {"provider": "ollama", "modelId": "qwen3:latest", "maxTokens": 120000, "temperature": 0.2}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "azureOpenaiBaseURL": "https://your-endpoint.openai.azure.com/", "userId": "**********"}}