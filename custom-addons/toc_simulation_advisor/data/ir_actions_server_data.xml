<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="action_trigger_bottleneck_analysis" model="ir.actions.server">
            <field name="name">Trigger Bottleneck Analysis</field>
            <field name="model_id" ref="mrp.model_mrp_workcenter"/>
            <field name="state">code</field>
            <field name="code">
if record:
    suggestion = """
**Exploit the Constraint:**
*   Ensure the work center is never idle. Prioritize work orders that will be processed by this work center.
*   Check for any quality issues that may be causing rework.

**Subordinate Everything Else:**
*   Do not release new work orders that will add to the queue of this work center.
*   Consider offloading some of the work to other work centers if possible.

**Elevate the Constraint:**
*   Consider adding more capacity to this work center (e.g., more machines, more operators, longer working hours).
*   Review the routing for products that use this work center. Can any operations be moved to other work centers?
"""
    env['toc.advisor'].create({
        'name': f"Bottleneck detected at {record.name}",
        'workcenter_id': record.id,
        'suggestion_ids': [(0, 0, {'name': suggestion})],
    })
            </field>
        </record>

        <record id="action_complete_workorders" model="ir.actions.server">
            <field name="name">Complete Work Orders</field>
            <field name="model_id" ref="mrp.model_mrp_workorder"/>
            <field name="state">code</field>
            <field name="code">
for workorder in records:
    if workorder.state == 'progress':
        workorder.button_finish()
            </field>
        </record>
    </data>
</odoo>