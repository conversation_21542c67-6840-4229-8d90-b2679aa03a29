<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="automation_trigger_bottleneck_analysis" model="base.automation">
            <field name="name">Trigger Bottleneck Analysis</field>
            <field name="model_id" ref="mrp.model_mrp_workcenter"/>
            <field name="trigger">on_write</field>
            <field name="filter_pre_domain">[('x_is_bottleneck', '=', False)]</field>
            <field name="filter_domain">[('x_is_bottleneck', '=', True)]</field>
            <field name="action_server_ids" eval="[(6, 0, [ref('toc_simulation_advisor.action_trigger_bottleneck_analysis')])]"/>
        </record>
    </data>
</odoo>