# -*- coding: utf-8 -*-
from odoo.tests.common import TransactionCase
from odoo import fields
from datetime import timedelta

class TestSimulation(TransactionCase):
    def setUp(self, *args, **kwargs):
        super(TestSimulation, self).setUp(*args, **kwargs)
        self.SimulationScenario = self.env['simulation.scenario']
        self.Product = self.env['product.template']
        self.Partner = self.env['res.partner']
        self.MrpBom = self.env['mrp.bom']
        self.MrpWorkcenter = self.env['mrp.workcenter']

        # Create some dummy data for the test
        self.vendor = self.Partner.create({'name': 'Test Vendor', 'supplier_rank': 1})
        self.component = self.Product.create({
            'name': 'Test Component',
            'type': 'consu',
            'seller_ids': [(0, 0, {'partner_id': self.vendor.id})]
        })
        self.finished_good = self.Product.create({'name': 'Test Finished Good', 'type': 'consu'})

        self.workcenter = self.MrpWorkcenter.create({'name': 'Test Workcenter'})

        self.bom = self.MrpBom.create({
            'product_tmpl_id': self.finished_good.id,
            'product_qty': 1,
            'bom_line_ids': [(0, 0, {'product_id': self.component.product_variant_id.id, 'product_qty': 2})],
            'operation_ids': [(0, 0, {'name': 'Test Operation', 'workcenter_id': self.workcenter.id, 'time_cycle_manual': 15})]
        })
        self.operation = self.bom.operation_ids

        self.customers = self.Partner.create([
            {'name': 'Test Customer 1', 'customer_rank': 1},
            {'name': 'Test Customer 2', 'customer_rank': 1},
        ])

    def test_create_and_run_simulation(self):
        """Test creating and running a simulation scenario."""
        scenario = self.SimulationScenario.create({
            'name': 'Test Simulation',
            'date_from': fields.Date.today(),
            'date_to': fields.Date.today() + timedelta(days=7),
            'num_orders': 5,
            'product_ids': [(6, 0, self.finished_good.ids)],
            'customer_ids': [(6, 0, self.customers.ids)],
            'vendor_ids': [(6, 0, self.vendor.ids)],
            'operation_ids': [(6, 0, self.operation.ids)],
        })
        self.assertEqual(scenario.state, 'draft')

        scenario.run_simulation()
        self.assertEqual(scenario.state, 'done')

        # Check that sale orders were created
        # Check that sale orders were created
        sale_orders = self.env['sale.order'].search([('simulation_id', '=', scenario.id)])
        self.assertEqual(len(sale_orders), 5, "Should create 5 sale orders")

        # Check that MO was created
        mrp_orders = self.env['mrp.production'].search([('simulation_id', '=', scenario.id)])
        self.assertGreaterEqual(len(mrp_orders), 1, "Should create at least one manufacturing order")

        # Check that PO was created
        purchase_orders = self.env['purchase.order'].search([('simulation_id', '=', scenario.id)])
        self.assertGreaterEqual(len(purchase_orders), 1, "Should create at least one purchase order")

        # Check that workorders were created and started
        workorders = mrp_orders.mapped('workorder_ids')
        self.assertGreater(len(workorders), 0, "Should have created workorders")
        self.assertTrue(all(wo.state == 'progress' for wo in workorders), "All workorders should be in progress")