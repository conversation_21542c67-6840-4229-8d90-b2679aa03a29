# TOC Simulation and Advisor

This Odoo module provides a simulation tool to automatically generate transactional data (Sales, Manufacturing, Purchase Orders) based on user-defined parameters. It allows users to visualize the real-time impact of production loads on the TOC dashboard and receive AI-driven advice to resolve system bottlenecks.

## Features

*   **Dummy Data Generation**: Quickly populate a test environment with a baseline of products, BoMs, vendors, and customers.
*   **Simulation Scenarios**: Configure and save simulation parameters, including date ranges, order volumes, and specific master data to use.
*   **Automated Order Generation**: The simulation engine automatically creates `sale.order`, `mrp.production`, and `purchase.order` records.
*   **TOC Dashboard Integration**: Real-time updates to the core TOC Dashboard to visualize the impact of the simulation.
*   **TOC Advisor**: Automatically detects bottlenecks and provides a preliminary list of suggestions based on the Theory of Constraints.

## Development Status

All primary development tasks for the initial version of this module are complete. The project is currently in the usability testing phase.

### Completed Tasks:
- Initial Module Setup
- Dummy Data Generation (UI and Logic)
- "Buy" and "Produce" Product Creation with Reordering Rules
- Simulation Configuration Screen and Parameter Inputs
- Save/Load Scenario Functionality
- Sales, Manufacturing, and Purchase Order Generation Engines
- Simulated Order Processing Logic
- Live TOC Dashboard Updates
- Dynamic Bottleneck Visualization
- Bottleneck Analysis Trigger and Advisor Panel
- Data Integrity Validation
- Simulation Management and Reset Functions

### Deferred Tasks:
- Non-intrusive Progress Indicator for Simulation

### Cancelled Tasks:
- Function to Reset Foundational Dummy Data