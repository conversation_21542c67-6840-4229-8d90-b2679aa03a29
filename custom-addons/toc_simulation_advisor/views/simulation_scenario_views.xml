<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Form View -->
        <record id="simulation_scenario_view_form" model="ir.ui.view">
            <field name="name">simulation.scenario.view.form</field>
            <field name="model">simulation.scenario</field>
            <field name="arch" type="xml">
                <form string="Simulation Scenario">
                    <header>
                        <button name="generate_dummy_data" string="Generate Dummy Data" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                        <button name="run_simulation" string="Run Simulation" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                        <button name="reset_simulation" string="Reset" type="object" invisible="state in ('draft')"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,running,done"/>
                    </header>
                    <sheet>
                        <div class="oe_button_box" name="button_box">
                            <button name="action_view_sale_orders" type="object" class="oe_stat_button" icon="fa-dollar">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="sale_order_count"/></span>
                                    <span class="o_stat_text">Sale Orders</span>
                                </div>
                            </button>
                            <button name="action_view_mrp_orders" type="object" class="oe_stat_button" icon="fa-cogs">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="mrp_order_count"/></span>
                                    <span class="o_stat_text">Manufacturing</span>
                                </div>
                            </button>
                            <button name="action_view_purchase_orders" type="object" class="oe_stat_button" icon="fa-shopping-cart">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="purchase_order_count"/></span>
                                    <span class="o_stat_text">Purchase Orders</span>
                                </div>
                            </button>
                            <button name="action_view_mrp_operations" type="object" class="oe_stat_button" icon="fa-wrench">
                                <div class="o_field_widget o_stat_info">
                                    <span class="o_stat_value"><field name="mrp_operation_count"/></span>
                                    <span class="o_stat_text">Operations</span>
                                </div>
                            </button>
                        </div>
                        <group>
                            <field name="name"/>
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="num_orders"/>
                        </group>
                        <notebook>
                            <page string="Products">
                                <field name="product_ids"/>
                            </page>
                            <page string="Customers">
                                <field name="customer_ids"/>
                            </page>
                            <page string="Vendors">
                                <field name="vendor_ids"/>
                            </page>
                            <page string="Operations">
                                <field name="operation_ids"/>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <!-- Tree View -->
        <record id="simulation_scenario_view_list" model="ir.ui.view">
            <field name="name">simulation.scenario.view.list</field>
            <field name="model">simulation.scenario</field>
            <field name="arch" type="xml">
                <list string="Simulation Scenarios">
                    <field name="name"/>
                    <field name="date_from"/>
                    <field name="date_to"/>
                    <field name="num_orders"/>
                    <field name="state"/>
                </list>
            </field>
        </record>

        <!-- Action -->
        <record id="simulation_scenario_action" model="ir.actions.act_window">
            <field name="name">Simulation Scenarios</field>
            <field name="res_model">simulation.scenario</field>
            <field name="view_mode">list,form</field>
        </record>

        <!-- Menu -->
        <!-- Main TOC Menu -->
        <menuitem id="menu_toc_root"
                  name="TOC"
                  parent="mrp.menu_mrp_root"
                  sequence="98"/>

        <menuitem id="menu_simulation_scenario"
                  name="Simulation Scenarios"
                  parent="menu_toc_root"
                  action="simulation_scenario_action"
                  sequence="1"/>
    </data>
</odoo>