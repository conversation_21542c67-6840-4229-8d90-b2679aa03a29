<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Form View for toc.advisor -->
        <record id="toc_advisor_view_form" model="ir.ui.view">
            <field name="name">toc.advisor.view.form</field>
            <field name="model">toc.advisor</field>
            <field name="arch" type="xml">
                <form string="TOC Advisor">
                    <header>
                        <button name="action_acknowledge" string="Acknowledge" type="object" class="oe_highlight" invisible="state != 'new'"/>
                        <button name="action_resolve" string="Resolve" type="object" class="oe_highlight" invisible="state != 'acknowledged'"/>
                        <field name="state" widget="statusbar" statusbar_visible="new,acknowledged,resolved"/>
                    </header>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="workcenter_id"/>
                        </group>
                        <notebook>
                            <page string="Suggestions">
                                <field name="suggestion_ids">
                                    <list editable="bottom">
                                        <field name="name"/>
                                        <field name="description"/>
                                        <field name="ranking"/>
                                        <field name="state"/>
                                    </list>
                                </field>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <!-- Tree View for toc.advisor -->
        <record id="toc_advisor_view_list" model="ir.ui.view">
            <field name="name">toc.advisor.view.list</field>
            <field name="model">toc.advisor</field>
            <field name="arch" type="xml">
                <list string="TOC Advisor">
                    <field name="name"/>
                    <field name="workcenter_id"/>
                    <field name="state"/>
                </list>
            </field>
        </record>

        <!-- Form View for toc.suggestion -->
        <record id="toc_suggestion_view_form" model="ir.ui.view">
            <field name="name">toc.suggestion.view.form</field>
            <field name="model">toc.suggestion</field>
            <field name="arch" type="xml">
                <form string="TOC Suggestion">
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="description"/>
                            <field name="ranking"/>
                            <field name="state"/>
                            <field name="advisor_id"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Action -->
        <record id="toc_advisor_action" model="ir.actions.act_window">
            <field name="name">TOC Advisor</field>
            <field name="res_model">toc.advisor</field>
            <field name="view_mode">list,form</field>
        </record>

        <!-- Menu -->
        <menuitem id="menu_toc_advisor"
                  name="TOC Advisor"
                  parent="mrp.menu_mrp_root"
                  action="toc_advisor_action"
                  sequence="2"/>
    </data>
</odoo>