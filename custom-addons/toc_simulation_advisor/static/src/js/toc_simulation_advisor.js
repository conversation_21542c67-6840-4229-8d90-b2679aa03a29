/** @odoo-module **/

import { registry } from "@web/core/registry";

const TocSimulationListener = {
    dependencies: ["bus_service", "action", "notification"],
    
    start(env, { bus_service, action, notification }) {
        this.notification = notification;
        this.action = action;
        this.progressToast = null;

        // Use an arrow function to preserve 'this' context
        bus_service.subscribe("toc_dashboard", (payload) => {
            if (payload.type === 'toc_update') {
                this._onDashboardUpdate(payload.payload);
            } else if (payload.type === 'bottleneck_alert') {
                this._onBottleneckAlert(payload.payload);
            } else if (payload.type === 'simulation_progress') {
                this._onSimulationProgress(payload.payload);
            }
        });
        console.log("TOC Simulation listener started on 'toc_dashboard' channel.");
    },

    _onSimulationProgress(payload) {
        if (!this.progressToast) {
            this.progressToast = this.notification.add(payload.message, {
                title: "Simulation In Progress",
                type: 'info',
                sticky: true,
            });
        } else {
            // To update an existing toast, we pass its ID back to the add method.
            this.notification.add(payload.message, {
                title: "Simulation In Progress",
                type: 'info',
                sticky: true,
                id: this.progressToast,
            });
        }

        if (payload.current >= payload.total) {
            setTimeout(() => {
                this.notification.close(this.progressToast);
                this.progressToast = null;
            }, 2000);
        }
    },

    _onBottleneckAlert(payload) {
        this.notification.add(`Bottleneck detected at Work Center!`, {
            title: "Bottleneck Alert",
            type: 'warning',
            buttons: [
                {
                    name: 'View Advisor',
                    onClick: () => {
                        this.action.doAction({
                            type: 'ir.actions.act_window',
                            res_model: 'toc.advisor',
                            res_id: payload.advisor_id,
                            views: [[false, 'form']],
                            target: 'current',
                        });
                    },
                },
            ],
        });
    },

    _onDashboardUpdate(payload) {
        const activeController = this.action.currentController;
        if (activeController && activeController.action.tag === 'toc_dashboard') {
            this._updateWorkcenterCard(payload.workcenter_id, payload.metrics);
        }
    },

    _updateWorkcenterCard(workcenterId, metrics) {
        const cardSelector = `.toc_workcenter_card[data-workcenter-id='${workcenterId}']`;
        const card = document.querySelector(cardSelector);

        if (!card) {
            return;
        }

        const cardHeader = card.querySelector('.card-header');
        if (cardHeader) {
            cardHeader.classList.remove('bg-success', 'bg-warning', 'bg-danger');
            if (metrics.x_utilization_color) {
                cardHeader.classList.add(`bg-${metrics.x_utilization_color}`);
            }
        }

        this._updateMetric(card, 'x_utilization_percentage', metrics.x_utilization_percentage);
        this._updateMetric(card, 'x_work_order_queue_time', metrics.x_work_order_queue_time);
        this._updateMetric(card, 'x_average_processing_time', metrics.x_average_processing_time);
        this._updateMetric(card, 'x_wip_value', metrics.x_wip_value);
        this._updateMetric(card, 'x_scrap_rate_percentage', metrics.x_scrap_rate_percentage);
    },

    _updateMetric(card, metricName, value) {
        const element = card.querySelector(`[data-metric='${metricName}'] .badge`);
        if (element && value !== undefined) {
            let formattedValue = value;
            if (typeof metricName === 'string') {
                if (metricName.includes('percentage')) {
                    formattedValue = `${parseFloat(value).toFixed(2)}%`;
                } else if (metricName.includes('time')) {
                    formattedValue = `${parseFloat(value).toFixed(2)} hrs`;
                } else if (metricName.includes('value')) {
                    formattedValue = `$${parseFloat(value).toFixed(2)}`;
                }
            }
            element.textContent = formattedValue;
        }
    }
};

registry.category("services").add("tocSimulationListener", TocSimulationListener);