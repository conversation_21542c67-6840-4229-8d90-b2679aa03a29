# Copyright 2024 VPerfectCS
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0).

{
    'name': 'TOC Simulation and Advisor',
    'version': '********.0',
    'category': 'Manufacturing',
    'summary': 'Simulate production scenarios and get AI-driven advice on bottlenecks.',
    'description': """
        This module provides a simulation tool to automatically generate transactional data
        (Sales, Manufacturing, Purchase Orders) based on user-defined parameters.
        It allows users to visualize the real-time impact of production loads on the
        TOC dashboard and receive AI-driven advice to resolve system bottlenecks.
    """,
    'author': 'VPerfectCS',
    'website': 'https://www.vperfectcs.com',
    'support': '<EMAIL>',
    'depends': [
        'odoo_toc_integration',
        'base_automation',
        'purchase_repair'
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/toc_advisor_views.xml',
        'data/ir_sequence_data.xml',
        'views/simulation_scenario_views.xml',
        'data/ir_actions_server_data.xml',
        'data/base_automation_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            # 'toc_simulation_advisor/static/src/scss/simulation.scss',
            'toc_simulation_advisor/static/src/js/toc_simulation_advisor.js',
        ],
        'web.assets_tests': [
            # 'toc_simulation_advisor/static/src/tests/tours/**/*',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}