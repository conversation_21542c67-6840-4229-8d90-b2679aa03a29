# -*- coding: utf-8 -*-
from odoo import models, fields, api

class MrpWorkcenter(models.Model):
    """
    Extend mrp.workcenter to trigger the TOC Advisor.
    """
    _inherit = 'mrp.workcenter'

    def _check_for_bottleneck_alerts(self, workcenter):
        # First, run the original logic from the parent module
        res = super(MrpWorkcenter, self)._check_for_bottleneck_alerts(workcenter)

        # Then, add our new logic to trigger the advisor
        threshold = self.env['ir.config_parameter'].sudo().get_param('odoo_toc_integration.high_utilization_threshold', 90.0)
        if workcenter.x_utilization_percentage >= float(threshold):
            # Check if an advisor analysis has already been created recently
            # to avoid duplicate entries for the same ongoing bottleneck.
            existing_advisor = self.env['toc.advisor'].search([
                ('workcenter_id', '=', workcenter.id),
                ('state', 'in', ['draft', 'analyzing']),
                ('create_date', '>=', fields.Datetime.subtract(fields.Datetime.now(), days=1))
            ], limit=1)

            if not existing_advisor:
                advisor = self.env['toc.advisor'].create({
                    'name': f"Analysis for {workcenter.name} on {fields.Date.today()}",
                    'workcenter_id': workcenter.id,
                    'state': 'new',
                })
                self.env['bus.bus']._sendone(
                    'toc_dashboard',
                    'bottleneck_alert',
                    {
                        'workcenter_id': workcenter.id,
                        'advisor_id': advisor.id,
                    }
                )
        return res