# -*- coding: utf-8 -*-
from odoo import models, fields, api

class TocAdvisor(models.Model):
    """
    Stores analysis and suggestions for a detected bottleneck.
    """
    _name = 'toc.advisor'
    _inherit = ['mail.thread']
    _description = 'TOC Bottleneck Advisor'
    _order = 'create_date desc'

    name = fields.Char(
        string='Analysis Title',
        required=True,
        store=True
    )
    simulation_scenario_id = fields.Many2one(
        'simulation.scenario',
        string='Simulation Scenario',
        ondelete='cascade',
        help="The simulation run that triggered this analysis."
    )
    workcenter_id = fields.Many2one(
        'mrp.workcenter',
        string='Bottlenecked Work Center',
        required=True,
        readonly=True
    )
    analysis_date = fields.Datetime(
        string='Analysis Date',
        default=fields.Datetime.now,
        readonly=True
    )
    state = fields.Selection([
        ('new', 'New'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
    ], string='Status', default='new', required=True)
    
    suggestion_ids = fields.One2many(
        'toc.suggestion',
        'advisor_id',
        string='Suggestions'
    )

    @api.depends('workcenter_id', 'create_date')
    def _compute_name(self):
        for record in self:
            if record.workcenter_id and record.create_date:
                record.name = f"Analysis for {record.workcenter_id.name} on {fields.Date.to_string(record.create_date)}"
            else:
                record.name = "New Analysis"

    def action_acknowledge(self):
        self.write({'state': 'acknowledged'})

    def action_resolve(self):
        self.write({'state': 'resolved'})

    @api.model_create_multi
    def create(self, vals_list):
        records = super(TocAdvisor, self).create(vals_list)
        for record in records:
            record._generate_suggestions()
        return records

    def _generate_suggestions(self):
        self.ensure_one()
        suggestions = [
            {
                'name': 'Exploit: Ensure the bottleneck is never idle.',
                'description': 'Prioritize work orders for this work center. Check for any quality issues causing rework.',
                'ranking': 1,
            },
            {
                'name': 'Subordinate: Do not overload the bottleneck.',
                'description': 'Do not release new work orders that will add to the queue of this work center. Consider offloading some work.',
                'ranking': 2,
            },
            {
                'name': 'Elevate: Increase the bottleneck\'s capacity.',
                'description': 'Consider adding more capacity (e.g., more machines, more operators, longer working hours). Review the routing for products that use this work center.',
                'ranking': 3,
            }
        ]
        for suggestion in suggestions:
            self.env['toc.suggestion'].create({
                'advisor_id': self.id,
                'name': suggestion['name'],
                'description': suggestion['description'],
                'ranking': suggestion['ranking'],
            })


class TocSuggestion(models.Model):
    """
    Represents a single actionable suggestion to resolve a bottleneck.
    """
    _name = 'toc.suggestion'
    _description = 'TOC Suggestion'

    advisor_id = fields.Many2one('toc.advisor', string='Advisor', required=True, ondelete='cascade')
    name = fields.Char(string='Suggestion', required=True)
    description = fields.Text(string='Details')
    ranking = fields.Integer(string='Rank', default=10)
    state = fields.Selection([
        ('new', 'New'),
        ('implemented', 'Implemented'),
        ('dismissed', 'Dismissed')
    ], string='Status', default='new')