# -*- coding: utf-8 -*-
import logging
import random
from datetime import datetime, timedelta
from odoo import fields, models, api

from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class Mrp(models.Model):
    _inherit = 'mrp.production'

    simulation_id = fields.Many2one('simulation.scenario', string='Simulation Scenario', help="Link to the simulation scenario that generated this manufacturing order.")

class Purchase(models.Model):
    _inherit = 'purchase.order'

    simulation_id = fields.Many2one('simulation.scenario', string='Simulation Scenario', help="Link to the simulation scenario that generated this purchase order.")

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    simulation_id = fields.Many2one('simulation.scenario', string='Simulation Scenario', help="Link to the simulation scenario that generated this sale order.")

class MrpRoutingWorkcenter(models.Model):
    _inherit = 'mrp.routing.workcenter'

    simulation_id = fields.Many2one('simulation.scenario', string='Simulation Scenario', help="Link to the simulation scenario that generated this operation.")

class SimulationScenario(models.Model):
    _name = 'simulation.scenario'
    _description = 'Simulation Scenario'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(string='Scenario Name', required=True, readonly=True, default=lambda self: ('New'))
    date_from = fields.Date(string='Start Date')
    date_to = fields.Date(string='End Date')
    num_orders = fields.Integer(string='Number of Orders', default=11)
    product_ids = fields.Many2many('product.template', string='Products')
    customer_ids = fields.Many2many('res.partner', 'simulation_scenario_customer_rel', string='Customers', domain="[('customer_rank', '>', 0)]")
    vendor_ids = fields.Many2many('res.partner', 'simulation_scenario_vendor_rel', string='Vendors', domain="[('supplier_rank', '>', 0)]")
    operation_ids = fields.Many2many('mrp.routing.workcenter', 'simulation_scenario_operation_rel', string='Operations')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('running', 'Running'),
        ('done', 'Done'),
        ('error', 'Error'),
    ], string='Status', default='draft', readonly=True)
    sale_order_count = fields.Integer(compute='_compute_order_counts', string="Sale Orders",store=True)
    mrp_order_count = fields.Integer(compute='_compute_order_counts', string="Manufacturing Orders",store=True)
    purchase_order_count = fields.Integer(compute='_compute_order_counts', string="Purchase Orders",store=True)
    mrp_operation_count = fields.Integer(compute='_compute_order_counts', string="Manufacturing Operations", store=True)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', ('New')) == ('New'):
                vals['name'] = self.env['ir.sequence'].next_by_code('simulation.scenario') or ('New')
        return super(SimulationScenario, self).create(vals_list)

    def generate_dummy_data(self):
        """
        Action to generate dummy data dynamically, including reordering rules and BOMs.
        """
        _logger.info("Starting dummy data generation...")
        now = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        res_partner = self.env['res.partner']
        product_template = self.env['product.template']
        orderpoint = self.env['stock.warehouse.orderpoint']
        bom = self.env['mrp.bom']
        mrp_workcenter = self.env['mrp.workcenter']
        # Get warehouse and routes
        warehouse = self.env['stock.warehouse'].search([('company_id', '=', self.env.company.id)], limit=1)
        buy_route = self.env.ref('stock.route_warehouse0_buy', raise_if_not_found=False)
        manufacture_route = self.env.ref('mrp.route_warehouse0_manufacture', raise_if_not_found=False)

        # Generate Vendors
        new_vendors = self.env['res.partner']
        for i in range(1, self.num_orders + 1):
            vendor = res_partner.create({
                'name': f'VendorTest_{now}_{i:03d}',
                'company_type': 'company',
                'supplier_rank': 1,
            })
            new_vendors |= vendor
        _logger.info(f"Generated {self.num_orders} dummy vendors.")
        vendor_list = new_vendors.ids

        # Generate "Buy" Products (Components)
        buy_products = self.env['product.template']
        for i in range(1, self.num_orders + 1):
            product = product_template.create({
                'name': f'Component_{now}_{i:03d}',
                'type': 'consu',
                'categ_id': self.env.ref('product.product_category_all').id,
                'purchase_ok': True,
                'sale_ok': False,
                'is_storable': True,
                'seller_ids': [(0, 0, {'partner_id': random.choice(vendor_list)})]
            })
            orderpoint.create({
                'warehouse_id': warehouse.id,
                'location_id': warehouse.lot_stock_id.id,
                'product_id': product.product_variant_id.id,
                'product_min_qty': 5,
                'product_max_qty': 20,
            })
            buy_products |= product
        _logger.info(f"Generated {len(buy_products)} component products with reordering rules.")

        # Generate Work Centers
        workcenters = self.env['mrp.workcenter']
        for i in range(1, 4):  # Create 3 work centers
            wc = mrp_workcenter.create({
                'name': f'Work Center {now}_{i:03d}',
                'resource_calendar_id': self.env.company.resource_calendar_id.id,
            })
            workcenters |= wc
        _logger.info(f"Generated {len(workcenters)} work centers.")

        # Generate "Produce" Products (Finished Goods)
        produce_products = self.env['product.template']
        all_new_operations = self.env['mrp.routing.workcenter']
        wc_count = len(workcenters)
        for i in range(1, self.num_orders + 1):
            product = product_template.create({
                'name': f'FinishedGood_{now}_{i:03d}',
                'type': 'consu',
                'categ_id': self.env.ref('product.product_category_all').id,
                'purchase_ok': False,
                'is_storable': True,
                'sale_ok': True,
                'route_ids': [(6, 0, [manufacture_route.id])] if manufacture_route else []
            })
            # Create BOM with operations
            new_bom = bom.create({
                'product_tmpl_id': product.id,
                'product_qty': 1,
                'type': 'normal',
                'bom_line_ids': [
                    (0, 0, {'product_id': p.product_variant_id.id, 'product_qty': random.randint(1, 3)})
                    for p in random.sample(list(buy_products), k=min(3, len(buy_products)))
                ],
                'operation_ids': [
                    (0, 0, {
                        'name': f'Operation {j + 1} for {product.name}',
                        'workcenter_id': workcenters[(i + j) % wc_count].id if wc_count > 0 else False,
                        'time_cycle_manual': random.randint(10, 30),
                        'sequence': (j + 1) * 10,
                        'simulation_id': self.id,
                    }) for j in range(random.randint(1, 3))
                ]
            })
            all_new_operations |= new_bom.operation_ids
            # Create Reordering Rule
            orderpoint.create({
                'warehouse_id': warehouse.id,
                'location_id': warehouse.lot_stock_id.id,
                'product_id': product.product_variant_id.id,
                'product_min_qty': 10,
                'product_max_qty': 50,
            })
            produce_products |= product
        _logger.info(f"Generated {len(produce_products)} finished products with BOMs and reordering rules.")

        # Generate Customers
        new_customers = self.env['res.partner']
        for i in range(1, self.num_orders + 1):
            customer = res_partner.create({
                'name': f'CustomerTest_{now}_{i:03d}',
                'company_type': 'person',
                'customer_rank': 1,
            })
            new_customers |= customer
        _logger.info(f"Generated {self.num_orders} dummy customers.")

        self.write({
            'vendor_ids': [(6, 0, new_vendors.ids)],
            'product_ids': [(6, 0, (buy_products | produce_products).ids)],
            'customer_ids': [(6, 0, new_customers.ids)],
            'operation_ids': [(6, 0, all_new_operations.ids)],
        })
        
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    @api.depends('state')
    def _compute_order_counts(self):
        for scenario in self:
            if scenario.state == 'draft':
                scenario.sale_order_count = 0
                scenario.mrp_order_count = 0
                scenario.purchase_order_count = 0
                scenario.mrp_operation_count = 0
            else:
                sale_orders = self.env['sale.order'].search([('origin', '=', scenario.name), ('simulation_id', '=', scenario.id)])
                scenario.sale_order_count = len(sale_orders)

                mrp_orders = self.env['mrp.production'].search([('simulation_id', '=', scenario.id)])
                scenario.mrp_order_count = len(mrp_orders)

                purchase_orders = self.env['purchase.order'].search([('simulation_id', '=', scenario.id)])
                scenario.purchase_order_count = len(purchase_orders)

                operations = self.env['mrp.routing.workcenter'].search([('simulation_id', '=', scenario.id)])
                scenario.mrp_operation_count = len(operations)

    def _get_sale_orders(self):
        return self.env['sale.order'].search([('origin', '=', self.name), ('simulation_id', '=', self.id)])

    def _get_mrp_orders(self):
        return self.env['mrp.production'].search([('simulation_id', '=', self.id)])

    def _get_purchase_orders(self):
        return self.env['purchase.order'].search([('simulation_id', '=', self.id)])

    def _get_mrp_operations(self):
        return self.env['mrp.routing.workcenter'].search([('simulation_id', '=', self.id)])

    def action_view_mrp_operations(self):
        self.ensure_one()
        operation_ids = self._get_mrp_operations().ids
        return {
            'name': ('Generated Operations'),
            'res_model': 'mrp.routing.workcenter',
            'type': 'ir.actions.act_window',
            'domain': [('id', 'in', operation_ids)],
            'view_mode': 'list,form',
        }

    def action_view_sale_orders(self):
        self.ensure_one()
        sale_order_ids = self._get_sale_orders().ids
        action = {
            'res_model': 'sale.order',
            'type': 'ir.actions.act_window'
        }
        if len(sale_order_ids) == 1:
            action.update({
                'view_mode': 'form',
                'res_id': sale_order_ids[0],
                'views': [(self.env.ref('sale.view_order_form').id, 'form')],
            })
        else:
            action.update({
                'name': ('Generated Sale Orders'),
                'domain': [('id', 'in', sale_order_ids)],
                'view_mode': 'list,form',
                'views': [(self.env.ref('sale.view_order_tree').id, 'list'), (self.env.ref('sale.view_order_form').id, 'form')],
            })
        return action

    def action_view_mrp_orders(self):
        self.ensure_one()
        mrp_order_ids = self._get_mrp_orders().ids
        action = {
            'res_model': 'mrp.production',
            'type': 'ir.actions.act_window',
        }
        if len(mrp_order_ids) == 1:
            action.update({
                'view_mode': 'form',
                'res_id': mrp_order_ids[0],
                'views': [(self.env.ref('mrp.mrp_production_form_view').id, 'form')],
            })
        else:
            action.update({
                'name': ('Generated Manufacturing Orders'),
                'domain': [('id', 'in', mrp_order_ids)],
                'view_mode': 'list,form',
                'views': [(self.env.ref('mrp.mrp_production_tree_view').id, 'list'), (self.env.ref('mrp.mrp_production_form_view').id, 'form')],
            })
        return action

    def action_view_purchase_orders(self):
        self.ensure_one()
        purchase_order_ids = self._get_purchase_orders().ids
        action = {
            'res_model': 'purchase.order',
            'type': 'ir.actions.act_window',
        }
        if len(purchase_order_ids) == 1:
            action.update({
                'view_mode': 'form',
                'res_id': purchase_order_ids[0],
                'views': [(self.env.ref('purchase.purchase_order_form').id, 'form')],
            })
        else:
            action.update({
                'name': ('Generated Purchase Orders'),
                'domain': [('id', 'in', purchase_order_ids)],
                'view_mode': 'list,form',
                'views': [(self.env.ref('purchase.purchase_order_tree').id, 'list'), (self.env.ref('purchase.purchase_order_form').id, 'form')],
            })
        return action

    def _notify_progress(self, current, total):
        """Helper to send a progress notification."""
        self.ensure_one()
        message = f"Processing order {current} of {total}..."
        self.env['bus.bus']._sendone(
            'toc_dashboard',
            'simulation_progress',
            {
                'message': message,
                'current': current,
                'total': total,
            }
        )

    def run_simulation(self):
        self.ensure_one()
        self.state = 'running'
        _logger.info(f"Running simulation for scenario: {self.name}")
        _logger.info(f"Date range: {self.date_from} to {self.date_to}")
        _logger.info(f"Number of orders: {self.num_orders}")
        _logger.info(f"Products: {self.product_ids.mapped('name')}")
        _logger.info(f"Customers: {self.customer_ids.mapped('name')}")

        if not self.date_from or not self.date_to:
            raise UserError("Please set a start and end date for the simulation.")

        if self.date_from > self.date_to:
            raise UserError("Start Date cannot be after End Date.")

        if not self.customer_ids:
            raise UserError("Please select at least one customer for the simulation.")

        if not self.product_ids:
            raise UserError("Please select at least one product for the simulation.")

        # Determine the pool of products to use in the simulation
        manufacturable_products = self.product_ids.filtered(lambda p: p.bom_ids)
        if self.operation_ids:
            # If operations are specified, filter products to those that use these operations
            boms_with_ops = self.env['mrp.bom'].search([
                ('operation_ids', 'in', self.operation_ids.ids),
                ('product_tmpl_id', 'in', manufacturable_products.ids)
            ])
            products_with_selected_ops_ids = boms_with_ops.mapped('product_tmpl_id').ids
            manufacturable_products = manufacturable_products.filtered(
                lambda p: p.id in products_with_selected_ops_ids
            )

        if not manufacturable_products:
            raise UserError("None of the selected products have Bills of Materials, or none of them use the specified operations. The simulation cannot run.")

        start_date = self.date_from
        end_date = self.date_to
        time_difference = end_date - start_date
        days_difference = time_difference.days

        SaleOrder = self.env['sale.order']
        created_sale_orders = self.env['sale.order']
        for i in range(self.num_orders):
            self._notify_progress(i + 1, self.num_orders)
            customer = random.choice(self.customer_ids)
            # product_template is now chosen from the pre-filtered list
            product_template = random.choice(manufacturable_products)
            
            # Generate random date for the order
            random_number_of_days = random.randrange(days_difference + 1)
            random_date = start_date + timedelta(days=random_number_of_days)
            # Add a random time to it during business hours
            random_time = timedelta(hours=random.randint(8, 17), minutes=random.randint(0, 59), seconds=random.randint(0, 59))
            random_datetime = datetime.combine(random_date, datetime.min.time()) + random_time

            order_vals = {
                'partner_id': customer.id,
                'simulation_id': self.id,
                'origin': self.name,
                'date_order': random_datetime,
                'order_line': [(0, 0, {
                    'product_id': product_template.product_variant_id.id,
                    'product_uom_qty': random.randint(1, 10),
                })],
            }
            sale_order = SaleOrder.create(order_vals)
            sale_order.action_confirm()
            created_sale_orders |= sale_order
            _logger.info(f"Created and confirmed Sale Order: {sale_order.name} with date {random_datetime.strftime('%Y-%m-%d')}")
            _logger.info("Running procurement scheduler to generate MOs and POs...")
            self.env['procurement.group'].run_scheduler()
            _logger.info("Procurement scheduler finished.")

        # Finding MOs manually based on Finished goods product with bom reference in current model field product_ids
        finished_goods = self.product_ids.filtered(lambda p: p.bom_ids)
        mrp_orders = self.env['mrp.production'].search([('product_id', 'in', finished_goods.product_variant_id.ids), ('state', 'not in', ['done', 'cancel'])])
        _logger.info(f"Found {len(mrp_orders)} Manufacturing Orders linked to the created Sale Orders.")

        # Finding POs manually based on components product with not defined as bom
        components = self.product_ids.filtered(lambda p: not p.bom_ids)
        purchase_orders = self.env['purchase.order'].search([('order_line.product_id', 'in', components.mapped('product_variant_id').ids), ('state', 'not in', ['done', 'cancel'])])
        _logger.info(f"Found {len(purchase_orders)} Purchase Orders linked to the created Sale Orders.")

        _logger.info(f"Generated {len(created_sale_orders)} Sale Orders, {len(mrp_orders)} Manufacturing Orders, and {len(purchase_orders)} Purchase Orders.")
        if mrp_orders:
            _logger.info("Confirming Manufacturing Orders...")
            mrp_orders.write({'simulation_id': self.id})  # Link MOs to the simulation scenario
            mrp_orders.filtered(lambda mo: mo.state == 'draft').action_confirm()
            # self._notify_dashboard_update(f'{len(mrp_orders)} Manufacturing Orders confirmed.')
            _logger.info("Manufacturing Orders confirmed.")
            
            # Plan the MOs to reserve components and create work orders
            mrp_orders.button_plan()
            # self._notify_dashboard_update(f'{len(mrp_orders)} Manufacturing Orders planned.')
            _logger.info("Manufacturing Orders planned.")

            # Automatically start the work orders that are ready
            workorders = mrp_orders.mapped('workorder_ids').filtered(lambda wo: wo.state == 'ready')
            if workorders:
                _logger.info(f"Found {len(workorders)} ready work orders to start.")
                workorders.button_start()
                _logger.info("Automatically started ready work orders.")

        if purchase_orders:
            _logger.info("Confirming Purchase Orders...")
            purchase_orders.write({'simulation_id': self.id})  # Link POs to the simulation scenario
            purchase_orders.filtered(lambda po: po.state in ['draft', 'sent']).button_confirm()
            # self._notify_dashboard_update(f'{len(purchase_orders)} Purchase Orders confirmed.')
            _logger.info("Purchase Orders confirmed.")

        _logger.info("Simulation finished. MOs and POs have been created and confirmed.")
        # call _compute_order_counts here to recalculate the counts
        self._compute_order_counts()
        # self._notify_dashboard_update('Simulation finished. Finalizing results...')
        _logger.info(f"--- Simulation for {self.name} completed successfully ---")
        # Update the state to 'done' after simulation
        self.state = 'done'
        # self._notify_dashboard_update('Simulation complete. Dashboard is up to date.')
        _logger.info(f"Simulation scenario {self.name} is now in 'done' state.")
        return True

    def reset_simulation(self):
        self.ensure_one()
        _logger.info(f"--- Starting Simulation Reset for {self.name} ---")

        sale_orders = self._get_sale_orders()
        if not sale_orders:
            self.state = 'draft'
            self.invalidate_recordset()
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
        _logger.info(f"Found {len(sale_orders)} Sale Orders to reset.")
        # Get related MOs, POs, and Pickings
        mrp_orders = self._get_mrp_orders()
        purchase_orders = self._get_purchase_orders()
        operations = self._get_mrp_operations()
        pickings = sale_orders.mapped('picking_ids') | mrp_orders.mapped('picking_ids')

        _logger.info(f"Found {len(sale_orders)} SOs, {len(mrp_orders)} MOs, {len(purchase_orders)} POs, {len(operations)} Operations, {len(pickings)} Pickings.")

        # 1. Cancel everything in dependency order (children first)
        # Use sudo() for permissions. Filter for states that can be cancelled.
        
        _logger.info("Cancelling related pickings...")
        pickings.filtered(lambda p: p.state not in ['done', 'cancel']).sudo().action_cancel()

        _logger.info("Cancelling related purchase orders...")
        purchase_orders.filtered(lambda po: po.state not in ['done', 'cancel']).sudo().button_cancel()

        _logger.info("Cancelling related manufacturing orders...")
        mrp_orders.filtered(lambda mo: mo.state not in ['done', 'cancel']).sudo().action_cancel()

        _logger.info("Cancelling sale orders...")
        for so in sale_orders.filtered(lambda so: so.state not in ['done', 'cancel']):
            so.sudo().action_cancel()

        # 2. Unlink all records now that they are cancelled
        _logger.info("Unlinking all related records...")
        operations.sudo().unlink()
        purchase_orders.sudo().unlink()
        mrp_orders.sudo().unlink()
        sale_orders.sudo().unlink()
        
        self.state = 'draft'
        _logger.info(f"--- Simulation Reset for {self.name} Complete ---")
        
        return True
