<odoo>
    <!-- tree View -->
    <record id="view_llm_model_tree" model="ir.ui.view">
        <field name="name">llm.model.tree</field>
        <field name="model">llm.model</field>
        <field name="arch" type="xml">
            <list string="LLM Models" default_order="sequence">
                <field name="sequence" widget="handle"/>
                <field name="display_name"/>
                <field name="name"/>
                <field name="provider_type"/>
                <field name="active"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_llm_model_form" model="ir.ui.view">
        <field name="name">llm.model.form</field>
        <field name="model">llm.model</field>
        <field name="arch" type="xml">
            <form string="LLM Model">
                <sheet>
                    <group>
                        <field name="display_name"/>
                        <field name="name"/>
                        <field name="provider_type"/>
                        <field name="active"/>
                        <field name="sequence"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_llm_model" model="ir.actions.act_window">
        <field name="name">LLM Models</field>
        <field name="res_model">llm.model</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p>Manage supported LLM models by provider.</p>
        </field>
    </record>

    <!-- Menu for LLM Model -->
    <menuitem id="menu_llm_model"
        name="LLM Models"
        parent="vpcs_llm_provider.menu_llm_provider_configuration"
        sequence="102"
        action="action_llm_model"
        groups="base.group_system"/>
</odoo>
