<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- list View -->
    <record id="view_llm_provider_list" model="ir.ui.view">
        <field name="name">llm.provider.list</field>
        <field name="model">llm.provider</field>
        <field name="arch" type="xml">
            <list string="LLM Providers" decoration-success="state == 'ready'" decoration-warning="state == 'processing'" decoration-danger="state in ('rate_limited', 'error')">
                <field name="sequence" widget="handle" />
                <field name="name" />
                <field name="provider_type" />
                <field name="model" />
                <field name="state" />
                <field name="last_used" />
                <field name="request_count" />
                <field name="total_requests" />
                <field name="successful_requests" />
                <field name="average_response_time" widget="float_time" />
                <field name="active" widget="boolean_toggle" />
            </list>
        </field>
    </record>
    <!-- Form View -->
    <record id="view_llm_provider_form" model="ir.ui.view">
        <field name="name">llm.provider.form</field>
        <field name="model">llm.provider</field>
        <field name="arch" type="xml">
            <form string="LLM Provider">
                <header>
                    <button name="action_test_connection" string="Test Connection" type="object" class="oe_highlight" invisible="state == 'processing'" />
                    <button name="action_reset_state" string="Reset State" type="object" invisible="state == 'ready'" />
                    <field name="state" widget="statusbar" statusbar_visible="ready,processing,rate_limited,error" />
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}" />
                        </button>
                    </div>
                    <div class="oe_title">
                        <label for="name" class="oe_edit_only" />
                        <h1>
                            <field name="name" placeholder="e.g. OpenAI GPT-4" />
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="provider_type" />
                            <field name="model" />  
                            <!-- <field name="model_id" string="Model"/> -->
                            <field name="api_key" password="True" required="True" />
                            <field name="base_url" placeholder="Optional, for custom endpoints" />
                        </group>
                        <group>
                            <field name="sequence" groups="base.group_no_one" />
                            <field name="timeout" />
                            <field name="max_retries" />
                            <field name="retry_delay" />
                            <field name="retried_correction" />
                        </group>
                        <group>
                            <field name="prompt_text" />
                            <field name="result_text" />
                        </group>
                        <group> 
                            <field name="show_generate_button" invisible="1"/>
                            <button name="generate_response"  string="Generate Response" type="object" class="oe_highlight" invisible="not show_generate_button" />
                        </group>
                    </group>
                    <notebook>
                        <page string="Usage Statistics" name="usage_stats">
                            <group>
                                <group string="Request Statistics">
                                    <field name="total_requests" />
                                    <field name="successful_requests" />
                                    <field name="failed_requests" />
                                    <field name="average_response_time" widget="float_time" />
                                    <field name="last_used" />
                                </group>
                                <group string="Rate Limit Information">
                                    <field name="request_count" />
                                    <field name="max_requests" />
                                    <field name="rate_limit_reset" />
                                    <field name="last_error_time" />
                                </group>
                            </group>
                            <group string="Error Information" invisible="not last_error">
                                <field name="last_error" nolabel="1" />
                            </group>
                        </page>
                        <page string="Advanced Settings" name="advanced" groups="base.group_system">
                            <group>
                                <group string="Ollama Configuration" invisible="provider_type != 'ollama'">
                                    <field name="ollama_host" />
                                    <field name="ollama_port" />
                                </group>
                                <group string="System Message">
                                    <field name="system_message"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>
    <!-- Search View -->
    <record id="view_llm_provider_search" model="ir.ui.view">
        <field name="name">llm.provider.search</field>
        <field name="model">llm.provider</field>
        <field name="arch" type="xml">
            <search string="Search LLM Providers">
                <field name="name" />
                <field name="provider_type" />
                <field name="model" />
                <field name="state" />
                <separator />
                <filter string="Active" name="active" domain="[('active', '=', True)]" />
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]" />
                <separator />
                <filter string="Ready" name="ready" domain="[('state', '=', 'ready')]" />
                <filter string="Processing" name="processing" domain="[('state', '=', 'processing')]" />
                <filter string="Rate Limited" name="rate_limited" domain="[('state', '=', 'rate_limited')]" />
                <filter string="Error" name="error" domain="[('state', '=', 'error')]" />
                <separator />
                <filter string="Used Today" name="used_today" domain="[('last_used', '>=', context_today().strftime('%Y-%m-%d'))]" />
                <group expand="0" string="Group By">
                    <filter string="Provider Type" name="group_by_type" context="{'group_by': 'provider_type'}" />
                    <filter string="State" name="group_by_state" context="{'group_by': 'state'}" />
                    <filter string="Last Used" name="group_by_last_used" context="{'group_by': 'last_used:day'}" />
                </group>
            </search>
        </field>
    </record>
    <!-- Kanban View -->
    <record id="view_llm_provider_kanban" model="ir.ui.view">
        <field name="name">llm.provider.kanban</field>
        <field name="model">llm.provider</field>
        <field name="arch" type="xml">
            <kanban class="o_kanban_mobile">
                <field name="name" />
                <field name="provider_type" />
                <field name="model" />
                <field name="state" />
                <field name="last_used" />
                <field name="request_count" />
                <field name="total_requests" />
                <templates>
                    <t t-name="kanban-box">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <strong class="o_kanban_record_title">
                                    <field name="name" />
                                </strong>
                                <div class="o_kanban_record_subtitle">
                                    <field name="provider_type" /> -
                                    <field name="model" />
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <span class="badge badge-pill" t-attf-class="badge-#{record.state.raw_value == 'ready' ? 'success' : 
                                                           record.state.raw_value == 'processing' ? 'warning' : 'danger'}">
                                            <field name="state" />
                                        </span>
                                    </div>
                                    <div class="oe_kanban_bottom_right">
                                        <small class="text-muted">
                                            Requests:
                                            <field name="total_requests" />
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>
    <!-- Action -->
    <record id="action_vpcs_llm_provider" model="ir.actions.act_window">
        <field name="name">LLM Providers</field>
        <field name="res_model">llm.provider</field>
        <field name="view_mode">list,kanban,form</field>
        <field name="context">{'search_default_active': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first LLM Provider!
            </p>
            <p>
                LLM Providers are used to generate AI-powered response for your Odoo models.
                Configure different providers to ensure reliable response generation.
            </p>
        </field>
    </record>
    <!-- Menu -->
    <menuitem id="menu_vpcs_llm_provider" name="LLM Provider" sequence="20" 
        groups="base.group_system" web_icon="ai_tour_automation,static/description/icon.png" />
    <menuitem id="menu_llm_provider_configuration" name="Configuration" parent="vpcs_llm_provider.menu_vpcs_llm_provider" sequence="20" groups="base.group_system" />
    <menuitem id="menu_vpcs_llm_provider_parent" name="LLM Providers" 
        parent="vpcs_llm_provider.menu_llm_provider_configuration" sequence="101" 
        action="action_vpcs_llm_provider" />
</odoo>