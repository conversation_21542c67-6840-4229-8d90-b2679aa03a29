<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">
            How to access a trial on VPCS cloud platform
        </h2>
        <h3 class="oe_slogan">
            Watch this video for a step-by-step guide on the trial process.<br/>
            You can try out the module on our cloud platform.
        </h3>
        <a class="text-center" style="font-size: 20px; margin-left: 50%;" target="_blank" href="https://www.youtube.com/watch?v=9AVfB13QaBo">Trial Guide</a>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">
            LLM Provider Integration in Odoo
        </h2>
        <h3 class="oe_slogan">
            This module helps you integrate different LLM providers into Odoo by simply entering an API key.
        </h3>
        <div style="margin-left: 20%; width: 60%; font-size: 18px">
            <h2>Supported LLM Providers</h2>
            <ul style="font-size: 15px;">
                <li>Google Gemini</li>
                <li>Groq</li>
                <li>Anthropic Claude</li>
                <li>OpenAI</li>
            </ul>
            <h2>Features @LLM provider Module</h2>
              <ul style="font-size: 15px;">
                  <li>You can use all the mentioned LLM providers in one module.</li>
                  <li>Test the module by asking a prompt and generating a response.</li>
              </ul>
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B; margin-top: 100px;">
            Generate API Key For Specific Model
        </h2>
        <div class="oe_demo oe_picture oe_screenshot mx-auto">
            <img src="generate key.png" style="max-width: 100%; height: auto;">
        </div>
    </div>
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B; margin-top: 100px;">
            Groq Model API Key Generate 
        </h2>
        <h3 class="oe_slogan">
            Search for the Groq API key, then go to the API and Keys menu and create an API key
        </h3>
        <div class="oe_demo oe_picture oe_screenshot mx-auto" >
            <img src="providerAPIkey3.png" style="max-width: 100%; height: auto;">
        </div>
     </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B; margin-top: 100px;">
            Enter your API key and test the connection with the model or API
        </h2>
        <h3 class="oe_slogan">
            Easily test the model in Odoo after a successful connection message.
        </h3>
        <div class="oe_demo oe_picture oe_screenshot mx-auto" >
            <img src="testConnection.png" style="max-width: 100%; height: auto;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B; margin-top: 100px;">
            Test different models by asking anything in the prompt and generating a response.
        </h2>
        <div class="oe_demo oe_picture oe_screenshot mx-auto" >
            <img src="GenerateResponse.png" style="max-width: 100%; height: auto;">
        </div>
    </div>
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B; margin-top: 100px; ">
            Use the same process for different providers. The example here shows Google Provider.
        </h2>
        <div class="oe_demo oe_picture oe_screenshot mx-auto" >
            <img src="UsingGoogle.png" style="max-width: 100%; height: auto;">
        </div>
    </div>
</section>

<section class="oe_container oe_dark" style="padding: 30px;">
    <div class="oe_spaced">
        <a href="http://www.vperfectcs.com" target="_blank">
            <h2 class="oe_slogan" style="color:#875A7B;">
                <img src="Vperfectcs.png" width="100%" height="auto">
            </h2>
        </a>
        <h3 class="oe_slogan">Veracious solutions to grow your business.</h3>
    </div>
    <div class="text-center">
        You can checkout our shop for demo and all apps on the below link <br/>
        <h3><a href="https://vpcscloud.com/shop" target="_blank">vpcscloud.com</a></h3>
    </div>
    <div class="text-center">
        <a href="https://vperfectcs.com/aboutus" target="_blank">
            <h2>About us</h2>
        </a>
        <div>
            <a href="http://www.vperfectcs.com/" target="_blank">Website</a> |
            <a href="http://www.vperfectcs.com/blog/our-blog-1" target="_blank">Blog</a> |
            <a href="http://www.vperfectcs.com/contactus" target="_blank">Contact us</a> |
            <a href="mailto:<EMAIL>" onClick="javascript:window.open('mailto:<EMAIL>', 'mail');event.preventDefault()" target="_blank">Request New Feature</a>
        </div>
    </div>
</section>
