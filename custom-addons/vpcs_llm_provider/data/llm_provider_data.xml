<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!-- Groq Default Provider -->
    <record id="llm_provider_groq_default" model="llm.provider">
        <field name="name">gemma2-9b-it</field>
        <field name="provider_type">groq</field>
        <field name="model">gemma2-9b-it</field>
        <field name="base_url">https://api.groq.com/v1</field>
        <field name="timeout">30</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
    </record>
    <!-- OpenAI Default Provider -->
    <record id="llm_provider_openai_default" model="llm.provider">
        <field name="name">OpenAI-gpt-4o</field>
        <field name="provider_type">openai</field>
        <field name="model">gpt-4o</field>
        <field name="base_url">https://api.openai.com/v1</field>
        <field name="timeout">60</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
    </record>
    <!-- Anthropic Default Provider -->
    <record id="llm_provider_anthropic_default" model="llm.provider">
        <field name="name">Anthropic-claude</field>
        <field name="provider_type">anthropic</field>
        <field name="model">claude-3-5-sonnet-20240620</field>
        <field name="base_url">https://api.anthropic.com/v1</field>
        <field name="timeout">60</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
    </record>
    <!-- Google Default Provider -->
    <record id="llm_provider_google_gemini_2_0_flash" model="llm.provider">
        <field name="name">gemini-2.0-flash</field>
        <field name="provider_type">google</field>
        <field name="model">gemini-2.0-flash</field>
        <field name="base_url">https://generativelanguage.googleapis.com/v1/models</field>
        <field name="timeout">60</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
    </record>
    <!-- Google Default Provider -->
    <record id="llm_provider_google_gemini_1_5_flash" model="llm.provider">
        <field name="name">gemini-1.5-flash</field>
        <field name="provider_type">google</field>
        <field name="model">gemini-1.5-flash</field>
        <field name="base_url">https://generativelanguage.googleapis.com/v1/models</field>
        <field name="timeout">60</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
    </record>
</odoo>