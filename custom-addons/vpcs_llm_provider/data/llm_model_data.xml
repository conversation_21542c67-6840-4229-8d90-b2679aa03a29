<odoo>

    <!-- Google Models -->
    <record id="llm_model_google_gemini_15_pro" model="llm.model">
        <field name="name">gemini-1.5-pro-latest</field>
        <field name="display_name">Gemini 1.5 Pro Latest</field>
        <field name="provider_type">google</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_google_gemini_15_flash" model="llm.model">
        <field name="name">gemini-1.5-flash-latest</field>
        <field name="display_name">Gemini 1.5 Flash Latest</field>
        <field name="provider_type">google</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_google_gemini_20_flash" model="llm.model">
        <field name="name">gemini-2.0-flash</field>
        <field name="display_name">Gemini 2.0 Flash</field>
        <field name="provider_type">google</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_google_gemini_10_pro" model="llm.model">
        <field name="name">gemini-1.0-pro</field>
        <field name="display_name">Gemini 1.0 Pro</field>
        <field name="provider_type">google</field>
        <field name="active">1</field>
    </record>

    <!--OpenAI Models -->
    <record id="llm_model_openai_gpt_4_turbo" model="llm.model">
        <field name="name">gpt-4-turbo</field>
        <field name="display_name">GPT-4 Turbo</field>
        <field name="provider_type">openai</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_openai_gpt_4" model="llm.model">
        <field name="name">gpt-4</field>
        <field name="display_name">GPT-4</field>
        <field name="provider_type">openai</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_openai_gpt_35_turbo" model="llm.model">
        <field name="name">gpt-3.5-turbo</field>
        <field name="display_name">GPT-3.5 Turbo</field>
        <field name="provider_type">openai</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_openai_gpt_35_turbo_16k" model="llm.model">
        <field name="name">gpt-3.5-turbo-16k</field>
        <field name="display_name">GPT-3.5 Turbo 16K</field>
        <field name="provider_type">openai</field>
        <field name="active">1</field>
    </record>

    <!-- Anthropic Models -->
    <record id="llm_model_anthropic_claude_3_opus" model="llm.model">
        <field name="name">claude-3-opus-20240229</field>
        <field name="display_name">Claude 3 Opus</field>
        <field name="provider_type">anthropic</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_anthropic_claude_3_sonnet" model="llm.model">
        <field name="name">claude-3-sonnet-20240229</field>
        <field name="display_name">Claude 3 Sonnet</field>
        <field name="provider_type">anthropic</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_anthropic_claude_3_haiku" model="llm.model">
        <field name="name">claude-3-haiku-20240307</field>
        <field name="display_name">Claude 3 Haiku</field>
        <field name="provider_type">anthropic</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_anthropic_claude_sonnet_4" model="llm.model">
        <field name="name">claude-sonnet-4-20250514</field>
        <field name="display_name">Claude Sonnet 4</field>
        <field name="provider_type">anthropic</field>
        <field name="active">1</field>
    </record>

    <!--Groq Models -->
    <record id="llm_model_groq_llama3_8b" model="llm.model">
        <field name="name">llama3-8b-8192</field>
        <field name="display_name">Llama 3 8B</field>
        <field name="provider_type">groq</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_groq_llama3_70b" model="llm.model">
        <field name="name">llama3-70b-8192</field>
        <field name="display_name">Llama 3 70B</field>
        <field name="provider_type">groq</field>
        <field name="active">1</field>
    </record>
   
    <!--  Ollama Models -->
    <record id="llm_model_ollama_llama3" model="llm.model">
        <field name="name">llama3</field>
        <field name="display_name">Llama 3</field>
        <field name="provider_type">ollama</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_ollama_mistral" model="llm.model">
        <field name="name">mistral</field>
        <field name="display_name">Mistral</field>
        <field name="provider_type">ollama</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_ollama_codellama" model="llm.model">
        <field name="name">codellama</field>
        <field name="display_name">Code Llama</field>
        <field name="provider_type">ollama</field>
        <field name="active">1</field>
    </record>
    <record id="llm_model_ollama_phi" model="llm.model">
        <field name="name">phi</field>
        <field name="display_name">Phi</field>
        <field name="provider_type">ollama</field>
        <field name="active">1</field>
    </record>

</odoo>
