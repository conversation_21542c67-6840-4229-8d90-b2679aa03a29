from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import groq
import logging
import json
import os
import time
import re
from datetime import timedelta
import openai
from openai import OpenAI
import anthropic
from anthropic import Anthropic
import google.generativeai as genai

_logger = logging.getLogger(__name__)


class LLMProvider(models.Model):
    _name = "llm.provider"
    _description = "LLM Provider Configuration"
    _inherit = ["mail.thread", "mail.activity.mixin"]
    _order = "last_used desc, sequence, id"

    name = fields.Char("Name", required=True)
    sequence = fields.Integer("Sequence", default=10)
    active = fields.Boolean(default=True)
    provider_type = fields.Selection(
        [
            ("groq", "Groq"),
            ("openai", "OpenAI"),
            ("anthropic", "Anthropic"),
            ("ollama", "Ollama"),
            ("google", "Google"),
        ],
        string="Provider Type",
        required=True,
    )

    # API Configuration
    api_key = fields.Char("API Key")
    model = fields.Char("Model Name", required=True)
    base_url = fields.Char("Base URL")
    timeout = fields.Integer("Timeout (seconds)", default=120)
    max_retries = fields.Integer("Max Retries", default=3)
    retry_delay = fields.Integer("Retry Delay (seconds)", default=5)
    system_message = fields.Text(
        "System Message",
        help="Default system message to be used with the model",
        default="You are a helpful assistant specialized in creating Odoo tours.",
    )
    show_generate_button = fields.Boolean(string="Show Generate Button", default=False)

    # input prompt
    prompt_text = fields.Text(
        string="Prompt", help="Enter prompt for AI response", default="Ask me anything"
    )

    # response result
    result_text = fields.Text(
        string="Response", help="AI generated response", default="", readonly=True
    )

    # Usage Tracking
    last_used = fields.Datetime("Last Used", readonly=True)
    total_requests = fields.Integer("Total Requests", default=0, readonly=True)
    successful_requests = fields.Integer(
        "Successful Requests", default=0, readonly=True
    )
    failed_requests = fields.Integer("Failed Requests", default=0, readonly=True)
    average_response_time = fields.Float(
        "Average Response Time (s)", default=0, readonly=True
    )

    # State management
    state = fields.Selection(
        [
            ("ready", "Ready"),
            ("processing", "Processing"),
            ("rate_limited", "Rate Limited"),
            ("error", "Error"),
        ],
        default="ready",
        tracking=True,
    )
    last_error = fields.Text("Last Error", tracking=True)
    last_error_time = fields.Datetime("Last Error Time", readonly=True)
    rate_limit_reset = fields.Datetime("Rate Limit Reset Time")
    request_count = fields.Integer("Request Count", default=0)
    max_requests = fields.Integer("Max Requests per Hour", default=100)

    # Ollama specific fields
    ollama_host = fields.Char("Ollama Host", default="host.docker.internal")
    ollama_port = fields.Char("Ollama Port", default="11434")
    retried_correction = fields.Integer(
        "Retried Correction", help="it will correct the query if not valid", default=2
    )

    def _check_rate_limit(self):
        """Check if the provider is currently rate limited"""
        if self.state != "rate_limited":
            return False

        if not self.rate_limit_reset:
            return False

        now = fields.Datetime.now()
        if now >= self.rate_limit_reset:
            self.write(
                {"state": "ready", "rate_limit_reset": False, "request_count": 0}
            )
            return False

        return True

    def _handle_rate_limit_error(self, reset_time=None):
        """Handle rate limit error by updating provider state"""
        if not reset_time:
            # Default to 1 hour if no reset time provided
            now = fields.Datetime.now()
            reset_time = now + timedelta(hours=1)

        self.write(
            {
                "state": "rate_limited",
                "rate_limit_reset": reset_time,
                "request_count": self.max_requests,  # Set to max to prevent further requests
                "last_error": "Rate limit exceeded",
                "last_error_time": fields.Datetime.now(),
            }
        )

        _logger.warning("Provider %s rate limited until %s", self.name, reset_time)

    def _increment_request_count(self):
        """Increment the request counter and check limits"""
        self.request_count += 1
        if self.request_count >= self.max_requests:
            self._handle_rate_limit_error()
            return False
        return True

    def generate(self, prompt, **kwargs):
        """Generate completion using the configured provider"""
        if self._check_rate_limit():
            raise UserError(
                _("Provider is currently rate limited. Please try again later.")
            )

        if not self._increment_request_count():
            raise UserError(_("Request limit exceeded. Please try again later."))

        try:
            provider_class = self._get_provider_class()
            provider = provider_class(self)
            start_time = time.time()
            result = provider.generate(prompt, **kwargs)
            end_time = time.time()
            response_time = end_time - start_time

            # Update usage tracking
            self.write(
                {
                    "last_used": fields.Datetime.now(),
                    "total_requests": self.total_requests + 1,
                    "successful_requests": self.successful_requests + 1,
                    "average_response_time": (
                        self.average_response_time * self.total_requests + response_time
                    )
                    / (self.total_requests + 1),
                }
            )

            # Reset error state on successful generation
            if self.state in ["error", "rate_limited"]:
                self.write(
                    {
                        "state": "ready",
                        "last_error": False,
                        "last_error_time": False,
                        "rate_limit_reset": False,
                    }
                )

            return result

        except Exception as e:
            error_msg = str(e)
            self.write(
                {
                    "state": "error",
                    "last_error": error_msg,
                    "last_error_time": fields.Datetime.now(),
                    "total_requests": self.total_requests + 1,
                    "failed_requests": self.failed_requests + 1,
                }
            )

            if "rate limit" in error_msg.lower():
                self._handle_rate_limit_error()

            raise UserError(_("Failed to generate completion: %s", error_msg))

    def generate_response(self):
        """Generate completion using the configured provider based on user prompt"""
        prompt = self.prompt_text

        try:
            self.action_test_connection()
            provider_class = self._get_provider_class()
            provider = provider_class(self)
            # Generate response
            result = provider.generate(prompt)
            # Display output in result_text
            self.result_text = result

        except Exception as e:
            raise UserError(
                _("An error occurred while generating the response: %s" % str(e))
            )

    def _get_provider_class(self):
        """Get the appropriate provider class based on provider_type"""
        if self.provider_type == "groq":
            return GroqProvider
        elif self.provider_type == "openai":
            return OpenAIProvider
        elif self.provider_type == "anthropic":
            return AnthropicProvider
        elif self.provider_type == "google":
            return GoogleProvider
        elif self.provider_type == "ollama":
            return OllamaProvider
        else:
            raise UserError(_("Unsupported provider type: %s") % self.provider_type)

    def action_test_connection(self):
        """Test the connection to the LLM provider."""
        self.ensure_one()
        try:
            provider_class = self._get_provider_class()
            provider = provider_class(self)
            test_prompt = "This is a test message to verify the connection."
            result = provider.generate(test_prompt)
            print("result Field", result)
            if result:
                self.write(
                    {
                        "state": "processing",
                        "last_error": False,
                        "show_generate_button": True,
                    }
                )
                return {
                    "type": "ir.actions.client",
                    "tag": "display_notification",
                    "params": {
                        "title": _("Success"),
                        "message": _(
                            "Successfully connected to %s", self.provider_type
                        ),
                        "sticky": False,
                        "type": "success",
                    },
                }
        except Exception as e:
            error_msg = str(e)
            self.write(
                {
                    "state": "error",
                    "last_error": error_msg,
                    "last_error_time": fields.Datetime.now(),
                    "show_generate_button": False,
                }
            )
            return {
                "type": "ir.actions.client",
                "tag": "display_notification",
                "params": {
                    "title": _("Error"),
                    "message": error_msg,
                    "sticky": True,
                    "type": "danger",
                },
            }

    def action_reset_state(self):
        """Reset the provider state to ready."""
        self.ensure_one()
        self.write(
            {
                "state": "ready",
                "last_error": False,
                "request_count": 0,
                "rate_limit_reset": False,
            }
        )
        return {
            "type": "ir.actions.client",
            "tag": "display_notification",
            "params": {
                "title": _("Success"),
                "message": _("Provider state has been reset"),
                "sticky": False,
                "type": "success",
            },
        }


class BaseLLMProvider:
    def __init__(self, config):
        self.config = config

    def generate(self, prompt, **kwargs):
        """Base generate method - should be overridden by provider implementations"""
        raise NotImplementedError()

    def _handle_request(
        self, url, method="POST", data=None, headers=None, timeout=None
    ):
        """Handle HTTP request with detailed logging"""
        try:
            _logger.info(f"Making request to: {url}")
            _logger.info(f"Headers: {headers}")
            if data:
                _logger.info(f"Request data: {json.dumps(data, indent=2)}")

            timeout = timeout or self.config.timeout or 120
            response = requests.request(
                method, url, json=data, headers=headers, timeout=timeout
            )

            _logger.info(f"Response status code: {response.status_code}")
            _logger.info(f"Response headers: {dict(response.headers)}")

            try:
                response_data = response.json()
                _logger.info(f"Response data: {json.dumps(response_data, indent=2)}")
            except ValueError:
                _logger.info(f"Raw response text: {response.text}")

            if not response.ok:
                response.raise_for_status()
            return response.json()

        except Exception as e:
            _logger.error(f"Request failed: {str(e)}")
            if hasattr(e, "response") and e.response is not None:
                _logger.error(f"Error response: {e.response.text}")
            raise


class OllamaProvider(BaseLLMProvider):
    """Ollama provider implementation."""

    def generate(self, prompt, **kwargs):
        """Generate text using Ollama's API."""
        if not self.config.ollama_host or not self.config.ollama_port:
            raise UserError(_("Ollama host and port must be configured"))

        url = f"http://{self.config.ollama_host}:{self.config.ollama_port}/api/generate"

        data = {
            "model": self.config.model,
            "prompt": prompt,
            "system": self.config.system_message,
            "stream": False,
        }

        try:
            response = requests.post(url, json=data, timeout=self.config.timeout)
            response.raise_for_status()
            result = response.json()
            return result.get("response", "")
        except requests.exceptions.RequestException as e:
            raise UserError(_("Failed to connect to Ollama: %s") % str(e))


class GoogleProvider(BaseLLMProvider):
    """Google Gemini API provider implementation."""

    def generate(self, prompt, **kwargs):
        """Generate text using Google's Gemini API."""
        try:
            # Configure the API key
            genai.configure(api_key=self.config.api_key)
            
            # Create the model
            model = genai.GenerativeModel(self.config.model)
            
            # Generate content
            response = model.generate_content(prompt)
            
            return response.text

        except Exception as e:
            raise UserError(_("Failed to generate completion: %s") % str(e))


class GroqProvider(BaseLLMProvider):
    """Groq API provider implementation."""

    def generate(self, prompt, **kwargs):
        """Generate text using Groq's API."""
        try:
            client = groq.Groq(api_key=self.config.api_key)

            chat_completion = client.chat.completions.create(
                messages=[
                    {"role": "system", "content": self.config.system_message},
                    {"role": "user", "content": prompt},
                ],
                model=self.config.model,
                temperature=0.7,
                max_tokens=4096,
                top_p=1,
                stream=False,
            )
            
            return chat_completion.choices[0].message.content

        except groq.APIStatusError as e:
            if "rate_limit_exceeded" in str(e):
                raise UserError(_("Rate limit exceeded: %s") % str(e))
            raise UserError(_("Groq API error: %s") % str(e))
        except Exception as e:
            raise UserError(_("Failed to generate completion: %s") % str(e))


class OpenAIProvider(BaseLLMProvider):
    """OpenAI API provider implementation."""

    def generate(self, prompt, **kwargs):
        """Generate text using OpenAI's API."""
        openai.api_key = self.config.api_key
        if self.config.base_url:
            client = OpenAI(api_key=self.config.api_key, base_url=self.config.base_url)
        else:
            client = OpenAI(api_key=self.config.api_key)

        try:
            response = client.chat.completions.create(
                model=self.config.model,
                messages=[
                    {"role": "system", "content": self.config.system_message},
                    {"role": "user", "content": prompt},
                ],
                temperature=0.7,
                max_tokens=2048,
                top_p=1.0,
                frequency_penalty=0.0,
                presence_penalty=0.0,
                timeout=self.config.timeout,
            )
            return response.choices[0].message.content

        except openai.RateLimitError as e:
            self.config.write(
                {
                    "state": "rate_limited",
                    "last_error": str(e),
                    "last_error_time": fields.Datetime.now(),
                    "rate_limit_reset": fields.Datetime.now() + timedelta(hours=1),
                }
            )
            raise UserError(_("OpenAI rate limit exceeded. Please try again later."))

        except openai.APIError as e:
            self.config.write(
                {
                    "state": "error",
                    "last_error": str(e),
                    "last_error_time": fields.Datetime.now(),
                }
            )
            raise UserError(_("OpenAI API error: %s") % str(e))

        except Exception as e:
            self.config.write(
                {
                    "state": "error",
                    "last_error": str(e),
                    "last_error_time": fields.Datetime.now(),
                }
            )
            raise UserError(_("Error generating text with OpenAI: %s") % str(e))


class AnthropicProvider(BaseLLMProvider):
    """Anthropic API provider implementation."""

    def generate(self, prompt, **kwargs):
        """Generate text using Anthropic's API."""
        try:
            client = Anthropic(api_key=self.config.api_key)

            # Create the message using the correct format
            message = client.messages.create(
                model=self.config.model or "claude-3-opus-20240229",
                max_tokens=2048,
                system=self.config.system_message,
                messages=[{"role": "user", "content": prompt}],
            )

            # Extract the response text
            if message.content and len(message.content) > 0:
                return message.content[0].text
            return ""

        except anthropic.RateLimitError as e:
            self.config.write(
                {
                    "state": "rate_limited",
                    "last_error": str(e),
                    "last_error_time": fields.Datetime.now(),
                    "rate_limit_reset": fields.Datetime.now() + timedelta(hours=1),
                }
            )
            _logger.error("Anthropic rate limit error: %s", str(e))
            raise UserError(_("Anthropic rate limit exceeded. Please try again later."))

        except anthropic.APIError as e:
            self.config.write(
                {
                    "state": "error",
                    "last_error": str(e),
                    "last_error_time": fields.Datetime.now(),
                }
            )
            _logger.error("Anthropic API error: %s", str(e))
            raise UserError(_("Anthropic API error: %s") % str(e))

        except Exception as e:
            self.config.write(
                {
                    "state": "error",
                    "last_error": str(e),
                    "last_error_time": fields.Datetime.now(),
                }
            )
            _logger.error("Error generating text with Anthropic: %s", str(e))
            raise UserError(_("Error generating text with Anthropic: %s") % str(e))
