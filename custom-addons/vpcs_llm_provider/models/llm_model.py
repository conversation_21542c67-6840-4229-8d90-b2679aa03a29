# -*- coding: utf-8 -*-
from odoo import models, fields


class LLMModel(models.Model):
    _name = 'llm.model'
    _description = 'LLM Model Registry'
    _order = 'sequence, display_name'

    name = fields.Char(
        string="Model ID",
        required=True,
        help="The internal model identifier to use in the API call (e.g., gpt-4o, mixtral-8x7b)."
    )
    display_name = fields.Char(
        string="Display Name",
        required=True,
        help="User-friendly name for UI display."
    )
    provider_type = fields.Selection(
        selection=[
            ('openai', 'OpenAI'),
            ('groq', 'Groq'),
            ('anthropic', 'Anthropic'),
            ('google', 'Google'),
            ('ollama', 'Ollama'),
            # Add more providers if needed
        ],
        string="Provider",
        required=True,
        help="The LLM provider this model belongs to."
    )
    active = fields.Boolean(default=True, help="Only active models will be selectable in providers.")
    supports_system_message = fields.Boolean(string="Supports System Message", default=True)
    sequence = fields.Integer(string="Sequence", default=10)
