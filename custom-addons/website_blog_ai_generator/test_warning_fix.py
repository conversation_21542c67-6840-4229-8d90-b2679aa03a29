#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Test script to verify that pytubefix deprecation warnings are properly suppressed.
This script can be run independently to test the warning suppression functionality.
"""

import sys
import warnings
import logging

# Set up logging to capture warnings
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_pytubefix_import_without_suppression():
    """Test importing pytubefix without warning suppression to show the warnings."""
    print("=" * 60)
    print("Testing pytubefix import WITHOUT warning suppression:")
    print("=" * 60)
    
    try:
        # This should show the deprecation warnings
        from pytubefix import YouTube
        print("✅ pytubefix imported successfully (with warnings)")
        return True
    except ImportError as e:
        print(f"❌ pytubefix import failed: {e}")
        return False

def test_pytubefix_import_with_suppression():
    """Test importing pytubefix with our warning suppression utility."""
    print("\n" + "=" * 60)
    print("Testing pytubefix import WITH warning suppression:")
    print("=" * 60)
    
    try:
        # Import our warning suppression utility
        from utils.warning_suppression import safe_import_pytubefix, configure_external_library_warnings
        
        # Configure warning filters
        configure_external_library_warnings()
        
        # Import pytubefix safely
        YouTube, success = safe_import_pytubefix()
        
        if success:
            print("✅ pytubefix imported successfully (warnings suppressed)")
            return True
        else:
            print("❌ pytubefix import failed")
            return False
            
    except ImportError as e:
        print(f"❌ Warning suppression utility import failed: {e}")
        return False

def test_youtube_functionality():
    """Test basic YouTube functionality if available."""
    print("\n" + "=" * 60)
    print("Testing YouTube functionality:")
    print("=" * 60)
    
    try:
        from utils.warning_suppression import safe_import_pytubefix
        YouTube, success = safe_import_pytubefix()
        
        if not success:
            print("⚠️  pytubefix not available, skipping functionality test")
            return False
            
        # Test with a sample YouTube URL (this won't actually download anything)
        test_url = "https://www.youtube.com/watch?v=dQw4w9WgXcQ"  # Rick Roll for testing
        print(f"Testing with URL: {test_url}")
        
        # Just create the YouTube object to test basic functionality
        yt = YouTube(test_url)
        print(f"✅ YouTube object created successfully")
        print(f"   Video title: {yt.title}")
        print(f"   Video length: {yt.length} seconds")
        
        return True
        
    except Exception as e:
        print(f"❌ YouTube functionality test failed: {e}")
        return False

if __name__ == "__main__":
    print("Testing pytubefix warning suppression fix")
    print("This script tests the warning suppression functionality for the website_blog_ai_generator module")
    
    # Test 1: Import without suppression (to show the problem)
    test1_result = test_pytubefix_import_without_suppression()
    
    # Test 2: Import with suppression (to show the fix)
    test2_result = test_pytubefix_import_with_suppression()
    
    # Test 3: Basic functionality test
    test3_result = test_youtube_functionality()
    
    print("\n" + "=" * 60)
    print("SUMMARY:")
    print("=" * 60)
    print(f"Import without suppression: {'✅ PASS' if test1_result else '❌ FAIL'}")
    print(f"Import with suppression:    {'✅ PASS' if test2_result else '❌ FAIL'}")
    print(f"YouTube functionality:      {'✅ PASS' if test3_result else '❌ FAIL'}")
    
    if test2_result:
        print("\n🎉 SUCCESS: Warning suppression is working correctly!")
        print("The pytubefix deprecation warnings should now be suppressed in your Odoo module.")
    else:
        print("\n❌ FAILURE: Warning suppression is not working properly.")
        print("Please check the implementation and try again.")
    
    sys.exit(0 if test2_result else 1)
