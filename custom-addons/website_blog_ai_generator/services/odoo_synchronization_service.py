# -*- coding: utf-8 -*-
import xmlrpc.client
import logging

_logger = logging.getLogger(__name__)

class OdooSynchronizationService:
    """
    A service to synchronize records to a remote Odoo instance.
    """

    def __init__(self, connection):
        self.connection = connection
        self.uid = None

    def _connect(self):
        """
        Establishes a connection to the remote Odoo instance.
        """
        if self.uid:
            return
        try:
            common = xmlrpc.client.ServerProxy(f'{self.connection.url}/xmlrpc/2/common')
            self.uid = common.authenticate(self.connection.db, self.connection.username, self.connection.api_key, {})
            if not self.uid:
                _logger.error("Authentication failed for remote Odoo instance.")
                return False
            return True
        except Exception as e:
            _logger.error(f"Failed to connect to remote Odoo instance: {e}")
            return False

    def create_blog_post(self, blog_post_data):
        """
        Creates a blog post on the remote Odoo instance.

        :param blog_post_data: A dictionary of values for the blog.post model.
        :return: The ID of the newly created blog post, or None if creation fails.
        """
        if not self._connect():
            return None

        try:
            models = xmlrpc.client.ServerProxy(f'{self.connection.url}/xmlrpc/2/object')

            # Fetch remote blog_id
            blog_name = blog_post_data.pop('blog_id', None)
            if blog_name:
                blog_ids = models.execute_kw(self.connection.db, self.uid, self.connection.api_key, 'blog.blog', 'search', [[['name', '=', blog_name]]], {'limit': 1})
                if blog_ids:
                    blog_post_data['blog_id'] = blog_ids[0]
                else:
                    _logger.warning(f"Blog '{blog_name}' not found on remote instance. Blog post will be created without a blog.")

            # Fetch remote website_id
            website_name = blog_post_data.pop('website_id', None)
            if website_name:
                website_ids = models.execute_kw(self.connection.db, self.uid, self.connection.api_key, 'website', 'search', [[['name', '=', website_name]]], {'limit': 1})
                if website_ids:
                    blog_post_data['website_id'] = website_ids[0]
                else:
                    _logger.warning(f"Website '{website_name}' not found on remote instance. Blog post will be created without a website.")

            # Handle tags
            tag_names = blog_post_data.pop('tag_ids', [])
            tag_ids = []
            if tag_names:
                for tag_name in tag_names:
                    # Search for the tag
                    remote_tag_ids = models.execute_kw(self.connection.db, self.uid, self.connection.api_key, 'blog.tag', 'search', [[['name', '=', tag_name]]], {'limit': 1})
                    if remote_tag_ids:
                        tag_ids.append(remote_tag_ids[0])
                    else:
                        # Create the tag if it doesn't exist
                        new_tag_id = models.execute_kw(self.connection.db, self.uid, self.connection.api_key, 'blog.tag', 'create', [{'name': tag_name}])
                        tag_ids.append(new_tag_id)
                blog_post_data['tag_ids'] = [(6, 0, tag_ids)]


            blog_post_id = models.execute_kw(
                self.connection.db, self.uid, self.connection.api_key,
                'blog.post', 'create', [blog_post_data]
            )
            _logger.info(f"Successfully created blog post with ID {blog_post_id} on remote Odoo instance.")
            return blog_post_id
        except Exception as e:
            _logger.error(f"Failed to create blog post on remote Odoo instance: {e}")
            return None