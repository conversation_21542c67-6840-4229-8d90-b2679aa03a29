# AI Blog Post Generator

## Overview

The **AI Blog Post Generator** is a powerful Odoo 18 module designed to automate and streamline the creation of high-quality blog content. By integrating with advanced AI models through the `vpcs_llm_provider` module, it allows users to generate comprehensive blog posts from various sources, including YouTube videos, GitHub repositories, or simple topic descriptions.

This module is perfect for content marketers, technical writers, and businesses looking to scale their content strategy with minimal manual effort. It handles everything from text generation and HTML formatting to metadata optimization, delivering a publication-ready draft directly within Odoo.

## Key Features

* **Multi-Source Content Generation:**
    * **YouTube:** Provide a URL to automatically extract the video transcript and generate a blog post from it.
    * **GitHub:** Input a raw file URL from a public GitHub repository (e.g., the URL for a `README.md` file) to generate technical articles, code explanations, or release notes. **Note:** Private repositories are not supported.
    * **Topic:** Simply describe a topic, and the AI will write a detailed blog post about it.

* **Rich HTML Formatting:**
    * The generated content is automatically formatted with proper HTML tags (`<h1>`, `<h2>`, `<p>`, `<ul>`, `<li>`, `<code>`, etc.), making it fully compatible with the Odoo website editor.

* **Featured Image Support:**
    * Upload a featured image directly from the generation request form.
    * Control the placement of the image (top or bottom of the post).
    * The AI will even generate a relevant caption for the image.

* **Automated Metadata and SEO:**
    * The module automatically generates and populates all critical metadata fields:
        * **Subtitle:** A catchy subtitle to complement the main title.
        * **Tags:** Relevant tags to categorize the post.
        * **SEO Meta Title:** An optimized title for search engines.
        * **SEO Meta Description:** A compelling description to improve click-through rates.
        * **SEO Meta Keywords:** Relevant keywords to enhance search visibility.

* **Automated Social Media Posts:**
    * Alongside the blog post, the module also generates ready-to-use posts for major social media platforms:
        * **LinkedIn:** A professional post tailored for a business audience.
        * **Facebook:** An engaging and more casual post.
        * **Twitter/X:** A concise and catchy tweet.
    * Each generated post includes a `[URL]` placeholder, making it easy to add the link to your published blog post.

* **PPT Generation from Blog Post:**
    * Automatically generate a PowerPoint presentation (PPTX) from the content of the generated blog post.
    * The PPT includes a title slide and content slides with bullet points, structured to provide a concise summary of the blog post.
    * This feature is ideal for creating quick summaries or presentation materials based on your blog content.

* **Multi-Website Support:**
    * Seamlessly works in a multi-website environment, allowing you to assign each generated blog post to a specific website.

* **Asynchronous Processing:**
    * All generation requests are handled in the background using Odoo's queue job system, ensuring the UI remains responsive and you can continue working without interruption.

* **Remote Odoo Synchronization:**
    * Configure connections to one or more remote Odoo instances.
    * Automatically or manually synchronize your generated blog posts to a remote Odoo database, making it easy to manage content across multiple Odoo environments.

* **Flexible AI Model Selection:**
    * Choose from a variety of supported AI models from different providers (e.g., Google, Groq, Anthropic Claude, OpenAI, Ollama) to balance cost, speed, and quality for each generation request. The selection will dynamically display models from providers with configured API keys.

## How to Use

1.  **(Admin) Configure Remote Connections:**
    * Go to `Website > AI Content > Odoo Connections`.
    * Create and test connections to your remote Odoo instances.
2.  **Navigate to the Blog Generation Requests:** Go to `Website > AI Content > Blog Generation Requests`.
2.  **Create a New Request:**
    * Click "Create" to open a new request form.
    * Provide a **Title** for your blog post.
    * Select the **Blog** and **Website** where the post will be published.
    * Choose a **Source Type** (YouTube, GitHub, or Topic) and fill in the corresponding input field.
    * (Optional) Upload a **Featured Image** and choose its **Placement**.
    * (Optional) Add any **Additional Instructions** for the AI.
    * (Optional) Select a **Remote Odoo Connection** if you want to sync the post automatically.
    * Select your desired **AI Model**. The available models will be filtered based on the LLM providers for which you have configured API keys.
3.  **Generate the Blog Post:**
    * Click the "Generate" button. The request will be added to a queue for background processing.
    * The status of the request will update automatically as it moves from `Draft` to `Pending`, `Processing`, and finally `Completed` or `Failed`.
4.  **Review and Publish:**
    * Once the status is "Completed," a link to the newly created **Generated Blog Post** will appear on the form.
    * If you configured a remote connection, the post will be automatically synced. You can also use the "Sync to Remote" button to trigger a manual sync.
    * You can find the generated social media content in the **Social Media** tab on the request form.
    * Click the link to the blog post, review the main content, and make any final edits.
    * To generate a PPT, click the "Generate PPT" button on the completed blog generation request form. A downloadable PPT file will be created.
    * When you're ready, publish the post to your website and use the generated social media content to promote it.

## Dependencies

* `website_blog`
* `vpcs_llm_provider`
* `queue_job`
* `pytubefix` (External Python library)
* `requests` (External Python library)
* `pydantic` (External Python library)
* `python-pptx` (External Python library)
* `beautifulsoup4` (External Python library)

## Installation

1.  Ensure all dependent Odoo apps are installed.
2.  Install the required Python libraries: `pip install pytubefix requests pydantic python-pptx beautifulsoup4`
3.  Add this module to your Odoo addons path.
4.  Restart the Odoo server.
5.  Go to `Apps`, search for "AI Blog Post Generator," and click "Install."

## Known Issues & Fixes

### PyTubeFix Deprecation Warnings (FIXED)

**Issue**: The module was showing deprecation warnings from the `pytubefix` library related to `enum.auto()` usage that will be deprecated in Python 3.13.

**Status**: ✅ **FIXED** - Warning suppression has been implemented.

**Details**: The warnings have been suppressed using a dedicated warning suppression utility. See `PYTUBEFIX_WARNING_FIX.md` for technical details.

**Test**: Run `python test_warning_fix.py` in the module directory to verify the fix.