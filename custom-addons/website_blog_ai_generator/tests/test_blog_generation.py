# -*- coding: utf-8 -*-
from odoo.tests.common import HttpCase, tagged

@tagged('post_install', '-at_install')
class TestBlogGeneration(HttpCase):

    def test_01_generate_blog_from_topic(self):
        """Test the blog generation from a topic."""
        # Create a request
        request = self.env['blog.generation.request'].create({
            'name': 'Test Blog Post',
            'source_type': 'topic',
            'topic_description': 'This is a test blog post about Odoo.',
        })
        self.assertTrue(request)

        # Trigger the generation
        request.action_generate_blog_post()

        # Check the state
        self.assertEqual(request.state, 'pending', "Request should be in pending state.")

        # Since the job is processed asynchronously, we cannot directly check the final state here.
        # We can, however, check that the job was created.
        # For a full test, we would need to run the queue job worker.
        # This test ensures the initial part of the process works.