<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>

        <!-- Form View for odoo.connection -->
        <record id="view_odoo_connection_form" model="ir.ui.view">
            <field name="name">odoo.connection.form</field>
            <field name="model">odoo.connection</field>
            <field name="arch" type="xml">
                <form string="Odoo Connection">
                    <header>
                        <button name="test_connection" type="object" string="Test Connection" class="oe_highlight"/>
                    </header>
                    <sheet>
                        <group>
                            <field name="name"/>
                            <field name="url"/>
                            <field name="db"/>
                            <field name="username"/>
                            <field name="api_key" password="True"/>
                        </group>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- List View for odoo.connection -->
        <record id="view_odoo_connection_list" model="ir.ui.view">
            <field name="name">odoo.connection.list</field>
            <field name="model">odoo.connection</field>
            <field name="arch" type="xml">
                <list string="Odoo Connections">
                    <field name="name"/>
                    <field name="url"/>
                    <field name="db"/>
                    <field name="username"/>
                </list>
            </field>
        </record>

        <!-- Action for odoo.connection -->
        <record id="action_odoo_connection" model="ir.actions.act_window">
            <field name="name">Odoo Connections</field>
            <field name="res_model">odoo.connection</field>
            <field name="view_mode">list,form</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    Create a new connection to a remote Odoo instance.
                </p>
            </field>
        </record>

    </data>
</odoo>