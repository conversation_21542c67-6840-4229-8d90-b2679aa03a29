<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- Form View -->
    <record id="view_blog_generation_request_form" model="ir.ui.view">
            <field name="name">blog.generation.request.form</field>
            <field name="model">blog.generation.request</field>
            <field name="arch" type="xml">
                <form string="Blog Generation Request">
                    <header>
                        <button name="action_generate_blog_post" string="Generate" type="object" class="oe_highlight"/>
                        <button name="action_sync_to_remote" string="Sync to Remote" type="object" class="oe_highlight" invisible="state != 'completed' or not odoo_connection_id"/>
                        <button name="action_reset_to_draft" string="Reset to Draft" type="object" class="oe_highlight" invisible="state != 'failed'"/>
                        <button name="action_generate_ppt" type="object" string="Download PowerPoint" class="btn-success" icon="fa-download"/>
                        <field name="state" widget="statusbar" statusbar_visible="draft,pending,processing,completed,failed"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" placeholder="e.g. How to Deploy Odoo on Docker"/>
                            </h1>
                        </div>
                        <group>
                            <group>
                                <field name="source_type"/>
                                <field name="blog_id" required="1"/>
                                <field name="website_id" required="1"/>
                                <field name="youtube_url" invisible="source_type != 'youtube'"/>
                                <field name="github_repo_path" invisible="source_type != 'github'"/>
                                <field name="topic_description" invisible="source_type != 'topic'"/>
                                <field name="additional_instructions" placeholder="e.g., Summarize this into 5 key points..."/>
                            </group>
                            <group>
                                <field name="model_id" placeholder="Select a provider"/>
                                <field name="provider_type" />
                                <field name="use_queue_job"/>
                                <field name="odoo_connection_id" options="{'no_create': True, 'no_open': True}"/>
                                <field name="blog_post_id"/>
                                <field name="image" widget="image" class="oe_avatar"/>
                                <field name="image_placement" invisible="not image"/>
                            </group>
                        </group>
                        <notebook>
                            <page string="Error" name="error">
                                <field name="error_message"/>
                            </page>
                            <page string="Social Media" name="social_media">
                                <group>
                                    <field name="linkedin_post" readonly="1"/>
                                    <field name="facebook_post" readonly="1"/>
                                    <field name="twitter_post" readonly="1"/>
                                </group>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter />
                </form>
            </field>
        </record>

    <!-- List View -->
    <record id="view_blog_generation_request_list" model="ir.ui.view">
        <field name="name">blog.generation.request.list</field>
        <field name="model">blog.generation.request</field>
        <field name="arch" type="xml">
            <list string="Blog Generation Requests">
                <field name="name"/>
                <field name="source_type"/>
                <field name="state"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <!-- Kanban View -->
    <record id="view_blog_generation_request_kanban" model="ir.ui.view">
        <field name="name">blog.generation.request.kanban</field>
        <field name="model">blog.generation.request</field>
        <field name="arch" type="xml">
            <kanban default_group_by="state">
                <field name="name"/>
                <field name="source_type"/>
                <field name="state"/>
                <field name="create_date"/>
                <templates>
                    <t t-name="card">
                        <div t-attf-class="oe_kanban_global_click">
                            <div class="oe_kanban_details">
                                <strong><field name="name"/></strong>
                                <div>
                                    <span>Source: </span>
                                    <field name="source_type"/>
                                </div>
                                <div>
                                    <span>Created: </span>
                                    <field name="create_date" widget="date"/>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- Action -->
    <record id="action_blog_generation_request" model="ir.actions.act_window">
        <field name="name">Blog Generation Requests</field>
        <field name="res_model">blog.generation.request</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new blog generation request.
            </p>
        </field>
    </record>

</odoo>