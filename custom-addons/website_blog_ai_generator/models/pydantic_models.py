from pydantic import BaseModel, Field
from typing import List, Optional

class BlogPostSEO(BaseModel):
    meta_title: str = Field(..., description="An SEO-optimized title (around 60 characters).")
    meta_description: str = Field(..., description="An SEO-optimized description (around 155-160 characters).")
    meta_keywords: List[str] = Field(..., description="A list of relevant SEO keywords.")

class SocialMediaPosts(BaseModel):
    linkedin_post: str = Field(..., description="A professional post for LinkedIn (around 200-250 words) with a link placeholder [URL].")
    facebook_post: str = Field(..., description="An engaging post for Facebook (around 100-150 words) with a link placeholder [URL].")
    twitter_post: str = Field(..., description="A short, catchy tweet for Twitter/X (under 280 characters) with a link placeholder [URL].")

class PPTSlide(BaseModel):
    title: str = Field(..., description="Title of the slide")
    bullet_points: List[str] = Field(..., description="Bullet points for the slide")

class PPTContent(BaseModel):
    slides: List[PPTSlide] = Field(..., description="List of slides for the presentation")

class BlogPostContent(BaseModel):
    title: str = Field(..., description="The main title of the blog post.")
    html_content: str = Field(..., description="The main body of the blog post in rich HTML format.")
    subtitle: str = Field(..., description="A catchy subtitle for the blog post.")
    tags: List[str] = Field(..., description="A list of 3-5 relevant keywords for tags.")
    image_caption: Optional[str] = Field(None, description="A short, descriptive caption for the featured image.")
    seo: BlogPostSEO = Field(..., description="SEO-related metadata for the blog post.")
    social_media: SocialMediaPosts = Field(..., description="Generated posts for social media platforms.")
    ppt_content: Optional[PPTContent] = Field(default=None, description="PowerPoint presentation content")