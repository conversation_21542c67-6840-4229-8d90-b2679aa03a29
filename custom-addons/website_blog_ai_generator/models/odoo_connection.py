# -*- coding: utf-8 -*-
import xmlrpc.client
import logging

from odoo import models, fields, api
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class OdooConnection(models.Model):
    """
    Model to store connection details for remote Odoo instances.
    """
    _name = 'odoo.connection'
    _description = 'Odoo Connection'

    name = fields.Char(string='Connection Name', required=True, help="A user-friendly name for the connection.")
    url = fields.Char(string='Odoo URL', required=True, help="The URL of the remote Odoo instance (e.g., https://my-odoo.com).")
    db = fields.Char(string='Database', required=True, help="The name of the database to connect to.")
    username = fields.Char(string='Username', required=True, help="The username (login) for the remote Odoo instance.")
    api_key = fields.Char(string='API Key', required=True, help="The user's API key or password for the remote instance.")

    def test_connection(self):
        """
        Tests the connection to the remote Odoo instance using XML-RPC.
        """
        self.ensure_one()
        common = xmlrpc.client.ServerProxy(f'{self.url}/xmlrpc/2/common')
        try:
            uid = common.authenticate(self.db, self.username, self.api_key, {})
            if uid:
                message = f"Successfully connected to {self.url} as user '{self.username}' (UID: {uid})."
                notification_type = 'success'
            else:
                message = "Authentication failed. Please check your credentials."
                notification_type = 'danger'
        except xmlrpc.client.Fault as e:
            message = f"XML-RPC Fault: {e.faultString}"
            _logger.error(message)
            notification_type = 'danger'
        except Exception as e:
            message = f"An unexpected error occurred: {e}"
            _logger.error(message)
            notification_type = 'danger'

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'Connection Test',
                'message': message,
                'type': notification_type,
                'sticky': False,
            }
        }