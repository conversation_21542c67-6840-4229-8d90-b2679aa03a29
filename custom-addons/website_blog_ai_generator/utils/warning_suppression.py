# -*- coding: utf-8 -*-
"""
Utility module for suppressing known deprecation warnings from external libraries.
This helps keep the Odoo logs clean while maintaining functionality.
"""

import warnings
import contextlib
import logging

_logger = logging.getLogger(__name__)


@contextlib.contextmanager
def suppress_pytubefix_warnings():
    """
    Context manager to suppress pytubefix deprecation warnings.
    
    These warnings are related to enum.auto() usage in pytubefix library
    that will be deprecated in Python 3.13. Since this is an external library
    issue, we suppress these specific warnings to keep logs clean.
    """
    with warnings.catch_warnings():
        warnings.filterwarnings(
            "ignore",
            message="In 3.13 the default `auto()`/`_generate_next_value_` will require all values to be sortable and support adding +1",
            category=DeprecationWarning,
            module="pytubefix.sabr.core.server_abr_stream"
        )
        yield


def configure_external_library_warnings():
    """
    Configure warning filters for all known external library issues.
    Call this once at module initialization to suppress known warnings.
    """
    # Suppress pytubefix enum.auto() deprecation warnings
    warnings.filterwarnings(
        "ignore",
        message="In 3.13 the default `auto()`/`_generate_next_value_` will require all values to be sortable and support adding +1",
        category=DeprecationWarning,
        module="pytubefix.sabr.core.server_abr_stream"
    )
    
    _logger.info("External library warning filters configured")


def safe_import_pytubefix():
    """
    Safely import pytubefix with warning suppression.
    
    Returns:
        tuple: (YouTube class or None, success boolean)
    """
    try:
        with suppress_pytubefix_warnings():
            from pytubefix import YouTube
            return YouTube, True
    except ImportError as e:
        _logger.warning("pytubefix import failed: %s", e)
        return None, False
