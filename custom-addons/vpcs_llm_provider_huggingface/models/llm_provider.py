from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import requests
import json

_logger = logging.getLogger(__name__)

class LLMProvider(models.Model):
    _inherit = "llm.provider"

    provider_type = fields.Selection(
        selection_add=[("huggingface", "Hugging Face")],
        ondelete={"huggingface": "cascade"},
    )

    def generate(self, prompt, **kwargs):
        """Override to delegate to the Hugging Face abstract provider."""
        if self.provider_type == 'huggingface':
            # Note the different abstract model name to avoid conflicts
            return self.env['huggingface.api.provider'].generate(self, prompt, **kwargs)
        return super(LLMProvider, self).generate(prompt, **kwargs)

    def action_test_connection(self):
        """Override to provide custom test connection for Hugging Face."""
        if self.provider_type == 'huggingface':
            self.ensure_one()
            try:
                test_prompt = "This is a test message to verify the connection."
                self.env['huggingface.api.provider'].generate(self, test_prompt)
                
                self.write({'state': 'ready', 'last_error': False, 'show_generate_button': True})
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _("Success"),
                        'message': _("Successfully connected to %s", self.provider_type),
                        'sticky': False,
                        'type': 'success',
                    },
                }
            except Exception as e:
                error_msg = str(e)
                self.write({'state': 'error', 'last_error': error_msg, 'last_error_time': fields.Datetime.now(), 'show_generate_button': False})
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _("Error"),
                        'message': error_msg,
                        'sticky': True,
                        'type': 'danger',
                    },
                }
        return super(LLMProvider, self).action_test_connection()

    def generate_response(self):
        """Override to handle the 'Generate Response' button for Hugging Face."""
        if self.provider_type == 'huggingface':
            prompt = self.prompt_text
            try:
                result = self.generate(prompt)
                self.result_text = json.dumps(result, indent=2)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'reload',
                }
            except Exception as e:
                self.result_text = str(e)
                raise UserError(_("An error occurred while generating the response: %s") % str(e))
        return super(LLMProvider, self).generate_response()


class HuggingFaceApiProvider(models.AbstractModel):
    _name = 'huggingface.api.provider'
    _description = 'Hugging Face Direct API Provider'

    def generate(self, config, prompt, **kwargs):
        """Generate using Hugging Face models via the direct Inference API."""
        if not config.base_url:
            raise UserError(_("Hugging Face Inference API URL is not configured in the provider."))

        url = config.base_url
        
        # Handle different input types
        if isinstance(prompt, bytes):
            # For image data, send as binary
            request_data = prompt
            headers = {'Content-Type': 'application/octet-stream'}
        else:
            # For text data, send as JSON
            request_data = {"inputs": prompt}
            headers = {'Content-Type': 'application/json'}

        try:
            if config.api_key:
                headers['Authorization'] = f'Bearer {config.api_key}'

            timeout = config.timeout or 120
            
            if isinstance(prompt, bytes):
                response = requests.post(url, data=request_data, headers=headers, timeout=timeout)
            else:
                response = requests.post(url, json=request_data, headers=headers, timeout=timeout)
                
            response.raise_for_status()
            
            # Handle different response types
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                return response.json()
            else:
                # For binary responses (like images), return the content
                return response.content
                
        except Exception as e:
            _logger.error("Hugging Face Inference API error: %s", str(e))
            raise UserError(_("Failed to generate with Hugging Face Inference API: %s") % str(e))