/** @odoo-module **/

import { rpc } from "@web/core/network/rpc";

const DEBUG = true;

function log(message) {
    if (DEBUG) {
        console.log("[HF Integration] " + message);
    }
}

function inspectDOM() {
    if (!DEBUG) return;

    log("Inspecting DOM for textareas...");
    const allTextareas = document.querySelectorAll('textarea');
    log(`Found ${allTextareas.length} textareas in total`);

    allTextareas.forEach((textarea, index) => {
        if (index < 10) {
            const classes = textarea.className || 'no-class';
            const id = textarea.id || 'no-id';
            const name = textarea.name || 'no-name';
            const placeholder = textarea.placeholder || 'no-placeholder';
            const parent = textarea.parentNode?.className || 'no-parent';

            log(`Textarea ${index}: class=${classes}, id=${id}, name=${name}, placeholder=${placeholder}, parent=${parent}`);
        }
    });

    const odooElements = document.querySelectorAll('.o_chatter, .o_Chatter, .o_mail_thread, .o_Composer, .o-mail-Composer');
    log(`Found ${odooElements.length} Odoo chatter elements`);
    
    // Inspect attachment elements
    log("Inspecting attachment elements...");
    const imageAttachments = document.querySelectorAll('.o-mail-AttachmentImage');
    const cardAttachments = document.querySelectorAll('.o-mail-AttachmentCard');
    log(`Found ${imageAttachments.length} image attachments and ${cardAttachments.length} card attachments`);
    
    imageAttachments.forEach((element, index) => {
        if (index < 3) {
            const title = element.getAttribute('title') || 'no-title';
            const imgSrc = element.querySelector('img')?.getAttribute('src') || 'no-src';
            log(`Image attachment ${index}: title=${title}, src=${imgSrc.substring(0, 50)}`);
        }
    });
    
    cardAttachments.forEach((element, index) => {
        if (index < 3) {
            const title = element.getAttribute('title') || 'no-title';
            const nameText = element.querySelector('.text-truncate')?.textContent?.trim() || 'no-name';
            log(`Card attachment ${index}: title=${title}, name=${nameText}`);
        }
    });
}

function showNotification(message, isError = false) {
    const notification = document.createElement("div");
    notification.className = "o-hf-notification";
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${isError ? '#f44336' : '#4CAF50'};
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
        transition: opacity 0.5s;
    `;
    notification.innerText = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = "0";
        setTimeout(() => notification.remove(), 500);
    }, 4000);
}

let activeTasks = [];

async function loadActiveTasks() {
    try {
        const result = await rpc('/odoo_huggingface_mcp/get_active_tasks');
        if (result.success) {
            // Filter out heavy processing tasks that should use dedicated interface
            const heavyTasks = ['ocr', 'image-to-text', 'text-to-image', 'speech-to-text', 'text-to-speech'];
            activeTasks = result.tasks.filter(task => !heavyTasks.includes(task.task_type));
            log(`Loaded ${activeTasks.length} active tasks (filtered out heavy processing tasks)`);
            
            // Log filtered tasks for debugging
            const filteredCount = result.tasks.length - activeTasks.length;
            if (filteredCount > 0) {
                log(`Filtered out ${filteredCount} heavy processing tasks - use dedicated AI Processing interface`);
            }
        } else {
            log(`Error loading tasks: ${result.error}`);
        }
    } catch (err) {
        log(`Failed to load active tasks: ${err}`);
    }
}

function addHuggingFaceButtons(promptInput) {
    if (!promptInput || promptInput.querySelector('.o-hf-btn-wrapper')) {
        return;
    }

    const textarea = promptInput.querySelector('textarea');
    if (!textarea) return;

    log("Adding HF buttons to element: " + (promptInput.className || 'unknown'));

    const wrapper = document.createElement('div');
    wrapper.className = 'o-hf-btn-wrapper';
    wrapper.style.cssText = `
        position: absolute;
        right: 40px;
        top: 5px;
        z-index: 9999;
        display: flex;
        gap: 3px;
    `;

    // Create buttons for each active task
    activeTasks.forEach(task => {
        const btn = createTaskButton(task, textarea);
        wrapper.appendChild(btn);
    });

    promptInput.appendChild(wrapper);
}

function getChatterAttachments() {
    const attachments = [];
    
    // Odoo 18 image attachments (o-mail-AttachmentImage)
    const imageAttachments = document.querySelectorAll('.o-mail-AttachmentImage');
    imageAttachments.forEach(element => {
        const imgElement = element.querySelector('img[src]');
        const title = element.getAttribute('title') || element.getAttribute('aria-label');
        
        if (imgElement && title) {
            const src = imgElement.getAttribute('src');
            if (src && title.match(/\.(jpg|jpeg|png|gif|bmp|tiff)$/i)) {
                attachments.push({
                    name: title,
                    url: src,
                    element: element,
                    type: 'image'
                });
                log(`Found image attachment: ${title}`);
            }
        }
    });
    
    // Odoo 18 file attachments (o-mail-AttachmentCard) - check for images
    const cardAttachments = document.querySelectorAll('.o-mail-AttachmentCard');
    cardAttachments.forEach(element => {
        const title = element.getAttribute('title') || element.getAttribute('aria-label');
        const nameElement = element.querySelector('.text-truncate');
        
        if (title || nameElement) {
            const name = title || nameElement?.textContent?.trim() || '';
            
            // Check if it's an image file by name
            if (name && name.match(/\.(jpg|jpeg|png|gif|bmp|tiff)$/i)) {
                // Try to get download URL or construct it
                const downloadBtn = element.querySelector('[data-download-url]');
                let url = '';
                
                if (downloadBtn) {
                    url = downloadBtn.getAttribute('data-download-url');
                } else {
                    // Try to construct URL from onclick or other attributes
                    const clickableElement = element.querySelector('[t-on-click]');
                    if (clickableElement) {
                        // This would need the attachment ID, which might be in data attributes
                        // For now, we'll mark it as found but without URL
                        url = '#attachment-' + name; // Placeholder
                    }
                }
                
                attachments.push({
                    name: name,
                    url: url,
                    element: element,
                    type: 'card'
                });
                log(`Found card attachment: ${name}`);
            }
        }
    });
    
    log(`Total found ${attachments.length} image attachments`);
    return attachments;
}

function createTaskButton(task, textarea) {
    const btn = document.createElement('button');
    btn.className = 'btn btn-sm btn-secondary o-hf-btn';
    btn.innerHTML = `<img src="/odoo_huggingface_mcp/static/description/${task.icon_name}" style="width: 16px; height: 16px;" alt="${task.name}" />`;
    btn.title = task.button_title || task.name;
    btn.style.cssText = `
        background-color: #f0f0f0 !important;
        border: 1px solid #ccc !important;
        padding: 2px 5px;
        line-height: 1;
        min-width: 24px;
    `;
    
    btn.addEventListener('click', async (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        let options = {};
        let prompt = textarea.value;
        let attachmentData = null;

        // Check if this is a heavy processing task that should use dedicated interface
        const heavyTasks = ['ocr', 'image-to-text', 'text-to-image', 'speech-to-text', 'text-to-speech'];
        if (heavyTasks.includes(task.task_type)) {
            const confirmed = confirm(`${task.name} is a heavy processing task that may take several minutes.\n\nFor better experience with progress tracking, we recommend using the dedicated AI Processing interface.\n\nWould you like to open the AI Processing Wizard instead?`);
            if (confirmed) {
                // Open AI Processing Wizard
                window.open('/web#action=odoo_huggingface_mcp.action_huggingface_ai_wizard', '_blank');
            }
            return;
        }
        
        // For remaining light tasks, require text prompt
        if (!textarea.value.trim()) {
            showNotification("Please enter a prompt.", true);
            return;
        }

        // Handle other special task options
        if (task.task_type === 'translation') {
            const lang = window.prompt("Enter target language code (e.g., 'fr' for French):", "fr");
            if (!lang) return;
            options.target_language = lang;
        }

        // Show different messages for different task types
        if (task.task_type === 'text-to-image') {
            showNotification(`Generating image... This may take up to 3 minutes.`);
        } else if (task.task_type === 'ocr' || task.task_type === 'image-to-text') {
            showNotification(`Processing image... This may take up to 2 minutes.`);
        } else if (task.task_type === 'translation') {
            showNotification(`Translating text... This may take up to 1.5 minutes for long texts.`);
        } else {
            showNotification(`Executing ${task.name}...`);
        }
        btn.disabled = true;
        
        try {
            // Set longer timeout for different task types
            let timeout = 60000; // Default 1 minute
            if (task.task_type === 'text-to-image') {
                timeout = 180000; // 3 minutes for image generation
            } else if (task.task_type === 'ocr' || task.task_type === 'image-to-text') {
                timeout = 120000; // 2 minutes for OCR and image-to-text
            } else if (task.task_type === 'translation') {
                timeout = 90000; // 1.5 minutes for translation (especially long texts)
            }
            
            const timeoutPromise = new Promise((_, reject) => 
                setTimeout(() => reject(new Error('Request timeout')), timeout)
            );
            
            const requestData = {
                task: task.task_type,
                prompt: prompt,
                options: options,
            };
            
            // Note: Heavy processing tasks are now handled in dedicated interface
            
            const rpcPromise = rpc('/odoo_huggingface_mcp/execute_task', requestData);
            
            const result = await Promise.race([rpcPromise, timeoutPromise]);
            
            // Debug logging
            console.log('=== TASK RESPONSE DEBUG ===');
            console.log('Full result object:', result);
            console.log('Result type:', typeof result);
            console.log('Result keys:', Object.keys(result || {}));
            console.log('Task type:', task.task_type);
            console.log('Output type:', task.output_type);
            console.log('Has success property:', 'success' in (result || {}));
            console.log('Success value:', result?.success);
            console.log('=== END DEBUG ===');

            if (result && result.success) {
                if (task.output_type === 'text') {
                    console.log('Setting textarea value to:', result.result);
                    textarea.value = result.result;
                    textarea.focus();
                    textarea.dispatchEvent(new Event('input', { bubbles: true }));
                    console.log('Textarea value set successfully');
                } else if (task.task_type === 'text-to-image') {
                    // Handle image generation result
                    if (result.image_url) {
                        // Create and display image in chatter
                        const imageHtml = `<img src="${result.image_url}" alt="Generated Image" style="max-width: 300px; border-radius: 5px; margin: 10px 0;"/>`;
                        textarea.value = `Generated image:\n${imageHtml}`;
                        textarea.focus();
                        textarea.dispatchEvent(new Event('input', { bubbles: true }));
                    } else {
                        textarea.value = result.result || 'Image generated successfully';
                        textarea.focus();
                        textarea.dispatchEvent(new Event('input', { bubbles: true }));
                    }
                } else {
                    // For other non-text results, show in notification
                    showNotification(`${task.name} complete! Check console for result.`);
                    console.log(`${task.name} result:`, result.result);
                }
                showNotification(`${task.name} complete!`);
            } else {
                console.log('Task failed or no success property');
                console.log('Error message:', result?.error || 'No error message');
                showNotification(`Error: ${result?.error || 'Unknown error'}`, true);
            }
        } catch (err) {
            console.error(`${task.name} error`, err);
            showNotification(`${task.name} service failed.`, true);
        } finally {
            btn.disabled = false;
        }
    });
    
    return btn;
}

function checkForTextareas() {
    let addedCount = 0;

    const odooSelectors = [
        '.o-mail-Composer-input',
        '.o-mail-Composer',
        '.o_chatter',
        '.o_chatter_composer',
        '.o_mail_composer',
        '.o_thread_composer',
        '.o_thread_window_composer',
        '.o_message_log',
        '.o_mail_log'
    ];

    odooSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (element.querySelector('textarea')) {
                if (!element.querySelector('.o-hf-btn-wrapper')) {
                    addHuggingFaceButtons(element);
                    addedCount++;
                }
            } else if (element.tagName.toLowerCase() === 'textarea') {
                const parent = element.parentNode;
                if (parent && !parent.querySelector('.o-hf-btn-wrapper')) {
                    if (window.getComputedStyle(parent).position !== 'relative') {
                        parent.style.position = 'relative';
                    }
                    addHuggingFaceButtons(parent);
                    addedCount++;
                }
            }
        });
    });

    const directMessageTextareas = document.querySelectorAll('textarea[name="message"], textarea[name="body"], textarea.o_wysiwyg_textarea');
    directMessageTextareas.forEach(textarea => {
        log("Found direct message textarea: " + (textarea.name || 'unnamed'));

        const parent = textarea.parentNode;
        if (parent && !parent.querySelector('.o-hf-btn-wrapper')) {
            log("Adding HF buttons to direct textarea parent: " + parent.className);
            if (window.getComputedStyle(parent).position !== 'relative') {
                parent.style.position = 'relative';
            }
            addHuggingFaceButtons(parent);
            addedCount++;
        }
    });

    if (addedCount > 0) {
        log(`Added ${addedCount} HF button sets`);
    }
}

async function initHuggingFaceButtonObserver() {
    log("Initializing Hugging Face button observer...");

    // Load active tasks first
    await loadActiveTasks();
    
    checkForTextareas();
    inspectDOM();

    const observer = new MutationObserver(() => {
        checkForTextareas();
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });

    const checkIntervals = [0, 100, 500, 1000, 2000, 3000];
    checkIntervals.forEach(interval => {
        setTimeout(checkForTextareas, interval);
    });

    setInterval(checkForTextareas, 3000);
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initHuggingFaceButtonObserver);
} else {
    initHuggingFaceButtonObserver();
}