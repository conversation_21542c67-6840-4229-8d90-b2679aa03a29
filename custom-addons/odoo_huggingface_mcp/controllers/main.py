from odoo import http, _
from odoo.http import request
from odoo.exceptions import UserError

class HuggingFaceController(http.Controller):

    @http.route('/odoo_huggingface_mcp/execute_task', type='json', auth='user')
    def execute_task(self, task, prompt, options=None, attachment=None):
        """Execute a task using the active Hugging Face configuration."""
        import logging
        _logger = logging.getLogger(__name__)
        
        try:
            _logger.info(f"Executing task: {task}, prompt: {prompt[:50]}...")
            
            hf_config = request.env['huggingface.config'].search([('is_active', '=', True)], limit=1)
            
            if not hf_config:
                _logger.error("No active HuggingFace configuration found")
                return {'success': False, 'error': 'No active Hugging Face configuration found.'}
                
            # Handle attachment for OCR tasks
            if attachment and task == 'ocr':
                result = hf_config.generate_task_with_file(
                    prompt, task, 
                    file_content=attachment.get('content'),
                    filename=attachment.get('filename'),
                    mimetype=attachment.get('mimetype'),
                    options=options
                )
            else:
                result = hf_config.generate_task(prompt, task, options=options)
            
            _logger.info(f"Task completed successfully, result length: {len(str(result))}")
            return {'success': True, 'result': result}
            
        except UserError as e:
            _logger.error(f"UserError in execute_task: {str(e)}")
            return {'success': False, 'error': str(e)}
        except Exception as e:
            _logger.error(f"Exception in execute_task: {str(e)}", exc_info=True)
            return {'success': False, 'error': str(e)}
    
    @http.route('/odoo_huggingface_mcp/get_active_tasks', type='json', auth='user')
    def get_active_tasks(self):
        """Get all active task configurations for frontend."""
        try:
            tasks = request.env['huggingface.task'].get_active_tasks()
            return {'success': True, 'tasks': tasks}
        except Exception as e:
            return {'error': str(e)}