<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- 
            This record depends on an LLM provider. 
            Ensure a provider with the XML ID 'vpcs_llm_provider.llm_provider_openai_default'
            or similar exists for this record to be created successfully.
        -->
        <record id="huggingface_config_default" model="huggingface.config">
            <field name="name">Default Hugging Face MCP Server</field>
            <field name="is_active">True</field>
            <field name="mcp_command">npx</field>
            <field name="mcp_args">-y @llmindset/mcp-hfspace</field>
            <field name="mcp_env_vars">{}</field>
            <field name="huggingface_api_key">REPLACE_WITH_YOUR_KEY</field>
            <field name="llm_provider_id" ref="vpcs_llm_provider.llm_provider_openai_default"/>
        </record>
    </data>
</odoo>