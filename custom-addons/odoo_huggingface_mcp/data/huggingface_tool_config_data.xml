<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Corrected Tool Configurations -->

        <!-- API-based Tools -->
        <record id="tool_sentiment_analysis" model="huggingface.tool.config">
            <field name="name">Sentiment Analysis (RoBERTa)</field>
            <field name="task_type">sentiment-analysis</field>
            <field name="model_name">cardiffnlp/twitter-roberta-base-sentiment</field>
            <field name="endpoint_url">https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment</field>
            <field name="use_gradio" eval="False"/>
            <field name="gradio_space_url">cardiffnlp/twitter-roberta-base-sentiment</field>
        </record>

        <record id="tool_translation" model="huggingface.tool.config">
            <field name="name">Translation (Helsinki-NLP)</field>
            <field name="task_type">translation</field>
            <field name="model_name">Helsinki-NLP/opus-mt-en-{lang}</field>
            <field name="endpoint_url">https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-{lang}</field>
            <field name="use_gradio" eval="False"/>
            <field name="gradio_space_url">Helsinki-NLP/opus-mt-en-{lang}</field>
        </record>

        <record id="tool_summarization" model="huggingface.tool.config">
            <field name="name">Summarization (BART)</field>
            <field name="task_type">summarization</field>
            <field name="model_name">facebook/bart-large-cnn</field>
            <field name="endpoint_url">https://api-inference.huggingface.co/models/facebook/bart-large-cnn</field>
            <field name="use_gradio" eval="False"/>
            <field name="gradio_space_url">facebook/bart-large-cnn</field>
        </record>

        <!-- Gradio-based Tools -->
        <record id="tool_image_generation" model="huggingface.tool.config">
            <field name="name">Image Generation (FLUX.1 Schnell)</field>
            <field name="task_type">text-to-image</field>
            <field name="use_gradio" eval="True"/>
            <field name="gradio_space_url">VPCSinfo/flux1_schnell</field>
            <field name="model_name">Gradio: FLUX.1-schnell</field>
            <field name="endpoint_url">https://huggingface.co/spaces/VPCSinfo/flux1_schnell</field>
            <field name="gradio_api_endpoint">/infer</field>
            <field name="gradio_parameters">{"seed": 42, "randomize_seed": true, "width": 1024, "height": 1024, "num_inference_steps": 4}</field>
        </record>

        <record id="tool_ocr" model="huggingface.tool.config">
            <field name="name">OCR (Nanonets)</field>
            <field name="task_type">ocr</field>
            <field name="use_gradio" eval="True"/>
            <field name="gradio_space_url">VPCSinfo/Nanonets-OCR</field>
            <field name="model_name">Gradio: Nanonets-OCR</field>
            <field name="endpoint_url">https://huggingface.co/spaces/VPCSinfo/Nanonets-OCR</field>
            <field name="gradio_parameters">{"max_new_tokens": 4096}</field>
        </record>

        <record id="tool_image_to_text" model="huggingface.tool.config">
            <field name="name">Image to Text (Nanonets OCR)</field>
            <field name="task_type">image-to-text</field>
            <field name="use_gradio" eval="True"/>
            <field name="gradio_space_url">VPCSinfo/Nanonets-OCR</field>
            <field name="model_name">Gradio: Nanonets-OCR</field>
            <field name="endpoint_url">https://huggingface.co/spaces/VPCSinfo/Nanonets-OCR</field>
            <field name="gradio_parameters">{"max_new_tokens": 4096}</field>
        </record>

        <!-- Placeholder for Chat -->
        <record id="tool_chat" model="huggingface.tool.config">
            <field name="name">Chat (To be configured by user)</field>
            <field name="task_type">chat</field>
            <field name="use_gradio" eval="False"/>
            <field name="model_name">meta-llama/Llama-3-8b-chat-hf</field>
            <field name="endpoint_url">https://api-inference.huggingface.co/models/meta-llama/Llama-3-8b-chat-hf</field>
            <field name="gradio_space_url">meta-llama/Llama-3-8b-chat-hf</field>
        </record>

    </data>
</odoo>