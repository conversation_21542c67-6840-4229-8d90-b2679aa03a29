<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Verified & Aligned Task Definitions -->

        <record id="task_sentiment_analysis" model="huggingface.task">
            <field name="name">Sentiment Analysis</field>
            <field name="sequence">10</field>
            <field name="task_type">sentiment-analysis</field>
            <field name="huggingface_space">cardiffnlp/twitter-roberta-base-sentiment</field>
            <field name="description">Analyze the sentiment of text using RoBERTa model via API.</field>
            <field name="mcp_tool_name">hf_inference_api</field>
            <field name="parameters">{"model": "cardiffnlp/twitter-roberta-base-sentiment"}</field>
            <field name="is_active">True</field>
            <field name="requires_file">False</field>
            <field name="output_type">text</field>
            <field name="icon_name">icon_sentiment.png</field>
            <field name="button_title">Analyze Sentiment</field>
        </record>

        <record id="task_translation" model="huggingface.task">
            <field name="name">Translation</field>
            <field name="sequence">15</field>
            <field name="task_type">translation</field>
            <field name="huggingface_space">Helsinki-NLP/opus-mt-en-es</field>
            <field name="description">Translate text from English to Spanish using Opus MT via API.</field>
            <field name="mcp_tool_name">hf_inference_api</field>
            <field name="parameters">{"model": "Helsinki-NLP/opus-mt-en-es"}</field>
            <field name="is_active">True</field>
            <field name="requires_file">False</field>
            <field name="output_type">text</field>
            <field name="icon_name">Translation_icon.png</field>
            <field name="button_title">Translate Text</field>
        </record>

        <record id="task_text_summarization" model="huggingface.task">
            <field name="name">Text Summarization</field>
            <field name="sequence">20</field>
            <field name="task_type">summarization</field>
            <field name="huggingface_space">facebook/bart-large-cnn</field>
            <field name="description">Summarize long text content via API.</field>
            <field name="mcp_tool_name">hf_inference_api</field>
            <field name="parameters">{"model": "facebook/bart-large-cnn", "max_length": 150, "min_length": 30}</field>
            <field name="is_active">True</field>
            <field name="requires_file">False</field>
            <field name="output_type">text</field>
            <field name="icon_name">summarize_icon.png</field>
            <field name="button_title">Summarize Text</field>
        </record>

        <record id="task_image_generation" model="huggingface.task">
            <field name="name">Image Generation</field>
            <field name="sequence">30</field>
            <field name="task_type">text-to-image</field>
            <field name="huggingface_space">prithivMLmods/FLUX-LoRA-DLC</field>
            <field name="description">Generate an image from a text prompt using a Gradio Space.</field>
            <field name="mcp_tool_name">hf_space_run</field>
            <field name="parameters">{"space": "prithivMLmods/FLUX-LoRA-DLC"}</field>
            <field name="is_active">True</field>
            <field name="requires_file">False</field>
            <field name="output_type">image</field>
            <field name="icon_name">generate_image.png</field>
            <field name="button_title">Generate Image</field>
        </record>

        <record id="task_ocr" model="huggingface.task">
            <field name="name">OCR (Image And PDF)</field>
            <field name="sequence">40</field>
            <field name="task_type">ocr</field>
            <field name="huggingface_space">VPCSinfo/Nanonets-OCR</field>
            <field name="description">Extract text from images and PDF documents using Nanonets OCR.</field>
            <field name="mcp_tool_name">hf_space_run</field>
            <field name="parameters">{"space": "VPCSinfo/Nanonets-OCR"}</field>
            <field name="is_active">True</field>
            <field name="requires_file">True</field>
            <field name="file_types">.jpg,.png,.jpeg,.bmp,.tiff,.pdf</field>
            <field name="output_type">text</field>
            <field name="icon_name">ocr_icon.png</field>
            <field name="button_title">Extract Document Text</field>
        </record>

        <record id="task_image_to_text" model="huggingface.task">
            <field name="name">Image to Text</field>
            <field name="sequence">50</field>
            <field name="task_type">image-to-text</field>
            <field name="huggingface_space">VPCSinfo/Nanonets-OCR</field>
            <field name="description">Generate a caption or description for an image.</field>
            <field name="mcp_tool_name">hf_space_run</field>
            <field name="parameters">{"space": "VPCSinfo/Nanonets-OCR"}</field>
            <field name="is_active">True</field>
            <field name="requires_file">True</field>
            <field name="file_types">.jpg,.png,.jpeg,.bmp,.tiff</field>
            <field name="output_type">text</field>
            <field name="icon_name">image_to_text_icon.png</field>
            <field name="button_title">Get Image Caption</field>
        </record>

        <record id="task_chat" model="huggingface.task">
            <field name="name">Chat with AI</field>
            <field name="sequence">60</field>
            <field name="task_type">chat</field>
            <field name="huggingface_space">meta-llama/Llama-3-8b-chat-hf</field>
            <field name="description">Chat with AI using a configured LLM provider.</field>
            <field name="mcp_tool_name">hf_inference_api</field>
            <field name="parameters">{"model": "meta-llama/Llama-3-8b-chat-hf"}</field>
            <field name="is_active">True</field>
            <field name="requires_file">False</field>
            <field name="output_type">text</field>
            <field name="icon_name">chat_icon.png</field>
            <field name="button_title">Chat with AI</field>
        </record>

    </data>
</odoo>
