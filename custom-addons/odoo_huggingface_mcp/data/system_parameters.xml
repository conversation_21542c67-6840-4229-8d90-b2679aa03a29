<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Default HuggingFace Tool Endpoints -->
        <record id="param_sentiment_analysis_endpoint" model="ir.config_parameter">
            <field name="key">huggingface.sentiment_analysis.endpoint</field>
            <field name="value">https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment</field>
        </record>

        <record id="param_translation_endpoint" model="ir.config_parameter">
            <field name="key">huggingface.translation.endpoint</field>
            <field name="value">https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-{lang}</field>
        </record>

        <record id="param_summarization_endpoint" model="ir.config_parameter">
            <field name="key">huggingface.summarization.endpoint</field>
            <field name="value">https://api-inference.huggingface.co/models/facebook/bart-large-cnn</field>
        </record>

        <record id="param_image_to_text_endpoint" model="ir.config_parameter">
            <field name="key">huggingface.image_to_text.endpoint</field>
            <field name="value">https://api-inference.huggingface.co/models/Salesforce/blip2-opt-2.7b</field>
        </record>

        <record id="param_ocr_endpoint" model="ir.config_parameter">
            <field name="key">huggingface.ocr.endpoint</field>
            <field name="value">https://api-inference.huggingface.co/models/microsoft/trocr-large-printed</field>
        </record>

        <record id="param_text_to_image_endpoint" model="ir.config_parameter">
            <field name="key">huggingface.text_to_image.endpoint</field>
            <field name="value">https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-xl-base-1.0</field>
        </record>

        <record id="param_chat_endpoint" model="ir.config_parameter">
            <field name="key">huggingface.chat.endpoint</field>
            <field name="value">https://api-inference.huggingface.co/models/meta-llama/Llama-3-8b-chat-hf</field>
        </record>
    </data>
</odoo>