import os
import requests
import base64
import json
from dotenv import load_dotenv

# --- Configuration ---
# Load environment variables from .env file
load_dotenv()
HF_API_KEY = os.getenv("HF_API_KEY")
API_URL_BASE = "https://api-inference.huggingface.co/models/"

# --- Helper Function ---
def query_api(endpoint_url, data):
    """Generic function to query the Hugging Face API for text-based models."""
    headers = {
        "Authorization": f"Bearer {HF_API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {"inputs": data}
    response = requests.post(endpoint_url, headers=headers, json=payload, timeout=60)
    return response

# --- Main Test Function ---
def main():
    """Tests all configured Hugging Face endpoints that use the direct Inference API."""
    if not HF_API_KEY:
        print("🔴 ERROR: HF_API_KEY environment variable not found.")
        print("Please create a .env file with your Hugging Face API key.")
        return

    print("--- Starting Hugging Face Inference API Endpoint Verification ---")
    print("NOTE: This script only tests direct API endpoints. Tasks using Gradio Spaces must be tested within Odoo.")

    # List of API-based endpoints to be tested
    endpoints_to_test = [
        {
            "name": "Sentiment Analysis",
            "model_id": "cardiffnlp/twitter-roberta-base-sentiment",
            "sample_input": "I love how easy it is to use Odoo!"
        },
        {
            "name": "Summarization",
            "model_id": "facebook/bart-large-cnn",
            "sample_input": "Odoo is a suite of open source business apps that cover all your company needs: CRM, eCommerce, accounting, inventory, point of sale, project management, etc. Odoo's unique value proposition is to be at the same time very easy to use and fully integrated."
        }
    ]

    all_successful = True

    for endpoint in endpoints_to_test:
        print(f"\n--- Testing: {endpoint['name']} ({endpoint['model_id']}) ---")
        endpoint_url = API_URL_BASE + endpoint['model_id']
        
        try:
            response = query_api(endpoint_url, endpoint['sample_input'])
            
            if response.status_code == 200:
                print(f"✅ SUCCESS (Status: {response.status_code})")
                response_text = json.dumps(response.json())
                print(f"   Response: {response_text[:200]}...")
            else:
                print(f"❌ FAILED (Status: {response.status_code})")
                print(f"   Error: {response.text}")
                all_successful = False

        except requests.exceptions.RequestException as e:
            print(f"❌ FAILED (Connection Error)")
            print(f"   Error: {e}")
            all_successful = False

    print("\n--- Verification Complete ---")
    if all_successful:
        print("🎉 All configured Inference API endpoints responded successfully!")
    else:
        print("⚠️ Some API endpoints failed. Please review the logs above.")

if __name__ == "__main__":
    main()
