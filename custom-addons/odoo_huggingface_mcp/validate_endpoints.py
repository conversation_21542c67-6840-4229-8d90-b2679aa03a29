#!/usr/bin/env python3
"""
HuggingFace API Endpoints Validation Script
Validates all model endpoints used in the direct inference implementation
"""

import requests
import json
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def validate_huggingface_endpoints():
    """Validate all HuggingFace model endpoints with API key authentication"""
    
    # Get API key from environment
    api_key = os.getenv('HUGGINGFACE_API_KEY')
    if not api_key or api_key == 'your_huggingface_api_key_here':
        print("⚠️  Warning: No valid HuggingFace API key found in .env file")
        print("Please update the .env file with your actual API key")
        print("Get your key from: https://huggingface.co/settings/tokens\n")
    
    endpoints = {
        'sentiment-analysis': 'https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment',
        'translation': 'https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-fr',  # Test with French
        'summarization': 'https://api-inference.huggingface.co/models/facebook/bart-large-cnn',
        'image-to-text': 'https://api-inference.huggingface.co/models/nlpconnect/vit-gpt2-image-captioning',
        'ocr': 'https://api-inference.huggingface.co/models/microsoft/trocr-base-printed',
        'text-to-image': 'https://api-inference.huggingface.co/models/runwayml/stable-diffusion-v1-5',
        'chat': 'https://api-inference.huggingface.co/models/facebook/blenderbot-400M-distill',
    }
    
    # Test data for each endpoint type
    test_data = {
        'sentiment-analysis': {'inputs': 'I love this product!'},
        'translation': {'inputs': 'Hello, how are you?'},
        'summarization': {'inputs': 'The quick brown fox jumps over the lazy dog. This is a test sentence for summarization.'},
        'image-to-text': None,  # Skip image tests for now
        'ocr': None,  # Skip image tests for now
        'text-to-image': {'inputs': 'A beautiful sunset over mountains'},
        'chat': {'inputs': 'Hello, how are you today?'},
    }
    
    print("🔍 Validating HuggingFace API Endpoints")
    print("=" * 50)
    
    valid_endpoints = []
    invalid_endpoints = []
    
    for task_type, url in endpoints.items():
        try:
            # Prepare headers with API key if available
            headers = {}
            if api_key and api_key != 'your_huggingface_api_key_here':
                headers['Authorization'] = f'Bearer {api_key}'
            
            # First test: Check if endpoint exists
            response = requests.get(url, timeout=10)
            
            if response.status_code == 404:
                status = "❌ INVALID (model not found)"
                invalid_endpoints.append(task_type)
            else:
                # Second test: Try actual inference if we have API key and test data
                if api_key and api_key != 'your_huggingface_api_key_here' and test_data.get(task_type):
                    try:
                        headers['Content-Type'] = 'application/json'
                        inference_response = requests.post(
                            url, 
                            json=test_data[task_type], 
                            headers=headers, 
                            timeout=30
                        )
                        
                        if inference_response.status_code == 200:
                            status = "✅ VALID (inference successful)"
                            valid_endpoints.append(task_type)
                        elif inference_response.status_code == 503:
                            status = "⏳ VALID (model loading, try again later)"
                            valid_endpoints.append(task_type)
                        else:
                            status = f"⚠️  PARTIAL (endpoint exists, inference failed: {inference_response.status_code})"
                            
                    except requests.exceptions.RequestException:
                        status = "⚠️  PARTIAL (endpoint exists, inference test failed)"
                else:
                    # No API key or test data, just check if endpoint responds
                    if response.status_code == 401:
                        status = "✅ VALID (requires auth)"
                        valid_endpoints.append(task_type)
                    elif response.status_code == 200:
                        status = "✅ VALID (accessible)"
                        valid_endpoints.append(task_type)
                    else:
                        status = f"⚠️  UNKNOWN (status: {response.status_code})"
                
        except requests.exceptions.RequestException as e:
            status = f"❌ ERROR ({str(e)})"
            invalid_endpoints.append(task_type)
        
        print(f"{task_type:20} | {status}")
        print(f"{'':20} | {url}")
        print("-" * 80)
    
    print(f"\n📊 Summary:")
    print(f"Valid endpoints: {len(valid_endpoints)}")
    print(f"Invalid endpoints: {len(invalid_endpoints)}")
    
    if invalid_endpoints:
        print(f"\n❌ Issues found with: {', '.join(invalid_endpoints)}")
        return False
    else:
        print(f"\n✅ All endpoints validated successfully!")
        return True

def check_model_alternatives():
    """Check alternative models for any invalid endpoints"""
    
    alternatives = {
        'sentiment-analysis': [
            'cardiffnlp/twitter-roberta-base-sentiment-latest',
            'cardiffnlp/twitter-roberta-base-sentiment',
            'distilbert-base-uncased-finetuned-sst-2-english'
        ],
        'translation': [
            'Helsinki-NLP/opus-mt-en-fr',
            'Helsinki-NLP/opus-mt-en-es',
            't5-base'
        ],
        'summarization': [
            'facebook/bart-large-cnn',
            'sshleifer/distilbart-cnn-12-6',
            't5-base'
        ],
        'image-to-text': [
            'nlpconnect/vit-gpt2-image-captioning',
            'Salesforce/blip-image-captioning-base',
            'microsoft/git-base'
        ],
        'ocr': [
            'microsoft/trocr-base-printed',
            'microsoft/trocr-base-handwritten',
            'PaddlePaddle/PaddleOCR'
        ],
        'text-to-image': [
            'stabilityai/stable-diffusion-2',
            'stabilityai/stable-diffusion-2-1',
            'runwayml/stable-diffusion-v1-5'
        ],
        'chat': [
            'microsoft/DialoGPT-medium',
            'microsoft/DialoGPT-large',
            'facebook/blenderbot-400M-distill'
        ]
    }
    
    print("\n🔄 Checking Model Alternatives")
    print("=" * 50)
    
    for task_type, models in alternatives.items():
        print(f"\n{task_type.upper()}:")
        for model in models:
            url = f"https://api-inference.huggingface.co/models/{model}"
            try:
                response = requests.get(url, timeout=5)
                if response.status_code in [200, 401]:
                    print(f"  ✅ {model}")
                else:
                    print(f"  ❌ {model} (status: {response.status_code})")
            except:
                print(f"  ❌ {model} (connection error)")

def install_requirements():
    """Install required packages if not available"""
    try:
        import dotenv
    except ImportError:
        print("Installing python-dotenv...")
        os.system("pip install python-dotenv")
        print("✅ python-dotenv installed")

def main():
    """Main validation function"""
    print("🚀 HuggingFace Direct Inference Validation")
    print("=" * 60)
    
    # Install requirements if needed
    install_requirements()
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("Please create a .env file with your HuggingFace API key")
        return
    
    # Validate current endpoints
    endpoints_valid = validate_huggingface_endpoints()
    
    # Check alternatives if needed
    if not endpoints_valid:
        check_model_alternatives()
    
    print("\n" + "=" * 60)
    print("✅ Validation Complete!")
    
    api_key = os.getenv('HUGGINGFACE_API_KEY')
    if endpoints_valid:
        print("\n🎯 Ready for implementation!")
        if api_key and api_key != 'your_huggingface_api_key_here':
            print("\n✅ API key configured and endpoints tested")
        print("\nNext steps:")
        print("1. Update Odoo configuration with HuggingFace API key")
        print("2. Test direct inference in Odoo environment")
        print("3. Verify fallback chain works correctly")
    else:
        print("\n⚠️  Some endpoints need attention")
        print("Consider using alternative models listed above")
        
    if not api_key or api_key == 'your_huggingface_api_key_here':
        print("\n⚠️  Don't forget to update your .env file with a valid API key!")

if __name__ == "__main__":
    main()