# HuggingFace Endpoints Validation

This directory contains validation and testing tools for HuggingFace API endpoints used in the Odoo HuggingFace MCP module.

## Files

- **`.env`** - Environment configuration file for API keys
- **`validate_endpoints.py`** - Comprehensive endpoint validation script
- **`test_endpoints.py`** - Simple test script for working endpoints
- **`requirements.txt`** - Python dependencies

## Setup

1. **Install Dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Configure API Key**
   - Get your HuggingFace API key from: https://huggingface.co/settings/tokens
   - Update the `.env` file with your actual API key:
     ```
     HUGGINGFACE_API_KEY=your_actual_api_key_here
     ```

3. **Run Validation**
   ```bash
   python validate_endpoints.py
   ```

4. **Test Endpoints**
   ```bash
   python test_endpoints.py
   ```

## Validated Endpoints

### ✅ Working Endpoints
- **Sentiment Analysis**: `cardiffnlp/twitter-roberta-base-sentiment`
- **Translation**: `Helsinki-NLP/opus-mt-en-fr` (and other language pairs)
- **Summarization**: `facebook/bart-large-cnn`
- **Image-to-Text**: `nlpconnect/vit-gpt2-image-captioning`
- **OCR**: `microsoft/trocr-base-printed`

### ⚠️ Partially Working Endpoints
- **Text-to-Image**: `runwayml/stable-diffusion-v1-5` (endpoint exists, may need different parameters)
- **Chat**: `facebook/blenderbot-400M-distill` (endpoint exists, may need different parameters)

## Usage in Odoo

The validated endpoints are automatically used by the Odoo HuggingFace MCP module through the 3-tier fallback system:

1. **Direct Inference** (Primary) - Uses HuggingFace Inference API directly
2. **MCP Server** (Fallback) - Uses MCP server tools
3. **LLM Provider** (Final Fallback) - Uses configured LLM provider

## Configuration

Update the `HUGGINGFACE_ENDPOINTS` dictionary in `models/huggingface_config.py` to use different models:

```python
HUGGINGFACE_ENDPOINTS = {
    'sentiment-analysis': 'https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment',
    'translation': 'https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-{lang}',
    'summarization': 'https://api-inference.huggingface.co/models/facebook/bart-large-cnn',
    # ... other endpoints
}
```

## Troubleshooting

- **503 Service Unavailable**: Model is loading, try again in a few minutes
- **401 Unauthorized**: Check your API key configuration
- **404 Not Found**: Model doesn't exist or has been moved
- **Rate Limiting**: You may be hitting API rate limits

## Security

- Never commit your actual API key to version control
- Keep the `.env` file secure and add it to `.gitignore`
- Use environment variables in production deployments