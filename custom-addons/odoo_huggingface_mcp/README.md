# Odoo HuggingFace MCP Integration 🤗

A comprehensive AI integration module for Odoo 18 that connects to HuggingFace Spaces through the Model Context Protocol (MCP), providing advanced AI capabilities directly within the Odoo interface.

## 🚀 Features

### Core AI Capabilities
- **🎭 Sentiment Analysis**: Real-time emotion and tone analysis using RoBERTa models
- **🌍 Translation**: Multi-language translation with Helsinki-NLP models
- **🎨 Image Generation**: High-quality image creation using FLUX.1-schnell
- **👁️ OCR/Vision**: Text extraction from images and PDFs using Nanonets OCR
- **📝 Text Summarization**: Intelligent content summarization with BART models
- **💬 Chat/Conversation**: Advanced AI chat using Qwen 2.5 72B model
- **🔊 Text-to-Speech**: Audio generation using Parler TTS
- **🎤 Speech-to-Text**: Audio transcription using Whisper models

### Integration Features
- **📱 Chatter Integration**: AI buttons in all message composers
- **📎 Automatic File Detection**: OCR processes attached images automatically
- **🔄 Dual-Mode Operation**: MCP server + LLM fallback for reliability
- **🐳 Docker Ready**: Full containerization with automated setup
- **🔐 Secure Configuration**: Environment-based API key management
- **📊 Result Tracking**: Complete audit trail with processing times

## 🛠️ Installation

### Quick Start with Docker
```bash
# Clone and navigate to project
cd Odoo18Deployment

# Set your API keys in .env file
echo "HF_API_KEY=your_huggingface_token" >> .env

# Build and start
docker-compose build
docker-compose up -d
```

### Manual Installation
```bash
# Install Python dependencies
pip install pydantic-ai mcp asyncio

# Install Node.js MCP server
npm install -g @llmindset/mcp-hfspace

# Copy module to Odoo addons
cp -r odoo_huggingface_mcp /path/to/odoo/addons/

# Restart Odoo and install module
```

## ⚙️ Configuration

### 1. LLM Provider Setup
Navigate to **Settings → Technical → LLM Providers**
- **OpenAI**: API key format `sk-proj-...`
- **Google**: API key format `AIza...`
- **Anthropic**: API key format `sk-ant-...`
- **Groq**: API key format `gsk_...`

### 2. HuggingFace Configuration
Go to **Settings → HuggingFace MCP**
- **Name**: Configuration identifier
- **HuggingFace API Key**: Your HF token (`hf_...`)
- **Agent LLM Provider**: Select configured provider
- **MCP Command**: `npx` (auto-configured)
- **MCP Args**: `-y @llmindset/mcp-hfspace` (auto-configured)
- **Active**: ✅ Enable

### 3. Test Connection
Click **"Check Connection"** button:
- ✅ **Success**: Shows number of available MCP tools
- ⚠️ **Fallback**: LLM provider works, MCP server unavailable
- ❌ **Error**: Check API keys and configuration

## 🎯 Usage

### Chatter Integration
AI buttons appear automatically in all message composers:

#### Sentiment Analysis
- Type your text in the message field
- Click the **sentiment analysis** button
- Get instant emotion and confidence scores

#### Translation
- Enter text to translate
- Click **translation** button
- Select target language (French, Spanish, German, Hindi, Chinese)
- Receive translated text

#### OCR (Image and PDF Text Extraction)
- Attach an image file (jpg, png, gif, bmp, tiff) or PDF document
- Click **OCR** button (no text input needed)
- Extracted text appears in the message field
- Supports complex documents with tables, equations, and images

#### Image Generation
- Describe the image you want
- Click **image generation** button
- High-quality image created and saved

### Advanced Features

#### Chat with AI
- Use the **chat** button for conversations
- Powered by Qwen 2.5 72B model
- Context-aware responses

#### Audio Processing
- **Text-to-Speech**: Convert text to natural speech
- **Speech-to-Text**: Transcribe audio files to text

## 🏗️ Architecture

### MCP Server Integration
```
Odoo ↔ Pydantic AI Agent ↔ MCP Client ↔ @llmindset/mcp-hfspace ↔ HuggingFace Spaces
```

### Fallback Mechanism
```
MCP Available: Direct HuggingFace Space execution
MCP Unavailable: LLM provider direct processing
```

### File Processing
```
Chatter Attachment → Base64 Encoding → /tmp/mcp-files → MCP Processing → Result Storage
```

## 🔧 Technical Details

### Supported HuggingFace Spaces
- **Image Generation**: `black-forest-labs/FLUX.1-schnell`
- **OCR/Vision**: `VPCSinfo/Nanonets-OCR` (Images and PDFs)
- **Chat**: `Qwen/Qwen2.5-72B-Instruct`
- **Translation**: `Helsinki-NLP/opus-mt-*`
- **Sentiment**: `cardiffnlp/twitter-roberta-base-sentiment-latest`
- **TTS**: `parler-tts/parler_tts`
- **STT**: `hf-audio/whisper-large-v3-turbo`
- **Summarization**: `facebook/bart-large-cnn`

### API Endpoints
```python
POST /odoo_huggingface_mcp/execute_task
{
    "task": "sentiment-analysis|translation|ocr|chat|...",
    "prompt": "Your input text",
    "options": {"target_language": "fr"},  # Optional
    "attachment": {  # For file-based tasks
        "filename": "image.jpg",
        "content": "base64_encoded_data",
        "mimetype": "image/jpeg"
    }
}
```

### Database Models
- **`huggingface.config`**: MCP server configuration
- **`huggingface.task`**: Task type definitions and mappings
- **`huggingface.result`**: Result storage with audit trail

## 🐳 Docker Environment

### Container Features
- **Automated MCP Server**: Auto-installs and starts `@llmindset/mcp-hfspace`
- **Working Directory**: `/tmp/mcp-files` for file processing
- **Environment Variables**: Secure API key management
- **Health Checks**: MCP server startup validation
- **Port Configuration**: 8069 (Odoo), 8072 (Longpolling), 8080 (MCP)

### Environment Variables
```bash
# Required
HF_API_KEY=hf_your_token_here
OPENAI_API_KEY=sk-proj-your_key_here  # If using OpenAI
GOOGLE_API_KEY=AIza_your_key_here     # If using Google

# Optional
MCP_HF_WORK_DIR=/tmp/mcp-files
CLAUDE_DESKTOP_MODE=true
```

## 🔍 Troubleshooting

### Common Issues

#### MCP Server Connection Failed
```bash
# Check MCP server installation
docker exec odoo18-stack npm list -g @llmindset/mcp-hfspace

# Check logs
docker logs odoo18-stack | grep MCP
```

#### OCR Not Working
- Ensure image or PDF is attached before clicking OCR button
- Supported formats: jpg, png, gif, bmp, tiff, pdf
- Check file size (< 10MB recommended)
- For PDFs, ensure they contain text or images (not just scanned pages)
- Try using handle_file() method for proper Gradio serialization

#### Translation Errors
- Verify target language code (fr, es, de, hi, zh)
- Check HuggingFace API quota
- Try shorter text inputs

#### Google Model Errors
- Ensure GOOGLE_API_KEY is set correctly
- Check Google AI Studio quota
- Verify model permissions

### Debug Mode
Enable detailed logging:
```javascript
// In chatter_integration.js
const DEBUG = true;
```

### Log Analysis
```bash
# Odoo logs
docker logs odoo18-stack | grep "HF Integration"

# MCP server logs
docker logs odoo18-stack | grep "mcp-hfspace"
```

## 📊 Performance

### Typical Response Times
- **Sentiment Analysis**: 1-3 seconds
- **Translation**: 2-5 seconds
- **OCR**: 3-8 seconds
- **Image Generation**: 10-30 seconds
- **Chat**: 2-10 seconds

### Resource Usage
- **Memory**: ~500MB additional for MCP server
- **CPU**: Minimal (processing done on HuggingFace)
- **Storage**: Results cached in PostgreSQL
- **Network**: API calls to HuggingFace Spaces

## 🔐 Security

### API Key Management
- Keys stored in environment variables
- No hardcoded credentials
- Secure transmission to MCP server subprocess

### File Security
- Temporary files in `/tmp/mcp-files`
- Automatic cleanup after processing
- Base64 encoding for transmission

### Access Control
- Odoo user authentication required
- Company-level configuration isolation
- Activity logging for audit trails

## 🚀 Production Deployment

### Recommended Setup
```yaml
# docker-compose.yml
services:
  odoo18-stack:
    environment:
      - HF_API_KEY=${HF_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
    volumes:
      - ./mcp-files:/tmp/mcp-files
    deploy:
      resources:
        limits:
          memory: 4G
        reservations:
          memory: 2G
```

### Monitoring
- Check MCP server health: `curl localhost:8080/health`
- Monitor Odoo logs for AI task execution
- Track API usage through HuggingFace dashboard

## 📈 Roadmap

### Planned Features
- **Batch Processing**: Multiple files/texts at once
- **Custom Spaces**: User-defined HuggingFace Space integration
- **Workflow Integration**: AI tasks in Odoo workflows
- **Advanced Analytics**: Usage statistics and performance metrics

## 🤝 Contributing

### Development Setup
```bash
# Clone repository
git clone <repository_url>

# Install development dependencies
pip install -r requirements-dev.txt

# Run tests
python -m pytest tests/
```

### Adding New Task Types
1. Update `huggingface_task.py` selection field
2. Add task configuration in `huggingface_task_data.xml`
3. Implement prompt handling in `_build_task_prompt`
4. Add frontend button configuration
5. Test with MCP server and fallback mode

## 📄 License

This module is licensed under LGPL-3.0, same as Odoo.

## 🆘 Support

- **Documentation**: Check this README and inline code comments
- **Issues**: Create GitHub issues for bugs and feature requests
- **Community**: Join Odoo community forums for discussions

---

**🎉 Ready for Production!** This module provides enterprise-grade AI integration for Odoo 18 with comprehensive error handling, fallback mechanisms, and Docker containerization.