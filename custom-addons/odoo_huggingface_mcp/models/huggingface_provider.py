from odoo import models, api, _
from odoo.exceptions import UserError

class HuggingFaceProvider(models.AbstractModel):
    _name = 'huggingface.provider'
    _description = 'Hugging Face Provider Bridge'

    def _get_active_config(self):
        """Get the active Hugging Face configuration."""
        config = self.env['huggingface.config'].search([
            ('is_active', '=', True)
        ], limit=1)
        if not config:
            raise UserError(_("No active Hugging Face configuration found."))
        return config

    def generate_task(self, prompt, task, model=None, options=None):
        """Generate a task using the active Hugging Face configuration."""
        config = self._get_active_config()
        return config.generate_task(prompt, task, model=model, options=options)