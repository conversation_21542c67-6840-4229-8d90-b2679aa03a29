from odoo import models, fields, api
from datetime import datetime, timedelta

class HuggingFaceAIDashboard(models.TransientModel):
    _name = 'huggingface.ai.dashboard'
    _description = 'AI Processing Dashboard'

    # Statistics
    total_jobs = fields.Integer(compute="_compute_stats", string="Total Jobs")
    draft_jobs = fields.Integer(compute="_compute_stats", string="Draft Jobs")
    queued_jobs = fields.Integer(compute="_compute_stats", string="Queued Jobs")
    processing_jobs = fields.Integer(compute="_compute_stats", string="Processing Jobs")
    completed_jobs = fields.Integer(compute="_compute_stats", string="Completed Jobs")
    failed_jobs = fields.Integer(compute="_compute_stats", string="Failed Jobs")
    
    # Today's statistics
    today_jobs = fields.Integer(compute="_compute_stats", string="Today's Jobs")
    today_completed = fields.Integer(compute="_compute_stats", string="Today Completed")
    today_failed = fields.Integer(compute="_compute_stats", string="Today Failed")
    
    # Recent jobs
    recent_jobs = fields.One2many('huggingface.ai.processor', compute="_compute_recent_jobs", string="Recent Jobs")
    
    @api.depends()
    def _compute_stats(self):
        for record in self:
            processor_model = self.env['huggingface.ai.processor']
            
            # Total counts by status
            record.total_jobs = processor_model.search_count([])
            record.draft_jobs = processor_model.search_count([('status', '=', 'draft')])
            record.queued_jobs = processor_model.search_count([('status', '=', 'queued')])
            record.processing_jobs = processor_model.search_count([('status', '=', 'processing')])
            record.completed_jobs = processor_model.search_count([('status', '=', 'completed')])
            record.failed_jobs = processor_model.search_count([('status', '=', 'failed')])
            
            # Today's statistics
            today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
            today_domain = [('create_date', '>=', today_start)]
            
            record.today_jobs = processor_model.search_count(today_domain)
            record.today_completed = processor_model.search_count(today_domain + [('status', '=', 'completed')])
            record.today_failed = processor_model.search_count(today_domain + [('status', '=', 'failed')])
    
    @api.depends()
    def _compute_recent_jobs(self):
        for record in self:
            # Get last 10 jobs
            recent = self.env['huggingface.ai.processor'].search([], limit=10)
            record.recent_jobs = recent
    
    def action_view_all_jobs(self):
        """Open all processing jobs"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'All Processing Jobs',
            'res_model': 'huggingface.ai.processor',
            'view_mode': 'kanban,list,form',
            'target': 'current',
        }
    
    def action_view_processing_jobs(self):
        """Open active processing jobs"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Active Processing Jobs',
            'res_model': 'huggingface.ai.processor',
            'view_mode': 'kanban,list,form',
            'domain': [('status', 'in', ['queued', 'processing'])],
            'target': 'current',
        }
    
    def action_view_failed_jobs(self):
        """Open failed jobs"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'Failed Jobs',
            'res_model': 'huggingface.ai.processor',
            'view_mode': 'list,form',
            'domain': [('status', '=', 'failed')],
            'target': 'current',
        }
    
    def action_new_job_wizard(self):
        """Open new job wizard"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'New AI Processing Job',
            'res_model': 'huggingface.ai.wizard',
            'view_mode': 'form',
            'target': 'new',
        }

    @api.model
    def open_dashboard(self):
        """Finds an existing dashboard record or creates a new one, then opens its form view."""
        dashboard = self.search([], limit=1, order='id DESC')
        if not dashboard:
            dashboard = self.create({})
        return {
            'type': 'ir.actions.act_window',
            'name': 'AI Processing Dashboard',
            'res_model': self._name,
            'res_id': dashboard.id,
            'view_mode': 'form',
            'target': 'current',
        }