from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
import json
import requests

class HuggingFaceToolConfig(models.Model):
    _name = 'huggingface.tool.config'
    _description = 'HuggingFace Tool Configuration'
    _order = 'task_type, name'

    name = fields.Char('Tool Name', required=True)
    task_type = fields.Selection([
        ('sentiment-analysis', 'Sentiment Analysis'),
        ('translation', 'Translation'),
        ('summarization', 'Summarization'),
        ('image-to-text', 'Image to Text'),
        ('ocr', 'OCR'),
        ('text-to-image', 'Text to Image'),
        ('chat', 'Chat'),
    ], string='Task Type', required=True)
    endpoint_url = fields.Char('API Endpoint', required=True)
    model_name = fields.Char('Model Name', required=True)
    is_active = fields.Boolean('Active', default=True)
    parameters = fields.Text('Default Parameters (JSON)', default='{}')
    
    # Gradio Integration Fields
    use_gradio = fields.Boolean('Use Gradio Space', default=False, help="Use Gradio space as fallback")
    gradio_space_url = fields.Char('Gradio Space URL', help="HuggingFace Space URL (e.g., prithivMLmods/Multimodal-OCR2)")
    gradio_api_endpoint = fields.Char('Gradio API Endpoint', default='/generate_image', help="API endpoint within the Gradio space")
    gradio_parameters = fields.Text('Gradio Parameters (JSON)', default='{}', help="JSON parameters for Gradio API call")
    
    company_id = fields.Many2one('res.company', string='Company', default=lambda self: self.env.company)

    @api.constrains('endpoint_url')
    def _check_endpoint_url(self):
        for record in self:
            if not record.endpoint_url.startswith(('http://', 'https://')):
                raise ValidationError(_("Endpoint URL must start with http:// or https://"))

    @api.constrains('parameters', 'gradio_parameters')
    def _check_parameters_json(self):
        for record in self:
            if record.parameters:
                try:
                    json.loads(record.parameters)
                except json.JSONDecodeError:
                    raise ValidationError(_("Parameters must be valid JSON"))
            if record.gradio_parameters:
                try:
                    json.loads(record.gradio_parameters)
                except json.JSONDecodeError:
                    raise ValidationError(_("Gradio Parameters must be valid JSON"))

    def get_parameters_dict(self):
        """Get parameters as dictionary"""
        try:
            return json.loads(self.parameters or '{}')
        except json.JSONDecodeError:
            return {}
    
    def get_gradio_parameters_dict(self):
        """Get Gradio parameters as dictionary"""
        try:
            return json.loads(self.gradio_parameters or '{}')
        except json.JSONDecodeError:
            return {}

    def test_endpoint(self):
        """Test if endpoint is accessible"""
        try:
            response = requests.get(self.endpoint_url, timeout=5)
            if response.status_code in [200, 401]:  # 401 means endpoint exists but needs auth
                return True
            return False
        except:
            return False