from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import threading
import time
import base64
import os

_logger = logging.getLogger(__name__)

class HuggingFaceAIProcessor(models.Model):
    _name = 'huggingface.ai.processor'
    _description = 'AI Processing Job'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'
    _rec_name = 'name'

    name = fields.Char(required=True, default="AI Processing Job")
    task_type = fields.Selection([
        ('ocr', 'OCR - Extract Text'),
        ('image-to-text', 'Image Description'),
        ('text-to-image', 'Image Generation'),
        ('speech-to-text', 'Audio Transcription'),
        ('text-to-speech', 'Speech Generation'),
    ], required=True, string="Task Type")
    
    status = fields.Selection([
        ('draft', 'Draft'),
        ('queued', 'Queued'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ], default='draft', string="Status", tracking=True)
    
    priority = fields.Selection([
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ], default='normal', string="Priority")
    
    # Input/Output
    input_text = fields.Text(string="Input Text")
    input_files = fields.Many2many('ir.attachment', 'processor_input_rel', 
                                   string="Input Files")
    result_text = fields.Text("Result Text", readonly=True, copy=False)
    result_image = fields.Binary("Result Image", readonly=True, copy=False)
    output_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
    ], string="Output Type", compute='_compute_output_type', store=True, readonly=True)
    result_files = fields.Many2many('ir.attachment', 'processor_result_rel',
                                    string="Result Files", readonly=True)
    error_message = fields.Text(string="Error Message", readonly=True)
    
    # Progress Tracking
    progress_percent = fields.Float(string="Progress %", default=0.0, readonly=True)
    estimated_time = fields.Integer(string="Estimated Time (seconds)", readonly=True)
    processing_time = fields.Float(string="Processing Time (seconds)", readonly=True)
    
    # Metadata
    user_id = fields.Many2one('res.users', string="User", 
                              default=lambda self: self.env.user, readonly=True, tracking=True)
    company_id = fields.Many2one('res.company', string="Company",
                                 default=lambda self: self.env.company, readonly=True)
    start_date = fields.Datetime(string="Start Date", readonly=True)
    complete_date = fields.Datetime(string="Complete Date", readonly=True)
    
    # Computed fields
    duration = fields.Char(string="Duration", compute="_compute_duration", store=False)
    can_cancel = fields.Boolean(compute="_compute_can_cancel", store=False)
    
    @api.depends('start_date', 'complete_date')
    def _compute_duration(self):
        for record in self:
            if record.start_date and record.complete_date:
                delta = record.complete_date - record.start_date
                record.duration = f"{delta.total_seconds():.1f}s"
            elif record.start_date:
                delta = fields.Datetime.now() - record.start_date
                record.duration = f"{delta.total_seconds():.1f}s (ongoing)"
            else:
                record.duration = ""
    
    @api.depends('status')
    def _compute_can_cancel(self):
        for record in self:
            record.can_cancel = record.status in ['draft', 'queued', 'processing']
    
    def action_submit(self):
        """Submit the job for processing"""
        self.ensure_one()
        if not self.input_text and not self.input_files:
            raise UserError(_("Please provide input text or files."))
        
        self.status = 'queued'
        self.name = f"{dict(self._fields['task_type'].selection)[self.task_type]} - {self.create_date.strftime('%Y-%m-%d %H:%M')}"
        
        # Log activity
        self.message_post(body=_("Processing job submitted to queue."))
        
        # Schedule the background processing to start after the transaction is committed
        self.env.cr.precommit.add(self._process_async)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }
    
    def action_cancel(self):
        """Cancel the processing job"""
        self.ensure_one()
        if self.can_cancel:
            self.status = 'cancelled'
            self.complete_date = fields.Datetime.now()
            self.message_post(body=_("Processing job cancelled by user."))
    
    @api.depends('task_type')
    def _compute_output_type(self):
        for job in self:
            if job.task_type:
                # Find the corresponding task configuration to determine the output type
                task = self.env['huggingface.task'].search([('task_type', '=', job.task_type)], limit=1)
                if task:
                    job.output_type = task.output_type
                else:
                    job.output_type = 'text'  # Default to text if no specific task is found
            else:
                job.output_type = 'text'
    
    def action_retry(self):
        """Retry failed job"""
        self.ensure_one()
        if self.status == 'failed':
            self.status = 'queued'
            self.error_message = False
            self.progress_percent = 0.0
            self.start_date = False
            self.complete_date = False
            self.message_post(body=_("Processing job restarted."))
            self._process_async()
    
    def _process_async(self):
        """Process the AI task asynchronously in a new thread."""
        self.ensure_one()

        def process_job_thread(job_id, db_name):
            _logger.info(f"Starting new thread for job {job_id} on db {db_name}")
            registry = self.pool
            with registry.cursor() as cr:
                env = api.Environment(cr, self.env.uid, self.env.context)
                job = env['huggingface.ai.processor'].browse(job_id)
                try:
                    job._execute_processing()
                except Exception as e:
                    _logger.error(f"Error in processing thread for job {job_id}: {e}", exc_info=True)
                    job.write({
                        'status': 'failed',
                        'error_message': str(e)
                    })
                finally:
                    cr.commit()
            _logger.info(f"Thread for job {job_id} finished.")

        # Run in a separate thread
        thread = threading.Thread(target=process_job_thread, args=(self.id, self.env.cr.dbname))
        thread.start()
    
    def _execute_processing(self):
        """Execute the actual processing using the centralized task generation methods."""
        try:
            self.status = 'processing'
            self.start_date = fields.Datetime.now()
            self.progress_percent = 10.0
            self.env.cr.commit()

            config = self.env['huggingface.config'].search([('is_active', '=', True)], limit=1)
            if not config:
                raise UserError(_("No active HuggingFace configuration found."))

            self.progress_percent = 30.0
            self.env.cr.commit()

            result = None
            if self.task_type in ['ocr', 'image-to-text', 'speech-to-text']:
                if not self.input_files:
                    raise UserError(_("An input file is required for this task."))
                
                attachment = self.input_files[0]
                file_content = attachment.datas
                
                result = config.generate_task_with_file(
                    prompt=self.input_text or f"Process the file for {self.task_type}",
                    task_type=self.task_type,
                    file_content=file_content,
                    filename=attachment.name,
                    mimetype=attachment.mimetype,
                    store_result=False
                )
                self.result_text = str(result)

            elif self.task_type in ['text-to-image', 'text-to-speech']:
                if not self.input_text:
                    raise UserError(_("Input text is required for this task."))
                
                result = config.generate_task(
                    prompt=self.input_text,
                    task_type=self.task_type,
                    store_result=False
                )
                
                # Handle image or audio result
                image_data = None
                if isinstance(result, str) and os.path.exists(result):
                    with open(result, 'rb') as f:
                        image_data = f.read()
                elif isinstance(result, bytes):
                    image_data = result

                if image_data:
                    file_extension = 'png' if self.task_type == 'text-to-image' else 'mp3'
                    self.result_image = base64.b64encode(image_data)
                    self.result_text = False
                    self.env['ir.attachment'].create({
                        'name': f"{self.name}_result.{file_extension}",
                        'datas': self.result_image,
                        'res_model': self._name,
                        'res_id': self.id
                    })
                else:
                    self.result_text = str(result)
            
            else:
                raise UserError(_(f"Task type '{self.task_type}' is not supported for automated processing."))

            self.progress_percent = 100.0
            self.status = 'completed'
            self.complete_date = fields.Datetime.now()
            
            if self.start_date:
                delta = self.complete_date - self.start_date
                self.processing_time = delta.total_seconds()
            
            self.message_post(body=_("Processing completed successfully in %.1f seconds.") % self.processing_time)
            self.env.cr.commit()
            
        except Exception as e:
            _logger.error(f"AI processing error: {str(e)}", exc_info=True)
            self.error_message = str(e)
            self.status = 'failed'
            self.complete_date = fields.Datetime.now()
            
            # Log error
            self.message_post(body=_("Processing failed: %s") % str(e))
            
            self.env.cr.commit()
    
    @api.model
    def get_processing_stats(self):
        """Get processing statistics for dashboard"""
        stats = {}
        
        # Count by status
        for status in ['draft', 'queued', 'processing', 'completed', 'failed']:
            count = self.search_count([('status', '=', status)])
            stats[status] = count
        
        # Recent jobs
        recent_jobs = self.search([('create_date', '>=', fields.Datetime.now().replace(hour=0, minute=0, second=0))])
        stats['today_jobs'] = len(recent_jobs)
        
        return stats