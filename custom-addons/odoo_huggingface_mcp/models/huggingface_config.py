from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import asyncio
from .mcp_client import MC<PERSON>lient, MCPServer
from pydantic_ai import Agent
import os
import json
from PIL import Image # Added for Gradio image handling

_logger = logging.getLogger(__name__)

class HuggingFaceConfig(models.Model):
    _name = 'huggingface.config'
    _inherit = ['mail.thread', 'mail.activity.mixin', 'mcp.server.config']
    _description = 'Hugging Face Configuration'

    name = fields.Char('Name', required=True, tracking=True)
    is_active = fields.Boolean('Active', default=False, tracking=True)
    state = fields.Selection([
        ('draft', 'Not Configured'),
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected')
    ], string='Status', default='draft', readonly=True, tracking=True)
    company_id = fields.Many2one('res.company', string='Company',
                                 default=lambda self: self.env.company)
    huggingface_api_key = fields.Char("Hugging Face API Key")
    llm_provider_id = fields.Many2one(
        'llm.provider',
        string='Agent LLM Provider',
        required=True,
        tracking=True,
        help="The LLM provider that will power the agent to interpret and use the MCP tools."
    )
    
    # Agent Configuration for HuggingFace Models
    use_alternative_agent = fields.Boolean(
        'Use Alternative Agent',
        default=False,
        tracking=True,
        help="Use a different LLM provider as agent when primary provider is HuggingFace"
    )
    alternative_agent_provider_id = fields.Many2one(
        'llm.provider',
        string='Alternative Agent Provider',
        domain=[('provider_type', 'in', ['openai', 'anthropic', 'google', 'groq'])],
        tracking=True,
        help="Alternative LLM provider to use as agent (recommended for HuggingFace models)"
    )
    
    # Direct Inference Configuration
    use_direct_inference = fields.Boolean('Use Direct Inference', default=True, tracking=True,
                                         help="Use HuggingFace Direct Inference API as primary method")
    enable_mcp_fallback = fields.Boolean('Enable MCP Fallback', default=True, tracking=True,
                                       help="Use MCP server as fallback when direct inference fails")
    enable_llm_fallback = fields.Boolean('Enable LLM Fallback', default=True, tracking=True,
                                       help="Use LLM provider as final fallback")

    prompt_text = fields.Text(string="Prompt", help="Enter prompt for AI response", default="What is the latest news on AI?")
    result_text = fields.Text(string="Response", help="AI generated response", default="", readonly=True)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('is_active'):
                self.search([
                    ('company_id', '=', vals.get('company_id', self.env.company.id)),
                    ('is_active', '=', True)
                ]).write({'is_active': False})
        return super(HuggingFaceConfig, self).create(vals_list)

    def write(self, vals):
        if 'is_active' in vals and vals['is_active']:
            self.search([
                ('company_id', '=', self.company_id.id),
                ('id', '!=', self.id),
                ('is_active', '=', True)
            ]).write({'is_active': False})
        return super(HuggingFaceConfig, self).write(vals)

    def _get_mcp_client(self):
        """Create and return an MCPClient instance."""
        self.ensure_one()
        
        mcp_server_config = self._get_mcp_server_config()
        
        # Set the API key and Docker-specific environment for the subprocess
        if self.huggingface_api_key:
            mcp_server_config['env']['HF_TOKEN'] = self.huggingface_api_key
        
        # Set working directory for file operations in Docker environment
        mcp_server_config['env']['MCP_HF_WORK_DIR'] = '/tmp/mcp-files'
        mcp_server_config['env']['CLAUDE_DESKTOP_MODE'] = 'true'
        
        # Ensure working directory exists
        work_dir = '/tmp/mcp-files'
        if not os.path.exists(work_dir):
            try:
                os.makedirs(work_dir, exist_ok=True)
                _logger.info(f"Created MCP working directory: {work_dir}")
            except Exception as e:
                _logger.warning(f"Could not create MCP working directory {work_dir}: {e}")
        
        # Update args to include working directory
        if 'args' in mcp_server_config:
            # Check if work-dir is already in args
            args = mcp_server_config['args']
            if isinstance(args, str):
                if '--work-dir=' not in args:
                    mcp_server_config['args'] = f"{args} --work-dir={work_dir}"
            elif isinstance(args, list):
                if not any('--work-dir=' in arg for arg in args):
                    mcp_server_config['args'].append(f'--work-dir={work_dir}')
            
        config = {
            "mcpServers": {
                "mcp-hfspace": mcp_server_config
            }
        }
        
        client = MCPClient()
        client.config = config
        client.servers = [MCPServer("mcp-hfspace", mcp_server_config)]
                    
        return client

    def _get_mcp_work_dir(self):
        """Get the MCP working directory path."""
        return '/tmp/mcp-files'
    
    def _ensure_mcp_work_dir(self):
        """Ensure MCP working directory exists and is writable."""
        work_dir = self._get_mcp_work_dir()
        try:
            os.makedirs(work_dir, exist_ok=True)
            # Test write permissions
            test_file = os.path.join(work_dir, '.test_write')
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return True
        except Exception as e:
            _logger.error(f"MCP work directory {work_dir} not accessible: {e}")
            return False
    
    def _save_file_for_mcp(self, file_data, filename):
        """Save file data to MCP working directory for processing."""
        work_dir = self._get_mcp_work_dir()
        if not self._ensure_mcp_work_dir():
            raise UserError(_("Cannot access MCP working directory"))
        
        file_path = os.path.join(work_dir, filename)
        try:
            with open(file_path, 'wb') as f:
                f.write(file_data)
            _logger.info(f"Saved file for MCP processing: {file_path}")
            return file_path
        except Exception as e:
            _logger.error(f"Failed to save file {filename}: {e}")
            raise UserError(_(f"Failed to save file for processing: {e}"))

    def _get_pydantic_ai_model_string(self):
        """Constructs the correct model string for Pydantic AI."""
        self.ensure_one()
        
        # Use alternative agent if configured and primary is HuggingFace
        if (self.use_alternative_agent and 
            self.alternative_agent_provider_id and 
            self.llm_provider_id.provider_type == 'huggingface'):
            provider = self.alternative_agent_provider_id
        else:
            provider = self.llm_provider_id
        
        provider_type = provider.provider_type
        model_name = provider.model

        prefix_map = {
            'google': 'google-gla',
            'openai': 'openai',
            'anthropic': 'anthropic',
            'groq': 'groq',
            'mistral': 'mistral',
            'cohere': 'cohere',
            'bedrock': 'bedrock',
        }
        
        # For HuggingFace models without alternative, use HuggingFace OpenAI-compatible API
        if provider_type == 'huggingface':
            return f'openai:{model_name or "Qwen/Qwen2.5-72B-Instruct"}'
        
        prefix = prefix_map.get(provider_type, provider_type)
        return f'{prefix}:{model_name}'

    # HuggingFace Model Endpoints for Direct Inference
    HUGGINGFACE_ENDPOINTS = {
        'sentiment-analysis': 'https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment',
        'translation': 'https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-{lang}',
        'summarization': 'https://api-inference.huggingface.co/models/facebook/bart-large-cnn',
        'image-to-text': 'https://api-inference.huggingface.co/models/Salesforce/blip-image-captioning-large',
        'ocr': 'https://api-inference.huggingface.co/models/microsoft/trocr-base-handwritten',
        'text-to-image': 'https://api-inference.huggingface.co/models/stabilityai/stable-diffusion-2-1',
        'chat': 'https://api-inference.huggingface.co/models/Qwen/Qwen2.5-72B-Instruct',
    }
    
    def _get_system_endpoint(self, task_type):
        """Get endpoint from system parameters"""
        param_key = f'huggingface.{task_type.replace("-", "_")}.endpoint'
        return self.env['ir.config_parameter'].sudo().get_param(param_key)
    
    def _get_model_endpoint(self, task_type, options=None):
        """Get the appropriate model endpoint for task type with fallback logic"""
        # 1. Try to get from tool configuration
        tool_config = self.env['huggingface.tool.config'].search([
            ('task_type', '=', task_type),
            ('is_active', '=', True),
            ('company_id', 'in', [self.company_id.id, False])
        ], limit=1)
        
        if tool_config:
            endpoint = tool_config.endpoint_url
        else:
            # 2. Fallback to system parameters
            endpoint = self._get_system_endpoint(task_type)
            if not endpoint:
                # 3. Final fallback to hardcoded
                endpoint = self.HUGGINGFACE_ENDPOINTS.get(task_type)
        
        if not endpoint:
            raise UserError(_(f"No endpoint configured for task type: {task_type}"))
        
        # Handle language-specific endpoints
        if task_type == 'translation' and options and 'target_language' in options:
            target_lang = options['target_language']
            lang_map = {'fr': 'fr', 'es': 'es', 'de': 'de', 'zh': 'zh','hi': 'hi','gu': 'gu'}
            target_lang = lang_map.get(target_lang, target_lang)
            endpoint = endpoint.format(lang=target_lang)
        
        return endpoint
    
    def _get_tool_method(self, task_type):
        """Get appropriate tool method for task type"""
        method_map = {
            'sentiment-analysis': self._execute_sentiment_analysis_tool,
            'translation': self._execute_translation_tool,
            'summarization': self._execute_summarization_tool,
            'image-to-text': self._execute_image_to_text_tool,
            'ocr': self._execute_ocr_tool,
            'text-to-image': self._execute_text_to_image_tool,
            'chat': self._execute_chat_tool,
        }
        return method_map.get(task_type)
    
    # Individual Tool Methods
    def _execute_sentiment_analysis_tool(self, prompt, options=None):
        """Execute sentiment analysis using configured endpoint"""
        endpoint = self._get_model_endpoint('sentiment-analysis', options)
        return self._call_huggingface_api(endpoint, prompt, 'sentiment-analysis')
    
    def _execute_translation_tool(self, prompt, options=None):
        """Execute translation using configured LLM provider"""
        try:
            return self._call_llm_provider_api(prompt, 'translation', options)
        except Exception as e:
            _logger.warning(f"LLM provider translation failed: {e}, falling back to direct API")
            # Fallback to direct API with chunking for long text
            return self._call_translation_with_chunking(prompt, options)
    
    def _execute_summarization_tool(self, prompt, options=None):
        """Execute summarization using configured LLM provider"""
        try:
            return self._call_llm_provider_api(prompt, 'summarization', options)
        except Exception as e:
            _logger.warning(f"LLM provider summarization failed: {e}, falling back to direct API")
            # Fallback to direct API
            endpoint = self._get_model_endpoint('summarization', options)
            return self._call_huggingface_api(endpoint, prompt, 'summarization')
    
    def _execute_image_to_text_tool(self, file_data, options=None):
        """Execute image-to-text using configured endpoint"""
        endpoint = self._get_model_endpoint('image-to-text', options)
        return self._call_huggingface_api(endpoint, file_data, 'image-to-text')
    
    def _execute_ocr_tool(self, file_data, options=None):
        """Execute OCR using configured endpoint"""
        endpoint = self._get_model_endpoint('ocr', options)
        return self._call_huggingface_api(endpoint, file_data, 'ocr')
    
    def _execute_text_to_image_tool(self, prompt, options=None):
        """Execute text-to-image using configured endpoint"""
        endpoint = self._get_model_endpoint('text-to-image', options)
        return self._call_huggingface_api(endpoint, prompt, 'text-to-image')
    
    def _execute_chat_tool(self, prompt, options=None):
        """Execute chat using configured LLM provider"""
        return self._call_llm_provider_api(prompt, 'chat', options)
    
    def _call_huggingface_api(self, endpoint, data, task_type):
        """
        Calls the Hugging Face Inference API.
        Handles both binary data (images) and JSON data (text).
        """
        import requests
        import json
        from odoo.exceptions import UserError
        from odoo.tools.translate import _


        if not self.huggingface_api_key:
            raise UserError(_("Hugging Face API key is not set. Please configure it in the Hugging Face settings."))

        # Clean and prepare the API key
        api_key = self.huggingface_api_key.strip('"\'').strip()
        
        # Initialize headers
        headers = {}
        
        # Handle different input types
        if isinstance(data, bytes):
            # For binary data (images)
            request_data = data
            # Don't set Content-Type for binary data, let requests handle it
        else:
            # For text data
            request_data = {"inputs": data}
            headers['Content-Type'] = 'application/json'
        
        # Add authorization if API key is available
        if api_key:
            headers['Authorization'] = f'Bearer {api_key}'

        try:
            _logger.info(f"Calling Hugging Face API endpoint: {endpoint} for task: {task_type}")
            _logger.info(f"Request Headers: {json.dumps(headers, indent=2)}")
            
            # Make the API request
            if isinstance(data, bytes):
                response = requests.post(endpoint, data=request_data, headers=headers, timeout=120)
            else:
                _logger.info(f"Sending JSON payload: {json.dumps(request_data, ensure_ascii=False, indent=2)}")
                response = requests.post(endpoint, json=request_data, headers=headers, timeout=120)

            _logger.info(f"Hugging Face API response status: {response.status_code}")
            
            # Check for errors
            response.raise_for_status()
            
            # Handle different response types
            content_type = response.headers.get('content-type', '')
            if 'application/json' in content_type:
                return response.json()
            else:
                # For binary responses (like images), return the content
                return response.content
                
        except requests.exceptions.HTTPError as http_err:
            error_msg = f"HTTP error occurred: {http_err}"
            if hasattr(http_err, 'response') and http_err.response is not None:
                error_msg += f" - Response: {http_err.response.text}"
            _logger.error(error_msg)
            return {'error': error_msg}
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Failed to connect to Hugging Face API: {e}"
            _logger.error(error_msg)
            return {'error': error_msg}
            
        except Exception as e:
            error_msg = f"Unexpected error: {str(e)}"
            _logger.error(error_msg, exc_info=True)
            return {'error': error_msg}
    
    def _call_llm_provider_api(self, prompt, task_type, options=None):
        """Call LLM provider API using parent module structure"""
        # Use alternative agent if configured and primary is HuggingFace
        if (self.use_alternative_agent and 
            self.alternative_agent_provider_id and 
            self.llm_provider_id.provider_type == 'huggingface'):
            provider = self.alternative_agent_provider_id
        else:
            provider = self.llm_provider_id
        
        # Build task-specific messages
        messages = self._build_messages_for_task(task_type, prompt, options)
        
        try:
            # Use the parent module's LLM provider functionality
            # Convert messages to simple prompt for compatibility
            user_message = next((msg['content'] for msg in messages if msg['role'] == 'user'), prompt)
            system_message = next((msg['content'] for msg in messages if msg['role'] == 'system'), None)
            
            # Combine system and user messages
            if system_message:
                full_prompt = f"{system_message}\n\n{user_message}"
            else:
                full_prompt = user_message
            
            # Use the parent module's generate method (with prompt parameter)
            response = provider.generate(full_prompt)
            
            # Handle different response types
            if isinstance(response, dict):
                # Extract text from various possible response formats
                if 'generated_text' in response:
                    return response['generated_text']
                elif isinstance(response, list) and len(response) > 0:
                    if isinstance(response[0], dict) and 'generated_text' in response[0]:
                        return response[0]['generated_text']
                    else:
                        return str(response[0])
                else:
                    return str(response)
            elif isinstance(response, list) and len(response) > 0:
                if isinstance(response[0], dict) and 'generated_text' in response[0]:
                    return response[0]['generated_text']
                else:
                    return str(response[0])
            else:
                return str(response)
                
        except Exception as e:
            _logger.error(f"LLM provider API error: {str(e)}")
            raise UserError(_(f"LLM provider error: {str(e)}"))
    
    def _build_messages_for_task(self, task_type, prompt, options=None):
        """Build messages array for LLM provider based on task type"""
        messages = []
        
        # Add system message based on task type
        if task_type == 'chat':
            system_msg = "You are a helpful AI assistant."
        elif task_type == 'translation':
            target_lang = options.get('target_language', 'French') if options else 'French'
            lang_names = {'fr': 'French', 'es': 'Spanish', 'de': 'German', 'hi': 'Hindi', 'zh': 'Chinese', 'gu': 'Gujarati'}
            lang_name = lang_names.get(target_lang, target_lang)
            system_msg = f"You are a professional translator. Translate the following text to {lang_name}. Only return the translation, no explanations."
        elif task_type == 'summarization':
            system_msg = "You are a professional summarizer. Summarize the following text in 2-3 concise sentences. Focus on the key points and main ideas."
        elif task_type == 'sentiment-analysis':
            system_msg = "You are a sentiment analyzer. Analyze the sentiment and respond with only: POSITIVE, NEGATIVE, or NEUTRAL."
        else:
            system_msg = "You are a helpful AI assistant."
        
        messages.append({'role': 'system', 'content': system_msg})
        messages.append({'role': 'user', 'content': prompt})
        
        return messages
    

    
    def _call_huggingface_openai_translation(self, prompt, options=None):
        """Call HuggingFace translation using OpenAI-compatible API"""
        try:
            from openai import OpenAI
        except ImportError:
            raise UserError(_("openai library is required for translation. Install with: pip install openai"))
        
        if not self.huggingface_api_key:
            raise UserError(_("HuggingFace API key is required but not configured."))
        
        # Use Qwen model for translation
        model_name = 'Qwen/Qwen2.5-72B-Instruct'
        target_lang = options.get('target_language', 'French') if options else 'French'
        lang_names = {'fr': 'French', 'es': 'Spanish', 'de': 'German', 'hi': 'Hindi', 'zh': 'Chinese', 'gu': 'Gujarati'}
        lang_name = lang_names.get(target_lang, target_lang)
        
        try:
            client = OpenAI(
                base_url=f"https://api-inference.huggingface.co/models/{model_name}/v1",
                api_key=self.huggingface_api_key,
            )
            
            completion = client.chat.completions.create(
                model=model_name,
                messages=[
                    {
                        "role": "system",
                        "content": f"You are a professional translator. Translate the following text to {lang_name}. Only return the translation, no explanations."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=4000
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            _logger.error(f"HuggingFace OpenAI translation error: {str(e)}")
            raise UserError(_(f"Translation API error: {str(e)}"))
    
    def _call_translation_with_chunking(self, prompt, options=None):
        """Handle long text translation by chunking"""
        # Split long text into chunks
        max_chunk_size = 500  # characters
        if len(prompt) <= max_chunk_size:
            endpoint = self._get_model_endpoint('translation', options)
            return self._call_huggingface_api(endpoint, prompt, 'translation')
        
        # Split into sentences and group into chunks
        sentences = prompt.split('. ')
        chunks = []
        current_chunk = ""
        
        for sentence in sentences:
            if len(current_chunk + sentence) <= max_chunk_size:
                current_chunk += sentence + ". "
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence + ". "
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # Translate each chunk
        translated_chunks = []
        endpoint = self._get_model_endpoint('translation', options)
        
        for chunk in chunks:
            try:
                result = self._call_huggingface_api(endpoint, chunk, 'translation')
                translated_chunks.append(result)
            except Exception as e:
                _logger.warning(f"Chunk translation failed: {e}")
                translated_chunks.append(chunk)  # Keep original if translation fails
        
        return ' '.join(translated_chunks)
    
    def _call_huggingface_openai_summarization(self, prompt, options=None):
        """Call HuggingFace summarization using OpenAI-compatible API"""
        try:
            from openai import OpenAI
        except ImportError:
            raise UserError(_("openai library is required for summarization. Install with: pip install openai"))
        
        if not self.huggingface_api_key:
            raise UserError(_("HuggingFace API key is required but not configured."))
        
        # Use Qwen model for summarization
        model_name = 'Qwen/Qwen2.5-72B-Instruct'
        
        try:
            client = OpenAI(
                base_url=f"https://api-inference.huggingface.co/models/{model_name}/v1",
                api_key=self.huggingface_api_key,
            )
            
            completion = client.chat.completions.create(
                model=model_name,
                messages=[
                    {
                        "role": "system",
                        "content": "You are a professional summarizer. Summarize the following text in 2-3 concise sentences. Focus on the key points and main ideas."
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=500
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            _logger.error(f"HuggingFace OpenAI summarization error: {str(e)}")
            raise UserError(_(f"Summarization API error: {str(e)}"))
    
    def _execute_direct_inference(self, task_type, prompt, options=None):
        """Execute task using direct HuggingFace Inference API"""
        # Use tool method dispatcher
        tool_method = self._get_tool_method(task_type)
        if tool_method:
            raw_result = tool_method(prompt, options)
            # Process the result to ensure proper formatting
            processed_result = self._process_inference_result(raw_result, task_type)
            return processed_result
        else:
            raise UserError(_(f"No tool method available for task type: {task_type}"))
    
    def _process_inference_result(self, result, task_type):
        """Process and format the inference result based on task type"""
        # Handle nested arrays (flatten if needed)
        if isinstance(result, list) and len(result) == 1 and isinstance(result[0], list):
            result = result[0]
        
        if isinstance(result, list) and len(result) > 0:
            if task_type == 'sentiment-analysis':
                # Map generic labels to meaningful names
                label_map = {
                    'LABEL_0': 'NEGATIVE',
                    'LABEL_1': 'NEUTRAL', 
                    'LABEL_2': 'POSITIVE',
                    'NEGATIVE': 'NEGATIVE',
                    'NEUTRAL': 'NEUTRAL',
                    'POSITIVE': 'POSITIVE'
                }
                
                # Find the highest scoring sentiment
                best_sentiment = max(result, key=lambda x: x.get('score', 0))
                label = label_map.get(best_sentiment.get('label', ''), best_sentiment.get('label', 'UNKNOWN'))
                score = best_sentiment.get('score', 0)
                
                return f"Sentiment: {label} (confidence: {score:.1%})"
            elif task_type == 'translation':
                item = result[0]
                return item.get('translation_text', str(result))
            elif task_type == 'summarization':
                item = result[0]
                return item.get('summary_text', str(result))
            elif task_type in ['image-to-text', 'ocr']:
                item = result[0]
                return item.get('generated_text', str(result))
            elif task_type == 'text-to-image':
                return "Image generated successfully"
            else:
                return str(result[0]) if isinstance(result[0], dict) else str(result)
        elif isinstance(result, dict):
            if task_type == 'sentiment-analysis':
                label_map = {
                    'LABEL_0': 'NEGATIVE',
                    'LABEL_1': 'NEUTRAL', 
                    'LABEL_2': 'POSITIVE',
                    'NEGATIVE': 'NEGATIVE',
                    'NEUTRAL': 'NEUTRAL',
                    'POSITIVE': 'POSITIVE'
                }
                label = label_map.get(result.get('label', ''), result.get('label', 'UNKNOWN'))
                score = result.get('score', 0)
                return f"Sentiment: {label} (confidence: {score:.1%})"
            elif task_type == 'translation':
                return result.get('translation_text', str(result))
            elif task_type == 'summarization':
                return result.get('summary_text', str(result))
            elif task_type in ['image-to-text', 'ocr']:
                return result.get('generated_text', str(result))
            else:
                return str(result)
        return str(result)
    
    def _build_generic_task_prompt(self, task_type, prompt, options=None):
        """Build task-specific prompts for generic models"""
        if task_type == 'sentiment-analysis':
            return f"Analyze the sentiment of this text. Respond with only the sentiment (positive/negative/neutral) and confidence score (0-1): {prompt}"
        elif task_type == 'translation':
            target_lang = options.get('target_language', 'French') if options else 'French'
            return f"Translate this text to {target_lang}: {prompt}"
        elif task_type == 'summarization':
            return f"Summarize this text in 2-3 sentences: {prompt}"
        elif task_type == 'chat':
            return prompt
        else:
            return f"Process this {task_type} request: {prompt}"
    
    def _process_generic_result(self, result, task_type):
        """Process results from generic models"""
        raw_text = ""
        
        if isinstance(result, list) and len(result) > 0:
            item = result[0]
            if isinstance(item, dict):
                raw_text = item.get('generated_text', item.get('text', str(item)))
            else:
                raw_text = str(item)
        elif isinstance(result, dict):
            raw_text = result.get('generated_text', result.get('text', str(result)))
        elif isinstance(result, str):
            raw_text = result
        else:
            raw_text = str(result)
        
        # Parse task-specific responses
        if task_type == 'sentiment-analysis':
            return self._parse_sentiment_response(raw_text)
        elif task_type == 'translation':
            return self._parse_translation_response(raw_text)
        elif task_type == 'summarization':
            return self._parse_summarization_response(raw_text)
        elif task_type == 'text-to-image':
            return self._parse_text_to_image_response(raw_text)
        else:
            return raw_text
    
    def _parse_sentiment_response(self, text):
        """Parse sentiment analysis response from generic model"""
        import re
        
        # Look for pattern: sentiment confidence_score
        pattern = r'\b(positive|negative|neutral)\s+(\d*\.?\d+)'
        match = re.search(pattern, text.lower())
        
        if match:
            sentiment = match.group(1).title()
            confidence = float(match.group(2))
            return f"Sentiment: {sentiment} (confidence: {confidence:.2f})"
        
        # Fallback: look for just the sentiment word
        for sentiment in ['positive', 'negative', 'neutral']:
            if sentiment in text.lower():
                return f"Sentiment: {sentiment.title()}"
        
        # Last resort: return first line or first 100 chars
        first_line = text.split('\n')[0].strip()
        return first_line[:100] if len(first_line) > 100 else first_line
    
    def _parse_translation_response(self, text):
        """Parse translation response from generic model"""
        # Remove the original prompt if it's echoed back
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('Translate') and ':' not in line[:20]:
                return line
        return text.strip()
    
    def _parse_summarization_response(self, text):
        """Parse summarization response from generic model"""
        # Remove the original prompt if it's echoed back
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('Summarize') and len(line) > 20:
                return line
        return text.strip()
    
    def _parse_text_to_image_response(self, text):
        """Parse text-to-image response from generic model"""
        # Generic text models can't generate images, return helpful message
        return "Note: This model cannot generate images. Please use MCP fallback or a dedicated image generation model."

    def _get_task_config(self, task_type):
        """Get task configuration from huggingface.task model"""
        task_config = self.env['huggingface.task'].search([
            ('task_type', '=', task_type),
            ('is_active', '=', True)
        ], limit=1)
        
        if not task_config:
            raise UserError(_(f"No active task configuration found for '{task_type}'"))
        
        return task_config
    
    def _build_task_prompt(self, task_config, prompt, options=None, use_fallback=False):
        """Build specific prompt for the task type"""
        task_type = task_config.task_type
        tool_name = task_config.mcp_tool_name
        parameters = task_config.get_parameters_dict()
        
        # Merge options with default parameters
        if options:
            parameters.update(options)
        
        if use_fallback:
            # Direct LLM prompts without MCP tools
            if task_type == 'sentiment-analysis':
                return f"Analyze the sentiment of this text and return only the sentiment (positive/negative/neutral) and confidence score: '{prompt}'"
            
            elif task_type == 'translation':
                target_lang = options.get('target_language', 'fr') if options else 'fr'
                lang_names = {'fr': 'French', 'es': 'Spanish', 'de': 'German', 'hi': 'Hindi', 'zh': 'Chinese','gu': 'Gujarati'}
                lang_name = lang_names.get(target_lang, target_lang)
                return f"Translate this text to {lang_name} and return only the translation: '{prompt}'"
            
            elif task_type == 'summarization':
                return f"Summarize this text in 2-3 sentences: '{prompt}'"
            
            elif task_type == 'chat':
                return f"Respond to this message as an AI assistant: '{prompt}'"
            
            else:
                return f"Process this {task_type} request: '{prompt}'"
        
        # MCP tool prompts - Updated for HFSpace MCP server
        if task_type == 'sentiment-analysis':
            space = parameters.get('space', 'Qwen/Qwen2.5-72B-Instruct')
            return f"Use the {tool_name} tool with space '{space}' and message 'Analyze the sentiment of this text and respond with positive, negative, or neutral: {prompt}'"
        
        elif task_type == 'translation':
            target_lang = options.get('target_language', 'French') if options else 'French'
            space = parameters.get('space', 'Qwen/Qwen2.5-72B-Instruct')
            return f"Use the {tool_name} tool with space '{space}' and message 'Translate this text to {target_lang}: {prompt}'"
        
        elif task_type == 'text-to-image':
            space = parameters.get('space', 'black-forest-labs/FLUX.1-schnell')
            return f"Use the {tool_name} tool with space '{space}' and message '{prompt}'"
        
        elif task_type == 'summarization':
            space = parameters.get('space', 'Qwen/Qwen2.5-72B-Instruct')
            return f"Use the {tool_name} tool with space '{space}' and message 'Summarize this text: {prompt}'"
        
        elif task_type == 'ocr' or task_type == 'image-to-text':
            space = parameters.get('space', 'merve/paligemma2-vqav2')
            return f"Use the {tool_name} tool with space '{space}' and message 'Extract text from this image'"
        
        elif task_type == 'chat':
            space = parameters.get('space', 'Qwen/Qwen2.5-72B-Instruct')
            return f"Use the {tool_name} tool with space '{space}' and message '{prompt}'"
        
        else:
            # Generic fallback
            space = parameters.get('space', 'Qwen/Qwen2.5-72B-Instruct')
            return f"Use the {tool_name} tool with space '{space}' and message '{prompt}'"

    def action_check_connection(self):
        """Check connection by running a simple command through the agent."""
        self.ensure_one()
        client = self._get_mcp_client()

        if not self.llm_provider_id:
            raise UserError(_("Please select an Agent LLM Provider before checking the connection."))

        api_key_env_map = {
            'openai': 'OPENAI_API_KEY',
            'google': 'GOOGLE_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'groq': 'GROQ_API_KEY',
        }
        
        api_key_set = False
        env_var_to_set = None
        try:
            # Use alternative agent if configured
            if (self.use_alternative_agent and 
                self.alternative_agent_provider_id and 
                self.llm_provider_id.provider_type == 'huggingface'):
                llm_provider = self.alternative_agent_provider_id
            else:
                llm_provider = self.llm_provider_id
                
            provider_type = llm_provider.provider_type
            
            if provider_type in api_key_env_map and llm_provider.api_key:
                env_var_to_set = api_key_env_map[provider_type]
                os.environ[env_var_to_set] = llm_provider.api_key
                api_key_set = True

            try:
                mcp_tools = asyncio.run(client.start())
                _logger.info(f"MCP tools loaded: {len(mcp_tools)} tools available")
                for tool in mcp_tools:
                    _logger.info(f"Available tool: {tool.name}")
                use_mcp_test = True
            except Exception as mcp_error:
                _logger.error(f"MCP server startup failed: {mcp_error}")
                # Continue without MCP tools - test LLM directly
                mcp_tools = []
                use_mcp_test = False
                
            model_string = self._get_pydantic_ai_model_string()
            agent = Agent(model_string, tools=mcp_tools)

            # Log available tools for debugging
            if mcp_tools:
                _logger.info(f"Available MCP tools: {[tool.name for tool in mcp_tools]}")
                for tool in mcp_tools:
                    _logger.info(f"Tool: {tool.name} - Description: {getattr(tool, 'description', 'No description')}")
            
            # Use appropriate test prompt based on available tools
            if use_mcp_test and mcp_tools:
                # Try to find the correct tool name
                tool_names = [tool.name for tool in mcp_tools]
                if 'hf_space_chat' in tool_names:
                    test_prompt = "Use the hf_space_chat tool with space 'Qwen/Qwen2.5-72B-Instruct' and message 'Hello, please respond with Connection successful'"
                elif 'hf_whoami' in tool_names:
                    test_prompt = "Use the hf_whoami tool to get user information."
                else:
                    test_prompt = f"Use any available tool from: {tool_names}"
            else:
                test_prompt = "Hello, please respond with 'Connection successful' to confirm you are working."
                _logger.warning("MCP tools not available, using direct LLM connection")
            
            result = agent.run_sync(test_prompt)

            _logger.info("Connection check successful. Result: %s", result.output)
            self.write({'state': 'connected'})
            
            if use_mcp_test and mcp_tools:
                success_msg = f'Successfully connected to Hugging Face MCP server with {len(mcp_tools)} tools.'
            else:
                success_msg = 'LLM provider connected successfully. Using fallback mode (MCP server unavailable).'

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _(success_msg),
                    'type': 'success',
                }
            }
        except Exception as e:
            _logger.error("Hugging Face MCP connection error: %s", str(e), exc_info=True)
            self.write({'state': 'disconnected'})
            raise UserError(_("Could not connect to Hugging Face MCP server: %s") % str(e))
        finally:
            if api_key_set and env_var_to_set:
                del os.environ[env_var_to_set]
            if client:
                asyncio.run(client.cleanup())

    def generate_task(self, prompt, task_type, options=None, store_result=True):
        """Generate a task with 3-tier fallback system: Direct Inference -> MCP -> LLM"""
        import time
        start_time = time.time()
        
        # Create result record if storing
        result_record = None
        if store_result:
            result_record = self.env['huggingface.result'].create_result(
                task_type, prompt, self.id
            )
        
        result = None
        method_used = None
        errors = []
        
        tool_config = self.env['huggingface.tool.config'].search([
            ('task_type', '=', task_type),
            ('is_active', '=', True),
            ('company_id', 'in', [self.company_id.id, False])
        ], limit=1)

        if tool_config and tool_config.use_gradio:
            try:
                _logger.info(f"Attempting Gradio for task '{task_type}'")
                result = self._execute_gradio_tool(task_type, prompt, options, mimetype=None)
                method_used = 'gradio_space'
                _logger.info(f"Gradio successful for task '{task_type}'")
            except Exception as e:
                error_msg = f"Gradio space failed: {str(e)}"
                errors.append(error_msg)
                _logger.warning(error_msg)
        else:
            # Standard fallback chain for non-Gradio tasks
            if self.use_direct_inference:
                try:
                    _logger.info(f"Attempting direct inference for task '{task_type}'")
                    result = self._execute_direct_inference(task_type, prompt, options)
                    method_used = 'direct_inference'
                    _logger.info(f"Direct inference successful for task '{task_type}'")
                except Exception as e:
                    error_msg = f"Direct inference failed: {str(e)}"
                    errors.append(error_msg)
                    _logger.warning(error_msg)
            
            if not result and self.enable_mcp_fallback:
                try:
                    _logger.info(f"Attempting MCP fallback for task '{task_type}'")
                    result = self._execute_mcp(task_type, prompt, options)
                    method_used = 'mcp_server'
                    _logger.info(f"MCP fallback successful for task '{task_type}'")
                except Exception as e:
                    error_msg = f"MCP server failed: {str(e)}"
                    errors.append(error_msg)
                    _logger.warning(error_msg)
        
        # 4th Priority: Try LLM Fallback
        if not result and self.enable_llm_fallback and self.llm_provider_id:
            try:
                _logger.info(f"Attempting LLM fallback for task '{task_type}'")
                result = self._execute_llm_fallback(task_type, prompt, options)
                method_used = 'llm_fallback'
                _logger.info(f"LLM fallback successful for task '{task_type}'")
            except Exception as e:
                error_msg = f"LLM fallback failed: {str(e)}"
                errors.append(error_msg)
                _logger.warning(error_msg)
        
        # Handle failure case
        if not result:
            processing_time = time.time() - start_time
            
            # Provide specific error messages for different task types
            if task_type == 'ocr':
                final_error = "OCR service is currently unavailable. Please try again later or contact administrator."
            elif task_type == 'text-to-image':
                final_error = "Image generation service is currently unavailable. Please try again later."
            else:
                final_error = f"All methods failed: {'; '.join(errors)}"
            
            if result_record:
                result_record.mark_failed(final_error)
                result_record.processing_time = processing_time
            
            _logger.error(f"Task '{task_type}' completely failed: {final_error}")
            raise UserError(_(final_error))
        
        # Store successful result
        processing_time = time.time() - start_time
        if result_record:
            result_record.mark_completed(
                result_text=result,
                processing_time=processing_time,
                raw_data={
                    'method_used': method_used,
                    'errors_encountered': errors,
                    'task_type': task_type
                }
            )
        
        _logger.info(f"Task '{task_type}' completed using {method_used} in {processing_time:.2f}s")
        return result

    def _execute_mcp(self, task_type, prompt, options=None):
        """Execute task using MCP server (existing implementation)"""
        client = self._get_mcp_client()
        
        if not self.llm_provider_id:
            raise UserError(_("Agent LLM Provider not configured"))
        
        # Get task configuration
        task_config = self._get_task_config(task_type)
        
        api_key_env_map = {
            'openai': 'OPENAI_API_KEY',
            'google': 'GOOGLE_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'groq': 'GROQ_API_KEY',
        }

        api_key_set = False
        env_var_to_set = None
        try:
            # Use alternative agent if configured
            if (self.use_alternative_agent and 
                self.alternative_agent_provider_id and 
                self.llm_provider_id.provider_type == 'huggingface'):
                llm_provider = self.alternative_agent_provider_id
            else:
                llm_provider = self.llm_provider_id
                
            provider_type = llm_provider.provider_type

            if provider_type in api_key_env_map and llm_provider.api_key:
                env_var_to_set = api_key_env_map[provider_type]
                os.environ[env_var_to_set] = llm_provider.api_key
                api_key_set = True

            mcp_tools = asyncio.run(client.start())
            model_string = self._get_pydantic_ai_model_string()
            agent = Agent(model_string, tools=mcp_tools)

            # Build specific prompt for the task
            full_prompt = self._build_task_prompt(task_config, prompt, options, use_fallback=False)
            _logger.info(f"MCP prompt: {full_prompt}")
            _logger.info(f"Available MCP tools: {[tool.name for tool in mcp_tools] if mcp_tools else 'None'}")
            result = agent.run_sync(full_prompt)
            return result.output
            
        finally:
            if api_key_set and env_var_to_set:
                del os.environ[env_var_to_set]
            if client:
                asyncio.run(client.cleanup())
    
    def _execute_llm_fallback(self, task_type, prompt, options=None):
        """Execute task using direct LLM provider (no MCP tools)"""
        if not self.llm_provider_id:
            raise UserError(_("Agent LLM Provider not configured"))
        
        # Get task configuration for fallback prompt
        task_config = self.env['huggingface.task'].search([
            ('task_type', '=', task_type),
            ('is_active', '=', True)
        ], limit=1)
        
        api_key_env_map = {
            'openai': 'OPENAI_API_KEY',
            'google': 'GOOGLE_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'groq': 'GROQ_API_KEY',
        }

        api_key_set = False
        env_var_to_set = None
        try:
            # Use alternative agent if configured
            if (self.use_alternative_agent and 
                self.alternative_agent_provider_id and 
                self.llm_provider_id.provider_type == 'huggingface'):
                llm_provider = self.alternative_agent_provider_id
            else:
                llm_provider = self.llm_provider_id
                
            provider_type = llm_provider.provider_type

            if provider_type in api_key_env_map and llm_provider.api_key:
                env_var_to_set = api_key_env_map[provider_type]
                os.environ[env_var_to_set] = llm_provider.api_key
                api_key_set = True

            model_string = self._get_pydantic_ai_model_string()
            agent = Agent(model_string, tools=[])  # No MCP tools

            # Build fallback prompt
            full_prompt = self._build_task_prompt(task_config, prompt, options, use_fallback=True)
            result = agent.run_sync(full_prompt)
            return result.output
            
        finally:
            if api_key_set and env_var_to_set:
                del os.environ[env_var_to_set]

    def generate_task_with_file(self, prompt, task_type, file_content=None, filename=None, mimetype=None, options=None, store_result=True):
        """Generate a task with file input using 3-tier fallback system"""
        import time
        import base64
        start_time = time.time()
        
        # Create result record if storing
        result_record = None
        if store_result:
            result_record = self.env['huggingface.result'].create_result(
                task_type, prompt, self.id
            )
        
        if not file_content:
            error_msg = "File content is required for this task."
            if result_record:
                result_record.mark_failed(error_msg)
            raise UserError(_(error_msg))
        
        result = None
        method_used = None
        errors = []
        file_data = base64.b64decode(file_content)
        
        # Store file in result record
        if result_record:
            result_record.store_file_result(file_data, filename, mimetype)
        
        tool_config = self.env['huggingface.tool.config'].search([
            ('task_type', '=', task_type),
            ('is_active', '=', True),
            ('company_id', 'in', [self.company_id.id, False])
        ], limit=1)

        if tool_config and tool_config.use_gradio:
            try:
                _logger.info(f"Attempting Gradio for file task '{task_type}'")
                result = self._execute_gradio_tool(task_type, file_data, options, mimetype=mimetype)
                method_used = 'gradio_space'
                _logger.info(f"Gradio successful for file task '{task_type}'")
            except Exception as e:
                error_msg = f"Gradio space failed: {str(e)}"
                errors.append(error_msg)
                _logger.warning(error_msg)
        else:
            # Standard fallback chain for non-Gradio file tasks
            if self.use_direct_inference:
                try:
                    _logger.info(f"Attempting direct inference for file task '{task_type}'")
                    result = self._execute_direct_inference_with_file(task_type, file_data, filename, options)
                    method_used = 'direct_inference'
                    _logger.info(f"Direct inference successful for file task '{task_type}'")
                except Exception as e:
                    error_msg = f"Direct inference failed: {str(e)}"
                    errors.append(error_msg)
                    _logger.warning(error_msg)

            if not result and self.enable_mcp_fallback:
                try:
                    _logger.info(f"Attempting MCP fallback for file task '{task_type}'")
                    result = self._execute_mcp_with_file(task_type, file_data, filename, options)
                    method_used = 'mcp_server'
                    _logger.info(f"MCP fallback successful for file task '{task_type}'")
                except Exception as e:
                    error_msg = f"MCP server failed: {str(e)}"
                    errors.append(error_msg)
                    _logger.warning(error_msg)
        
        # 4th Priority: Use regular task generation as fallback
        if not result:
            try:
                _logger.info(f"Attempting regular task generation for file task '{task_type}'")
                result = self.generate_task(prompt, task_type, options, store_result=False)
                method_used = 'llm_fallback'
            except Exception as e:
                error_msg = f"LLM fallback failed: {str(e)}"
                errors.append(error_msg)
                _logger.warning(error_msg)
        
        # Handle results
        processing_time = time.time() - start_time
        
        if not result:
            final_error = f"All file processing methods failed: {'; '.join(errors)}"
            if result_record:
                result_record.mark_failed(final_error)
                result_record.processing_time = processing_time
            raise UserError(_(final_error))
        
        # Store successful result
        if result_record:
            result_record.mark_completed(
                result_text=result,
                processing_time=processing_time,
                raw_data={
                    'method_used': method_used,
                    'filename': filename,
                    'mimetype': mimetype,
                    'errors_encountered': errors
                }
            )
        
        return result
    
    def _execute_direct_inference_with_file(self, task_type, file_data, filename, options=None):
        """Execute file-based task using direct HuggingFace Inference API"""
        # Use the tool method for file-based tasks
        tool_method = self._get_tool_method(task_type)
        if tool_method:
            raw_result = tool_method(file_data, options)
            # Process the result to ensure proper formatting
            processed_result = self._process_inference_result(raw_result, task_type)
            return processed_result
        else:
            raise UserError(_(f"No tool method available for file task type: {task_type}"))
    
    def _execute_gradio_tool(self, task_type, data, options=None, mimetype=None):
        """Execute task using Gradio space, handling different data types and API endpoints."""
        try:
            from gradio_client import Client, handle_file
            import tempfile
            import os
        except ImportError:
            raise UserError(_("gradio_client and Pillow are required for Gradio integration. Install with: pip install gradio_client Pillow"))

        tool_config = self.env['huggingface.tool.config'].search([
            ('task_type', '=', task_type),
            ('use_gradio', '=', True),
            ('is_active', '=', True),
            ('company_id', 'in', [self.company_id.id, False])
        ], limit=1)

        if not tool_config or not tool_config.gradio_space_url:
            raise UserError(_(f"No active Gradio space configured for task type: {task_type}"))

        try:
            # Use space URL directly for private spaces
            space_url = tool_config.gradio_space_url
            
            _logger.info(f"Connecting to Gradio space: {space_url}")
            client = Client(space_url, hf_token=self.huggingface_api_key)
            _logger.info(f"Successfully connected to Gradio space")
            
            # Log available endpoints for debugging
            try:
                endpoints = client.endpoints
                _logger.info(f"Available endpoints: {endpoints}")
            except Exception as e:
                _logger.warning(f"Could not get endpoints: {e}")

            if isinstance(data, bytes):
                # This is a file task (e.g., OCR)
                is_pdf = mimetype == 'application/pdf'
                suffix = '.pdf' if is_pdf else '.png'
                
                with tempfile.NamedTemporaryFile(delete=False, suffix=suffix) as tmp_file:
                    tmp_file.write(data)
                    tmp_path = tmp_file.name

                try:
                    gradio_params = tool_config.get_gradio_parameters_dict()
                    max_tokens = gradio_params.get('max_new_tokens', 4096)
                    _logger.info(f"Using max_tokens: {max_tokens}, gradio_params: {gradio_params}")

                    if task_type == 'ocr':
                        # Based on Nanonets-OCR app.py, we have two separate functions
                        if is_pdf:
                            # Call the PDF OCR function - try without explicit api_name first
                            _logger.info(f"Calling PDF OCR, file: {tmp_path}")
                            file_handle = handle_file(tmp_path)
                            _logger.info(f"PDF file handle created: {type(file_handle)}")
                            try:
                                # Try with explicit API name first
                                result = client.predict(file_handle, max_tokens, api_name='/ocr_pdf_gradio')
                            except Exception as e:
                                _logger.warning(f"API name call failed: {e}, trying default")
                                # Fallback to default endpoint
                                result = client.predict(file_handle, max_tokens)
                        else:
                            # Call the Image OCR function
                            _logger.info(f"Calling Image OCR, file: {tmp_path}")
                            file_handle = handle_file(tmp_path)
                            _logger.info(f"File handle created: {type(file_handle)}")
                            try:
                                # Try with explicit API name first
                                result = client.predict(file_handle, max_tokens, api_name='/ocr_image_gradio')
                            except Exception as e:
                                _logger.warning(f"API name call failed: {e}, trying default")
                                # Fallback to default endpoint
                                result = client.predict(file_handle, max_tokens)
                    else:
                        # Fallback for other file-based gradio tasks
                        api_name = tool_config.gradio_api_endpoint or None
                        result = client.predict(tmp_path, api_name=api_name)
                    
                    return result
                finally:
                    if os.path.exists(tmp_path):
                        os.unlink(tmp_path)
            else:
                # This is a text task (e.g., text-to-image)
                gradio_params = tool_config.get_gradio_parameters_dict()
                api_name = tool_config.gradio_api_endpoint or None

                if task_type == 'text-to-image':
                    # Use dynamic API name from tool config
                    api_name = tool_config.gradio_api_endpoint or '/infer'
                    result = client.predict(
                        prompt=data,
                        seed=gradio_params.get('seed', 42),
                        randomize_seed=gradio_params.get('randomize_seed', True),
                        width=gradio_params.get('width', 1024),
                        height=gradio_params.get('height', 1024),
                        num_inference_steps=gradio_params.get('num_inference_steps', 4),
                        api_name=api_name
                    )
                    # Return the image (first element of the result tuple)
                    if isinstance(result, (list, tuple)) and result:
                        return result[0]
                    return result
                else:
                    # Generic text processing
                    _logger.info(f"Generic text processing with data: {data[:100]}..., api_name: {api_name}")
                    return client.predict(data, api_name=api_name)

        except Exception as e:
            error_msg = str(e)
            _logger.error(f"Gradio execution error for {task_type}: {error_msg}", exc_info=True)
            
            # Provide more specific error messages
            if "Object of type PngImageFile is not JSON serializable" in error_msg:
                raise UserError(_("Image serialization error. The image format may not be supported by the Gradio space."))
            elif "ValidationError" in error_msg and "ImageData" in error_msg:
                raise UserError(_("Invalid image data format. Please try with a different image format (PNG, JPG)."))
            elif "Connection" in error_msg or "timeout" in error_msg.lower():
                raise UserError(_("Connection to Gradio space failed. The service may be temporarily unavailable."))
            else:
                raise UserError(_(f"Gradio execution failed: {error_msg}"))
    
    def _execute_mcp_with_file(self, task_type, file_data, filename, options=None):
        """Execute file-based task using MCP server (existing implementation)"""
        # Save file to MCP working directory
        mcp_file_path = self._save_file_for_mcp(file_data, filename)
        
        # Build OCR-specific prompt with file reference
        full_prompt = f"Use the vision model to extract text from the image file: {filename}. The file is available in the working directory."
        
        # Process with MCP server
        client = self._get_mcp_client()
        
        api_key_env_map = {
            'openai': 'OPENAI_API_KEY',
            'google': 'GOOGLE_API_KEY',
            'anthropic': 'ANTHROPIC_API_KEY',
            'groq': 'GROQ_API_KEY',
        }

        api_key_set = False
        env_var_to_set = None
        
        try:
            llm_provider = self.llm_provider_id
            provider_type = llm_provider.provider_type

            if provider_type in api_key_env_map and llm_provider.api_key:
                env_var_to_set = api_key_env_map[provider_type]
                os.environ[env_var_to_set] = llm_provider.api_key
                api_key_set = True

            model_string = self._get_pydantic_ai_model_string()
            agent = Agent(
                model_string,
                tools=asyncio.run(client.start())
            )

            # Execute OCR with the agent
            _logger.info(f"Executing file task with MCP: {full_prompt}")
            _logger.info(f"File: {filename}, Size: {len(file_data)} bytes")
            
            result = agent.run_sync(full_prompt)
            return result.output
            
        finally:
            if api_key_set and env_var_to_set:
                del os.environ[env_var_to_set]
            if client:
                asyncio.run(client.cleanup())

    def generate_response(self):
        """Generate a response using the configured LLM provider directly."""
        self.ensure_one()
        if not self.prompt_text:
            raise UserError(_("Please enter a prompt."))
            
        try:
            # Use LLM provider directly for testing, not the complex task system
            result = self._call_llm_provider_api(self.prompt_text, 'chat', None)
            self.result_text = result
            return {
                'type': 'ir.actions.client',
                'tag': 'reload',
            }
        except Exception as e:
            self.result_text = str(e)
            raise UserError(_("An error occurred while generating the response: %s") % str(e))