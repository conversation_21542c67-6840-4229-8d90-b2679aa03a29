from odoo import models, fields

class MCPServerConfig(models.AbstractModel):
    _name = 'mcp.server.config'
    _description = 'MCP Server Configuration (Abstract)'

    mcp_command = fields.Char("MCP Command", default="npx")
    mcp_args = fields.Char("MCP Arguments", help="Comma-separated list of arguments")
    mcp_env_vars = fields.Char("Environment Variables", help="Comma-separated list of key=value pairs")

    def _get_mcp_server_config(self):
        """Returns the MCP server configuration as a dictionary."""
        self.ensure_one()
        
        args = self.mcp_args.split(' ') if self.mcp_args else []
        
        env = {}
        if self.mcp_env_vars:
            for pair in self.mcp_env_vars.split(','):
                if '=' in pair:
                    key, value = pair.split('=', 1)
                    env[key.strip()] = value.strip()
                    
        return {
            "command": self.mcp_command,
            "args": args,
            "env": env
        }