from odoo import models, fields, api, _
from odoo.exceptions import ValidationError
import json

class HuggingFaceTask(models.Model):
    _name = 'huggingface.task'
    _description = 'Hugging Face Task Configuration'
    _order = 'sequence, name'

    name = fields.Char('Task Name', required=True)
    sequence = fields.Integer('Sequence', default=10)
    task_type = fields.Selection([
        ('text-classification', 'Text Classification'),
        ('sentiment-analysis', 'Sentiment Analysis'),
        ('translation', 'Translation'),
        ('text-to-image', 'Text to Image'),
        ('image-to-text', 'Image to Text'),
        ('text-to-speech', 'Text to Speech'),
        ('speech-to-text', 'Speech to Text'),
        ('document-question-answering', 'Document Q&A'),
        ('summarization', 'Summarization'),
        ('ocr', 'OCR'),
        ('chat', 'Chat/Conversation'),
    ], string='Task Type', required=True)
    
    huggingface_space = fields.Char('HuggingFace Space/Model', required=True,
                                   help="The HuggingFace model or space identifier")
    mcp_tool_name = fields.Char('MCP Tool Name', required=True,
                               help="The exact tool name from HF MCP server")
    
    description = fields.Text('Description')
    parameters = fields.Text('Default Parameters (JSON)',
                           help="Default parameters in JSON format")
    
    is_active = fields.Boolean('Active', default=True)
    requires_file = fields.Boolean('Requires File Input', default=False)
    file_types = fields.Char('Supported File Types',
                           help="Comma-separated file extensions (e.g., .pdf,.jpg,.png)")
    
    output_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
        ('audio', 'Audio'),
        ('json', 'JSON Data'),
    ], string='Output Type', default='text')
    
    icon_name = fields.Char('Icon Filename', help="Icon file in static/description/")
    button_title = fields.Char('Button Title', help="Title shown on hover")
    
    @api.constrains('parameters')
    def _check_parameters_json(self):
        for record in self:
            if record.parameters:
                try:
                    json.loads(record.parameters)
                except json.JSONDecodeError:
                    raise ValidationError(_("Parameters must be valid JSON format"))
    
    def get_parameters_dict(self):
        """Return parameters as dictionary"""
        self.ensure_one()
        if self.parameters:
            try:
                return json.loads(self.parameters)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def get_file_types_list(self):
        """Return supported file types as list"""
        self.ensure_one()
        if self.file_types:
            return [ft.strip() for ft in self.file_types.split(',')]
        return []
    
    @api.model
    def get_active_tasks(self):
        """Return all active tasks for frontend"""
        tasks = self.search([('is_active', '=', True)])
        return [{
            'id': task.id,
            'name': task.name,
            'task_type': task.task_type,
            'icon_name': task.icon_name,
            'button_title': task.button_title,
            'requires_file': task.requires_file,
            'file_types': task.get_file_types_list(),
            'output_type': task.output_type,
        } for task in tasks]