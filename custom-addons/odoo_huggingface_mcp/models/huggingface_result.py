from odoo import models, fields, api, _
import json
import base64
import logging
from datetime import timedelta

_logger = logging.getLogger(__name__)

class HuggingFaceResult(models.Model):
    _name = 'huggingface.result'
    _description = 'HuggingFace Task Results'
    _order = 'create_date desc'

    name = fields.Char('Result Name', compute='_compute_name', store=True)
    task_id = fields.Many2one('huggingface.task', 'Task', required=True)
    task_type = fields.Selection(
        string='Task Type',
        related='task_id.task_type',
        store=True,
    )
    
    input_text = fields.Text('Input Text', required=True)
    result_text = fields.Text('Text Result')
    result_file = fields.Binary('File Result')
    result_filename = fields.Char('Result Filename')
    result_data = fields.Text('Raw Result Data', help="JSON data from HF API")
    
    processing_time = fields.Float('Processing Time (seconds)')
    user_id = fields.Many2one('res.users', 'User', default=lambda self: self.env.user)
    config_id = fields.Many2one('huggingface.config', 'Configuration')
    
    state = fields.Selection([
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Status', default='processing')
    
    error_message = fields.Text('Error Message')
    
    @api.depends('task_id', 'input_text', 'create_date')
    def _compute_name(self):
        for record in self:
            task_name = record.task_id.name if record.task_id else 'Unknown'
            input_preview = record.input_text[:50] + '...' if len(record.input_text or '') > 50 else record.input_text or ''
            record.name = f"{task_name}: {input_preview}"
    

    
    def get_result_data_dict(self):
        """Return result data as dictionary"""
        self.ensure_one()
        if self.result_data:
            try:
                return json.loads(self.result_data)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_result_data(self, data):
        """Set result data from dictionary"""
        self.ensure_one()
        if isinstance(data, dict):
            self.result_data = json.dumps(data)
        else:
            self.result_data = str(data)
    
    def store_file_result(self, file_content, filename, content_type='application/octet-stream'):
        """Store binary file result"""
        self.ensure_one()
        if isinstance(file_content, str):
            # If it's base64 string, decode it
            try:
                file_content = base64.b64decode(file_content)
            except:
                file_content = file_content.encode('utf-8')
        
        self.result_file = base64.b64encode(file_content)
        self.result_filename = filename
    
    @api.model
    def create_result(self, task_type, input_text, config_id=None):
        """Create a new result record"""
        task = self.env['huggingface.task'].search([
            ('task_type', '=', task_type),
            ('is_active', '=', True)
        ], limit=1)
        
        if not task:
            raise ValueError(f"No active task found for type: {task_type}")
        
        if not config_id:
            config = self.env['huggingface.config'].search([('is_active', '=', True)], limit=1)
            config_id = config.id if config else None
        
        return self.create({
            'task_id': task.id,
            'input_text': input_text,
            'config_id': config_id,
            'state': 'processing',
        })
    
    def mark_completed(self, result_text=None, result_file=None, filename=None, processing_time=None, raw_data=None):
        """Mark result as completed with data"""
        self.ensure_one()
        vals = {'state': 'completed'}
        
        if result_text:
            vals['result_text'] = result_text
        
        if result_file and filename:
            self.store_file_result(result_file, filename)
        
        if processing_time:
            vals['processing_time'] = processing_time
        
        if raw_data:
            self.set_result_data(raw_data)
        
        self.write(vals)
    
    def mark_failed(self, error_message):
        """Mark result as failed with error"""
        self.ensure_one()
        self.write({
            'state': 'failed',
            'error_message': error_message,
        })
    
    @api.model
    def cleanup_old_results(self, days=30):
        """Clean up old results (called by cron)"""
        cutoff_date = fields.Datetime.now() - timedelta(days=days)
        old_results = self.search([('create_date', '<', cutoff_date)])
        count = len(old_results)
        old_results.unlink()
        _logger.info(f"Cleaned up {count} old HuggingFace results")
        return count