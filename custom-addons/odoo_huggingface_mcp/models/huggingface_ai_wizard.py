from odoo import models, fields, api, _
from odoo.exceptions import UserError

class HuggingFaceAIWizard(models.TransientModel):
    _name = 'huggingface.ai.wizard'
    _description = 'AI Processing Wizard'

    # Step 1: Task Selection
    task_type = fields.Selection([
        ('ocr', 'OCR - Extract Text from Images and PDFs'),
        ('image-to-text', 'Image Description - Describe Images'),
        ('text-to-image', 'Image Generation - Create Images from Text'),
        ('speech-to-text', 'Audio Transcription - Convert Speech to Text'),
        ('text-to-speech', 'Speech Generation - Convert Text to Speech'),
    ], required=True, string="Task Type")
    
    task_description = fields.Html(compute="_compute_task_description", string="Description")
    
    # Step 2: Input
    input_text = fields.Text(string="Input Text")
    input_files = fields.Many2many('ir.attachment', string="Input Files")

    # Computed fields for UI control
    requires_text_input = fields.Boolean(compute='_compute_input_requirements')
    requires_file_input = fields.<PERSON><PERSON>an(compute='_compute_input_requirements')
    
    # Step 3: Configuration
    priority = fields.Selection([
        ('low', 'Low'),
        ('normal', 'Normal'),
        ('high', 'High'),
        ('urgent', 'Urgent'),
    ], default='normal', string="Priority")
    
    job_name = fields.Char(string="Job Name")
    
    # Wizard state
    state = fields.Selection([
        ('task_selection', 'Task Selection'),
        ('input', 'Input'),
        ('configuration', 'Configuration'),
        ('review', 'Review'),
        ('submitted', 'Submitted'),
    ], default='task_selection', string="State")
    
    @api.depends('task_type')
    def _compute_input_requirements(self):
        for record in self:
            text_tasks = ['text-to-image', 'text-to-speech']
            file_tasks = ['ocr', 'image-to-text', 'speech-to-text']
            
            record.requires_text_input = record.task_type in text_tasks
            record.requires_file_input = record.task_type in file_tasks

    @api.depends('task_type')
    def _compute_task_description(self):
        descriptions = {
            'ocr': '''
                <h4>OCR - Optical Character Recognition</h4>
                <p>Extract text from images and PDF documents. Supports complex documents with tables, equations, and images.</p>
                <ul>
                    <li><strong>Input:</strong> Image files (JPG, PNG, GIF, BMP, TIFF) or PDF documents</li>
                    <li><strong>Output:</strong> Extracted text with structured formatting</li>
                    <li><strong>Features:</strong> Tables in HTML, equations in LaTeX, image descriptions</li>
                    <li><strong>Processing Time:</strong> 30 seconds - 3 minutes</li>
                </ul>
            ''',
            'image-to-text': '''
                <h4>Image Description</h4>
                <p>Generate detailed descriptions of images, identifying objects, scenes, and activities.</p>
                <ul>
                    <li><strong>Input:</strong> Image files (JPG, PNG, GIF)</li>
                    <li><strong>Output:</strong> Descriptive text</li>
                    <li><strong>Processing Time:</strong> 30 seconds - 2 minutes</li>
                </ul>
            ''',
            'text-to-image': '''
                <h4>Image Generation</h4>
                <p>Create images from text descriptions using AI image generation models.</p>
                <ul>
                    <li><strong>Input:</strong> Text prompt describing the desired image</li>
                    <li><strong>Output:</strong> Generated image file</li>
                    <li><strong>Processing Time:</strong> 1-3 minutes</li>
                </ul>
            ''',
            'speech-to-text': '''
                <h4>Audio Transcription</h4>
                <p>Convert speech in audio files to text transcription.</p>
                <ul>
                    <li><strong>Input:</strong> Audio files (MP3, WAV)</li>
                    <li><strong>Output:</strong> Transcribed text</li>
                    <li><strong>Processing Time:</strong> 30 seconds - 5 minutes</li>
                    <li><strong>Status:</strong> Coming Soon</li>
                </ul>
            ''',
            'text-to-speech': '''
                <h4>Speech Generation</h4>
                <p>Convert text to natural-sounding speech audio.</p>
                <ul>
                    <li><strong>Input:</strong> Text to be spoken</li>
                    <li><strong>Output:</strong> Audio file</li>
                    <li><strong>Processing Time:</strong> 30 seconds - 2 minutes</li>
                    <li><strong>Status:</strong> Coming Soon</li>
                </ul>
            ''',
        }
        
        for record in self:
            record.task_description = descriptions.get(record.task_type, '')
    
    def action_next(self):
        """Move to next step"""
        if self.state == 'task_selection':
            self.state = 'input'
        elif self.state == 'input':
            self._validate_input()
            self.state = 'configuration'
        elif self.state == 'configuration':
            self.state = 'review'
        elif self.state == 'review':
            return self.action_submit()
        
        return self._reopen_wizard()
    
    def action_back(self):
        """Move to previous step"""
        if self.state == 'input':
            self.state = 'task_selection'
        elif self.state == 'configuration':
            self.state = 'input'
        elif self.state == 'review':
            self.state = 'configuration'
        
        return self._reopen_wizard()
    
    def action_submit(self):
        """Submit the processing job"""
        self._validate_input()
        
        # Generate job name if not provided
        if not self.job_name:
            task_name = dict(self._fields['task_type'].selection)[self.task_type]
            self.job_name = f"{task_name} - {fields.Datetime.now().strftime('%Y-%m-%d %H:%M')}"
        
        # Create processing job
        processor = self.env['huggingface.ai.processor'].create({
            'name': self.job_name,
            'task_type': self.task_type,
            'priority': self.priority,
            'input_text': self.input_text,
            'input_files': [(6, 0, self.input_files.ids)],
        })
        
        # Submit for processing
        processor.action_submit()
        
        self.state = 'submitted'
        
        return {
            'type': 'ir.actions.act_window',
            'name': 'Processing Job Created',
            'res_model': 'huggingface.ai.processor',
            'res_id': processor.id,
            'view_mode': 'form',
            'target': 'current',
        }
    
    def _validate_input(self):
        """Validate input based on task type"""
        if self.task_type == 'text-to-image':
            if not self.input_text:
                raise UserError(_("Text prompt is required for image generation."))
        elif self.task_type in ['ocr', 'image-to-text']:
            if not self.input_files:
                raise UserError(_("File is required for this task."))
            # Validate file types
            for file in self.input_files:
                if self.task_type == 'ocr':
                    # OCR supports both images and PDFs
                    if not file.mimetype or not (file.mimetype.startswith('image/') or file.mimetype == 'application/pdf'):
                        raise UserError(_("Only image files (JPG, PNG, GIF, BMP, TIFF) and PDF documents are supported for OCR."))
                else:
                    # Image-to-text only supports images
                    if not file.mimetype or not file.mimetype.startswith('image/'):
                        raise UserError(_("Only image files are supported for image description."))
        elif self.task_type in ['speech-to-text']:
            if not self.input_files:
                raise UserError(_("Audio file is required for this task."))
        elif self.task_type == 'text-to-speech':
            if not self.input_text:
                raise UserError(_("Text input is required for speech generation."))
    
    def _reopen_wizard(self):
        """Reopen wizard with current state"""
        return {
            'type': 'ir.actions.act_window',
            'name': 'AI Processing Wizard',
            'res_model': 'huggingface.ai.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
        }