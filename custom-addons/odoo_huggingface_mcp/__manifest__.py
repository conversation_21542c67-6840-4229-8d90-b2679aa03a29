{
    'name': 'Odoo Hugging Face MCP Integration',
    'version': '1.0',
    'category': 'Tools',
    'summary': 'Hugging Face AI integration for Odoo using MCP server',
    'description': '''
This module integrates Hugging Face AI capabilities into Odoo using the MCP server.
Features include:
- Light AI tasks in chatter (chat, sentiment, translation, summarization)
- Heavy AI processing with dedicated interface (OCR, image generation, etc.)
- Progress tracking and queue management
- Wizard-based job creation
- Processing dashboard and analytics
- Multi-tier fallback system (Direct API → Gradio → MCP → LLM)
''',
    'author': 'VPerfectCS',
    'website': 'https://www.vperfectcs.com',
    'depends': [
        'base',
        'mail',
        'web',
        'vpcs_llm_provider_huggingface',
    ],
    'data': [
        'security/ir.model.access.csv',
        'data/huggingface_config_data.xml',
        'data/huggingface_tool_config_data.xml',
        'data/huggingface_task_data.xml',
        'data/system_parameters.xml',
        'views/huggingface_config_views.xml',
        'views/huggingface_task_views.xml',
        'views/huggingface_result_views.xml',
        'views/huggingface_tool_config_views.xml',
        'views/huggingface_ai_processor_views.xml',
        'views/huggingface_ai_wizard_views.xml',
        'views/huggingface_ai_dashboard_views.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'odoo_huggingface_mcp/static/src/js/chatter_integration.js',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
    'icon': 'odoo_huggingface_mcp/static/description/icon.png',
   'external_dependencies': {
       'python': [
           'pydantic-ai',
           'gradio_client'
           ]
    },
}