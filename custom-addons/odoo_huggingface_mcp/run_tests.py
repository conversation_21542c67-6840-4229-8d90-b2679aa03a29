#!/usr/bin/env python3
"""
Test runner for HuggingFace MCP module
Run this script to execute all unit tests
"""

import subprocess
import sys
import os

def run_odoo_tests():
    """Run Odoo unit tests for the HuggingFace MCP module"""
    
    # Get the current directory
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
    
    print("🧪 Running HuggingFace MCP Unit Tests")
    print("=" * 50)
    
    # Test modules to run
    test_modules = [
        'odoo_huggingface_mcp.tests.test_huggingface_config',
        'odoo_huggingface_mcp.tests.test_huggingface_task',
        'odoo_huggingface_mcp.tests.test_huggingface_result',
        'odoo_huggingface_mcp.tests.test_huggingface_provider',
        'odoo_huggingface_mcp.tests.test_controllers',
        'odoo_huggingface_mcp.tests.test_integration',
    ]
    
    # Odoo test command
    odoo_bin = os.path.join(project_root, 'odoo', 'odoo-bin')
    if not os.path.exists(odoo_bin):
        odoo_bin = 'odoo'  # Try system odoo
    
    base_cmd = [
        odoo_bin,
        '--test-enable',
        '--stop-after-init',
        '--log-level=test',
        '--addons-path=' + os.path.dirname(current_dir),
        '-d', 'test_db',  # Use test database
    ]
    
    all_passed = True
    
    for test_module in test_modules:
        print(f"\n🔍 Running {test_module}")
        print("-" * 40)
        
        cmd = base_cmd + ['--test-tags', test_module]
        
        try:
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"✅ {test_module} - PASSED")
            else:
                print(f"❌ {test_module} - FAILED")
                print("STDOUT:", result.stdout[-500:])  # Last 500 chars
                print("STDERR:", result.stderr[-500:])  # Last 500 chars
                all_passed = False
                
        except subprocess.TimeoutExpired:
            print(f"⏰ {test_module} - TIMEOUT")
            all_passed = False
        except Exception as e:
            print(f"💥 {test_module} - ERROR: {e}")
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        return 0
    else:
        print("💥 SOME TESTS FAILED!")
        return 1

def run_python_tests():
    """Run Python unit tests without Odoo"""
    
    print("\n🐍 Running Python Unit Tests (Mock Mode)")
    print("=" * 50)
    
    try:
        import unittest
        
        # Discover and run tests
        loader = unittest.TestLoader()
        start_dir = os.path.join(os.path.dirname(__file__), 'tests')
        suite = loader.discover(start_dir, pattern='test_*.py')
        
        runner = unittest.TextTestRunner(verbosity=2)
        result = runner.run(suite)
        
        if result.wasSuccessful():
            print("✅ Python tests passed!")
            return 0
        else:
            print("❌ Python tests failed!")
            return 1
            
    except Exception as e:
        print(f"💥 Error running Python tests: {e}")
        return 1

def main():
    """Main test runner"""
    
    print("🚀 HuggingFace MCP Test Suite")
    print("=" * 60)
    
    # Check if we should run Odoo tests or Python tests
    if len(sys.argv) > 1 and sys.argv[1] == '--python-only':
        return run_python_tests()
    elif len(sys.argv) > 1 and sys.argv[1] == '--odoo-only':
        return run_odoo_tests()
    else:
        # Run both
        print("Running both Odoo and Python tests...")
        
        odoo_result = run_odoo_tests()
        python_result = run_python_tests()
        
        if odoo_result == 0 and python_result == 0:
            print("\n🎉 ALL TESTS PASSED!")
            return 0
        else:
            print("\n💥 SOME TESTS FAILED!")
            return 1

if __name__ == "__main__":
    sys.exit(main())