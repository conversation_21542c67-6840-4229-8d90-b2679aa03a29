from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json


class TestHuggingFaceIntegration(TransactionCase):
    """Integration tests for the complete HuggingFace MCP workflow"""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create test LLM providers
        cls.llm_provider = cls.env['llm.provider'].create({
            'name': 'Test OpenAI Provider',
            'provider_type': 'openai',
            'model': 'gpt-3.5-turbo',
            'api_key': 'test-key',
        })
        
        cls.hf_provider = cls.env['llm.provider'].create({
            'name': 'Test HF Provider',
            'provider_type': 'huggingface',
            'model': 'Qwen/Qwen2.5-72B-Instruct',
            'api_key': 'hf-test-key',
            'base_url': 'https://router.huggingface.co/hf-inference/models/Qwen/Qwen2.5-72B-Instruct',
        })
        
        # Create test task configurations
        cls.sentiment_task = cls.env['huggingface.task'].create({
            'name': 'Sentiment Analysis',
            'task_type': 'sentiment-analysis',
            'mcp_tool_name': 'hf_sentiment',
            'is_active': True,
            'parameters': json.dumps({'model': 'cardiffnlp/twitter-roberta-base-sentiment'}),
        })
        
        cls.translation_task = cls.env['huggingface.task'].create({
            'name': 'Translation',
            'task_type': 'translation',
            'mcp_tool_name': 'hf_translate',
            'is_active': True,
            'parameters': json.dumps({'model': 'Helsinki-NLP/opus-mt-en-fr'}),
        })

    def setUp(self):
        super().setUp()
        self.hf_config = self.env['huggingface.config'].create({
            'name': 'Test Integration Config',
            'llm_provider_id': self.llm_provider.id,
            'huggingface_provider_id': self.hf_provider.id,
            'huggingface_api_key': 'test-hf-key',
            'use_direct_inference': True,
            'enable_mcp_fallback': True,
            'enable_llm_fallback': True,
            'is_active': True,
        })

    @patch.object(type(env['llm.provider']), 'generate')
    def test_direct_inference_workflow(self, mock_generate):
        """Test complete direct inference workflow"""
        # Mock successful direct inference
        mock_generate.return_value = {'generated_text': 'The sentiment is positive with high confidence.'}
        
        result = self.hf_config.generate_task('I love this product!', 'sentiment-analysis')
        
        self.assertEqual(result, 'The sentiment is positive with high confidence.')
        mock_generate.assert_called_once()

    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_direct_inference')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_mcp')
    def test_fallback_to_mcp_workflow(self, mock_mcp, mock_direct):
        """Test fallback to MCP when direct inference fails"""
        # Direct inference fails
        mock_direct.side_effect = Exception("Direct inference failed")
        # MCP succeeds
        mock_mcp.return_value = "Sentiment: POSITIVE (confidence: 0.95)"
        
        result = self.hf_config.generate_task('I love this product!', 'sentiment-analysis')
        
        self.assertEqual(result, "Sentiment: POSITIVE (confidence: 0.95)")
        mock_direct.assert_called_once()
        mock_mcp.assert_called_once()

    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_direct_inference')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_mcp')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_llm_fallback')
    def test_complete_fallback_chain(self, mock_llm, mock_mcp, mock_direct):
        """Test complete fallback chain workflow"""
        # All methods called in sequence
        mock_direct.side_effect = Exception("Direct failed")
        mock_mcp.side_effect = Exception("MCP failed")
        mock_llm.return_value = "Sentiment: POSITIVE (confidence: 0.90)"
        
        result = self.hf_config.generate_task('I love this product!', 'sentiment-analysis')
        
        self.assertEqual(result, "Sentiment: POSITIVE (confidence: 0.90)")
        mock_direct.assert_called_once()
        mock_mcp.assert_called_once()
        mock_llm.assert_called_once()

    def test_result_storage_workflow(self):
        """Test that results are properly stored"""
        with patch.object(self.hf_config, '_execute_direct_inference') as mock_direct:
            mock_direct.return_value = "Sentiment: POSITIVE (confidence: 0.95)"
            
            # Execute task with result storage
            result = self.hf_config.generate_task('I love this product!', 'sentiment-analysis', store_result=True)
            
            # Check result was stored
            stored_result = self.env['huggingface.result'].search([
                ('input_text', '=', 'I love this product!'),
                ('task_type', '=', 'sentiment-analysis'),
                ('config_id', '=', self.hf_config.id)
            ], limit=1)
            
            self.assertTrue(stored_result)
            self.assertEqual(stored_result.state, 'completed')
            self.assertEqual(stored_result.result_text, "Sentiment: POSITIVE (confidence: 0.95)")
            self.assertGreater(stored_result.processing_time, 0)

    def test_translation_with_options_workflow(self):
        """Test translation workflow with language options"""
        with patch.object(self.hf_config, '_execute_direct_inference') as mock_direct:
            mock_direct.return_value = "Bonjour le monde"
            
            result = self.hf_config.generate_task(
                'Hello world', 
                'translation', 
                options={'target_language': 'fr'}
            )
            
            self.assertEqual(result, "Bonjour le monde")
            mock_direct.assert_called_once_with(
                'translation', 'Hello world', {'target_language': 'fr'}
            )

    def test_file_processing_workflow(self):
        """Test file processing workflow"""
        import base64
        
        fake_image = b"fake image data"
        image_b64 = base64.b64encode(fake_image).decode('utf-8')
        
        with patch.object(self.hf_config, '_execute_direct_inference_with_file') as mock_direct:
            mock_direct.return_value = "Extracted text: Hello World"
            
            result = self.hf_config.generate_task_with_file(
                'Extract text', 'ocr', image_b64, 'test.jpg', 'image/jpeg'
            )
            
            self.assertEqual(result, "Extracted text: Hello World")

    def test_configuration_validation_workflow(self):
        """Test configuration validation workflow"""
        # Test with missing HF provider
        config_no_hf = self.env['huggingface.config'].create({
            'name': 'Config No HF',
            'llm_provider_id': self.llm_provider.id,
            'use_direct_inference': True,
            'is_active': False,
        })
        
        with self.assertRaises(Exception):
            config_no_hf.generate_task('Test', 'sentiment-analysis')

    def test_multiple_task_types_workflow(self):
        """Test workflow with multiple task types"""
        tasks = [
            ('I love this!', 'sentiment-analysis', 'Positive sentiment'),
            ('Hello world', 'translation', 'Bonjour le monde'),
            ('Long text here...', 'summarization', 'Short summary'),
        ]
        
        with patch.object(self.hf_config, '_execute_direct_inference') as mock_direct:
            for prompt, task_type, expected in tasks:
                mock_direct.return_value = expected
                result = self.hf_config.generate_task(prompt, task_type)
                self.assertEqual(result, expected)

    def test_error_handling_workflow(self):
        """Test error handling throughout the workflow"""
        # Test with invalid task type
        with self.assertRaises(Exception):
            self.hf_config.generate_task('Test', 'invalid-task-type')
        
        # Test with all methods disabled
        self.hf_config.use_direct_inference = False
        self.hf_config.enable_mcp_fallback = False
        self.hf_config.enable_llm_fallback = False
        
        with self.assertRaises(Exception):
            self.hf_config.generate_task('Test', 'sentiment-analysis')

    def test_performance_tracking_workflow(self):
        """Test performance tracking throughout workflow"""
        with patch.object(self.hf_config, '_execute_direct_inference') as mock_direct:
            mock_direct.return_value = "Test result"
            
            import time
            start_time = time.time()
            
            result = self.hf_config.generate_task('Test', 'sentiment-analysis')
            
            end_time = time.time()
            
            # Check that result was returned
            self.assertEqual(result, "Test result")
            
            # Check that processing time was reasonable
            processing_time = end_time - start_time
            self.assertLess(processing_time, 1.0)  # Should be fast with mocking

    def test_concurrent_requests_workflow(self):
        """Test handling of concurrent requests"""
        import threading
        import time
        
        results = []
        errors = []
        
        def execute_task(prompt):
            try:
                with patch.object(self.hf_config, '_execute_direct_inference') as mock_direct:
                    mock_direct.return_value = f"Result for: {prompt}"
                    result = self.hf_config.generate_task(prompt, 'sentiment-analysis')
                    results.append(result)
            except Exception as e:
                errors.append(str(e))
        
        # Create multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=execute_task, args=(f"Test prompt {i}",))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Check results
        self.assertEqual(len(results), 5)
        self.assertEqual(len(errors), 0)

    def test_configuration_switching_workflow(self):
        """Test switching between configurations"""
        # Create second configuration
        config2 = self.env['huggingface.config'].create({
            'name': 'Second Config',
            'llm_provider_id': self.llm_provider.id,
            'is_active': True,  # This should deactivate the first config
        })
        
        # Check that first config is deactivated
        self.hf_config.refresh()
        self.assertFalse(self.hf_config.is_active)
        self.assertTrue(config2.is_active)