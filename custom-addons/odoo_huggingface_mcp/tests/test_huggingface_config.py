from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
from unittest.mock import patch, MagicMock
import json


class TestHuggingFaceConfig(TransactionCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create test LLM providers
        cls.llm_provider = cls.env['llm.provider'].create({
            'name': 'Test OpenAI Provider',
            'provider_type': 'openai',
            'model': 'gpt-3.5-turbo',
            'api_key': 'test-key',
            'base_url': 'https://api.openai.com/v1',
        })
        
        cls.hf_provider = cls.env['llm.provider'].create({
            'name': 'Test HF Provider',
            'provider_type': 'huggingface',
            'model': 'Qwen/Qwen2.5-72B-Instruct',
            'api_key': 'hf-test-key',
            'base_url': 'https://router.huggingface.co/hf-inference/models/Qwen/Qwen2.5-72B-Instruct',
        })
        
        # Create test task configuration
        cls.task_config = cls.env['huggingface.task'].create({
            'name': 'Test Sentiment Analysis',
            'task_type': 'sentiment-analysis',
            'mcp_tool_name': 'hf_sentiment',
            'is_active': True,
            'parameters': json.dumps({'model': 'cardiffnlp/twitter-roberta-base-sentiment'}),
        })

    def setUp(self):
        super().setUp()
        self.hf_config = self.env['huggingface.config'].create({
            'name': 'Test HF Config',
            'llm_provider_id': self.llm_provider.id,
            'huggingface_provider_id': self.hf_provider.id,
            'huggingface_api_key': 'test-hf-key',
            'use_direct_inference': True,
            'enable_mcp_fallback': True,
            'enable_llm_fallback': True,
            'is_active': True,
        })

    def test_config_creation(self):
        """Test HuggingFace configuration creation"""
        self.assertTrue(self.hf_config.id)
        self.assertEqual(self.hf_config.name, 'Test HF Config')
        self.assertTrue(self.hf_config.use_direct_inference)
        self.assertTrue(self.hf_config.enable_mcp_fallback)
        self.assertTrue(self.hf_config.enable_llm_fallback)

    def test_only_one_active_config(self):
        """Test that only one config can be active per company"""
        # Create second config
        config2 = self.env['huggingface.config'].create({
            'name': 'Test HF Config 2',
            'llm_provider_id': self.llm_provider.id,
            'is_active': True,
        })
        
        # First config should be deactivated
        self.hf_config.refresh()
        self.assertFalse(self.hf_config.is_active)
        self.assertTrue(config2.is_active)

    def test_get_model_endpoint(self):
        """Test model endpoint retrieval"""
        endpoint = self.hf_config._get_model_endpoint('sentiment-analysis')
        expected = 'https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment'
        self.assertEqual(endpoint, expected)

    def test_get_model_endpoint_translation(self):
        """Test translation endpoint with language parameter"""
        endpoint = self.hf_config._get_model_endpoint('translation', {'target_language': 'fr'})
        expected = 'https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-fr'
        self.assertEqual(endpoint, expected)

    def test_get_model_endpoint_invalid_task(self):
        """Test invalid task type raises error"""
        with self.assertRaises(UserError):
            self.hf_config._get_model_endpoint('invalid-task')

    def test_build_generic_task_prompt(self):
        """Test generic task prompt building"""
        prompt = self.hf_config._build_generic_task_prompt('sentiment-analysis', 'I love this!')
        expected = "Analyze the sentiment of this text and respond with only 'positive', 'negative', or 'neutral' followed by a confidence score: I love this!"
        self.assertEqual(prompt, expected)

    def test_build_generic_task_prompt_translation(self):
        """Test translation prompt building"""
        prompt = self.hf_config._build_generic_task_prompt('translation', 'Hello', {'target_language': 'Spanish'})
        expected = "Translate this text to Spanish: Hello"
        self.assertEqual(prompt, expected)

    def test_process_inference_result_sentiment(self):
        """Test sentiment analysis result processing"""
        result = [{'label': 'POSITIVE', 'score': 0.95}]
        processed = self.hf_config._process_inference_result(result, 'sentiment-analysis')
        expected = "Sentiment: POSITIVE (confidence: 0.95)"
        self.assertEqual(processed, expected)

    def test_process_inference_result_translation(self):
        """Test translation result processing"""
        result = [{'translation_text': 'Bonjour'}]
        processed = self.hf_config._process_inference_result(result, 'translation')
        self.assertEqual(processed, 'Bonjour')

    def test_process_generic_result(self):
        """Test generic result processing"""
        # Test list response
        result = [{'generated_text': 'This is positive sentiment'}]
        processed = self.hf_config._process_generic_result(result, 'sentiment-analysis')
        self.assertEqual(processed, 'This is positive sentiment')
        
        # Test dict response
        result = {'generated_text': 'Positive sentiment detected'}
        processed = self.hf_config._process_generic_result(result, 'sentiment-analysis')
        self.assertEqual(processed, 'Positive sentiment detected')
        
        # Test string response
        result = 'Direct string response'
        processed = self.hf_config._process_generic_result(result, 'sentiment-analysis')
        self.assertEqual(processed, 'Direct string response')

    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._get_mcp_client')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.asyncio')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.Agent')
    def test_execute_mcp_success(self, mock_agent, mock_asyncio, mock_client):
        """Test successful MCP execution"""
        # Mock MCP client and agent
        mock_client_instance = MagicMock()
        mock_client.return_value = mock_client_instance
        mock_asyncio.run.return_value = [MagicMock()]
        
        mock_agent_instance = MagicMock()
        mock_agent.return_value = mock_agent_instance
        mock_agent_instance.run_sync.return_value.output = "Sentiment: POSITIVE (confidence: 0.95)"
        
        result = self.hf_config._execute_mcp('sentiment-analysis', 'I love this!')
        self.assertEqual(result, "Sentiment: POSITIVE (confidence: 0.95)")

    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._get_mcp_client')
    def test_execute_mcp_no_provider(self, mock_client):
        """Test MCP execution without LLM provider"""
        self.hf_config.llm_provider_id = False
        
        with self.assertRaises(UserError) as cm:
            self.hf_config._execute_mcp('sentiment-analysis', 'test')
        
        self.assertIn('Agent LLM Provider not configured', str(cm.exception))

    @patch.object(type(env['llm.provider']), 'generate')
    def test_execute_direct_inference_generic(self, mock_generate):
        """Test direct inference with generic model"""
        mock_generate.return_value = {'generated_text': 'Positive sentiment detected'}
        
        result = self.hf_config._execute_direct_inference('sentiment-analysis', 'I love this!')
        self.assertEqual(result, 'Positive sentiment detected')

    @patch.object(type(env['llm.provider']), 'generate')
    def test_execute_direct_inference_specific(self, mock_generate):
        """Test direct inference with task-specific model"""
        # Remove base_url to trigger task-specific endpoint
        self.hf_config.huggingface_provider_id.base_url = False
        mock_generate.return_value = [{'label': 'POSITIVE', 'score': 0.95}]
        
        result = self.hf_config._execute_direct_inference('sentiment-analysis', 'I love this!')
        self.assertEqual(result, 'Sentiment: POSITIVE (confidence: 0.95)')

    def test_execute_direct_inference_no_provider(self):
        """Test direct inference without HF provider"""
        self.hf_config.huggingface_provider_id = False
        
        with self.assertRaises(UserError) as cm:
            self.hf_config._execute_direct_inference('sentiment-analysis', 'test')
        
        self.assertIn('HuggingFace provider not configured', str(cm.exception))

    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_direct_inference')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_mcp')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_llm_fallback')
    def test_generate_task_direct_inference_success(self, mock_llm, mock_mcp, mock_direct):
        """Test task generation with successful direct inference"""
        mock_direct.return_value = "Sentiment: POSITIVE (confidence: 0.95)"
        
        result = self.hf_config.generate_task('I love this!', 'sentiment-analysis')
        
        self.assertEqual(result, "Sentiment: POSITIVE (confidence: 0.95)")
        mock_direct.assert_called_once()
        mock_mcp.assert_not_called()
        mock_llm.assert_not_called()

    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_direct_inference')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_mcp')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_llm_fallback')
    def test_generate_task_fallback_chain(self, mock_llm, mock_mcp, mock_direct):
        """Test task generation fallback chain"""
        # Direct inference fails
        mock_direct.side_effect = Exception("Direct inference failed")
        # MCP fails
        mock_mcp.side_effect = Exception("MCP failed")
        # LLM succeeds
        mock_llm.return_value = "Sentiment: POSITIVE (confidence: 0.90)"
        
        result = self.hf_config.generate_task('I love this!', 'sentiment-analysis')
        
        self.assertEqual(result, "Sentiment: POSITIVE (confidence: 0.90)")
        mock_direct.assert_called_once()
        mock_mcp.assert_called_once()
        mock_llm.assert_called_once()

    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_direct_inference')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_mcp')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_llm_fallback')
    def test_generate_task_all_methods_fail(self, mock_llm, mock_mcp, mock_direct):
        """Test task generation when all methods fail"""
        mock_direct.side_effect = Exception("Direct inference failed")
        mock_mcp.side_effect = Exception("MCP failed")
        mock_llm.side_effect = Exception("LLM failed")
        
        with self.assertRaises(UserError) as cm:
            self.hf_config.generate_task('I love this!', 'sentiment-analysis')
        
        self.assertIn('All methods failed', str(cm.exception))

    def test_generate_task_disabled_methods(self):
        """Test task generation with disabled methods"""
        self.hf_config.use_direct_inference = False
        self.hf_config.enable_mcp_fallback = False
        self.hf_config.enable_llm_fallback = False
        
        with self.assertRaises(UserError) as cm:
            self.hf_config.generate_task('I love this!', 'sentiment-analysis')
        
        self.assertIn('All methods failed', str(cm.exception))

    @patch('base64.b64decode')
    @patch('odoo.addons.odoo_huggingface_mcp.models.huggingface_config.HuggingFaceConfig._execute_direct_inference_with_file')
    def test_generate_task_with_file_success(self, mock_direct_file, mock_b64decode):
        """Test file task generation with direct inference"""
        mock_b64decode.return_value = b'fake_image_data'
        mock_direct_file.return_value = "Extracted text: Hello World"
        
        result = self.hf_config.generate_task_with_file(
            'Extract text', 'ocr', 'base64_encoded_image', 'test.jpg', 'image/jpeg'
        )
        
        self.assertEqual(result, "Extracted text: Hello World")

    def test_generate_task_with_file_no_content(self):
        """Test file task generation without file content"""
        with self.assertRaises(UserError) as cm:
            self.hf_config.generate_task_with_file('Extract text', 'ocr')
        
        self.assertIn('File content is required', str(cm.exception))

    def test_get_task_config_success(self):
        """Test successful task configuration retrieval"""
        config = self.hf_config._get_task_config('sentiment-analysis')
        self.assertEqual(config, self.task_config)

    def test_get_task_config_not_found(self):
        """Test task configuration not found"""
        with self.assertRaises(UserError) as cm:
            self.hf_config._get_task_config('non-existent-task')
        
        self.assertIn('No active task configuration found', str(cm.exception))

    def test_build_task_prompt_sentiment(self):
        """Test MCP task prompt building for sentiment analysis"""
        prompt = self.hf_config._build_task_prompt(self.task_config, 'I love this!')
        self.assertIn('hf_sentiment', prompt)
        self.assertIn('I love this!', prompt)

    def test_build_task_prompt_fallback(self):
        """Test fallback task prompt building"""
        prompt = self.hf_config._build_task_prompt(self.task_config, 'I love this!', use_fallback=True)
        self.assertIn('positive/negative/neutral', prompt)
        self.assertIn('I love this!', prompt)

    def test_pydantic_ai_model_string(self):
        """Test Pydantic AI model string construction"""
        model_string = self.hf_config._get_pydantic_ai_model_string()
        self.assertEqual(model_string, 'openai:gpt-3.5-turbo')

    def test_generate_response(self):
        """Test generate response method"""
        self.hf_config.prompt_text = "Test prompt"
        
        with patch.object(self.hf_config, 'generate_task') as mock_generate:
            mock_generate.return_value = "Test response"
            
            self.hf_config.generate_response()
            
            self.assertEqual(self.hf_config.result_text, "Test response")
            mock_generate.assert_called_once_with("Test prompt", "Respond to the user's query.")

    def test_generate_response_no_prompt(self):
        """Test generate response without prompt"""
        self.hf_config.prompt_text = ""
        
        with self.assertRaises(UserError) as cm:
            self.hf_config.generate_response()
        
        self.assertIn('Please enter a prompt', str(cm.exception))