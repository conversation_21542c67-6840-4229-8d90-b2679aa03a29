from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
from unittest.mock import patch, MagicMock


class TestHuggingFaceProvider(TransactionCase):
    """Test cases for HuggingFace provider integration"""

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create test LLM provider
        cls.hf_provider = cls.env['llm.provider'].create({
            'name': 'Test HF Provider',
            'provider_type': 'huggingface',
            'model': 'test-model',
            'api_key': 'test-key',
            'base_url': 'https://api-inference.huggingface.co/models/test-model',
        })

    @patch('requests.post')
    def test_huggingface_provider_generate_success(self, mock_post):
        """Test successful HuggingFace provider generation"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'content-type': 'application/json'}
        mock_response.json.return_value = [{'generated_text': 'Test response'}]
        mock_post.return_value = mock_response
        
        result = self.hf_provider.generate('Test prompt')
        
        self.assertEqual(result, [{'generated_text': 'Test response'}])
        mock_post.assert_called_once()

    @patch('requests.post')
    def test_huggingface_provider_generate_error(self, mock_post):
        """Test HuggingFace provider generation error"""
        # Mock error response
        mock_post.side_effect = Exception('API Error')
        
        with self.assertRaises(UserError) as cm:
            self.hf_provider.generate('Test prompt')
        
        self.assertIn('Failed to generate with Hugging Face Inference API', str(cm.exception))

    @patch('requests.post')
    def test_huggingface_provider_binary_data(self, mock_post):
        """Test HuggingFace provider with binary data"""
        # Mock binary response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.headers = {'content-type': 'image/png'}
        mock_response.content = b'fake_image_data'
        mock_post.return_value = mock_response
        
        result = self.hf_provider.generate(b'binary_input')
        
        self.assertEqual(result, b'fake_image_data')

    def test_huggingface_provider_no_base_url(self):
        """Test HuggingFace provider without base URL"""
        self.hf_provider.base_url = False
        
        with self.assertRaises(UserError) as cm:
            self.hf_provider.generate('Test prompt')
        
        self.assertIn('Hugging Face Inference API URL is not configured', str(cm.exception))

    def test_huggingface_provider_test_connection_success(self):
        """Test successful connection test"""
        with patch.object(self.hf_provider, 'generate') as mock_generate:
            mock_generate.return_value = 'Connection successful'
            
            result = self.hf_provider.action_test_connection()
            
            self.assertEqual(result['params']['type'], 'success')
            self.hf_provider.refresh()
            self.assertEqual(self.hf_provider.state, 'ready')

    def test_huggingface_provider_test_connection_error(self):
        """Test connection test error"""
        with patch.object(self.hf_provider, 'generate') as mock_generate:
            mock_generate.side_effect = Exception('Connection failed')
            
            result = self.hf_provider.action_test_connection()
            
            self.assertEqual(result['params']['type'], 'danger')
            self.hf_provider.refresh()
            self.assertEqual(self.hf_provider.state, 'error')