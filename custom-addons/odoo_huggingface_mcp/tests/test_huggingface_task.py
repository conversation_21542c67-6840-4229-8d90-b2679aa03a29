from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
import json


class TestHuggingFaceTask(TransactionCase):

    def setUp(self):
        super().setUp()
        self.task = self.env['huggingface.task'].create({
            'name': 'Test Sentiment Task',
            'task_type': 'sentiment-analysis',
            'mcp_tool_name': 'hf_sentiment',
            'description': 'Test sentiment analysis task',
            'parameters': json.dumps({
                'model': 'cardiffnlp/twitter-roberta-base-sentiment',
                'return_all_scores': False
            }),
            'is_active': True,
        })

    def test_task_creation(self):
        """Test task creation"""
        self.assertTrue(self.task.id)
        self.assertEqual(self.task.name, 'Test Sentiment Task')
        self.assertEqual(self.task.task_type, 'sentiment-analysis')
        self.assertTrue(self.task.is_active)

    def test_get_parameters_dict(self):
        """Test parameters dictionary retrieval"""
        params = self.task.get_parameters_dict()
        expected = {
            'model': 'cardiffnlp/twitter-roberta-base-sentiment',
            'return_all_scores': False
        }
        self.assertEqual(params, expected)

    def test_get_parameters_dict_empty(self):
        """Test parameters dictionary with empty parameters"""
        self.task.parameters = ""
        params = self.task.get_parameters_dict()
        self.assertEqual(params, {})

    def test_get_parameters_dict_invalid_json(self):
        """Test parameters dictionary with invalid JSON"""
        self.task.parameters = "invalid json"
        params = self.task.get_parameters_dict()
        self.assertEqual(params, {})

    def test_task_type_selection(self):
        """Test task type selection values"""
        task_types = dict(self.task._fields['task_type'].selection)
        expected_types = [
            'sentiment-analysis',
            'translation',
            'summarization',
            'text-to-image',
            'image-to-text',
            'ocr',
            'chat'
        ]
        
        for task_type in expected_types:
            self.assertIn(task_type, task_types)

    def test_multiple_tasks_same_type(self):
        """Test multiple tasks of same type"""
        task2 = self.env['huggingface.task'].create({
            'name': 'Another Sentiment Task',
            'task_type': 'sentiment-analysis',
            'mcp_tool_name': 'hf_sentiment_v2',
            'is_active': False,
        })
        
        self.assertTrue(task2.id)
        self.assertEqual(task2.task_type, 'sentiment-analysis')
        self.assertFalse(task2.is_active)

    def test_task_search_by_type(self):
        """Test searching tasks by type"""
        tasks = self.env['huggingface.task'].search([
            ('task_type', '=', 'sentiment-analysis'),
            ('is_active', '=', True)
        ])
        
        self.assertIn(self.task, tasks)

    def test_task_parameters_update(self):
        """Test updating task parameters"""
        new_params = {
            'model': 'new-model',
            'threshold': 0.8
        }
        self.task.parameters = json.dumps(new_params)
        
        params = self.task.get_parameters_dict()
        self.assertEqual(params, new_params)