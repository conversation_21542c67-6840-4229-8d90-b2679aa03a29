from odoo.tests.common import HttpCase
from odoo.exceptions import UserError
from unittest.mock import patch
import json
import base64


class TestHuggingFaceControllers(HttpCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create test LLM provider
        cls.llm_provider = cls.env['llm.provider'].create({
            'name': 'Test Provider',
            'provider_type': 'openai',
            'model': 'gpt-3.5-turbo',
            'api_key': 'test-key',
        })
        
        # Create test HF config
        cls.hf_config = cls.env['huggingface.config'].create({
            'name': 'Test Config',
            'llm_provider_id': cls.llm_provider.id,
            'is_active': True,
        })

    def test_execute_task_endpoint(self):
        """Test execute task HTTP endpoint"""
        with patch.object(self.hf_config, 'generate_task') as mock_generate:
            mock_generate.return_value = "Sentiment: POSITIVE (confidence: 0.95)"
            
            response = self.url_open(
                '/odoo_huggingface_mcp/execute_task',
                data=json.dumps({
                    'prompt': 'I love this!',
                    'task_type': 'sentiment-analysis',
                    'options': {}
                }),
                headers={'Content-Type': 'application/json'}
            )
            
            self.assertEqual(response.status_code, 200)
            result = json.loads(response.content.decode())
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['result'], "Sentiment: POSITIVE (confidence: 0.95)")

    def test_execute_task_with_file_endpoint(self):
        """Test execute task with file HTTP endpoint"""
        with patch.object(self.hf_config, 'generate_task_with_file') as mock_generate:
            mock_generate.return_value = "Extracted text: Hello World"
            
            # Create fake image data
            fake_image = b"fake image data"
            image_b64 = base64.b64encode(fake_image).decode('utf-8')
            
            response = self.url_open(
                '/odoo_huggingface_mcp/execute_task_with_file',
                data=json.dumps({
                    'prompt': 'Extract text',
                    'task_type': 'ocr',
                    'file_content': image_b64,
                    'filename': 'test.jpg',
                    'mimetype': 'image/jpeg',
                    'options': {}
                }),
                headers={'Content-Type': 'application/json'}
            )
            
            self.assertEqual(response.status_code, 200)
            result = json.loads(response.content.decode())
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['result'], "Extracted text: Hello World")

    def test_execute_task_missing_parameters(self):
        """Test execute task with missing parameters"""
        response = self.url_open(
            '/odoo_huggingface_mcp/execute_task',
            data=json.dumps({
                'prompt': 'I love this!',
                # Missing task_type
            }),
            headers={'Content-Type': 'application/json'}
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.content.decode())
        self.assertEqual(result['status'], 'error')
        self.assertIn('Missing required parameter', result['message'])

    def test_execute_task_invalid_json(self):
        """Test execute task with invalid JSON"""
        response = self.url_open(
            '/odoo_huggingface_mcp/execute_task',
            data='invalid json',
            headers={'Content-Type': 'application/json'}
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.content.decode())
        self.assertEqual(result['status'], 'error')
        self.assertIn('Invalid JSON', result['message'])

    def test_execute_task_no_active_config(self):
        """Test execute task with no active configuration"""
        # Deactivate config
        self.hf_config.is_active = False
        
        response = self.url_open(
            '/odoo_huggingface_mcp/execute_task',
            data=json.dumps({
                'prompt': 'I love this!',
                'task_type': 'sentiment-analysis',
            }),
            headers={'Content-Type': 'application/json'}
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.content.decode())
        self.assertEqual(result['status'], 'error')
        self.assertIn('No active HuggingFace configuration', result['message'])

    def test_execute_task_generation_error(self):
        """Test execute task when generation fails"""
        with patch.object(self.hf_config, 'generate_task') as mock_generate:
            mock_generate.side_effect = UserError("Generation failed")
            
            response = self.url_open(
                '/odoo_huggingface_mcp/execute_task',
                data=json.dumps({
                    'prompt': 'I love this!',
                    'task_type': 'sentiment-analysis',
                }),
                headers={'Content-Type': 'application/json'}
            )
            
            self.assertEqual(response.status_code, 500)
            result = json.loads(response.content.decode())
            self.assertEqual(result['status'], 'error')
            self.assertIn('Generation failed', result['message'])

    def test_execute_task_with_options(self):
        """Test execute task with additional options"""
        with patch.object(self.hf_config, 'generate_task') as mock_generate:
            mock_generate.return_value = "Bonjour le monde"
            
            response = self.url_open(
                '/odoo_huggingface_mcp/execute_task',
                data=json.dumps({
                    'prompt': 'Hello world',
                    'task_type': 'translation',
                    'options': {'target_language': 'fr'}
                }),
                headers={'Content-Type': 'application/json'}
            )
            
            self.assertEqual(response.status_code, 200)
            result = json.loads(response.content.decode())
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['result'], "Bonjour le monde")
            
            # Verify options were passed
            mock_generate.assert_called_once_with(
                'Hello world', 'translation', {'target_language': 'fr'}, True
            )

    def test_execute_task_get_method_not_allowed(self):
        """Test that GET method is not allowed"""
        response = self.url_open('/odoo_huggingface_mcp/execute_task')
        self.assertEqual(response.status_code, 405)  # Method Not Allowed

    def test_execute_task_with_file_missing_file_content(self):
        """Test execute task with file but missing file content"""
        response = self.url_open(
            '/odoo_huggingface_mcp/execute_task_with_file',
            data=json.dumps({
                'prompt': 'Extract text',
                'task_type': 'ocr',
                'filename': 'test.jpg',
                # Missing file_content
            }),
            headers={'Content-Type': 'application/json'}
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.content.decode())
        self.assertEqual(result['status'], 'error')
        self.assertIn('Missing required parameter', result['message'])

    def test_execute_task_with_file_invalid_base64(self):
        """Test execute task with file but invalid base64"""
        response = self.url_open(
            '/odoo_huggingface_mcp/execute_task_with_file',
            data=json.dumps({
                'prompt': 'Extract text',
                'task_type': 'ocr',
                'file_content': 'invalid_base64!@#',
                'filename': 'test.jpg',
            }),
            headers={'Content-Type': 'application/json'}
        )
        
        self.assertEqual(response.status_code, 400)
        result = json.loads(response.content.decode())
        self.assertEqual(result['status'], 'error')
        self.assertIn('Invalid base64', result['message'])

    def test_cors_headers(self):
        """Test CORS headers are properly set"""
        response = self.url_open(
            '/odoo_huggingface_mcp/execute_task',
            data=json.dumps({
                'prompt': 'I love this!',
                'task_type': 'sentiment-analysis',
            }),
            headers={'Content-Type': 'application/json'}
        )
        
        # Check CORS headers
        self.assertIn('Access-Control-Allow-Origin', response.headers)
        self.assertIn('Access-Control-Allow-Methods', response.headers)
        self.assertIn('Access-Control-Allow-Headers', response.headers)