from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError
import base64
import json


class TestHuggingFaceResult(TransactionCase):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        
        # Create test LLM provider
        cls.llm_provider = cls.env['llm.provider'].create({
            'name': 'Test Provider',
            'provider_type': 'openai',
            'model': 'gpt-3.5-turbo',
            'api_key': 'test-key',
        })
        
        # Create test HF config
        cls.hf_config = cls.env['huggingface.config'].create({
            'name': 'Test Config',
            'llm_provider_id': cls.llm_provider.id,
            'is_active': True,
        })

    def setUp(self):
        super().setUp()
        self.result = self.env['huggingface.result'].create({
            'task_type': 'sentiment-analysis',
            'input_text': 'I love this product!',
            'config_id': self.hf_config.id,
            'state': 'pending',
        })

    def test_result_creation(self):
        """Test result record creation"""
        self.assertTrue(self.result.id)
        self.assertEqual(self.result.task_type, 'sentiment-analysis')
        self.assertEqual(self.result.input_text, 'I love this product!')
        self.assertEqual(self.result.state, 'pending')

    def test_create_result_class_method(self):
        """Test create_result class method"""
        result = self.env['huggingface.result'].create_result(
            'translation', 'Hello world', self.hf_config.id
        )
        
        self.assertEqual(result.task_type, 'translation')
        self.assertEqual(result.input_text, 'Hello world')
        self.assertEqual(result.config_id, self.hf_config)
        self.assertEqual(result.state, 'pending')

    def test_mark_completed(self):
        """Test marking result as completed"""
        self.result.mark_completed(
            result_text="Sentiment: POSITIVE (confidence: 0.95)",
            processing_time=2.5,
            raw_data={'method': 'direct_inference'}
        )
        
        self.assertEqual(self.result.state, 'completed')
        self.assertEqual(self.result.result_text, "Sentiment: POSITIVE (confidence: 0.95)")
        self.assertEqual(self.result.processing_time, 2.5)
        self.assertEqual(self.result.raw_data, "{'method': 'direct_inference'}")

    def test_mark_failed(self):
        """Test marking result as failed"""
        error_msg = "API connection failed"
        self.result.mark_failed(error_msg)
        
        self.assertEqual(self.result.state, 'failed')
        self.assertEqual(self.result.error_message, error_msg)

    def test_store_file_result(self):
        """Test storing file result"""
        file_data = b"fake image data"
        filename = "test.jpg"
        mimetype = "image/jpeg"
        
        self.result.store_file_result(file_data, filename, mimetype)
        
        self.assertEqual(self.result.file_name, filename)
        self.assertEqual(self.result.file_mimetype, mimetype)
        self.assertEqual(self.result.file_content, base64.b64encode(file_data).decode('utf-8'))

    def test_get_file_data(self):
        """Test retrieving file data"""
        file_data = b"fake image data"
        self.result.file_content = base64.b64encode(file_data).decode('utf-8')
        
        retrieved_data = self.result.get_file_data()
        self.assertEqual(retrieved_data, file_data)

    def test_get_file_data_no_content(self):
        """Test retrieving file data when no content exists"""
        retrieved_data = self.result.get_file_data()
        self.assertIsNone(retrieved_data)

    def test_result_state_selection(self):
        """Test result state selection values"""
        states = dict(self.result._fields['state'].selection)
        expected_states = ['pending', 'completed', 'failed']
        
        for state in expected_states:
            self.assertIn(state, states)

    def test_task_type_selection(self):
        """Test task type selection values"""
        task_types = dict(self.result._fields['task_type'].selection)
        expected_types = [
            'sentiment-analysis',
            'translation',
            'summarization',
            'text-to-image',
            'image-to-text',
            'ocr',
            'chat'
        ]
        
        for task_type in expected_types:
            self.assertIn(task_type, task_types)

    def test_result_search_by_state(self):
        """Test searching results by state"""
        # Create completed result
        completed_result = self.env['huggingface.result'].create({
            'task_type': 'translation',
            'input_text': 'Hello',
            'config_id': self.hf_config.id,
            'state': 'completed',
        })
        
        pending_results = self.env['huggingface.result'].search([
            ('state', '=', 'pending')
        ])
        completed_results = self.env['huggingface.result'].search([
            ('state', '=', 'completed')
        ])
        
        self.assertIn(self.result, pending_results)
        self.assertIn(completed_result, completed_results)

    def test_result_search_by_task_type(self):
        """Test searching results by task type"""
        results = self.env['huggingface.result'].search([
            ('task_type', '=', 'sentiment-analysis')
        ])
        
        self.assertIn(self.result, results)

    def test_processing_time_calculation(self):
        """Test processing time is properly stored"""
        import time
        start_time = time.time()
        time.sleep(0.1)  # Small delay
        end_time = time.time()
        processing_time = end_time - start_time
        
        self.result.mark_completed(
            result_text="Test result",
            processing_time=processing_time
        )
        
        self.assertGreater(self.result.processing_time, 0.1)
        self.assertLess(self.result.processing_time, 1.0)

    def test_raw_data_storage(self):
        """Test raw data storage and retrieval"""
        raw_data = {
            'method_used': 'direct_inference',
            'model': 'test-model',
            'confidence': 0.95
        }
        
        self.result.mark_completed(
            result_text="Test result",
            raw_data=raw_data
        )
        
        # Raw data should be stored as string
        self.assertEqual(self.result.raw_data, str(raw_data))

    def test_result_with_config_relationship(self):
        """Test result relationship with config"""
        self.assertEqual(self.result.config_id, self.hf_config)
        
        # Test reverse relationship
        config_results = self.hf_config.result_ids
        self.assertIn(self.result, config_results)