#!/usr/bin/env python3
"""
Simple test script to demonstrate HuggingFace endpoint functionality
"""

import os
import requests
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_sentiment_analysis():
    """Test sentiment analysis endpoint"""
    api_key = os.getenv('HUGGINGFACE_API_KEY')
    if not api_key or api_key == 'your_huggingface_api_key_here':
        print("⚠️  No valid API key found")
        return
    
    url = "https://api-inference.huggingface.co/models/cardiffnlp/twitter-roberta-base-sentiment"
    headers = {"Authorization": f"Bearer {api_key}"}
    data = {"inputs": "I love this new AI technology!"}
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        print("✅ Sentiment Analysis Test:")
        print(f"   Input: {data['inputs']}")
        print(f"   Result: {result}")
        print()
    except Exception as e:
        print(f"❌ Sentiment Analysis failed: {e}")

def test_translation():
    """Test translation endpoint"""
    api_key = os.getenv('HUGGINGFACE_API_KEY')
    if not api_key or api_key == 'your_huggingface_api_key_here':
        print("⚠️  No valid API key found")
        return
    
    url = "https://api-inference.huggingface.co/models/Helsinki-NLP/opus-mt-en-fr"
    headers = {"Authorization": f"Bearer {api_key}"}
    data = {"inputs": "Hello, how are you today?"}
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        print("✅ Translation Test:")
        print(f"   Input: {data['inputs']}")
        print(f"   Result: {result}")
        print()
    except Exception as e:
        print(f"❌ Translation failed: {e}")

def test_summarization():
    """Test summarization endpoint"""
    api_key = os.getenv('HUGGINGFACE_API_KEY')
    if not api_key or api_key == 'your_huggingface_api_key_here':
        print("⚠️  No valid API key found")
        return
    
    url = "https://api-inference.huggingface.co/models/facebook/bart-large-cnn"
    headers = {"Authorization": f"Bearer {api_key}"}
    data = {"inputs": "Artificial Intelligence has revolutionized many industries. Machine learning algorithms can now process vast amounts of data and make predictions with remarkable accuracy. This technology is being used in healthcare, finance, transportation, and many other sectors to improve efficiency and outcomes."}
    
    try:
        response = requests.post(url, headers=headers, json=data, timeout=30)
        response.raise_for_status()
        result = response.json()
        print("✅ Summarization Test:")
        print(f"   Input: {data['inputs'][:50]}...")
        print(f"   Result: {result}")
        print()
    except Exception as e:
        print(f"❌ Summarization failed: {e}")

def main():
    """Run all endpoint tests"""
    print("🧪 Testing HuggingFace Endpoints")
    print("=" * 50)
    
    api_key = os.getenv('HUGGINGFACE_API_KEY')
    if not api_key or api_key == 'your_huggingface_api_key_here':
        print("⚠️  Warning: Please update your .env file with a valid HuggingFace API key")
        print("Get your key from: https://huggingface.co/settings/tokens")
        print()
    
    test_sentiment_analysis()
    test_translation()
    test_summarization()
    
    print("🎯 Testing complete!")
    print("\nNote: Image-based endpoints (OCR, image-to-text) require binary image data")
    print("Text-to-image and chat endpoints may require different model configurations")

if __name__ == "__main__":
    main()