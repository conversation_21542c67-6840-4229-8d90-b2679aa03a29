<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <!-- AI Processor Form View -->
    <record id="view_huggingface_ai_processor_form" model="ir.ui.view">
        <field name="name">huggingface.ai.processor.form</field>
        <field name="model">huggingface.ai.processor</field>
        <field name="arch" type="xml">
            <form string="AI Processing Job">
                <header>
                    <button name="action_submit" type="object" string="Submit for Processing" 
                            class="btn-primary" invisible="status != 'draft'"/>
                    <button name="action_cancel" type="object" string="Cancel" 
                            invisible="not can_cancel"/>
                    <button name="action_retry" type="object" string="Retry" 
                            invisible="status != 'failed'"/>
                    <field name="status" widget="statusbar" statusbar_visible="draft,queued,processing,completed"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <label for="name"/>
                        <h1>
                            <field name="name" placeholder="AI Processing Job"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="task_type" required="1"/>
                            <field name="priority"/>
                            <field name="user_id"/>
                        </group>
                        <group>
                            <field name="progress_percent" widget="progressbar"/>
                            <field name="duration"/>
                            <field name="can_cancel" invisible="1"/>
                        </group>
                    </group>
                    
                    <notebook>
                        <page string="Input" name="input">
                            <group>
                                <field name="input_text" widget="text" 
                                       required="task_type == 'text-to-image'"/>
                                <field name="input_files" widget="many2many_binary" 
                                       required="task_type in ['ocr', 'image-to-text', 'speech-to-text']"/>
                            </group>
                        </page>
                        <page string="Results" name="results">
                            <group>
                                <!-- The output_type field drives the conditional visibility of the result fields -->
                                <field name="output_type" invisible="1"/>
                                <!-- Show text result only if output_type is 'text' -->
                                <field name="result_text" widget="text" readonly="1" invisible="output_type != 'text'"/>
                                <!-- Show image result only if output_type is 'image' -->
                                <field name="result_image" widget="image" readonly="1" class="oe_avatar" invisible="output_type != 'image'"/>
                                <field name="result_files" widget="many2many_binary" readonly="1"/>
                                <field name="error_message" widget="text" readonly="1" 
                                       invisible="not error_message"/>
                            </group>
                        </page>
                        <page string="Processing Info" name="info">
                            <group>
                                <group>
                                    <field name="start_date" readonly="1"/>
                                    <field name="complete_date" readonly="1"/>
                                    <field name="processing_time" readonly="1"/>
                                </group>
                                <group>
                                    <field name="estimated_time" readonly="1"/>
                                    <field name="company_id" readonly="1"/>
                                    <field name="create_date" readonly="1"/>
                                </group>
                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- AI Processor List View -->
    <record id="view_huggingface_ai_processor_list" model="ir.ui.view">
        <field name="name">huggingface.ai.processor.list</field>
        <field name="model">huggingface.ai.processor</field>
        <field name="arch" type="xml">
            <list string="AI Processing Jobs">
                <field name="name"/>
                <field name="task_type"/>
                <field name="status" widget="badge"/>
                <field name="priority" widget="priority"/>
                <field name="progress_percent" widget="progressbar"/>
                <field name="user_id"/>
                <field name="create_date"/>
                <field name="duration"/>
            </list>
        </field>
    </record>

    <!-- AI Processor Kanban View -->
    <record id="view_huggingface_ai_processor_kanban" model="ir.ui.view">
        <field name="name">huggingface.ai.processor.kanban</field>
        <field name="model">huggingface.ai.processor</field>
        <field name="arch" type="xml">
            <kanban default_group_by="status" class="o_kanban_small_column">
                <field name="name"/>
                <field name="task_type"/>
                <field name="status"/>
                <field name="progress_percent"/>
                <field name="user_id"/>
                <field name="create_date"/>
                <templates>
                    <t t-name="card">
                        <div class="oe_kanban_card oe_kanban_global_click">
                            <div class="oe_kanban_content">
                                <div class="o_kanban_record_top">
                                    <div class="o_kanban_record_headings">
                                        <strong class="o_kanban_record_title">
                                            <field name="name"/>
                                        </strong>
                                    </div>
                                    <div class="o_kanban_record_subtitle">
                                        <field name="task_type"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_body">
                                    <div class="progress" style="height: 10px;">
                                        <div class="progress-bar" role="progressbar" 
                                             t-attf-style="width: #{record.progress_percent.raw_value}%"/>
                                    </div>
                                    <div class="mt-2">
                                        <i class="fa fa-user" title="User"/> <field name="user_id"/>
                                    </div>
                                </div>
                                <div class="o_kanban_record_bottom">
                                    <div class="oe_kanban_bottom_left">
                                        <field name="create_date" widget="date"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </t>
                </templates>
            </kanban>
        </field>
    </record>

    <!-- AI Processor Search View -->
    <record id="view_huggingface_ai_processor_search" model="ir.ui.view">
        <field name="name">huggingface.ai.processor.search</field>
        <field name="model">huggingface.ai.processor</field>
        <field name="arch" type="xml">
            <search string="AI Processing Jobs">
                <field name="name"/>
                <field name="task_type"/>
                <field name="user_id"/>
                <filter string="My Jobs" name="my_jobs" domain="[('user_id', '=', uid)]"/>
                <filter string="Draft" name="draft" domain="[('status', '=', 'draft')]"/>
                <filter string="Processing" name="processing" domain="[('status', 'in', ['queued', 'processing'])]"/>
                <filter string="Completed" name="completed" domain="[('status', '=', 'completed')]"/>
                <filter string="Failed" name="failed" domain="[('status', '=', 'failed')]"/>
                <separator/>
                <filter string="Today" name="today" domain="[('create_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="week" domain="[('create_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_status" context="{'group_by': 'status'}"/>
                    <filter string="Task Type" name="group_task_type" context="{'group_by': 'task_type'}"/>
                    <filter string="User" name="group_user" context="{'group_by': 'user_id'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'create_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- AI Processor Action -->
    <record id="action_huggingface_ai_processor" model="ir.actions.act_window">
        <field name="name">AI Processing Jobs</field>
        <field name="res_model">huggingface.ai.processor</field>
        <field name="view_mode">kanban,list,form</field>
        <field name="context">{'search_default_my_jobs': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first AI processing job!
            </p>
            <p>
                Submit heavy AI processing tasks like OCR, image generation, and more.
                Track progress and manage results efficiently.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_ai_processing_root" name="AI Processing" sequence="50"/>

    <menuitem id="menu_ai_processing_jobs" name="All Jobs" 
              parent="menu_ai_processing_root" action="action_huggingface_ai_processor" sequence="20"/>
    
    <menuitem id="menu_ai_processing_image" name="Image Processing" 
              parent="menu_ai_processing_root" sequence="30"/>
    
    <menuitem id="menu_ai_processing_ocr" name="OCR" 
              parent="menu_ai_processing_image" 
              action="action_huggingface_ai_processor" 
              sequence="10"/>
    
    <menuitem id="menu_ai_processing_img2txt" name="Image Description" 
              parent="menu_ai_processing_image" 
              action="action_huggingface_ai_processor" 
              sequence="20"/>
    
    <menuitem id="menu_ai_processing_txt2img" name="Image Generation" 
              parent="menu_ai_processing_image" 
              action="action_huggingface_ai_processor" 
              sequence="30"/>

</odoo>