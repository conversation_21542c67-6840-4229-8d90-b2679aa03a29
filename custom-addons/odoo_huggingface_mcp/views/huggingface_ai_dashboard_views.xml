<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AI Dashboard Form View -->
    <record id="view_huggingface_ai_dashboard_form" model="ir.ui.view">
        <field name="name">huggingface.ai.dashboard.form</field>
        <field name="model">huggingface.ai.dashboard</field>
        <field name="arch" type="xml">
            <form string="AI Processing Dashboard" create="false" edit="false" delete="false">
                <sheet>
                    <div class="oe_title">
                        <h1>AI Processing Dashboard</h1>
                        <p>Monitor and manage your AI processing jobs</p>
                    </div>
                    
                    <!-- Quick Actions -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <button name="action_new_job_wizard" type="object" 
                                    class="btn btn-primary btn-lg mr-2">
                                <i class="fa fa-plus"/> New Processing Job
                            </button>
                            <button name="action_view_all_jobs" type="object" 
                                    class="btn btn-secondary mr-2">
                                <i class="fa fa-list"/> View All Jobs
                            </button>
                            <button name="action_view_processing_jobs" type="object" 
                                    class="btn btn-info mr-2">
                                <i class="fa fa-spinner"/> Active Jobs
                            </button>
                        </div>
                    </div>
                    
                    <!-- Statistics Cards -->
                    <div class="row">
                        <!-- Total Jobs Card -->
                        <div class="col-md-3 mb-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><field name="total_jobs"/></h4>
                                            <p class="mb-0">Total Jobs</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-tasks fa-2x" title="Total Jobs"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Processing Jobs Card -->
                        <div class="col-md-3 mb-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><field name="processing_jobs"/></h4>
                                            <p class="mb-0">Processing</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-spinner fa-2x" title="Processing Jobs"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Completed Jobs Card -->
                        <div class="col-md-3 mb-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><field name="completed_jobs"/></h4>
                                            <p class="mb-0">Completed</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-check fa-2x" title="Completed Jobs"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Failed Jobs Card -->
                        <div class="col-md-3 mb-3">
                            <div class="card bg-danger text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4><field name="failed_jobs"/></h4>
                                            <p class="mb-0">Failed</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="fa fa-exclamation-triangle fa-2x" title="Failed Jobs"/>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Status Breakdown -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Job Status Breakdown</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Draft</td>
                                            <td><field name="draft_jobs"/></td>
                                        </tr>
                                        <tr>
                                            <td>Queued</td>
                                            <td><field name="queued_jobs"/></td>
                                        </tr>
                                        <tr>
                                            <td>Processing</td>
                                            <td><field name="processing_jobs"/></td>
                                        </tr>
                                        <tr>
                                            <td>Completed</td>
                                            <td><field name="completed_jobs"/></td>
                                        </tr>
                                        <tr>
                                            <td>Failed</td>
                                            <td><field name="failed_jobs"/></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5>Today's Activity</h5>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tr>
                                            <td>Jobs Created</td>
                                            <td><field name="today_jobs"/></td>
                                        </tr>
                                        <tr>
                                            <td>Completed</td>
                                            <td><field name="today_completed"/></td>
                                        </tr>
                                        <tr>
                                            <td>Failed</td>
                                            <td><field name="today_failed"/></td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recent Jobs -->
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header d-flex justify-content-between">
                                    <h5>Recent Jobs</h5>
                                    <button name="action_view_all_jobs" type="object" class="btn btn-sm btn-outline-primary">
                                        View All
                                    </button>
                                </div>
                                <div class="card-body">
                                    <field name="recent_jobs" readonly="1">
                                        <list create="false" edit="false" delete="false">
                                            <field name="name"/>
                                            <field name="task_type"/>
                                            <field name="status" widget="badge"/>
                                            <field name="progress_percent" widget="progressbar"/>
                                            <field name="create_date"/>
                                        </list>
                                    </field>
                                </div>
                            </div>
                        </div>
                    </div>
                </sheet>
            </form>
        </field>
    </record>

    <!-- AI Dashboard Action -->
    <record id="action_huggingface_ai_dashboard_server" model="ir.actions.server">
        <field name="name">AI Processing Dashboard</field>
        <field name="model_id" ref="model_huggingface_ai_dashboard"/>
        <field name="state">code</field>
        <field name="code">action = model.open_dashboard()</field>
    </record>
    
    <menuitem id="menu_ai_processing_dashboard" name="Dashboard" 
              parent="menu_ai_processing_root" action="action_huggingface_ai_dashboard_server" sequence="5"/>
</odoo>