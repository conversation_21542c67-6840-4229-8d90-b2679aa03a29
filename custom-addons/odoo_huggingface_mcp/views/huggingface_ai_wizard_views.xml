<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- AI Processing Wizard Form View -->
    <record id="view_huggingface_ai_wizard_form" model="ir.ui.view">
        <field name="name">huggingface.ai.wizard.form</field>
        <field name="model">huggingface.ai.wizard</field>
        <field name="arch" type="xml">
            <form string="AI Processing Wizard">
                <header>
                    <!-- Step indicators -->
                    <div class="o_wizard_steps">
                        <span class="badge badge-primary" invisible="state != 'task_selection'">1. Task Selection</span>
                        <span class="badge badge-secondary" invisible="state == 'task_selection'">1. Task Selection</span>
                        
                        <span class="badge badge-primary" invisible="state != 'input'">2. Input</span>
                        <span class="badge badge-secondary" invisible="state == 'input'">2. Input</span>
                        
                        <span class="badge badge-primary" invisible="state != 'configuration'">3. Configuration</span>
                        <span class="badge badge-secondary" invisible="state == 'configuration'">3. Configuration</span>
                        
                        <span class="badge badge-primary" invisible="state != 'review'">4. Review</span>
                        <span class="badge badge-secondary" invisible="state == 'review'">4. Review</span>
                    </div>
                </header>
                
                <sheet>
                    <field name="state" invisible="1"/>
                    
                    <!-- Step 1: Task Selection -->
                    <div invisible="state != 'task_selection'">
                        <div class="oe_title">
                            <h1>Select AI Processing Task</h1>
                            <p>Choose the type of AI processing you want to perform:</p>
                        </div>
                        <group>
                            <field name="task_type" widget="radio" required="1"/>
                        </group>
                        <div class="mt-3">
                            <field name="task_description" readonly="1"/>
                        </div>
                    </div>
                    
                    <!-- Step 2: Input -->
                    <div invisible="state != 'input'">
                        <div class="oe_title">
                            <h1>Provide Input</h1>
                            <p>Upload files or enter text based on your selected task:</p>
                        </div>
                        <group>
                            <field name="requires_text_input" invisible="1"/>
                            <field name="requires_file_input" invisible="1"/>
                            <field name="input_text" widget="text"
                                   invisible="not requires_text_input"/>
                            <field name="input_files" widget="many2many_binary"
                                   invisible="not requires_file_input"/>
                        </group>
                        
                        <!-- Task-specific help text -->
                        <div class="alert alert-info" role="alert" invisible="task_type != 'text-to-image'">
                            <strong>Tip:</strong> Be specific in your description. Include details about style, colors, composition, and mood for better results.
                        </div>
                        <div class="alert alert-info" role="alert" invisible="task_type not in ['ocr', 'image-to-text']">
                            <strong>Supported formats:</strong> JPG, PNG, GIF, BMP. Maximum file size: 10MB.
                        </div>
                        <div class="alert alert-warning" role="alert" invisible="task_type not in ['speech-to-text', 'text-to-speech']">
                            <strong>Coming Soon:</strong> Audio processing features are currently under development.
                        </div>
                    </div>
                    
                    <!-- Step 3: Configuration -->
                    <div invisible="state != 'configuration'">
                        <div class="oe_title">
                            <h1>Configure Processing</h1>
                            <p>Set processing options and priority:</p>
                        </div>
                        <group>
                            <field name="job_name" placeholder="Auto-generated if left empty"/>
                            <field name="priority"/>
                        </group>
                        
                        <div class="alert alert-info" role="alert">
                            <h5>Processing Priority Guide:</h5>
                            <ul>
                                <li><strong>Low:</strong> Process when system resources are available</li>
                                <li><strong>Normal:</strong> Standard processing queue (recommended)</li>
                                <li><strong>High:</strong> Priority processing for important tasks</li>
                                <li><strong>Urgent:</strong> Immediate processing (use sparingly)</li>
                            </ul>
                        </div>
                    </div>
                    
                    <!-- Step 4: Review -->
                    <div invisible="state != 'review'">
                        <div class="oe_title">
                            <h1>Review and Submit</h1>
                            <p>Please review your processing job before submission:</p>
                        </div>
                        <group>
                            <group>
                                <label for="task_type" string="Task Type"/>
                                <div>
                                    <field name="task_type" readonly="1" force_save="1"/>
                                </div>
                                <label for="priority" string="Priority"/>
                                <div>
                                    <field name="priority" readonly="1" force_save="1"/>
                                </div>
                                <label for="job_name" string="Job Name"/>
                                <div>
                                    <field name="job_name" readonly="1" force_save="1"/>
                                </div>
                            </group>
                            <group>
                                <label for="input_text" string="Input Text" invisible="not input_text"/>
                                <div invisible="not input_text">
                                    <field name="input_text" readonly="1" force_save="1"/>
                                </div>
                                <label for="input_files" string="Input Files" invisible="not input_files"/>
                                <div invisible="not input_files">
                                    <field name="input_files" readonly="1" force_save="1"/>
                                </div>
                            </group>
                        </group>
                        
                        <div class="alert alert-success" role="alert">
                            <strong>Ready to Process!</strong> Your job will be submitted to the processing queue. 
                            You can track progress from the AI Processing Jobs menu.
                        </div>
                    </div>
                </sheet>
                
                <footer>
                    <button string="Back" name="action_back" type="object" class="btn-secondary"
                            invisible="state in ['task_selection', 'submitted']"/>
                    <button string="Next" name="action_next" type="object" class="btn-primary"
                            invisible="state in ['review', 'submitted']"/>
                    <button string="Submit Job" name="action_next" type="object" class="btn-success"
                            invisible="state != 'review'"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- AI Processing Wizard Action -->
    <record id="action_huggingface_ai_wizard" model="ir.actions.act_window">
        <field name="name">AI Processing Wizard</field>
        <field name="res_model">huggingface.ai.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{}</field>
    </record>

    <menuitem id="menu_ai_processing_new" name="New Job (Wizard)" 
              parent="menu_ai_processing_root" action="action_huggingface_ai_wizard" sequence="10"/>

</odoo>