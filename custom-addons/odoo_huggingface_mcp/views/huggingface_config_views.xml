<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Hugging Face Configuration Form View -->
    <record id="view_huggingface_config_form" model="ir.ui.view">
        <field name="name">huggingface.config.form</field>
        <field name="model">huggingface.config</field>
        <field name="arch" type="xml">
            <form string="Hugging Face Configuration">
                <header>
                    <button name="action_check_connection" string="Check Connection" type="object" class="oe_highlight"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,connected,disconnected"/>
                   <button name="generate_response" string="Generate" type="object" class="oe_highlight" invisible="state != 'connected'"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="e.g. Company Hugging Face"/>
                        </h1>
                    </div>
                    <group>
                        <group string="Basic Configuration">
                            <field name="mcp_command"/>
                            <field name="mcp_args"/>
                            <field name="mcp_env_vars"/>
                            <field name="huggingface_api_key" password="True"/>
                            <field name="llm_provider_id"/>
                            <field name="use_alternative_agent"/>
                            <field name="alternative_agent_provider_id"/>
                            <field name="is_active"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group string="Inference Configuration">
                            <field name="use_direct_inference"/>
                            <field name="enable_mcp_fallback"/>
                            <field name="enable_llm_fallback"/>
                        </group>
                    </group>
                   <group invisible="state != 'connected'">
                       <field name="prompt_text" placeholder="Enter your prompt here..."/>
                       <field name="result_text" readonly="1"/>
                   </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Hugging Face Configuration List View -->
    <record id="view_huggingface_config_list" model="ir.ui.view">
        <field name="name">huggingface.config.list</field>
        <field name="model">huggingface.config</field>
        <field name="arch" type="xml">
            <list string="Hugging Face Configurations">
                <field name="name"/>
                <field name="mcp_command"/>
                <field name="is_active"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- Hugging Face Configuration Search View -->
    <record id="view_huggingface_config_search" model="ir.ui.view">
        <field name="name">huggingface.config.search</field>
        <field name="model">huggingface.config</field>
        <field name="arch" type="xml">
            <search string="Search Hugging Face Configuration">
                <field name="name"/>
                <field name="mcp_command"/>
                <field name="state"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Connected" name="connected" domain="[('state', '=', 'connected')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Company" name="group_by_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Hugging Face Configuration Action -->
    <record id="action_huggingface_config" model="ir.actions.act_window">
        <field name="name">Hugging Face Configuration</field>
        <field name="res_model">huggingface.config</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_huggingface_config_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Hugging Face configuration!
            </p>
            <p>
                Configure Hugging Face integration to enable AI capabilities in Odoo.
            </p>
        </field>
    </record>

    <!-- Hugging Face Configuration Menu -->
    <menuitem id="menu_huggingface_main"
              name="Hugging Face"
              sequence="11"/>

    <menuitem id="menu_huggingface_config"
              name="Configuration"
              parent="menu_huggingface_main"
              action="action_huggingface_config"
              sequence="10"/>
    <!-- Action for Hugging Face LLM Providers -->
    <record id="action_huggingface_llm_provider" model="ir.actions.act_window">
        <field name="name">Hugging Face LLM Providers</field>
        <field name="res_model">llm.provider</field>
        <field name="view_mode">list,form</field>
        <field name="domain">[('provider_type', '=', 'huggingface')]</field>
        <field name="context">{'default_provider_type': 'huggingface'}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create a new Hugging Face LLM Provider.
            </p>
            <p>
                This is where you configure the connection to your Hugging Face MCP server.
            </p>
        </field>
    </record>

    <!-- Menu item for Hugging Face LLM Providers -->
    <menuitem id="menu_huggingface_llm_provider"
              name="LLM Providers"
              parent="menu_huggingface_main"
              action="action_huggingface_llm_provider"
              sequence="20"/>
</odoo>