<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_huggingface_task_tree" model="ir.ui.view">
        <field name="name">huggingface.task.tree</field>
        <field name="model">huggingface.task</field>
        <field name="arch" type="xml">
            <list string="HuggingFace Tasks">
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="task_type"/>
                <field name="huggingface_space"/>
                <field name="is_active"/>
                <field name="requires_file"/>
                <field name="output_type"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_huggingface_task_form" model="ir.ui.view">
        <field name="name">huggingface.task.form</field>
        <field name="model">huggingface.task</field>
        <field name="arch" type="xml">
            <form string="HuggingFace Task">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="is_active" widget="boolean_button" options='{"terminology": "archive"}'/>
                        </button>
                    </div>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="task_type"/>
                            <field name="huggingface_space"/>
                            <field name="mcp_tool_name"/>
                            <field name="sequence"/>
                        </group>
                        <group>
                            <field name="requires_file"/>
                            <field name="file_types" invisible="not requires_file"/>
                            <field name="output_type"/>
                            <field name="icon_name"/>
                            <field name="button_title"/>
                        </group>
                    </group>
                    <group>
                        <field name="description"/>
                        <field name="parameters" widget="text" placeholder="Enter JSON parameters here..."/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_huggingface_task" model="ir.actions.act_window">
        <field name="name">HuggingFace Tasks</field>
        <field name="res_model">huggingface.task</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first HuggingFace task configuration!
            </p>
            <p>
                Configure tasks to map specific HuggingFace models and spaces to your Odoo interface.
            </p>
        </field>
    </record>

    <!-- Menu -->
    <menuitem id="menu_huggingface_task"
              name="Tasks"
              parent="menu_huggingface_main"
              action="action_huggingface_task"
              sequence="20"/>
</odoo>