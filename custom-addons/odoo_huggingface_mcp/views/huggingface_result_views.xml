<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_huggingface_result_tree" model="ir.ui.view">
        <field name="name">huggingface.result.tree</field>
        <field name="model">huggingface.result</field>
        <field name="arch" type="xml">
            <list string="HuggingFace Results">
                <field name="create_date"/>
                <field name="name"/>
                <field name="task_type"/>
                <field name="user_id"/>
                <field name="state" decoration-success="state == 'completed'" decoration-danger="state == 'failed'" decoration-warning="state == 'processing'"/>
                <field name="processing_time"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_huggingface_result_form" model="ir.ui.view">
        <field name="name">huggingface.result.form</field>
        <field name="model">huggingface.result</field>
        <field name="arch" type="xml">
            <form string="HuggingFace Result" create="false" edit="false">
                <header>
                    <field name="state" widget="statusbar" statusbar_visible="processing,completed,failed"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="task_id"/>
                            <field name="task_type"/>
                            <field name="user_id"/>
                            <field name="create_date"/>
                        </group>
                        <group>
                            <field name="config_id"/>
                            <field name="processing_time"/>
                            <field name="result_filename" invisible="result_filename == False"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Input">
                            <field name="input_text" widget="text"/>
                        </page>
                        <page string="Text Result" invisible="result_text == False">
                            <field name="result_text" widget="text"/>
                        </page>
                        <page string="File Result" invisible="result_file == False">
                            <field name="result_file" filename="result_filename"/>
                        </page>
                        <page string="Raw Data" invisible="result_data == False">
                            <field name="result_data" widget="text" placeholder="Raw JSON data..."/>
                        </page>
                        <page string="Error" invisible="error_message == False">
                            <field name="error_message" widget="text"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_huggingface_result_search" model="ir.ui.view">
        <field name="name">huggingface.result.search</field>
        <field name="model">huggingface.result</field>
        <field name="arch" type="xml">
            <search string="HuggingFace Results">
                <field name="name"/>
                <field name="task_type"/>
                <field name="user_id"/>
                <field name="input_text"/>
                <filter string="My Results" name="my_results" domain="[('user_id', '=', uid)]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                <filter string="Processing" name="processing" domain="[('state', '=', 'processing')]"/>
                <separator/>
                <filter string="Today" name="today" domain="[('create_date', '>=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                <filter string="This Week" name="this_week" domain="[('create_date', '>=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <group expand="0" string="Group By">
                    <filter string="Task Type" name="group_task_type" context="{'group_by': 'task_type'}"/>
                    <filter string="User" name="group_user" context="{'group_by': 'user_id'}"/>
                    <filter string="State" name="group_state" context="{'group_by': 'state'}"/>
                    <filter string="Date" name="group_date" context="{'group_by': 'create_date:day'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_huggingface_result" model="ir.actions.act_window">
        <field name="name">HuggingFace Results</field>
        <field name="res_model">huggingface.result</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_my_results': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No results yet!
            </p>
            <p>
                Results from HuggingFace tasks will appear here.
            </p>
        </field>
    </record>

    <!-- Menu -->
    <menuitem id="menu_huggingface_result"
              name="Results"
              parent="menu_huggingface_main"
              action="action_huggingface_result"
              sequence="30"/>
</odoo>