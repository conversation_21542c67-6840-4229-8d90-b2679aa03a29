<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tool Configuration Form View -->
    <record id="view_huggingface_tool_config_form" model="ir.ui.view">
        <field name="name">huggingface.tool.config.form</field>
        <field name="model">huggingface.tool.config</field>
        <field name="arch" type="xml">
            <form string="HuggingFace Tool Configuration">
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="e.g. Custom Sentiment Analysis"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="task_type"/>
                            <field name="endpoint_url"/>
                            <field name="model_name"/>
                        </group>
                        <group>
                            <field name="is_active"/>
                            <field name="use_gradio"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <group string="Parameters">
                        <field name="parameters" widget="text" placeholder="Enter JSON parameters here..."/>
                    </group>
                    <group string="Gradio Space Configuration" invisible="not use_gradio">
                        <group>
                            <field name="gradio_space_url" required="use_gradio"/>
                            <field name="gradio_api_endpoint"/>
                        </group>
                        <group>
                            <field name="gradio_parameters" widget="text" placeholder="Enter Gradio JSON parameters here..."/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Tool Configuration List View -->
    <record id="view_huggingface_tool_config_list" model="ir.ui.view">
        <field name="name">huggingface.tool.config.list</field>
        <field name="model">huggingface.tool.config</field>
        <field name="arch" type="xml">
            <list string="HuggingFace Tool Configurations">
                <field name="name"/>
                <field name="task_type"/>
                <field name="model_name"/>
                <field name="use_gradio"/>
                <field name="is_active"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- Tool Configuration Search View -->
    <record id="view_huggingface_tool_config_search" model="ir.ui.view">
        <field name="name">huggingface.tool.config.search</field>
        <field name="model">huggingface.tool.config</field>
        <field name="arch" type="xml">
            <search string="Search Tool Configurations">
                <field name="name"/>
                <field name="task_type"/>
                <field name="model_name"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Inactive" name="inactive" domain="[('is_active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="Task Type" name="group_by_task_type" context="{'group_by': 'task_type'}"/>
                    <filter string="Company" name="group_by_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Tool Configuration Action -->
    <record id="action_huggingface_tool_config" model="ir.actions.act_window">
        <field name="name">HuggingFace Tools</field>
        <field name="res_model">huggingface.tool.config</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_huggingface_tool_config_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Configure your HuggingFace tools!
            </p>
            <p>
                Set up custom endpoints and models for different AI tasks.
            </p>
        </field>
    </record>

    <!-- Menu item for HuggingFace Tools -->
    <menuitem id="menu_huggingface_tools"
              name="Tools Configuration"
              parent="menu_huggingface_main"
              action="action_huggingface_tool_config"
              sequence="15"/>
</odoo>