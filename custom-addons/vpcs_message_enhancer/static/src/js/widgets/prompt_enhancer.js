/** @odoo-module **/
import { rpc } from "@web/core/network/rpc";

// For debugging
const DEBUG = true;

function log(message) {
    if (DEBUG) {
        console.log("[Prompt Enhancer] " + message);
    }
}

function inspectDOM() {
    if (!DEBUG) return;

    log("Inspecting DOM for textareas...");
    const allTextareas = document.querySelectorAll('textarea');
    log(`Found ${allTextareas.length} textareas in total`);

    allTextareas.forEach((textarea, index) => {
        if (index < 10) { // Limit to first 10 for performance
            const classes = textarea.className || 'no-class';
            const id = textarea.id || 'no-id';
            const name = textarea.name || 'no-name';
            const placeholder = textarea.placeholder || 'no-placeholder';
            const parent = textarea.parentNode ?.className || 'no-parent';

            log(`Textarea ${index}: class=${classes}, id=${id}, name=${name}, placeholder=${placeholder}, parent=${parent}`);
        }
    });

    // Check for known Odoo elements
    const odooElements = document.querySelectorAll('.o_chatter, .o_Chatter, .o_mail_thread, .o_Composer');
    log(`Found ${odooElements.length} Odoo chatter elements`);
}

function showNotification(message, duration = 4000) {
    let notification = document.createElement("div");
    notification.className = "o-enhance-notification";
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: green;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 1000;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
        transition: opacity 0.5s;
    `;
    notification.innerText = message;
    document.body.appendChild(notification);

    // Hide after duration
    setTimeout(() => {
        notification.style.opacity = "0";
        setTimeout(() => notification.remove(), 500); // Ensure smooth fade-out
    }, duration);
}


(function() {
    function addEnhanceButton(promptInput) {
        if (!promptInput || promptInput.querySelector('.o-enhance-prompt-btn')) {
            return; // Already has the button or invalid element
        }

        log("Adding enhance button to element: " + (promptInput.className || 'unknown'));

        const enhanceBtn = document.createElement('button');
        enhanceBtn.className = 'btn btn-secondary position-absolute o-enhance-prompt-btn';
        enhanceBtn.style.cssText = `
            right: 5px; 
            top: 5px; 
            z-index: 9999 !important;
            padding: 3px 8px;
            min-width: 30px;
            background-color: #5a4f7f !important;
            color: white !important;
            border: none;
            border-radius: 3px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            display: flex !important;
            align-items: center;
            justify-content: center;
            opacity: 1 !important;
            visibility: visible !important;
            pointer-events: auto !important;
        `;
        enhanceBtn.title = 'Enhance your message';
        enhanceBtn.innerHTML = '<i class="fa fa-magic" style="color: white !important;"></i>';

        enhanceBtn.addEventListener('click', async (ev) => {
            ev.preventDefault();
            ev.stopPropagation();
            const textarea = promptInput.querySelector('textarea');
            if (!textarea) return;

            const userPrompt = textarea.value || "";

            if (!userPrompt.trim()) {
                alert("Please enter a message first.");
                return;
            }

            try {
                showNotification("Enhancing your message...");
                const response = await rpc('/prompt_enhancer/enhance_prompt', { prompt: userPrompt });
                if (response) {
                    textarea.value = response.enhanced_prompt;
                    textarea.focus();
                    const event = new Event('input', { bubbles: true });
                    textarea.dispatchEvent(event);
                }

            } catch (error) {
                console.error("Error enhancing message:", error);
                textarea.focus();
                const event = new Event('input', { bubbles: true });
                textarea.dispatchEvent(event);
            }
        });
        promptInput.appendChild(enhanceBtn);
    }

    function checkForChatGPTElements() {
        // Debug counter
        let addedCount = 0;

        // Original ChatGPT selectors
        const promptInputs = document.querySelectorAll('.o-prompt-input, .oe-prompt-input');
        if (promptInputs.length > 0) {
            promptInputs.forEach(input => {
                addEnhanceButton(input);
                addedCount++;
            });
        }

        // Dialog footers (modal dialogs)
        const dialogFooters = document.querySelectorAll('.modal-footer, footer');
        dialogFooters.forEach(footer => {
            const textareaContainers = footer.querySelectorAll('.position-relative');
            textareaContainers.forEach(container => {
                if (container.querySelector('textarea') && !container.querySelector('.o-enhance-prompt-btn')) {
                    addEnhanceButton(container);
                    addedCount++;
                }
            });
        });

        // Modal dialogs
        const dialogs = document.querySelectorAll('.modal-dialog, .modal-content');
        dialogs.forEach(dialog => {
            const textareas = dialog.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                const parent = textarea.closest('.position-relative');
                if (parent && !parent.querySelector('.o-enhance-prompt-btn')) {
                    addEnhanceButton(parent);
                    addedCount++;
                }
            });
        });

        const odooSelectors = [
            // Portal chatter composer
            '.o-mail-Composer-input',
            // Chatter composers
            '.o_chatter',
            '.o_chatter_composer',
            '.o_mail_composer',
            '.o_thread_composer',
            '.o_thread_window_composer',
            '.o_message_log',
            '.o_mail_log'
        ];
        odooSelectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                // For elements that are containers for textareas
                if (element.querySelector('textarea')) {
                    if (!element.querySelector('.o-enhance-prompt-btn')) {
                        addEnhanceButton(element);
                        addedCount++;
                    }
                } else if (element.tagName.toLowerCase() === 'textarea') {
                    const parent = element.parentNode;
                    if (parent && !parent.querySelector('.o-enhance-prompt-btn')) {
                        if (window.getComputedStyle(parent).position !== 'relative') {
                            parent.style.position = 'relative';
                        }
                        addEnhanceButton(parent);
                        addedCount++;
                    }
                }
            });
        });

        const directMessageTextareas = document.querySelectorAll('textarea[name="message"], textarea[name="body"], textarea.o_wysiwyg_textarea');
        directMessageTextareas.forEach(textarea => {
            log("Found direct message textarea: " + (textarea.name || 'unnamed'));

            const parent = textarea.parentNode;
            if (parent && !parent.querySelector('.o-enhance-prompt-btn')) {
                log("Adding button to direct textarea parent: " + parent.className);
                if (window.getComputedStyle(parent).position !== 'relative') {
                    parent.style.position = 'relative';
                }
                addEnhanceButton(parent);
                addedCount++;
            }
        });

        // Debug output
        if (addedCount > 0) {
            log(`Added ${addedCount} enhance buttons`);
        }
    }

    function initializePromptEnhancer() {
        log("Initializing Prompt Enhancer");

        // Run initial check
        checkForChatGPTElements();

        // Run DOM inspection
        inspectDOM();

        // Set up observer
        const observer = new MutationObserver(() => {
            checkForChatGPTElements();
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // Run multiple checks at startup to ensure we catch all elements
        const checkIntervals = [0, 100, 500, 1000, 2000, 3000];
        checkIntervals.forEach(interval => {
            setTimeout(checkForChatGPTElements, interval);
        });

        // Regular interval check
        setInterval(checkForChatGPTElements, 3000);

        // Listen for dynamic content changes
        document.addEventListener('DOMContentLoaded', () => {
            checkForChatGPTElements();
        });

        // Listen for page changes in Odoo
        window.addEventListener('hashchange', () => {
            setTimeout(checkForChatGPTElements, 100);
        });

        // Listen for Odoo's specific page change events
        document.addEventListener('o_web_client_ready', () => {
            setTimeout(checkForChatGPTElements, 100);
        });

        // Listen for Odoo's chatter ready event
        document.addEventListener('o_chatter_ready', () => {
            setTimeout(checkForChatGPTElements, 100);
        });
    }

    // Initialize immediately if document is already loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializePromptEnhancer);
    } else {
        initializePromptEnhancer();
    }

    // Also initialize on window load
    window.addEventListener('load', initializePromptEnhancer);

    // Initialize on Odoo's specific events
    document.addEventListener('o_web_client_ready', initializePromptEnhancer);
    document.addEventListener('o_chatter_ready', initializePromptEnhancer);

    // Initialize on hash changes (Odoo page navigation)
    window.addEventListener('hashchange', () => {
        setTimeout(initializePromptEnhancer, 100);
    });
})();