<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>VPCS Message Enhancer</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .feature-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .feature-title {
            color: #5a4f7f;
            font-size: 1.5em;
            margin-bottom: 15px;
        }
        .feature-description {
            margin-bottom: 15px;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 10px;
            position: relative;
        }
        .feature-list li:before {
            content: "•";
            color: #5a4f7f;
            font-weight: bold;
            position: absolute;
            left: -20px;
        }
        .user-guide {
            margin-top: 40px;
            padding: 20px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .guide-title {
            color: #5a4f7f;
            font-size: 1.5em;
            margin-bottom: 20px;
        }
        .guide-step {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .step-number {
            font-weight: bold;
            color: #5a4f7f;
        }
        .note {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        .screenshot {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .video-section {
            margin: 40px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            width: 100%;
            box-sizing: border-box;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            max-width: 1000px;
            margin: 20px auto;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            background: #000;
            padding-top: 56.25%; /* 16:9 Aspect Ratio */
        }
        
        .video-container video {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            background: #000;
        }
        
        .video-title {
            color: #5a4f7f;
            font-size: 1.8em;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .video-description {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
            font-size: 1.1em;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Responsive adjustments */
        @media screen and (max-width: 768px) {
            .video-section {
                padding: 15px;
                margin: 20px 0;
            }
            
            .video-title {
                font-size: 1.5em;
            }
            
            .video-description {
                font-size: 1em;
                padding: 0 15px;
            }
        }

        @media screen and (max-width: 480px) {
            .video-section {
                padding: 10px;
            }
            
            .video-title {
                font-size: 1.3em;
            }
            
            .video-container {
                margin: 10px auto;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>VPCS Message Enhancer</h1>
        <p>Enhance your messages with AI-powered suggestions</p>
    </div>

    <!-- Video Section -->
    <div class="video-section">
        <h2 class="video-title">Watch How It Works</h2>
        <p class="video-description">See VPCS Message Enhancer in action - enhancing messages in real-time</p>
        <div class="video-container">
            <video controls style="width:1000px">
                <source src="VPCS MESSAGE ENHANCER.webm" type="video/webm">
                Your browser does not support the video tag.
            </video>
        </div>
    </div>

    <div class="feature-section">
        <h2 class="feature-title">Features</h2>
        <div class="feature-description">
            The VPCS Message Enhancer module adds an AI-powered enhancement feature to your Odoo messages, helping you write more effective and professional communications.
        </div>
        <ul class="feature-list">
            <li>AI-powered message enhancement</li>
            <li>One-click enhancement button</li>
            <li>Works in multiple locations:
                <ul>
                    <li>ChatGPT window</li>
                    <li>Send message field in chatter</li>
                    <li>Log note field in chatter</li>
                </ul>
            </li>
            <li>Preserves message context and formatting</li>
            <li>Real-time enhancement suggestions</li>
            <li>Easy to use interface</li>
        </ul>
    </div>

    <div class="user-guide">
        <h2 class="guide-title">User Guide</h2>
        
        <div class="guide-step">
            <span class="step-number">Step 1:</span> Access the Enhancement Feature
            <p>The enhance button (magic wand icon) appears in the following locations:</p>
            <ul>
                <li>ChatGPT window - Top right corner of the input field</li>
                <li>Send message field - Top right corner of the message input</li>
                <li>Log note field - Top right corner of the note input</li>
            </ul>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 2:</span> Write Your Message
            <p>Type your message in any of the supported fields. The enhance button will be visible as a magic wand icon in the top right corner.</p>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 3:</span> Enhance Your Message
            <p>Click the magic wand icon to enhance your message. The system will analyze your text and provide an improved version while maintaining the original meaning.</p>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 4:</span> Review and Send
            <p>Review the enhanced message. You can:</p>
            <ul>
                <li>Accept the enhanced version</li>
                <li>Edit the enhanced version</li>
                <li>Revert to your original message</li>
            </ul>
        </div>

        <div class="note">
            <strong>Note:</strong> The enhancement feature works best with clear, well-structured messages. For best results, write your message first, then use the enhance button to improve it.
        </div>
    </div>

    <footer>
            <h2>Support & Contact</h2>
            <p>For questions, issues, or customization requests, our team is ready to help:</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📧</span>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <span>🌐</span>
                    <a href="http://vperfectcs.com/" target="_blank">http://vperfectcs.com/</a>
                </div>
            </div>
            <p>This module is available for purchase ($49.00 USD) and is protected by copyright law.</p>
            <p class="copyright">© 2023 VperfectCS. All rights reserved.</p>
        </footer>
</body>
</html>
