from odoo import http, _
from odoo.http import request
from odoo.addons.iap.tools import iap_tools
from odoo.exceptions import UserError, AccessError
import logging

_logger = logging.getLogger(__name__)
DEFAULT_OLG_ENDPOINT = "https://olg.api.odoo.com"


class PromptEnhancerController(http.Controller):
    @http.route(
        "/prompt_enhancer/enhance_prompt", type="json", auth="user", csrf=False
    )
    def enhance_prompt(self, prompt):
        if not prompt or not prompt.strip():
            return {"error": "Empty prompt"}
        
        # Try Odoo OLG first
        try:
            return self._enhance_with_olg(prompt)
        except Exception as e:
            _logger.warning("OLG enhancement failed: %s. Trying fallback providers.", str(e))
            
        # Fallback to custom LLM providers
        try:
            return self._enhance_with_fallback(prompt)
        except Exception as e:
            _logger.error("All enhancement methods failed: %s", str(e))
            return {"error": "Enhancement service temporarily unavailable"}
    
    def _enhance_with_olg(self, prompt):
        """Try enhancement using Odoo's OLG service"""
        IrConfigParameter = request.env["ir.config_parameter"].sudo()
        olg_api_endpoint = IrConfigParameter.get_param(
            "web_editor.olg_api_endpoint", DEFAULT_OLG_ENDPOINT
        )
        database_id = IrConfigParameter.get_param("database.uuid")
        instruction = "Improve the following user prompt for clarity, correctness, and completeness: "
        modified_prompt = f"{instruction}\n{prompt}"
        
        response = iap_tools.iap_jsonrpc(
            olg_api_endpoint + "/api/olg/1/chat",
            params={
                "prompt": modified_prompt,
                "conversation_history": [],
                "database_id": database_id,
            },
            timeout=30,
        )
        
        if response["status"] == "success":
            return {"enhanced_prompt": response["content"], "success": True, "provider": "odoo_olg"}
        elif response["status"] == "limit_call_reached":
            raise UserError(_("OLG rate limit reached"))
        else:
            raise UserError(_("OLG service error: %s", response.get("status", "unknown")))
    
    def _enhance_with_fallback(self, prompt):
        """Try enhancement using fallback LLM providers"""
        LLMProvider = request.env["llm.provider"].sudo()
        
        # Get active providers ordered by last_used (most recent first)
        providers = LLMProvider.search([
            ("active", "=", True),
            ("state", "=", "ready")
        ], order="last_used desc, sequence")
        
        if not providers:
            raise UserError(_("No active LLM providers available"))
        
        instruction = "Improve the following user prompt for clarity, correctness, and completeness: "
        modified_prompt = f"{instruction}\n{prompt}"
        
        for provider in providers:
            try:
                enhanced_prompt = provider.generate(modified_prompt)
                return {
                    "enhanced_prompt": enhanced_prompt, 
                    "success": True, 
                    "provider": f"{provider.provider_type}_{provider.name}"
                }
            except Exception as e:
                _logger.warning("Provider %s failed: %s", provider.name, str(e))
                continue
        
        raise UserError(_("All fallback providers failed"))