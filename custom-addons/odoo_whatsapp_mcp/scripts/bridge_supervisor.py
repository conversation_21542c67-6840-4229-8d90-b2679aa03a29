#!/usr/bin/env python3
"""
WhatsApp Bridge Supervisor - Auto-start and monitor bridge process
"""
import os
import sys
import time
import subprocess
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('/tmp/bridge_supervisor.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class BridgeSupervisor:
    def __init__(self):
        # Get the actual bridge directory relative to this script
        script_dir = Path(__file__).parent
        self.bridge_dir = script_dir.parent / "bridge"
        self.bridge_exec = self.bridge_dir / "bridge"
        self.pid_file = Path("/tmp/bridge.pid")
        self.process = None
        
    def is_bridge_running(self):
        """Check if bridge process is running"""
        if self.pid_file.exists():
            try:
                pid = int(self.pid_file.read_text().strip())
                os.kill(pid, 0)  # Check if process exists
                return True
            except (OSError, ValueError):
                self.pid_file.unlink(missing_ok=True)
        return False
    
    def start_bridge(self):
        """Start the bridge process"""
        if self.is_bridge_running():
            logger.info("Bridge is already running")
            return True
            
        try:
            logger.info("Starting WhatsApp Bridge...")
            os.chdir(self.bridge_dir)
            
            # Start bridge process
            self.process = subprocess.Popen(
                [str(self.bridge_exec)],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                cwd=self.bridge_dir
            )
            
            # Save PID
            self.pid_file.write_text(str(self.process.pid))
            logger.info(f"Bridge started with PID: {self.process.pid}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start bridge: {e}")
            return False
    
    def monitor_bridge(self):
        """Monitor bridge and restart if needed"""
        while True:
            if not self.is_bridge_running():
                logger.warning("Bridge process died, restarting...")
                self.start_bridge()
            time.sleep(30)  # Check every 30 seconds

def main():
    supervisor = BridgeSupervisor()
    
    if len(sys.argv) > 1 and sys.argv[1] == "monitor":
        supervisor.monitor_bridge()
    else:
        supervisor.start_bridge()

if __name__ == "__main__":
    main()