import subprocess
import os
import logging
import threading
import time
import requests

_logger = logging.getLogger(__name__)

# Global process reference
_bridge_process = None

def start_whatsapp_bridge():
    """
    Starts the Go WhatsApp bridge as a subprocess and captures its output.
    Uses production_bridge.go which is the latest and most complete implementation.
    """
    global _bridge_process
    
    if _bridge_process and _bridge_process.poll() is None:
        _logger.info("Bridge already running with PID: %s", _bridge_process.pid)
        return _bridge_process
    
    script_dir = os.path.dirname(os.path.abspath(__file__))
    bridge_dir = os.path.join(script_dir, '..', 'bridge')
    production_bridge_path = os.path.join(bridge_dir, 'production_bridge.go')
    
    if not os.path.exists(production_bridge_path):
        _logger.error("Production bridge file not found: %s", production_bridge_path)
        return None

    command = ['go', 'run', production_bridge_path]
    
    try:
        # Start the process
        _bridge_process = subprocess.Popen(
            command,
            cwd=bridge_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
            text=True
        )
        
        _logger.info("WhatsApp bridge process started with PID: %s", _bridge_process.pid)
        
        # Wait a moment to check if process starts successfully
        time.sleep(2)
        if _bridge_process.poll() is not None:
            # Process died, check error
            stderr_output = _bridge_process.stderr.read()
            _logger.error("Bridge process failed: %s", stderr_output)
            
            return None
        
        # Start output monitoring thread
        threading.Thread(target=_monitor_bridge_output, daemon=True).start()
        
        return _bridge_process

    except FileNotFoundError:
        _logger.error("Go executable not found. Please ensure Go is installed and in the system's PATH.")
        return None
    except Exception as e:
        _logger.error("Failed to start WhatsApp bridge: %s", str(e))
        return None

def _monitor_bridge_output():
    """Monitor bridge output for QR codes and status updates"""
    global _bridge_process
    if not _bridge_process:
        return
        
    try:
        while _bridge_process.poll() is None:
            line = _bridge_process.stdout.readline()
            if line:
                _logger.info("Bridge output: %s", line.strip())
                # TODO: Parse QR code from output and update config
            time.sleep(0.1)
    except Exception as e:
        _logger.error("Error monitoring bridge output: %s", str(e))

def stop_whatsapp_bridge():
    """Stop the WhatsApp bridge process"""
    global _bridge_process
    if _bridge_process and _bridge_process.poll() is None:
        _bridge_process.terminate()
        _bridge_process.wait(timeout=10)
        _logger.info("WhatsApp bridge stopped")
        _bridge_process = None
        return True
    return False

def is_bridge_running():
    """Check if bridge process is running"""
    global _bridge_process
    return _bridge_process and _bridge_process.poll() is None

def test_bridge_connection(url="http://localhost", port=8082):
    """Test connection to WhatsApp bridge"""
    try:
        response = requests.get(f"{url}:{port}/api/status", timeout=5)
        return response.status_code == 200
    except:
        return False

if __name__ == '__main__':
    # This part is for direct execution and testing of the script
    process = start_whatsapp_bridge()
    if process:
        try:
            # Read and print output for testing
            for line in iter(process.stdout.readline, ''):
                print(line, end='')
            process.stdout.close()
            return_code = process.wait()
            print(f"Process finished with return code {return_code}")
        except KeyboardInterrupt:
            print("Stopping bridge...")
            process.terminate()