#!/bin/bash
# Generate and display QR code for WhatsApp pairing

echo "🔄 Generating WhatsApp QR Code..."

# Check if bridge is running
if ! docker exec odoo18-stack pgrep -f "bridge" > /dev/null; then
    echo "⚠️  Bridge not running. Starting bridge..."
    docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh start
    sleep 5
fi

# Wait for bridge to be ready
echo "⏳ Waiting for bridge to initialize..."
for i in {1..10}; do
    if docker exec odoo18-stack curl -s http://localhost:8082/api/status > /dev/null 2>&1; then
        echo "✅ Bridge is ready!"
        break
    fi
    echo "   Attempt $i/10..."
    sleep 2
done

# Get QR code from API
echo "📱 Fetching QR code..."
QR_RESPONSE=$(docker exec odoo18-stack curl -s http://localhost:8082/api/qr)

# Parse response
if echo "$QR_RESPONSE" | grep -q "qr_code"; then
    QR_CODE=$(echo "$QR_RESPONSE" | docker exec -i odoo18-stack jq -r '.qr_code')
    
    if [ "$QR_CODE" != "null" ] && [ -n "$QR_CODE" ]; then
        echo ""
        echo "🔗 WHATSAPP QR CODE:"
        echo "=================================================="
        
        # Try to generate QR code visually
        if command -v qrencode >/dev/null 2>&1; then
            echo "$QR_CODE" | qrencode -t ANSIUTF8
        else
            # Fallback: show QR code data
            echo "📋 QR Code Data (copy to online QR generator):"
            echo "$QR_CODE"
            echo ""
            echo "🌐 Online QR Generator: https://www.qr-code-generator.com/"
        fi
        
        echo "=================================================="
        echo "📱 Instructions:"
        echo "1. Open WhatsApp on your phone"
        echo "2. Go to Settings > Linked Devices"
        echo "3. Tap 'Link a Device'"
        echo "4. Scan the QR code above"
        echo "=================================================="
        
        # Monitor for connection
        echo "⏳ Waiting for connection (timeout: 60s)..."
        for i in {1..30}; do
            STATUS=$(docker exec odoo18-stack curl -s http://localhost:8082/api/status | docker exec -i odoo18-stack jq -r '.connected')
            if [ "$STATUS" = "true" ]; then
                echo "✅ Successfully connected to WhatsApp!"
                exit 0
            fi
            sleep 2
            echo -n "."
        done
        echo ""
        echo "⏰ Connection timeout. QR code may have expired."
        echo "🔄 Run this script again to generate a new QR code."
        
    else
        echo "❌ Invalid QR code received"
    fi
elif echo "$QR_RESPONSE" | grep -q "already_connected"; then
    echo "✅ Already connected to WhatsApp!"
    docker exec odoo18-stack curl -s http://localhost:8082/api/status | docker exec -i odoo18-stack jq '.'
else
    echo "❌ Failed to get QR code"
    echo "Response: $QR_RESPONSE"
    echo ""
    echo "🔧 Troubleshooting:"
    echo "1. Check if bridge is running: docker exec odoo18-stack pgrep -f bridge"
    echo "2. Check bridge logs: docker exec odoo18-stack tail -f /mnt/extra-addons/odoo_whatsapp_mcp/bridge/bridge.log"
    echo "3. Restart bridge: docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart"
fi