#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update existing WhatsApp configurations to use port 8082
Run this from Odoo shell: python3 -c "exec(open('scripts/update_port.py').read())"
"""

import logging
_logger = logging.getLogger(__name__)

def update_whatsapp_config_ports():
    """Update all WhatsApp configurations to use port 8082"""
    try:
        # This would be run from within Odoo environment
        configs = env['whatsapp.config'].search([('bridge_port', '=', 8080)])
        if configs:
            configs.write({'bridge_port': 8082})
            _logger.info(f"Updated {len(configs)} WhatsApp configurations to use port 8082")
            print(f"✅ Updated {len(configs)} WhatsApp configurations to use port 8082")
        else:
            print("ℹ️  No configurations found using port 8080")
    except Exception as e:
        _logger.error(f"Error updating configurations: {e}")
        print(f"❌ Error updating configurations: {e}")

if __name__ == "__main__":
    # For direct execution (won't work without Odoo env)
    print("This script should be run from within Odoo environment")
    print("Example: python3 -c \"exec(open('scripts/update_port.py').read())\"")