#!/bin/bash
# Docker-specific WhatsApp Bridge startup script

set -e

BRIDGE_DIR="/mnt/extra-addons/odoo_whatsapp_mcp/bridge"
BRIDGE_EXEC="$BRIDGE_DIR/bridge"
PID_FILE="$BRIDGE_DIR/bridge.pid"

# Function to check if bridge is running
is_bridge_running() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            return 0
        else
            rm -f "$PID_FILE" 2>/dev/null || true
        fi
    fi
    return 1
}

# Function to start bridge
start_bridge() {
    if is_bridge_running; then
        echo "Bridge is already running"
        return 0
    fi
    
    echo "Starting WhatsApp Bridge..."
    cd "$BRIDGE_DIR"
    
    # Build bridge if executable doesn't exist
    if [ ! -f "$BRIDGE_EXEC" ]; then
        echo "Building WhatsApp Bridge..."
        go build -o bridge production_bridge.go
    fi
    
    # Start bridge in background
    nohup "$BRIDGE_EXEC" > bridge.log 2>&1 &
    echo $! > "$PID_FILE"
    
    echo "Bridge started with PID: $(cat $PID_FILE)"
    
    # Wait a moment and check if it's running
    sleep 3
    if is_bridge_running; then
        echo "✅ Bridge started successfully on port 8082"
        return 0
    else
        echo "❌ Bridge failed to start"
        return 1
    fi
}

# Function to stop bridge
stop_bridge() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if kill -0 "$PID" 2>/dev/null; then
            kill "$PID"
            rm -f "$PID_FILE" 2>/dev/null || true
            echo "Bridge stopped"
        else
            rm -f "$PID_FILE" 2>/dev/null || true
            echo "Bridge was not running"
        fi
    else
        echo "Bridge was not running"
    fi
}

# Function to check bridge status
status_bridge() {
    if is_bridge_running; then
        echo "Bridge is running (PID: $(cat $PID_FILE))"
        curl -s http://localhost:8082/api/status || echo "API not responding"
    else
        echo "Bridge is not running"
    fi
}

# Main command handling
case "${1:-start}" in
    start)
        start_bridge
        ;;
    stop)
        stop_bridge
        ;;
    restart)
        stop_bridge
        sleep 2
        start_bridge
        ;;
    status)
        status_bridge
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac