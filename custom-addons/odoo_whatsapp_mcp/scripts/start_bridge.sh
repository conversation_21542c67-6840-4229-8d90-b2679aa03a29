#!/bin/bash
# WhatsApp Bridge Auto-start Script

BRIDGE_DIR="/opt/odoo/custom-addons/odoo_whatsapp_mcp/bridge"
BRIDGE_EXEC="$BRIDGE_DIR/bridge"
BRIDGE_LOG="/var/log/whatsapp_bridge.log"

# Function to start bridge
start_bridge() {
    echo "Starting WhatsApp Bridge..."
    cd "$BRIDGE_DIR"
    nohup ./bridge > "$BRIDGE_LOG" 2>&1 &
    echo $! > /tmp/bridge.pid
    echo "Bridge started with PID: $(cat /tmp/bridge.pid)"
}

# Function to check if bridge is running
is_bridge_running() {
    if [ -f /tmp/bridge.pid ]; then
        PID=$(cat /tmp/bridge.pid)
        if ps -p $PID > /dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# Main logic
if is_bridge_running; then
    echo "Bridge is already running"
else
    start_bridge
fi