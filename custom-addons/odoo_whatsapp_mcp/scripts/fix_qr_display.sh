#!/bin/bash
# Fix QR code display in Docker container

echo "🔧 Fixing QR code display in Docker container..."

# Method 1: Use HTTP endpoint to get QR code
echo "📱 Getting QR code via HTTP API..."
docker exec odoo18-stack curl -s http://localhost:8082/api/qr | jq -r '.qr_code' > /tmp/qr_code.txt

if [ -s /tmp/qr_code.txt ] && [ "$(cat /tmp/qr_code.txt)" != "null" ]; then
    echo "✅ QR Code retrieved successfully!"
    echo "🔗 Scan this QR code with WhatsApp:"
    echo "=================================================="
    
    # Generate QR code using qrencode if available
    if command -v qrencode >/dev/null 2>&1; then
        qrencode -t ANSIUTF8 < /tmp/qr_code.txt
    else
        echo "📋 QR Code data (use online QR generator):"
        cat /tmp/qr_code.txt
        echo ""
        echo "🌐 Or visit: https://www.qr-code-generator.com/"
        echo "   Paste the above text to generate QR code"
    fi
    
    echo "=================================================="
    echo "📱 Instructions:"
    echo "1. Open WhatsApp on your phone"
    echo "2. Go to Settings > Linked Devices"
    echo "3. Tap 'Link a Device'"
    echo "4. Scan the QR code above"
    echo "=================================================="
else
    echo "❌ Could not retrieve QR code from API"
    echo "🔄 Trying alternative method..."
    
    # Method 2: Restart bridge with proper terminal settings
    echo "🔄 Restarting bridge with UTF-8 support..."
    docker exec -e LANG=C.UTF-8 -e LC_ALL=C.UTF-8 odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart
    
    echo "⏳ Waiting for QR code generation..."
    sleep 5
    
    # Check logs for QR code
    echo "📋 Bridge logs (look for QR code):"
    docker exec odoo18-stack tail -20 /mnt/extra-addons/odoo_whatsapp_mcp/bridge/bridge.log
fi

rm -f /tmp/qr_code.txt