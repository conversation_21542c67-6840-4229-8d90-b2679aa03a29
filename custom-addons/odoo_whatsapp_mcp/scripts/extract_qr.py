#!/usr/bin/env python3
"""
Extract QR code from WhatsApp bridge logs and display it properly
"""
import subprocess
import sys
import re

def get_bridge_logs():
    """Get the bridge logs from Docker container"""
    try:
        result = subprocess.run([
            'docker', 'exec', 'odoo18-stack', 
            'tail', '-50', '/mnt/extra-addons/odoo_whatsapp_mcp/bridge/bridge.log'
        ], capture_output=True, text=True, check=True)
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error getting logs: {e}")
        return None

def extract_qr_from_logs(logs):
    """Extract QR code pattern from logs"""
    if not logs:
        return None
    
    # Look for QR code pattern (lines with █ characters)
    lines = logs.split('\n')
    qr_lines = []
    in_qr = False
    
    for line in lines:
        # Start of QR code
        if '█████████████████████████████████████████████████████████████████' in line:
            in_qr = True
            qr_lines = [line]
        elif in_qr and '█' in line:
            qr_lines.append(line)
        elif in_qr and '▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀▀' in line:
            qr_lines.append(line)
            break
    
    return qr_lines if qr_lines else None

def restart_bridge():
    """Restart the bridge to generate a new QR code"""
    try:
        print("🔄 Restarting bridge to generate new QR code...")
        subprocess.run([
            'docker', 'exec', 'odoo18-stack',
            '/mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh', 'restart'
        ], check=True)
        return True
    except subprocess.CalledProcessError:
        return False

def main():
    print("🔍 Extracting QR code from WhatsApp bridge...")
    
    # Get current logs
    logs = get_bridge_logs()
    qr_lines = extract_qr_from_logs(logs)
    
    if not qr_lines:
        print("❌ No QR code found in current logs")
        if restart_bridge():
            print("⏳ Waiting for new QR code...")
            import time
            time.sleep(5)
            logs = get_bridge_logs()
            qr_lines = extract_qr_from_logs(logs)
    
    if qr_lines:
        print("\n🔗 WHATSAPP QR CODE:")
        print("=" * 65)
        for line in qr_lines:
            print(line)
        print("=" * 65)
        print("📱 Instructions:")
        print("1. Open WhatsApp on your phone")
        print("2. Go to Settings > Linked Devices")
        print("3. Tap 'Link a Device'")
        print("4. Scan the QR code above")
        print("=" * 65)
        print("\n💡 If QR code doesn't scan properly:")
        print("   - Make sure your terminal supports Unicode characters")
        print("   - Try zooming out to see the full QR code")
        print("   - Use a QR code reader app if WhatsApp doesn't work")
    else:
        print("❌ Could not extract QR code from logs")
        print("🔧 Try running: docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart")

if __name__ == "__main__":
    main()