#!/bin/bash
# Docker entrypoint script to start both Odoo and WhatsApp Bridge

# Start WhatsApp Bridge in background
echo "Starting WhatsApp Bridge..."
cd /opt/odoo/custom-addons/odoo_whatsapp_mcp/bridge
chmod +x bridge
nohup ./bridge > /var/log/whatsapp_bridge.log 2>&1 &
echo $! > /tmp/bridge.pid

# Start bridge supervisor
python3 /opt/odoo/custom-addons/odoo_whatsapp_mcp/scripts/bridge_supervisor.py monitor &

# Continue with original Odoo startup
exec "$@"