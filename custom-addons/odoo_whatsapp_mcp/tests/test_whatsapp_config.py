from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock

class TestWhatsAppConfig(TransactionCase):

    def setUp(self):
        super(TestWhatsAppConfig, self).setUp()
        self.WhatsAppConfig = self.env['whatsapp.config']
        self.whatsapp_config = self.WhatsAppConfig.create({
            'name': 'Test WhatsApp Config',
            'bridge_url': 'http://test-bridge',
            'bridge_port': 8080,
            'is_active': True,
        })

    @patch('odoo.addons.odoo_whatsapp_mcp.scripts.run_bridge.start_whatsapp_bridge')
    def test_action_get_qr_code_success(self, mock_start_bridge):
        """Test that getting the QR code successfully starts the bridge."""
        # Mock the return value of the start_whatsapp_bridge function
        mock_process = MagicMock()
        mock_start_bridge.return_value = mock_process

        # Call the method to test
        result = self.whatsapp_config.action_get_qr_code()

        # Assertions
        mock_start_bridge.assert_called_once()
        self.assertEqual(self.whatsapp_config.state, 'waiting_qr')
        self.assertIn('Bridge Started', result['params']['title'])

    @patch('odoo.addons.odoo_whatsapp_mcp.scripts.run_bridge.start_whatsapp_bridge')
    def test_action_get_qr_code_failure(self, mock_start_bridge):
        """Test that an error is raised if the bridge fails to start."""
        # Mock the start_whatsapp_bridge function to return None
        mock_start_bridge.return_value = None

        # Expect a UserError
        with self.assertRaises(Exception):
            self.whatsapp_config.action_get_qr_code()