import unittest
from unittest.mock import patch, MagicMock
import subprocess
import time

class TestBridgeIntegration(unittest.TestCase):
    """Test cases for WhatsApp Bridge integration"""

    def setUp(self):
        """Set up test environment"""
        from odoo.addons.odoo_whatsapp_mcp.scripts import run_bridge
        self.bridge_module = run_bridge

    @patch('subprocess.Popen')
    def test_bridge_startup(self, mock_popen):
        """Test bridge process startup"""
        mock_process = MagicMock()
        mock_process.pid = 12345
        mock_process.poll.return_value = None
        mock_popen.return_value = mock_process
        
        process = self.bridge_module.start_whatsapp_bridge()
        
        self.assertIsNotNone(process)
        self.assertEqual(process.pid, 12345)
        mock_popen.assert_called_once()

    @patch('subprocess.Popen')
    def test_bridge_already_running(self, mock_popen):
        """Test bridge when already running"""
        mock_process = MagicMock()
        mock_process.pid = 12345
        mock_process.poll.return_value = None
        
        # Set global process
        self.bridge_module._bridge_process = mock_process
        
        process = self.bridge_module.start_whatsapp_bridge()
        
        self.assertEqual(process, mock_process)
        mock_popen.assert_not_called()

    def test_bridge_running_check(self):
        """Test bridge running status check"""
        # Test when no process
        self.bridge_module._bridge_process = None
        self.assertFalse(self.bridge_module.is_bridge_running())
        
        # Test when process exists but terminated
        mock_process = MagicMock()
        mock_process.poll.return_value = 0  # Process terminated
        self.bridge_module._bridge_process = mock_process
        self.assertFalse(self.bridge_module.is_bridge_running())
        
        # Test when process is running
        mock_process.poll.return_value = None  # Process running
        self.assertTrue(self.bridge_module.is_bridge_running())

    @patch('requests.get')
    def test_bridge_connection_test(self, mock_get):
        """Test bridge connection testing"""
        # Test successful connection
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response
        
        result = self.bridge_module.test_bridge_connection()
        self.assertTrue(result)
        
        # Test failed connection
        mock_get.side_effect = Exception("Connection failed")
        result = self.bridge_module.test_bridge_connection()
        self.assertFalse(result)

    def test_bridge_stop(self):
        """Test bridge process stopping"""
        mock_process = MagicMock()
        mock_process.poll.return_value = None  # Running
        self.bridge_module._bridge_process = mock_process
        
        result = self.bridge_module.stop_whatsapp_bridge()
        
        self.assertTrue(result)
        mock_process.terminate.assert_called_once()
        mock_process.wait.assert_called_once_with(timeout=10)

if __name__ == '__main__':
    unittest.main()