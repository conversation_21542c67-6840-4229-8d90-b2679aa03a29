import json
from unittest.mock import patch, MagicMock
from odoo.tests.common import HttpCase

class TestWhatsAppWebhook(HttpCase):
    """Test cases for WhatsApp webhook endpoint"""

    def setUp(self):
        super().setUp()
        self.WhatsAppMessage = self.env['whatsapp.message']
        
        # Create test configuration
        self.config = self.env['whatsapp.config'].create({
            'name': 'Test Config',
            'bridge_url': 'http://localhost',
            'bridge_port': 8080,
            'is_active': True,
            'state': 'connected'
        })

    def test_webhook_endpoint_exists(self):
        """Test that webhook endpoint is accessible"""
        # Test webhook status endpoint
        response = self.url_open('/whatsapp/status')
        self.assertEqual(response.status_code, 200)

    def test_incoming_message_webhook(self):
        """Test processing incoming message via webhook"""
        webhook_data = {
            'type': 'text',
            'from': '+1234567890',
            'message': 'Hello from webhook test',
            'message_id': 'webhook_test_123',
            'timestamp': '2024-01-01T12:00:00Z'
        }
        
        # Simulate webhook call
        with patch.object(self.WhatsAppMessage, 'process_incoming_message') as mock_process:
            mock_process.return_value = True
            
            response = self.url_open(
                '/whatsapp/webhook',
                data=json.dumps(webhook_data),
                headers={'Content-Type': 'application/json'}
            )
            
            self.assertEqual(response.status_code, 200)
            mock_process.assert_called_once_with(webhook_data)

    def test_webhook_error_handling(self):
        """Test webhook error handling"""
        invalid_data = {'invalid': 'data'}
        
        with patch.object(self.WhatsAppMessage, 'process_incoming_message') as mock_process:
            mock_process.side_effect = Exception("Processing failed")
            
            response = self.url_open(
                '/whatsapp/webhook',
                data=json.dumps(invalid_data),
                headers={'Content-Type': 'application/json'}
            )
            
            self.assertEqual(response.status_code, 200)  # Should handle gracefully
            response_data = response.json()
            self.assertEqual(response_data['status'], 'error')

    def test_media_message_webhook(self):
        """Test processing media message via webhook"""
        media_webhook_data = {
            'type': 'image',
            'from': '+1234567890',
            'message': 'Check this image',
            'message_id': 'media_test_123',
            'attachment': 'base64_encoded_image_data',
            'filename': 'test_image.jpg'
        }
        
        # Test media message processing
        message = self.WhatsAppMessage.process_incoming_message(media_webhook_data)
        
        self.assertTrue(message)
        self.assertEqual(message.message_type, 'image')
        self.assertEqual(message.attachment_name, 'test_image.jpg')

    def test_contact_linking_webhook(self):
        """Test automatic contact linking via webhook"""
        # Create a test partner
        partner = self.env['res.partner'].create({
            'name': 'Test Contact',
            'phone': '+1234567890'
        })
        
        webhook_data = {
            'type': 'text',
            'from': '+1234567890',
            'message': 'Message from existing contact',
            'message_id': 'contact_link_test'
        }
        
        message = self.WhatsAppMessage.process_incoming_message(webhook_data)
        
        self.assertTrue(message)
        self.assertEqual(message.res_model, 'res.partner')
        self.assertEqual(message.res_id, partner.id)

    def test_webhook_authentication(self):
        """Test webhook authentication requirements"""
        # Test that webhook endpoint doesn't require authentication
        # (as it's called by external bridge)
        webhook_data = {'test': 'data'}
        
        response = self.url_open(
            '/whatsapp/webhook',
            data=json.dumps(webhook_data),
            headers={'Content-Type': 'application/json'}
        )
        
        # Should not return 401/403 (authentication errors)
        self.assertNotIn(response.status_code, [401, 403])