from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError

class TestWhatsAppConversation(TransactionCase):

    def setUp(self):
        super().setUp()
        self.WhatsAppConversation = self.env['whatsapp.conversation']
        self.WhatsAppMessage = self.env['whatsapp.message']
        self.Partner = self.env['res.partner']
        
        # Create test partner
        self.partner = self.Partner.create({
            'name': 'Test Contact',
            'phone': '+1234567890'
        })

    def test_conversation_creation(self):
        """Test conversation creation"""
        conversation = self.WhatsAppConversation.create({
            'phone': '+1234567890',
            'partner_id': self.partner.id
        })
        
        self.assertEqual(conversation.phone, '+1234567890')
        self.assertEqual(conversation.partner_id, self.partner)
        self.assertEqual(conversation.name, 'Test Contact')

    def test_get_or_create_conversation(self):
        """Test get or create conversation method"""
        # First call should create
        conversation1 = self.WhatsAppConversation.get_or_create_conversation('+1234567890')
        self.assertTrue(conversation1)
        self.assertEqual(conversation1.phone, '+1234567890')
        
        # Second call should return existing
        conversation2 = self.WhatsAppConversation.get_or_create_conversation('+1234567890')
        self.assertEqual(conversation1.id, conversation2.id)

    def test_mark_as_read(self):
        """Test marking conversation as read"""
        conversation = self.WhatsAppConversation.create({
            'phone': '+1234567890',
            'unread_count': 5
        })
        
        # Create unread messages
        for i in range(3):
            self.WhatsAppMessage.create({
                'phone': '+1234567890',
                'message': f'Test message {i}',
                'direction': 'inbound',
                'conversation_id': conversation.id,
                'is_read': False
            })
        
        conversation.action_mark_read()
        self.assertEqual(conversation.unread_count, 0)
        
        # Check messages are marked as read
        unread_messages = conversation.message_ids.filtered(lambda m: not m.is_read)
        self.assertEqual(len(unread_messages), 0)

    def test_send_reply(self):
        """Test sending reply from conversation"""
        conversation = self.WhatsAppConversation.create({
            'phone': '+1234567890',
            'reply_message': 'Test reply'
        })
        
        # Mock the send message method to avoid actual sending
        original_send = self.WhatsAppMessage.action_send_message
        def mock_send(self):
            self.write({'state': 'sent'})
            return True
        
        self.WhatsAppMessage.action_send_message = mock_send
        
        try:
            result = conversation.action_send_reply()
            self.assertEqual(result['params']['type'], 'success')
            self.assertEqual(conversation.reply_message, '')
            
            # Check message was created
            reply_message = self.WhatsAppMessage.search([
                ('conversation_id', '=', conversation.id),
                ('direction', '=', 'outbound')
            ])
            self.assertEqual(len(reply_message), 1)
            self.assertEqual(reply_message.message, 'Test reply')
        finally:
            # Restore original method
            self.WhatsAppMessage.action_send_message = original_send

    def test_archive_conversation(self):
        """Test archiving conversation"""
        conversation = self.WhatsAppConversation.create({
            'phone': '+1234567890'
        })
        
        self.assertFalse(conversation.is_archived)
        conversation.action_archive()
        self.assertTrue(conversation.is_archived)
        
        conversation.action_unarchive()
        self.assertFalse(conversation.is_archived)

    def test_last_message_preview(self):
        """Test last message preview computation"""
        conversation = self.WhatsAppConversation.create({
            'phone': '+1234567890'
        })
        
        # Create a message
        message = self.WhatsAppMessage.create({
            'phone': '+1234567890',
            'message': 'This is a test message for preview',
            'direction': 'inbound',
            'conversation_id': conversation.id
        })
        
        conversation._compute_last_message()
        self.assertEqual(conversation.last_message_preview, 'This is a test message for preview')
        
        # Test long message truncation
        long_message = 'A' * 150
        message.write({'message': long_message})
        conversation._compute_last_message()
        self.assertTrue(conversation.last_message_preview.endswith('...'))
        self.assertEqual(len(conversation.last_message_preview), 103)  # 100 + '...'