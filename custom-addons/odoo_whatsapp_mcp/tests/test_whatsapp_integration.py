import json
from unittest.mock import patch, MagicMock
from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError

class TestWhatsAppIntegration(TransactionCase):

    def setUp(self):
        super().setUp()
        self.WhatsAppConfig = self.env['whatsapp.config']
        self.WhatsAppMessage = self.env['whatsapp.message']
        
        # Create test configuration
        self.config = self.WhatsAppConfig.create({
            'name': 'Test WhatsApp Config',
            'bridge_url': 'http://localhost',
            'bridge_port': 8080,
            'is_active': True,
            'state': 'connected'
        })

    def test_config_creation(self):
        """Test WhatsApp configuration creation"""
        self.assertEqual(self.config.name, 'Test WhatsApp Config')
        self.assertEqual(self.config.bridge_port, 8080)
        self.assertTrue(self.config.is_active)

    def test_only_one_active_config(self):
        """Test that only one config can be active per company"""
        config2 = self.WhatsAppConfig.create({
            'name': 'Test Config 2',
            'bridge_url': 'http://localhost',
            'bridge_port': 8081,
            'is_active': True
        })
        
        # First config should be deactivated
        self.config.refresh()
        self.assertFalse(self.config.is_active)
        self.assertTrue(config2.is_active)

    @patch('requests.get')
    def test_connection_check_success(self, mock_get):
        """Test successful connection check"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'connected': True}
        mock_get.return_value = mock_response
        
        result = self.config.action_check_connection()
        
        self.assertEqual(result['params']['type'], 'success')
        self.assertEqual(self.config.state, 'connected')

    @patch('requests.get')
    def test_connection_check_failure(self, mock_get):
        """Test failed connection check"""
        mock_get.side_effect = Exception('Connection failed')
        
        with self.assertRaises(UserError):
            self.config.action_check_connection()

    def test_message_creation(self):
        """Test WhatsApp message creation"""
        message = self.WhatsAppMessage.create({
            'phone': '+1234567890',
            'message': 'Test message',
            'message_type': 'text',
            'direction': 'outbound'
        })
        
        self.assertEqual(message.phone, '+1234567890')
        self.assertEqual(message.state, 'draft')
        self.assertTrue(message.name.startswith('Test message'))

    @patch('requests.post')
    def test_message_sending_success(self, mock_post):
        """Test successful message sending"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {'message_id': 'test123'}
        mock_post.return_value = mock_response
        
        message = self.WhatsAppMessage.create({
            'phone': '+1234567890',
            'message': 'Test message',
            'message_type': 'text',
            'direction': 'outbound'
        })
        
        message.action_send_message()
        
        self.assertEqual(message.state, 'sent')
        self.assertEqual(message.whatsapp_message_id, 'test123')

    @patch('requests.post')
    def test_message_sending_failure(self, mock_post):
        """Test failed message sending"""
        mock_response = MagicMock()
        mock_response.status_code = 400
        mock_response.json.return_value = {'error': 'Invalid phone number'}
        mock_post.return_value = mock_response
        
        message = self.WhatsAppMessage.create({
            'phone': 'invalid',
            'message': 'Test message',
            'message_type': 'text',
            'direction': 'outbound'
        })
        
        with self.assertRaises(UserError):
            message.action_send_message()
        
        self.assertEqual(message.state, 'failed')

    def test_incoming_message_processing(self):
        """Test processing of incoming messages"""
        incoming_data = {
            'type': 'text',
            'from': '+1234567890',
            'message': 'Hello from WhatsApp',
            'message_id': 'incoming123'
        }
        
        message = self.WhatsAppMessage.process_incoming_message(incoming_data)
        
        self.assertTrue(message)
        self.assertEqual(message.direction, 'inbound')
        self.assertEqual(message.phone, '+1234567890')
        self.assertEqual(message.message, 'Hello from WhatsApp')
        self.assertEqual(message.state, 'delivered')

    def test_bridge_script_functions(self):
        """Test bridge script utility functions"""
        from odoo.addons.odoo_whatsapp_mcp.scripts import run_bridge
        
        # Test connection test function
        with patch('requests.get') as mock_get:
            mock_get.return_value.status_code = 200
            self.assertTrue(run_bridge.test_bridge_connection())
            
            mock_get.side_effect = Exception()
            self.assertFalse(run_bridge.test_bridge_connection())

    def test_webhook_endpoint(self):
        """Test webhook endpoint functionality"""
        # Simulate webhook call
        webhook_data = {
            'type': 'text',
            'from': '+1234567890',
            'message': 'Webhook test message',
            'message_id': 'webhook123'
        }
        
        # Test webhook processing
        result = self.WhatsAppMessage.process_incoming_message(webhook_data)
        self.assertTrue(result)
        
        # Verify message was created
        message = self.WhatsAppMessage.search([('whatsapp_message_id', '=', 'webhook123')])
        self.assertEqual(len(message), 1)
        self.assertEqual(message.direction, 'inbound')