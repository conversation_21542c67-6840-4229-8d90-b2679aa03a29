# WhatsApp MCP Docker Setup Guide

## Overview
This guide explains how to run the WhatsApp MCP integration in Docker containers with Odoo 18.

## Docker Configuration

### 1. Port Configuration
The WhatsApp bridge now runs on **port 8082** (changed from 8080 to avoid Docker conflicts).

**Updated docker-compose.yml:**
```yaml
services:
  odoo18-stack:
    ports:
      - "8069:8069"  # Odoo Web
      - "8072:8072"  # Odoo Longpolling
      - "8082:8082"  # WhatsApp Bridge (UPDATED)
      - "8081:8081"  # MCP Server
```

### 2. Dockerfile Updates
- ✅ Go 1.21.5 installed for WhatsApp bridge
- ✅ Port 8082 exposed
- ✅ Bridge dependencies included

### 3. Automatic Bridge Startup
The Docker entrypoint automatically:
- Builds the WhatsApp bridge from source
- Starts the bridge on port 8082
- Monitors bridge health
- Logs to `/var/log/whatsapp_bridge.log`

## Setup Instructions

### 1. Build and Start Containers
```bash
cd /Users/<USER>/workspace/18_Project/Odoo18Deployment
docker-compose up --build -d
```

### 2. Verify Bridge Status
```bash
# Check if bridge is running
docker exec odoo18-stack curl -s http://localhost:8082/api/status

# Check bridge logs
docker exec odoo18-stack tail -f /var/log/whatsapp_bridge.log

# Check bridge process
docker exec odoo18-stack ps aux | grep bridge
```

### 3. Configure WhatsApp in Odoo
1. Access Odoo: http://localhost:8069
2. Go to **WhatsApp > Configuration**
3. Create new configuration:
   - **Name**: Docker WhatsApp Bridge
   - **Bridge URL**: http://localhost
   - **Bridge Port**: 8082
   - **Active**: ✓

### 4. Get QR Code
1. Click **"Get QR Code"** button
2. Check container logs for QR code:
   ```bash
   docker logs odoo18-stack | grep -A 20 "SCAN THIS QR CODE"
   ```
3. Or check bridge logs:
   ```bash
   docker exec odoo18-stack tail -20 /var/log/whatsapp_bridge.log
   ```

## Container Management

### Bridge Management Commands
```bash
# Start bridge manually
docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh start

# Stop bridge
docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh stop

# Check bridge status
docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh status

# Restart bridge
docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart
```

### Container Logs
```bash
# Odoo logs
docker logs -f odoo18-stack

# Bridge logs specifically
docker exec odoo18-stack tail -f /var/log/whatsapp_bridge.log

# All container logs
docker-compose logs -f
```

## Network Configuration

### Internal Container Communication
- Odoo container: `odoo18-stack`
- Database container: `database18-stack`
- Bridge runs inside Odoo container on port 8082

### External Access
- Odoo Web: http://localhost:8069
- WhatsApp Bridge API: http://localhost:8082
- MCP Server: http://localhost:8081

## Troubleshooting

### Bridge Not Starting
1. **Check Go installation:**
   ```bash
   docker exec odoo18-stack go version
   ```

2. **Check bridge source:**
   ```bash
   docker exec odoo18-stack ls -la /mnt/extra-addons/odoo_whatsapp_mcp/bridge/
   ```

3. **Manual bridge build:**
   ```bash
   docker exec odoo18-stack bash -c "cd /mnt/extra-addons/odoo_whatsapp_mcp/bridge && go build -o bridge production_bridge.go"
   ```

### Port Conflicts
1. **Check port usage:**
   ```bash
   docker exec odoo18-stack netstat -tlnp | grep 8082
   ```

2. **Change port if needed:**
   - Update `production_bridge.go`
   - Update `docker-compose.yml`
   - Rebuild containers

### Permission Issues
```bash
# Fix permissions
docker exec odoo18-stack chown -R odoo:odoo /mnt/extra-addons/odoo_whatsapp_mcp/
```

### Database Connection
```bash
# Test database connection
docker exec odoo18-stack psql -h database18-stack -U odoo -d postgres -c "SELECT 1;"
```

## Development Workflow

### 1. Code Changes
After modifying bridge code:
```bash
# Rebuild bridge
docker exec odoo18-stack bash -c "cd /mnt/extra-addons/odoo_whatsapp_mcp/bridge && go build -o bridge production_bridge.go"

# Restart bridge
docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart
```

### 2. Odoo Module Updates
```bash
# Restart Odoo container
docker-compose restart odoo18-stack

# Or update module
docker exec odoo18-stack odoo-bin -u odoo_whatsapp_mcp --stop-after-init
```

### 3. Full Rebuild
```bash
# Complete rebuild
docker-compose down
docker-compose up --build -d
```

## Production Considerations

### 1. Persistent Storage
Ensure WhatsApp session data persists:
```yaml
volumes:
  - whatsapp-data:/mnt/extra-addons/odoo_whatsapp_mcp/bridge/store
```

### 2. Health Checks
Monitor bridge health:
```bash
# Add to monitoring
curl -f http://localhost:8082/api/status || exit 1
```

### 3. Backup Strategy
- Backup bridge store directory
- Backup Odoo database
- Backup configuration files

### 4. Security
- Use environment variables for sensitive data
- Implement proper firewall rules
- Regular security updates

## Environment Variables

### Optional Configuration
```yaml
environment:
  - WHATSAPP_BRIDGE_PORT=8082
  - WHATSAPP_BRIDGE_HOST=0.0.0.0
  - BRIDGE_LOG_LEVEL=INFO
```

---

## Quick Start Checklist

- [ ] Update docker-compose.yml port mapping (8080 → 8082)
- [ ] Build containers: `docker-compose up --build -d`
- [ ] Verify bridge: `curl http://localhost:8082/api/status`
- [ ] Configure WhatsApp in Odoo (port 8082)
- [ ] Get QR code and scan with WhatsApp
- [ ] Test message sending

**Status**: ✅ Docker setup complete and tested
**Bridge Port**: 8082
**Last Updated**: July 3, 2025