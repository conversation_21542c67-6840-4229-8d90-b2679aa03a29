#!/bin/bash
# Simple script to get WhatsApp QR code

echo "🔄 Getting WhatsApp QR Code..."

# Restart bridge to get fresh QR
docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart

echo "⏳ Waiting for QR code generation..."
sleep 5

echo ""
echo "🔗 WHATSAPP QR CODE:"
echo "================================================================="

# Get the QR code from logs
docker exec odoo18-stack tail -30 /mnt/extra-addons/odoo_whatsapp_mcp/bridge/bridge.log | grep -A 25 "█████████████████████████████████████████████████████████████████"

echo "================================================================="
echo "📱 Instructions:"
echo "1. Open WhatsApp on your phone"
echo "2. Go to Settings > Linked Devices"  
echo "3. Tap 'Link a Device'"
echo "4. Scan the QR code above"
echo "================================================================="
echo ""
echo "💡 Tips:"
echo "- Make sure your terminal window is wide enough"
echo "- Zoom out if the QR code appears cut off"
echo "- The QR code expires after ~20 seconds, run this script again if needed"
echo ""
echo "🔍 To check connection status:"
echo "docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh status"