<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Conversation List View -->
        <record id="view_whatsapp_conversation_list" model="ir.ui.view">
            <field name="name">WhatsApp Conversations</field>
            <field name="model">whatsapp.conversation</field>
            <field name="arch" type="xml">
                <list decoration-bf="unread_count &gt; 0" create="false">
                    <field name="partner_id" widget="many2one_avatar_user"/>
                    <field name="name"/>
                    <field name="phone"/>
                    <field name="last_message_preview"/>
                    <field name="last_message_date" widget="datetime"/>
                    <field name="unread_count" widget="badge" decoration-info="unread_count &gt; 0"/>
                    <field name="is_archived" column_invisible="1"/>
                </list>
            </field>
        </record>

        <!-- Conversation Form View -->
        <record id="view_whatsapp_conversation_form" model="ir.ui.view">
            <field name="name">WhatsApp Conversation</field>
            <field name="model">whatsapp.conversation</field>
            <field name="arch" type="xml">
                <form create="false">
                    <header>
                        <button name="action_mark_read" type="object" string="Mark as Read" 
                                invisible="unread_count == 0" class="btn-secondary"/>
                        <button name="action_create_channel" type="object" string="Create Channel" 
                                invisible="discuss_channel_id" class="btn-primary"/>
                        <button name="action_open_channel" type="object" string="Open Channel" 
                                invisible="not discuss_channel_id" class="btn-primary"/>
                        <button name="action_sync_to_channel" type="object" string="Sync to Channel" 
                                invisible="not discuss_channel_id" class="btn-secondary"/>
                        <button name="action_archive" type="object" string="Archive" 
                                invisible="is_archived" class="btn-secondary"/>
                        <button name="action_unarchive" type="object" string="Unarchive" 
                                invisible="not is_archived" class="btn-secondary"/>
                    </header>
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="name" readonly="1"/>
                            </h1>
                            <h2>
                                <field name="phone" readonly="1"/>
                            </h2>
                        </div>
                        <group>
                            <group>
                                <field name="partner_id" readonly="1"/>
                                <field name="last_message_date" readonly="1"/>
                            </group>
                            <group>
                                <field name="unread_count" readonly="1"/>
                                <field name="is_archived" readonly="1"/>
                                <field name="discuss_channel_id" readonly="1"/>
                                <field name="auto_create_channel"/>
                            </group>
                        </group>
                        <group string="Channel Members" invisible="not discuss_channel_id">
                            <field name="channel_members" widget="many2many_tags"/>
                        </group>
                        <notebook>
                            <page string="Messages" name="messages">
                                <field name="message_ids" readonly="1">
                                    <list decoration-muted="direction == 'inbound'" 
                                          decoration-info="direction == 'outbound'" create="false" edit="false">
                                        <field name="create_date" widget="datetime"/>
                                        <field name="direction" column_invisible="1"/>
                                        <field name="message"/>
                                        <field name="state" widget="badge"/>
                                        <field name="attachment_name" optional="show"/>
                                        <field name="is_read" column_invisible="1"/>
                                    </list>
                                </field>
                                <div class="mt-3">
                                    <field name="reply_message" placeholder="Type your message..." 
                                           widget="text" class="form-control"/>
                                    <button name="action_send_reply" type="object" 
                                            string="Send" class="btn btn-primary mt-2"/>
                                </div>
                            </page>
                        </notebook>
                    </sheet>
                    <chatter/>
                </form>
            </field>
        </record>

        <!-- Conversation Search View -->
        <record id="view_whatsapp_conversation_search" model="ir.ui.view">
            <field name="name">WhatsApp Conversation Search</field>
            <field name="model">whatsapp.conversation</field>
            <field name="arch" type="xml">
                <search>
                    <field name="name" string="Contact"/>
                    <field name="phone"/>
                    <field name="partner_id"/>
                    <separator/>
                    <filter name="unread" string="Unread" domain="[('unread_count', '&gt;', 0)]"/>
                    <filter name="archived" string="Archived" domain="[('is_archived', '=', True)]"/>
                    <filter name="active" string="Active" domain="[('is_archived', '=', False)]"/>
                    <separator/>
                    <filter name="today" string="Today" 
                            domain="[('last_message_date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0)))]"/>
                    <filter name="week" string="This Week" 
                            domain="[('last_message_date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                    <group expand="0" string="Group By">
                        <filter name="group_partner" string="Contact" context="{'group_by': 'partner_id'}"/>
                        <filter name="group_date" string="Date" context="{'group_by': 'last_message_date:day'}"/>
                    </group>
                </search>
            </field>
        </record>

        <!-- Unified Inbox View -->
        <record id="view_whatsapp_inbox_form" model="ir.ui.view">
            <field name="name">WhatsApp Inbox</field>
            <field name="model">whatsapp.conversation</field>
            <field name="arch" type="xml">
                <form create="false" edit="false">
                    <sheet>
                        <div class="row">
                            <div class="col-md-4">
                                <h3>Conversations</h3>
                                <field name="id" invisible="1"/>
                            </div>
                            <div class="col-md-8">
                                <h3>Messages</h3>
                                <div class="alert alert-info" role="status">
                                    <i class="fa fa-info-circle" title="Information"/> Select a conversation from the list to view messages
                                </div>
                            </div>
                        </div>
                    </sheet>
                </form>
            </field>
        </record>

        <!-- Window Actions -->
        <record id="action_whatsapp_conversations" model="ir.actions.act_window">
            <field name="name">WhatsApp Conversations</field>
            <field name="res_model">whatsapp.conversation</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('is_archived', '=', False)]</field>
            <field name="context">{'default_is_archived': False}</field>
            <field name="help" type="html">
                <p class="o_view_nocontent_smiling_face">
                    No WhatsApp conversations yet!
                </p>
                <p>
                    Conversations will appear here when you receive messages from WhatsApp.
                </p>
            </field>
        </record>

        <record id="action_whatsapp_inbox" model="ir.actions.act_window">
            <field name="name">WhatsApp Inbox</field>
            <field name="res_model">whatsapp.conversation</field>
            <field name="view_mode">list,form</field>
            <field name="view_id" ref="view_whatsapp_conversation_list"/>
            <field name="domain">[('is_archived', '=', False)]</field>
            <field name="context">{'default_is_archived': False}</field>
        </record>

        <record id="action_whatsapp_archived" model="ir.actions.act_window">
            <field name="name">Archived Conversations</field>
            <field name="res_model">whatsapp.conversation</field>
            <field name="view_mode">list,form</field>
            <field name="domain">[('is_archived', '=', True)]</field>
            <field name="context">{'default_is_archived': True}</field>
        </record>

    </data>
</odoo>