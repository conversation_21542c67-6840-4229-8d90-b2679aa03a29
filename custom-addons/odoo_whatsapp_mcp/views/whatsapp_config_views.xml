<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
    <!-- WhatsApp Configuration Form View -->
    <record id="view_whatsapp_config_form" model="ir.ui.view">
        <field name="name">whatsapp.config.form</field>
        <field name="model">whatsapp.config</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Configuration">
                <header>
                    <button name="action_get_qr_code" string="Start Bridge And Get QR Code" type="object" class="oe_highlight"
                            invisible="state in ['connected']"/>
                    <button name="action_refresh_qr_code" string="Refresh QR Code" type="object" class="btn-primary"
                            invisible="state not in ['waiting_qr']"/>
                    <button name="action_check_connection" string="Check Connection" type="object" class="btn-info"/>
                    <button name="action_sync_messages" string="Sync Messages" type="object" class="btn-success"
                            invisible="state not in ['connected']"/>
                    <button name="action_disconnect" string="Disconnect" type="object" class="btn-danger"
                            invisible="state not in ['connected']"/>
                    <button name="action_stop_bridge" string="Stop Bridge" type="object" class="btn-secondary"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,waiting_qr,connected"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="e.g. Company WhatsApp"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="bridge_url"/>
                            <field name="bridge_port"/>
                            <field name="is_active"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                        <group>
                            <field name="last_connection" readonly="1"/>
                        </group>
                    </group>
                    
                    <!-- QR Code Section -->
                    <div class="row" invisible="state != 'waiting_qr'">
                        <div class="col-12">
                            <div class="alert alert-info" role="status">
                                <h4 class="alert-heading">WhatsApp QR Code</h4>
                                <p>Scan this QR code with your WhatsApp mobile app to connect:</p>
                                <ol>
                                    <li>Open WhatsApp on your phone</li>
                                    <li>Go to <strong>Settings &gt; Linked Devices</strong></li>
                                    <li>Tap <strong>"Link a Device"</strong></li>
                                    <li>Scan the QR code below</li>
                                </ol>
                                <hr/>
                                <div class="text-center">
                                    <field name="qr_code" widget="image" options="{'size': [300, 300]}" nolabel="1"/>
                                </div>
                                <div class="text-center mt-3">
                                    <button name="action_refresh_qr_code" string="Refresh QR Code" type="object" class="btn btn-primary btn-sm"/>
                                    <button name="action_check_connection" string="Check Connection" type="object" class="btn btn-success btn-sm ml-2"/>
                                </div>
                                <small class="text-muted">QR codes expire after 20 seconds. Click &quot;Refresh QR Code&quot; if needed.</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Connection Status -->
                    <div class="row" invisible="state != 'connected'">
                        <div class="col-12">
                            <div class="alert alert-success" role="status">
                                <h4 class="alert-heading">WhatsApp Connected!</h4>
                                <p>Your WhatsApp is successfully connected and ready to use.</p>
                                <p><strong>Last Connection:</strong> <field name="last_connection" readonly="1" nolabel="1"/></p>
                            </div>
                        </div>
                    </div>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- WhatsApp Configuration Tree View -->
    <record id="view_whatsapp_config_tree" model="ir.ui.view">
        <field name="name">whatsapp.config.tree</field>
        <field name="model">whatsapp.config</field>
        <field name="arch" type="xml">
            <list string="WhatsApp Configurations">
                <field name="name"/>
                <field name="bridge_url"/>
                <field name="bridge_port"/>
                <field name="is_active"/>
                <field name="state"/>
                <field name="last_connection"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- WhatsApp Configuration Search View -->
    <record id="view_whatsapp_config_search" model="ir.ui.view">
        <field name="name">whatsapp.config.search</field>
        <field name="model">whatsapp.config</field>
        <field name="arch" type="xml">
            <search string="Search WhatsApp Configuration">
                <field name="name"/>
                <field name="bridge_url"/>
                <field name="state"/>
                <filter string="Active" name="active" domain="[('is_active', '=', True)]"/>
                <filter string="Connected" name="connected" domain="[('state', '=', 'connected')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Company" name="group_by_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Configuration Action -->
    <record id="action_whatsapp_config" model="ir.actions.act_window">
        <field name="name">WhatsApp Configuration</field>
        <field name="res_model">whatsapp.config</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_whatsapp_config_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first WhatsApp configuration!
            </p>
            <p>
                Configure WhatsApp integration to enable messaging in Odoo.
            </p>
        </field>
    </record>

    <!-- WhatsApp Configuration Menu -->
    <menuitem id="menu_whatsapp_root"
              name="WhatsApp"
              sequence="10"/>

    <menuitem id="menu_whatsapp_config"
              name="Configuration"
              parent="menu_whatsapp_root"
              action="action_whatsapp_config"
              sequence="10"/>
    </data>
</odoo>