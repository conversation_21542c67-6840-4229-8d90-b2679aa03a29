<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
    <!-- WhatsApp Message Form View -->
    <record id="view_whatsapp_message_form" model="ir.ui.view">
        <field name="name">whatsapp.message.form</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <form string="WhatsApp Message">
                <header>
                    <button name="action_send_message" string="Send" type="object" class="oe_highlight"
                            invisible="state not in ['draft']"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,sending,sent,delivered,read"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="message_type"/>
                            <field name="direction"/>
                            <field name="phone"/>
                            <field name="message"/>
                        </group>
                        <group>
                            <field name="attachment" filename="attachment_name" widget="binary"/>
                            <field name="attachment_name" invisible="1"/>
                            <field name="whatsapp_message_id" readonly="1"/>
                            <field name="error_message" readonly="1" invisible="state != 'failed'"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <group string="Related Document" invisible="not res_model">
                        <field name="res_model"/>
                        <field name="res_id"/>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- WhatsApp Message Tree View -->
    <record id="view_whatsapp_message_tree" model="ir.ui.view">
        <field name="name">whatsapp.message.tree</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <list string="WhatsApp Messages" decoration-info="state in ('draft','sending')" decoration-success="state in ('sent','delivered','read')" decoration-danger="state=='failed'">
                <field name="create_date"/>
                <field name="name"/>
                <field name="message_type"/>
                <field name="direction"/>
                <field name="phone"/>
                <field name="state"/>
                <field name="company_id" groups="base.group_multi_company"/>
            </list>
        </field>
    </record>

    <!-- WhatsApp Message Search View -->
    <record id="view_whatsapp_message_search" model="ir.ui.view">
        <field name="name">whatsapp.message.search</field>
        <field name="model">whatsapp.message</field>
        <field name="arch" type="xml">
            <search string="Search WhatsApp Messages">
                <field name="name"/>
                <field name="phone"/>
                <field name="message"/>
                <field name="whatsapp_message_id"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Sending" name="sending" domain="[('state', '=', 'sending')]"/>
                <filter string="Sent" name="sent" domain="[('state', '=', 'sent')]"/>
                <filter string="Delivered" name="delivered" domain="[('state', '=', 'delivered')]"/>
                <filter string="Read" name="read" domain="[('state', '=', 'read')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                <separator/>
                <filter string="Outbound" name="outbound" domain="[('direction', '=', 'outbound')]"/>
                <filter string="Inbound" name="inbound" domain="[('direction', '=', 'inbound')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                    <filter string="Type" name="group_by_type" context="{'group_by': 'message_type'}"/>
                    <filter string="Direction" name="group_by_direction" context="{'group_by': 'direction'}"/>
                    <filter string="Company" name="group_by_company" context="{'group_by': 'company_id'}" groups="base.group_multi_company"/>
                </group>
            </search>
        </field>
    </record>

    <!-- WhatsApp Message Action -->
    <record id="action_whatsapp_message" model="ir.actions.act_window">
        <field name="name">WhatsApp Messages</field>
        <field name="res_model">whatsapp.message</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_whatsapp_message_search"/>
        <field name="context">{'search_default_draft': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No WhatsApp messages found
            </p>
            <p>
                Create a new message or use the chatter to send WhatsApp messages.
            </p>
        </field>
    </record>

    <!-- WhatsApp Message Menu -->
    <menuitem id="menu_whatsapp_message"
              name="Messages"
              parent="menu_whatsapp_root"
              action="action_whatsapp_message"
              sequence="20"/>
    </data>
</odoo>