def migrate(cr, version):
    """Add new fields to whatsapp_message table"""
    
    # Add conversation_id field
    cr.execute("""
        ALTER TABLE whatsapp_message 
        ADD COLUMN IF NOT EXISTS conversation_id INTEGER
    """)
    
    # Add is_read field
    cr.execute("""
        ALTER TABLE whatsapp_message 
        ADD COLUMN IF NOT EXISTS is_read BOOLEAN DEFAULT FALSE
    """)
    
    # Add read_date field
    cr.execute("""
        ALTER TABLE whatsapp_message 
        ADD COLUMN IF NOT EXISTS read_date TIMESTAMP
    """)
    
    # Add reply_to_id field
    cr.execute("""
        ALTER TABLE whatsapp_message 
        ADD COLUMN IF NOT EXISTS reply_to_id INTEGER
    """)
    
    # Update existing records to set is_read = FALSE for inbound messages
    cr.execute("""
        UPDATE whatsapp_message 
        SET is_read = FALSE 
        WHERE direction = 'inbound' AND is_read IS NULL
    """)
    
    # Update existing records to set is_read = TRUE for outbound messages
    cr.execute("""
        UPDATE whatsapp_message 
        SET is_read = TRUE 
        WHERE direction = 'outbound' AND is_read IS NULL
    """)