{
    'name': 'Odoo WhatsApp MCP Integration',
    'version': '1.0.2',
    'category': 'Tools',
    'summary': 'WhatsApp integration for Odoo using MCP server',
    'description': '''
This module integrates WhatsApp messaging capabilities into Odoo using the Go WhatsApp Bridge.
Features include:
- Direct sending of WhatsApp messages from any Odoo record
- QR code authentication for WhatsApp Web
- Integration with Odoo's chatter mechanism
- Support for text and media messages
- Group chat support
- Message history and status tracking
- Discuss channel integration for WhatsApp conversations
- Bidirectional message sync between WhatsApp and Discuss
- Multi-user WhatsApp channel support
- Contact-based channel creation wizard
''',
    'author': 'VPerfectCS',
    'website': 'https://www.vperfectcs.com',
    'depends': [
        'base',
        'mail',
        'web',
    ],
    'data': [
        'security/ir.model.access.csv',
        'views/whatsapp_config_views.xml',
        'views/whatsapp_message_views.xml',
        'views/whatsapp_conversation_views.xml',
        'wizard/whatsapp_channel_wizard_views.xml',
        'views/whatsapp_menu_views.xml',
        'views/res_config_settings_views.xml',
        'data/whatsapp_data.xml',
        'data/ir_cron_data.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'odoo_whatsapp_mcp/static/src/js/whatsapp_widget.js',
            'odoo_whatsapp_mcp/static/src/xml/whatsapp_templates.xml',
            'odoo_whatsapp_mcp/static/src/css/whatsapp_styles.css',
        ],
    },
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}