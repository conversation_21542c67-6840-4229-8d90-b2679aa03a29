# WhatsApp QR Code Setup Guide

## ✅ Current Status
Your WhatsApp bridge is **already connected**! The system is ready to use.

## 🚀 How to Use WhatsApp QR Code in Odoo Frontend

### Step 1: Access WhatsApp Configuration
1. Login to your Odoo instance
2. Go to **Apps** and search for "WhatsApp"
3. Install/Upgrade the **Odoo WhatsApp MCP Integration** module
4. Navigate to **WhatsApp > Configuration**

### Step 2: Create WhatsApp Configuration
1. Click **Create** to add a new configuration
2. Fill in the details:
   - **Name**: `Company WhatsApp` (or any name)
   - **Bridge URL**: `http://localhost` 
   - **Bridge Port**: `8082`
   - **Active**: ✅ Check this box
3. **Save** the configuration

### Step 3: Generate QR Code
1. Click **"Start Bridge & Get QR Code"** button
2. The system will:
   - Start the WhatsApp bridge
   - Generate a QR code
   - Display it in the form
3. The status will change to **"Waiting for QR Scan"**

### Step 4: Scan QR Code with WhatsApp
1. Open **WhatsApp** on your phone
2. Go to **Settings > Linked Devices**
3. Tap **"Link a Device"**
4. Scan the QR code displayed in Odoo
5. Once connected, status changes to **"Connected"**

## 🔄 Available Actions

### In the WhatsApp Configuration Form:
- **Start Bridge & Get QR Code**: Starts bridge and generates QR
- **Refresh QR Code**: Gets a new QR code (QR codes expire ~20 seconds)
- **Check Connection**: Verifies current connection status
- **Disconnect**: Disconnects from WhatsApp
- **Stop Bridge**: Stops the bridge process

## 🎯 Key Features

### 1. **Visual QR Code Display**
- QR code appears as an image in the Odoo form
- Clear instructions for scanning
- Refresh button for expired codes

### 2. **Real-time Status Updates**
- Draft → Waiting for QR → Connected
- Visual status indicators
- Connection timestamps

### 3. **User-Friendly Interface**
- Bootstrap alerts for different states
- Emoji icons for better UX
- Responsive design

### 4. **Error Handling**
- Automatic retries for QR generation
- Clear error messages
- Fallback options

## 🛠️ Technical Implementation

### Backend (Python)
```python
def action_get_qr_code(self):
    # Starts bridge and fetches QR code from API
    # Generates QR image using qrcode library
    # Stores as binary field for display

def action_refresh_qr_code(self):
    # Refreshes QR without restarting bridge
    # Updates the QR image field

def _generate_qr_image(self, qr_data):
    # Converts QR string to PNG image
    # Returns base64 encoded image
```

### Frontend (XML)
```xml
<!-- QR Code Section -->
<div class="alert alert-info" invisible="state != 'waiting_qr'">
    <field name="qr_code" widget="image" options="{'size': [300, 300]}"/>
    <button name="action_refresh_qr_code" string="🔄 Refresh QR Code"/>
</div>
```

## 🔧 Troubleshooting

### If QR Code Doesn't Appear:
1. Check if `qrcode` library is installed:
   ```bash
   docker exec odoo18-stack pip install qrcode[pil]
   ```
2. Restart Odoo:
   ```bash
   docker-compose restart odoo18-stack
   ```

### If Bridge Doesn't Start:
1. Check bridge status:
   ```bash
   docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh status
   ```
2. Restart bridge manually:
   ```bash
   docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart
   ```
### If Bride failes then we can rebuild using following command:
```bash
   cd /mnt/extra-addons/odoo_whatsapp_mcp/bridge/
   go build -o bridge production_bridge.go

   timeout 10s ./bridge

   ls -la bridge && ./bridge

   chmod +x bridge && ./bridge &

   nohup ./bridge > bridge.log 2>&1 & echo "Bridge started with PID: $!”
```
### If QR Code Expires:
- Click **"Refresh QR Code"** button
- QR codes expire after ~20 seconds
- No need to restart the entire bridge

## 📱 Mobile App Instructions

### For WhatsApp Users:
1. **Android**: Settings → Linked devices → Link a device
2. **iPhone**: Settings → Linked Devices → Link a Device
3. Point camera at QR code in Odoo
4. Wait for confirmation

## 🎉 Success Indicators

### In Odoo:
- Status shows **"Connected"**
- Green success alert appears
- Last connection timestamp updates

### In WhatsApp:
- New linked device appears in settings
- Desktop/Web session shows as active

## 🔄 Maintenance

### Regular Tasks:
- Check connection status weekly
- Refresh QR if connection drops
- Monitor bridge logs for issues

### Commands:
```bash
# Check status
docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh status

# View logs
docker exec odoo18-stack tail -f /mnt/extra-addons/odoo_whatsapp_mcp/bridge/bridge.log

# Test API
python3 test_whatsapp_qr.py
```

## 🎯 Next Steps

1. **Test the Setup**: Create a WhatsApp config and generate QR code
2. **Integration**: Use WhatsApp messaging in other Odoo modules
3. **Customization**: Extend functionality as needed

---

**Note**: Your bridge is currently connected, so you can skip the QR scanning step and directly use WhatsApp messaging features!