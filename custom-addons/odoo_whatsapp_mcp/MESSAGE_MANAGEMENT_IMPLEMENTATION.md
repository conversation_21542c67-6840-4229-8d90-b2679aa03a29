# WhatsApp Message Management Interface - Implementation Plan

## Overview
Since the bridge is successfully receiving messages from different numbers, we need to implement a comprehensive message management interface in Odoo that allows users to:
- View all incoming WhatsApp messages in a unified inbox
- Reply directly from the interface
- Manage conversations by contact
- Track message status and delivery

## Current Status
✅ **Bridge receiving messages** - Confirmed working with different phone numbers
✅ **Webhook processing** - Messages are being processed and stored
✅ **Basic message model** - Database structure exists

## Implementation Plan

### Phase 1: Enhanced Message Model (1-2 days)

#### 1.1 Update WhatsApp Message Model
```python
# Additional fields needed in whatsapp.message model:
- conversation_id = fields.Char('Conversation ID')  # Group messages by phone
- is_read = fields.Boolean('Read', default=False)
- read_date = fields.Datetime('Read Date')
- partner_id = fields.Many2one('res.partner', 'Contact')
- reply_to_id = fields.Many2one('whatsapp.message', 'Reply To')
- message_thread = fields.Text('Thread Context')  # For conversation context
```

#### 1.2 Conversation Management
```python
# New model: whatsapp.conversation
- name = fields.Char('Conversation Name')
- phone = fields.Char('Phone Number', required=True)
- partner_id = fields.Many2one('res.partner', 'Contact')
- last_message_date = fields.Datetime('Last Message')
- unread_count = fields.Integer('Unread Messages')
- is_archived = fields.Boolean('Archived', default=False)
- message_ids = fields.One2many('whatsapp.message', 'conversation_id')
```

### Phase 2: Message Management Views (2-3 days)

#### 2.1 Conversation List View
```xml
<!-- Inbox-style conversation list -->
<record id="view_whatsapp_conversation_list" model="ir.ui.view">
    <field name="name">WhatsApp Conversations</field>
    <field name="model">whatsapp.conversation</field>
    <field name="arch" type="xml">
        <list decoration-bf="unread_count > 0" create="false">
            <field name="partner_id" widget="many2one_avatar_user"/>
            <field name="phone"/>
            <field name="last_message_date"/>
            <field name="unread_count" widget="badge" decoration-info="unread_count > 0"/>
            <field name="is_archived" column_invisible="1"/>
        </list>
    </field>
</record>
```

#### 2.2 Message Thread View
```xml
<!-- Chat-style message thread -->
<record id="view_whatsapp_message_thread" model="ir.ui.view">
    <field name="name">WhatsApp Message Thread</field>
    <field name="model">whatsapp.message</field>
    <field name="arch" type="xml">
        <list create="false" edit="false" decoration-muted="direction == 'inbound'" 
              decoration-info="direction == 'outbound'">
            <field name="create_date" widget="datetime"/>
            <field name="direction" column_invisible="1"/>
            <field name="message"/>
            <field name="state" widget="badge"/>
            <field name="attachment_name" optional="show"/>
        </list>
    </field>
</record>
```

#### 2.3 Unified Inbox Form View
```xml
<!-- Main inbox interface -->
<record id="view_whatsapp_inbox_form" model="ir.ui.view">
    <field name="name">WhatsApp Inbox</field>
    <field name="model">whatsapp.conversation</field>
    <field name="arch" type="xml">
        <form create="false" edit="false">
            <header>
                <button name="action_mark_read" type="object" string="Mark as Read"/>
                <button name="action_archive" type="object" string="Archive"/>
            </header>
            <sheet>
                <div class="row">
                    <!-- Left panel: Conversation list -->
                    <div class="col-md-4">
                        <field name="conversation_ids" widget="one2many_list" 
                               context="{'default_is_archived': False}"/>
                    </div>
                    <!-- Right panel: Message thread + reply -->
                    <div class="col-md-8">
                        <field name="message_ids" widget="whatsapp_thread"/>
                        <div class="mt-3">
                            <field name="reply_message" placeholder="Type your message..."/>
                            <button name="action_send_reply" type="object" 
                                    string="Send" class="btn-primary"/>
                        </div>
                    </div>
                </div>
            </sheet>
        </form>
    </field>
</record>
```

### Phase 3: JavaScript Components (2-3 days)

#### 3.1 WhatsApp Thread Widget
```javascript
// static/src/js/whatsapp_thread_widget.js
odoo.define('odoo_whatsapp_mcp.WhatsAppThread', function (require) {
    'use strict';
    
    const AbstractField = require('web.AbstractField');
    const fieldRegistry = require('web.field_registry');
    
    const WhatsAppThread = AbstractField.extend({
        template: 'WhatsAppThreadWidget',
        
        init: function () {
            this._super.apply(this, arguments);
            this.messages = [];
        },
        
        _renderMessages: function () {
            // Render messages in chat-style bubbles
            // Different styling for inbound vs outbound
            // Show timestamps, delivery status
        },
        
        _scrollToBottom: function () {
            // Auto-scroll to latest message
        },
        
        _refreshMessages: function () {
            // Periodic refresh for new messages
            setTimeout(this._refreshMessages.bind(this), 5000);
        }
    });
    
    fieldRegistry.add('whatsapp_thread', WhatsAppThread);
});
```

#### 3.2 Real-time Updates
```javascript
// static/src/js/whatsapp_notifications.js
odoo.define('odoo_whatsapp_mcp.Notifications', function (require) {
    'use strict';
    
    const WebClient = require('web.WebClient');
    
    WebClient.include({
        start: function () {
            this._super.apply(this, arguments);
            this._startWhatsAppPolling();
        },
        
        _startWhatsAppPolling: function () {
            // Poll for new messages every 10 seconds
            setInterval(this._checkNewMessages.bind(this), 10000);
        },
        
        _checkNewMessages: function () {
            this._rpc({
                model: 'whatsapp.message',
                method: 'get_unread_count',
            }).then(function (count) {
                if (count > 0) {
                    // Show browser notification
                    // Update UI indicators
                }
            });
        }
    });
});
```

### Phase 4: Backend Methods (1-2 days)

#### 4.1 Conversation Management Methods
```python
# models/whatsapp_conversation.py
class WhatsAppConversation(models.Model):
    _name = 'whatsapp.conversation'
    
    def action_mark_read(self):
        """Mark all messages in conversation as read"""
        self.message_ids.write({'is_read': True, 'read_date': fields.Datetime.now()})
        self.unread_count = 0
    
    def action_send_reply(self):
        """Send reply message"""
        if self.reply_message:
            message = self.env['whatsapp.message'].create({
                'phone': self.phone,
                'message': self.reply_message,
                'conversation_id': self.id,
                'direction': 'outbound'
            })
            message.action_send_message()
            self.reply_message = ''
    
    @api.model
    def get_or_create_conversation(self, phone):
        """Get existing conversation or create new one"""
        conversation = self.search([('phone', '=', phone)], limit=1)
        if not conversation:
            partner = self.env['res.partner'].search([
                '|', ('phone', '=', phone), ('mobile', '=', phone)
            ], limit=1)
            conversation = self.create({
                'phone': phone,
                'partner_id': partner.id if partner else False,
                'name': partner.name if partner else phone
            })
        return conversation
```

#### 4.2 Enhanced Message Processing
```python
# Update models/whatsapp_message.py
def process_incoming_message(self, data):
    """Enhanced incoming message processing"""
    # Create conversation if not exists
    conversation = self.env['whatsapp.conversation'].get_or_create_conversation(
        data.get('from')
    )
    
    # Create message
    message = self.create({
        'message_type': data.get('type', 'text'),
        'direction': 'inbound',
        'phone': data.get('from'),
        'message': data.get('message'),
        'whatsapp_message_id': data.get('message_id'),
        'conversation_id': conversation.id,
        'state': 'delivered'
    })
    
    # Update conversation
    conversation.write({
        'last_message_date': fields.Datetime.now(),
        'unread_count': conversation.unread_count + 1
    })
    
    # Trigger real-time notification
    self._notify_new_message(message)
    
    return message

def _notify_new_message(self, message):
    """Send real-time notification for new message"""
    self.env['bus.bus']._sendone(
        self.env.user.partner_id,
        'whatsapp_new_message',
        {
            'message_id': message.id,
            'phone': message.phone,
            'message': message.message,
            'conversation_id': message.conversation_id.id
        }
    )
```

### Phase 5: Menu and Actions (1 day)

#### 5.1 Updated Menu Structure
```xml
<!-- data/whatsapp_data.xml -->
<menuitem id="menu_whatsapp_root" name="WhatsApp" sequence="50"/>

<menuitem id="menu_whatsapp_inbox" name="Inbox" 
          parent="menu_whatsapp_root" sequence="10"
          action="action_whatsapp_inbox"/>

<menuitem id="menu_whatsapp_conversations" name="Conversations" 
          parent="menu_whatsapp_root" sequence="20"
          action="action_whatsapp_conversations"/>

<menuitem id="menu_whatsapp_messages" name="All Messages" 
          parent="menu_whatsapp_root" sequence="30"
          action="action_whatsapp_messages"/>
```

#### 5.2 Window Actions
```xml
<!-- Inbox action -->
<record id="action_whatsapp_inbox" model="ir.actions.act_window">
    <field name="name">WhatsApp Inbox</field>
    <field name="res_model">whatsapp.conversation</field>
    <field name="view_mode">form</field>
    <field name="view_id" ref="view_whatsapp_inbox_form"/>
    <field name="target">current</field>
</record>

<!-- Conversations action -->
<record id="action_whatsapp_conversations" model="ir.actions.act_window">
    <field name="name">WhatsApp Conversations</field>
    <field name="res_model">whatsapp.conversation</field>
    <field name="view_mode">list,form</field>
    <field name="domain">[('is_archived', '=', False)]</field>
</record>
```

## Implementation Timeline

### Week 1: Core Infrastructure
- **Day 1-2**: Update message model and create conversation model
- **Day 3-4**: Implement conversation management methods
- **Day 5**: Enhanced webhook processing with conversation linking

### Week 2: User Interface
- **Day 1-2**: Create conversation list and message thread views
- **Day 3-4**: Build unified inbox form view
- **Day 5**: Implement menu structure and actions

### Week 3: JavaScript & Real-time
- **Day 1-2**: WhatsApp thread widget development
- **Day 3-4**: Real-time notifications and polling
- **Day 5**: Testing and bug fixes

## Success Criteria

### MVP Features
- [x] Bridge receiving messages ✅
- [ ] Unified inbox showing all conversations
- [ ] Direct reply functionality from interface
- [ ] Message threading by contact
- [ ] Read/unread status tracking

### Enhanced Features
- [ ] Real-time message updates
- [ ] Desktop notifications
- [ ] Message search and filtering
- [ ] Quick reply templates
- [ ] Conversation archiving

### Advanced Features
- [ ] Message status indicators (sent/delivered/read)
- [ ] Bulk message operations
- [ ] Contact information panel
- [ ] Message export functionality

## Technical Considerations

### Performance
- Implement pagination for large conversation lists
- Use efficient database queries with proper indexing
- Cache frequently accessed conversation data

### User Experience
- Chat-style message bubbles with proper styling
- Responsive design for mobile/tablet access
- Keyboard shortcuts for quick actions
- Auto-scroll to latest messages

### Security
- Proper access controls for message viewing
- Audit logging for message operations
- Data encryption for sensitive conversations

---

*Implementation Priority: HIGH*
*Estimated Completion: 3 weeks*
*Dependencies: Current bridge and webhook functionality*