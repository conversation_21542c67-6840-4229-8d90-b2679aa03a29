from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class WhatsAppConversation(models.Model):
    _name = 'whatsapp.conversation'
    _description = 'WhatsApp Conversation'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'last_message_date desc'

    name = fields.Char('Conversation Name', compute='_compute_name', store=True)
    phone = fields.Char('Phone Number', required=True, index=True)
    partner_id = fields.Many2one('res.partner', 'Contact', index=True)
    last_message_date = fields.Datetime('Last Message', index=True)
    last_message_preview = fields.Text('Last Message Preview', compute='_compute_last_message')
    unread_count = fields.Integer('Unread Messages', default=0)
    is_archived = fields.Boolean('Archived', default=False)
    message_ids = fields.One2many('whatsapp.message', 'conversation_id', 'Messages')
    company_id = fields.Many2one('res.company', 'Company', default=lambda self: self.env.company)
    reply_message = fields.Text('Reply Message')
    
    # Discuss Channel Integration
    discuss_channel_id = fields.Many2one(
        'discuss.channel', 
        'Discuss Channel',
        help="Private discuss channel for this WhatsApp conversation"
    )
    auto_create_channel = fields.Boolean(
        'Auto Create Channel', 
        default=False,
        help="Automatically create discuss channel for new conversations"
    )
    channel_members = fields.Many2many(
        'res.users', 
        'whatsapp_channel_users_rel',
        'conversation_id', 
        'user_id',
        string='Channel Members',
        help="Users who can access this WhatsApp channel"
    )

    @api.depends('partner_id', 'phone')
    def _compute_name(self):
        for record in self:
            if record.partner_id:
                record.name = record.partner_id.name
            else:
                record.name = record.phone or 'Unknown'

    @api.depends('message_ids')
    def _compute_last_message(self):
        for record in self:
            last_message = record.message_ids.filtered(lambda m: m.message).sorted('create_date', reverse=True)[:1]
            if last_message:
                preview = last_message.message
                record.last_message_preview = preview[:100] + '...' if len(preview) > 100 else preview
            else:
                record.last_message_preview = ''

    @api.model
    def get_or_create_conversation(self, phone):
        """Get existing conversation or create new one"""
        conversation = self.search([('phone', '=', phone)], limit=1)
        if not conversation:
            partner = self.env['res.partner'].search([
                '|', ('phone', '=', phone), ('mobile', '=', phone)
            ], limit=1)
            conversation = self.create({
                'phone': phone,
                'partner_id': partner.id if partner else False,
                'last_message_date': fields.Datetime.now()
            })
            
            # Create activity for new conversation
            conversation.activity_schedule(
                'mail.mail_activity_data_todo',
                summary=f'New WhatsApp conversation from {phone}',
                note=f'A new WhatsApp conversation has been started with {phone}'
            )
            
            # Auto-create discuss channel if enabled
            if conversation.auto_create_channel:
                conversation._create_discuss_channel()
                
        return conversation

    def action_mark_read(self):
        """Mark all messages in conversation as read"""
        self.ensure_one()
        unread_messages = self.message_ids.filtered(lambda m: not m.is_read and m.direction == 'inbound')
        unread_messages.write({
            'is_read': True,
            'read_date': fields.Datetime.now()
        })
        self.unread_count = 0
        return True

    def action_send_reply(self):
        """Send reply message"""
        self.ensure_one()
        if not self.reply_message:
            raise UserError(_('Please enter a message to send'))
        
        message = self.env['whatsapp.message'].create({
            'phone': self.phone,
            'message': self.reply_message,
            'conversation_id': self.id,
            'direction': 'outbound',
            'message_type': 'text'
        })
        
        try:
            message.action_send_message()
            
            # Post to conversation chatter
            self.message_post(
                body=f"Sent WhatsApp message: {self.reply_message}",
                subject="WhatsApp Message Sent",
                message_type='comment'
            )
            
            # Sync to discuss channel if exists
            if self.discuss_channel_id:
                self._sync_messages_to_channel()
            
            self.reply_message = ''
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Message sent successfully'),
                    'type': 'success',
                }
            }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to send message: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_archive(self):
        """Archive conversation"""
        self.write({'is_archived': True})
        return True

    def action_unarchive(self):
        """Unarchive conversation"""
        self.write({'is_archived': False})
        return True

    @api.model
    def get_unread_count(self):
        """Get total unread message count for current user"""
        return sum(self.search([('is_archived', '=', False)]).mapped('unread_count'))

    def _create_discuss_channel(self):
        """Create discuss channel for WhatsApp conversation"""
        if self.discuss_channel_id:
            return self.discuss_channel_id
            
        channel = self.env['discuss.channel'].create_whatsapp_channel(
            self.id, 
            members=self.channel_members.ids if self.channel_members else None
        )
        
        # Sync existing messages to channel
        synced_count = self._sync_messages_to_channel()
        _logger.info("Synced %d messages to new channel %s", synced_count, channel.name)
        
        return channel
        
    def _sync_messages_to_channel(self):
        """Sync WhatsApp messages to discuss channel"""
        if not self.discuss_channel_id:
            return
            
        # Get messages that haven't been synced to channel and didn't originate from channel
        unsynced_messages = self.message_ids.filtered(
            lambda m: m.message and m.res_model != 'discuss.channel' and not m.is_synced_from_channel
        ).sorted('create_date')
        
        synced_count = 0
        for message in unsynced_messages:
            direction_icon = "📤" if message.direction == 'outbound' else "📥"
            sender = "You" if message.direction == 'outbound' else (self.partner_id.name or self.phone)
            
            # Format message content based on type
            if message.message:
                content = message.message
            elif message.attachment_name:
                content = f"[{message.message_type.title()}: {message.attachment_name}]"
            else:
                content = f"[{message.message_type.title()}]"
            
            # Post message to channel
            self.discuss_channel_id.message_post(
                body=f"{direction_icon} {sender}: {content}",
                message_type='comment',
                subtype_xmlid='mail.mt_comment'
            )
            
            # Mark message as synced
            message.write({
                'res_model': 'discuss.channel',
                'res_id': self.discuss_channel_id.id
            })
            synced_count += 1
            
        return synced_count
            
    def action_create_channel(self):
        """Manual action to create discuss channel"""
        self.ensure_one()
        if self.discuss_channel_id:
            return {
                'type': 'ir.actions.act_window',
                'name': 'WhatsApp Channel',
                'res_model': 'discuss.channel',
                'res_id': self.discuss_channel_id.id,
                'view_mode': 'form',
                'target': 'current'
            }
            
        channel = self._create_discuss_channel()
        return {
            'type': 'ir.actions.act_window',
            'name': 'WhatsApp Channel Created',
            'res_model': 'discuss.channel',
            'res_id': channel.id,
            'view_mode': 'form',
            'target': 'current'
        }
        
    def action_open_channel(self):
        """Open linked discuss channel"""
        self.ensure_one()
        if not self.discuss_channel_id:
            raise UserError(_('No discuss channel linked. Create one first.'))
            
        return {
            'type': 'ir.actions.act_window',
            'name': 'WhatsApp Channel',
            'res_model': 'discuss.channel',
            'res_id': self.discuss_channel_id.id,
            'view_mode': 'form',
            'target': 'current'
        }
        
    def action_sync_to_channel(self):
        """Manual action to sync messages to channel"""
        self.ensure_one()
        if not self.discuss_channel_id:
            raise UserError(_('No discuss channel linked. Create one first.'))
            
        synced_count = self._sync_messages_to_channel()
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('Synced %d messages to channel') % synced_count,
                'type': 'success',
            }
        }

    @api.model
    def auto_sync_messages(self):
        """Automatically sync messages from bridge - can be called by cron"""
        try:
            message_model = self.env['whatsapp.message']
            loaded_count = message_model.sync_messages_from_bridge()
            if loaded_count > 0:
                _logger.info("Auto-synced %d messages from bridge", loaded_count)
            return loaded_count
        except Exception as e:
            _logger.error("Auto-sync failed: %s", str(e))
            return 0
            
    @api.model
    def sync_all_channels(self):
        """Sync all conversations with discuss channels - called by cron"""
        try:
            conversations = self.search([('discuss_channel_id', '!=', False)])
            total_synced = 0
            
            for conversation in conversations:
                synced_count = conversation._sync_messages_to_channel()
                total_synced += synced_count
                
            if total_synced > 0:
                _logger.info("Synced %d messages to %d channels", total_synced, len(conversations))
            return total_synced
        except Exception as e:
            _logger.error("Channel sync failed: %s", str(e))
            return 0
            
    @api.model
    def manual_sync_all(self):
        """Manual sync for testing - combines message loading and channel sync"""
        try:
            # First sync messages from bridge
            loaded_count = self.auto_sync_messages()
            # Then sync to channels
            synced_count = self.sync_all_channels()
            
            _logger.info("Manual sync: loaded %d messages, synced %d to channels", 
                        loaded_count, synced_count)
            return {'loaded': loaded_count, 'synced': synced_count}
        except Exception as e:
            _logger.error("Manual sync failed: %s", str(e))
            return {'error': str(e)}