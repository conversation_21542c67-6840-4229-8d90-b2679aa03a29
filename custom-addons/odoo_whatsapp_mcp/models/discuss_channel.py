from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class DiscussChannel(models.Model):
    _inherit = 'discuss.channel'

    # WhatsApp Integration Fields
    whatsapp_conversation_id = fields.Many2one(
        'whatsapp.conversation', 
        'WhatsApp Conversation',
        help="Linked WhatsApp conversation for this channel"
    )
    is_whatsapp_channel = fields.Bo<PERSON>an(
        'WhatsApp Channel', 
        compute='_compute_is_whatsapp_channel',
        store=True,
        help="Indicates if this is a WhatsApp-linked channel"
    )
    whatsapp_phone = fields.Char(
        'WhatsApp Phone',
        related='whatsapp_conversation_id.phone',
        readonly=True,
        help="Phone number for WhatsApp conversation"
    )
    whatsapp_partner_id = fields.Many2one(
        'res.partner',
        'WhatsApp Contact',
        related='whatsapp_conversation_id.partner_id',
        readonly=True,
        help="Partner linked to WhatsApp conversation"
    )

    @api.depends('whatsapp_conversation_id')
    def _compute_is_whatsapp_channel(self):
        for record in self:
            record.is_whatsapp_channel = bool(record.whatsapp_conversation_id)

    def _notify_thread(self, message, msg_vals=False, **kwargs):
        """Override to handle WhatsApp message sending from discuss channel"""
        rdata = super()._notify_thread(message, msg_vals=msg_vals, **kwargs)
        
        # Only process if this is a WhatsApp channel and message is from a user (not system)
        if (self.is_whatsapp_channel and 
            msg_vals and 
            msg_vals.get('author_id')):
            
            # Skip if message contains WhatsApp sync indicators (📤 or 📥)
            message_body = msg_vals.get('body', '')
            if '📤' in message_body or '📥' in message_body:
                return rdata
            
            try:
                self._send_whatsapp_message_from_channel(message, msg_vals)
            except Exception as e:
                _logger.error("Failed to send WhatsApp message from channel: %s", str(e))
                # Don't raise exception to avoid breaking the discuss flow
                
        return rdata

    def _send_whatsapp_message_from_channel(self, message, msg_vals):
        """Send WhatsApp message when posted in discuss channel"""
        if not self.whatsapp_conversation_id:
            return
            
        # Extract message content
        message_body = msg_vals.get('body', '')
        
        # Check for attachments
        attachments = message.attachment_ids if hasattr(message, 'attachment_ids') else []
        
        # Skip if no content and no attachments
        if not message_body and not attachments:
            return
            
        # Clean HTML from message body
        import re
        clean_message = re.sub('<[^<]+?>', '', message_body).strip() if message_body else ''
        
        # Skip if message is a sync from WhatsApp (contains direction icons)
        if '📤' in clean_message or '📥' in clean_message:
            return
        
        # Handle attachments
        if attachments:
            for attachment in attachments:
                self._send_whatsapp_attachment(attachment, clean_message)
        elif clean_message:
            # Send text message only if no attachments
            self._send_whatsapp_text_message(clean_message)
            
    def _send_whatsapp_text_message(self, message_text):
        """Send text WhatsApp message"""
        whatsapp_message = self.env['whatsapp.message'].create({
            'phone': self.whatsapp_conversation_id.phone,
            'message': message_text,
            'message_type': 'text',
            'direction': 'outbound',
            'conversation_id': self.whatsapp_conversation_id.id,
            'res_model': 'discuss.channel',
            'res_id': self.id,
            'is_synced_from_channel': True
        })
        
        try:
            whatsapp_message.action_send_message()
            _logger.info("WhatsApp text message sent from channel: %s", self.name)
        except Exception as e:
            _logger.error("Failed to send WhatsApp text message: %s", str(e))
            self._post_error_notification(str(e))
            
    def _send_whatsapp_attachment(self, attachment, caption=''):
        """Send WhatsApp message with attachment"""
        # Determine message type based on file mimetype
        message_type = 'document'  # default
        if attachment.mimetype:
            if attachment.mimetype.startswith('image/'):
                message_type = 'image'
            elif attachment.mimetype.startswith('audio/'):
                message_type = 'audio'
            elif attachment.mimetype.startswith('video/'):
                message_type = 'video'
                
        whatsapp_message = self.env['whatsapp.message'].create({
            'phone': self.whatsapp_conversation_id.phone,
            'message': caption,
            'message_type': message_type,
            'direction': 'outbound',
            'conversation_id': self.whatsapp_conversation_id.id,
            'attachment': attachment.datas,
            'attachment_name': attachment.name,
            'res_model': 'discuss.channel',
            'res_id': self.id,
            'is_synced_from_channel': True
        })
        
        try:
            whatsapp_message.action_send_message()
            _logger.info("WhatsApp %s sent from channel: %s", message_type, self.name)
        except Exception as e:
            _logger.error("Failed to send WhatsApp %s: %s", message_type, str(e))
            self._post_error_notification(f"Failed to send {message_type}: {str(e)}")
            
    def _post_error_notification(self, error_msg):
        """Post error notification to channel"""
        self.message_post(
            body=f"❌ Failed to send WhatsApp message: {error_msg}",
            message_type='notification'
        )

    @api.model
    def create_whatsapp_channel(self, conversation_id, members=None):
        """Create a discuss channel for WhatsApp conversation"""
        conversation = self.env['whatsapp.conversation'].browse(conversation_id)
        if not conversation.exists():
            raise UserError(_('WhatsApp conversation not found'))
            
        # Check if channel already exists
        existing_channel = self.search([
            ('whatsapp_conversation_id', '=', conversation_id)
        ], limit=1)
        if existing_channel:
            return existing_channel
            
        # Determine channel name
        if conversation.partner_id:
            channel_name = f"WhatsApp: {conversation.partner_id.name}"
        else:
            channel_name = f"WhatsApp: {conversation.phone}"
            
        # Create group channel
        channel = self.create({
            'name': channel_name,
            'channel_type': 'channel',  # Group channel to allow multiple members
            'whatsapp_conversation_id': conversation_id,
            'description': f'WhatsApp conversation with {conversation.phone}'
        })
        
        # Add members to channel using proper Odoo method
        if members:
            partner_ids = self.env['res.users'].browse(members).mapped('partner_id.id')
        else:
            partner_ids = [self.env.user.partner_id.id]
            
        # Use channel's add_members method to avoid duplicates
        channel.add_members(partner_ids=partner_ids)
            
        # Link back to conversation
        conversation.write({'discuss_channel_id': channel.id})
        
        # Post welcome message
        channel.message_post(
            body=f"🟢 WhatsApp channel created for {conversation.phone}",
            message_type='notification'
        )
        
        return channel

    def action_open_whatsapp_conversation(self):
        """Open linked WhatsApp conversation"""
        self.ensure_one()
        if not self.whatsapp_conversation_id:
            raise UserError(_('No WhatsApp conversation linked to this channel'))
            
        return {
            'type': 'ir.actions.act_window',
            'name': 'WhatsApp Conversation',
            'res_model': 'whatsapp.conversation',
            'res_id': self.whatsapp_conversation_id.id,
            'view_mode': 'form',
            'target': 'current'
        }