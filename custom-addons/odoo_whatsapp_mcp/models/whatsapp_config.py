from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import json
import logging
try:
    import qrcode
except ImportError:
    qrcode = None

_logger = logging.getLogger(__name__)

class WhatsAppConfig(models.Model):
    _name = 'whatsapp.config'
    _description = 'WhatsApp Configuration'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char('Name', required=True)
    bridge_url = fields.Char('WhatsApp Bridge URL', required=True, 
                           help='URL of the Go WhatsApp Bridge server')
    bridge_port = fields.Integer('Bridge Port', required=True, default=8082)
    is_active = fields.Boolean('Active', default=False)
    state = fields.Selection([
        ('draft', 'Not Configured'),
        ('waiting_qr', 'Waiting for QR Scan'),
        ('connected', 'Connected'),
        ('disconnected', 'Disconnected')
    ], string='Status', default='draft', readonly=True)
    qr_code = fields.Binary('QR Code', attachment=True)
    last_connection = fields.Datetime('Last Connection')
    company_id = fields.Many2one('res.company', string='Company', 
                                default=lambda self: self.env.company)

    @api.model_create_multi
    def create(self, vals_list):
        # Ensure only one active configuration per company
        for vals in vals_list:
            if vals.get('is_active'):
                self.search([
                    ('company_id', '=', vals.get('company_id', self.env.company.id)),
                    ('is_active', '=', True)
                ]).write({'is_active': False})
        return super().create(vals_list)

    def write(self, vals):
        # Handle activation/deactivation
        if 'is_active' in vals and vals['is_active']:
            self.search([
                ('company_id', '=', self.company_id.id),
                ('id', '!=', self.id),
                ('is_active', '=', True)
            ]).write({'is_active': False})
        return super().write(vals)

    def action_get_qr_code(self):
        """Start the WhatsApp bridge and get the QR code."""
        self.ensure_one()
        import subprocess
        import os
        import time
        
        # Check if running in Docker
        is_docker = os.path.exists('/.dockerenv')
        
        if is_docker:
            # Use Docker-specific bridge startup
            bridge_script = '/mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh'
            try:
                result = subprocess.run(['/bin/bash', bridge_script, 'restart'], 
                                      capture_output=True, text=True, timeout=30)
                process = result.returncode == 0
                if not process:
                    _logger.error("Docker bridge start failed: %s", result.stderr)
            except Exception as e:
                _logger.error("Failed to start Docker bridge: %s", str(e))
                process = False
        else:
            # Use original method for non-Docker
            try:
                from odoo.addons.odoo_whatsapp_mcp.scripts import run_bridge
                process = run_bridge.start_whatsapp_bridge()
            except ImportError:
                _logger.error("Bridge script not found")
                process = False
        
        if process:
            self.write({'state': 'waiting_qr'})
            
            # Wait for bridge to initialize
            time.sleep(5)
            
            # Try to get QR code from bridge API
            qr_generated = False
            for attempt in range(3):  # Try 3 times
                try:
                    response = requests.get(f"http://127.0.0.1:{self.bridge_port}/api/qr", timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        if 'qr_code' in data and data['qr_code']:
                            qr_generated = self._generate_qr_image(data['qr_code'])
                            if qr_generated:
                                break
                        elif 'already_connected' in data.get('status', ''):
                            self.write({'state': 'connected', 'last_connection': fields.Datetime.now()})
                            return {
                                'type': 'ir.actions.client',
                                'tag': 'display_notification',
                                'params': {
                                    'title': _('Already Connected'),
                                    'message': _('WhatsApp is already connected!'),
                                    'type': 'success',
                                }
                            }
                except Exception as e:
                    _logger.warning("QR code fetch attempt %d failed: %s", attempt + 1, str(e))
                    time.sleep(2)
            
            if qr_generated:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('QR Code Ready'),
                        'message': _('QR Code generated! Scan it with WhatsApp to connect.'),
                        'type': 'success',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Bridge Started'),
                        'message': _('WhatsApp Bridge started! Use "Refresh QR Code" to get the QR code.'),
                        'type': 'info',
                    }
                }
        else:
            raise UserError(_('Failed to start WhatsApp Bridge. Check server logs for details.'))

    def action_check_connection(self):
        """Check connection status with WhatsApp Bridge"""
        self.ensure_one()
        try:
            response = requests.get(f"http://127.0.0.1:{self.bridge_port}/api/status", timeout=5)
            if response.status_code == 200:
                data = response.json()
                if data.get('connected'):
                    self.write({
                        'state': 'connected',
                        'last_connection': fields.Datetime.now()
                    })
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Success'),
                            'message': _('Connected to WhatsApp'),
                            'type': 'success',
                        }
                    }
                else:
                    self.write({'state': 'waiting_qr'})
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Info'),
                            'message': _('Bridge running but not connected. Please scan QR code.'),
                            'type': 'info',
                        }
                    }
            else:
                self.write({'state': 'disconnected'})
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Warning'),
                        'message': _('Bridge not responding'),
                        'type': 'warning',
                    }
                }
        except requests.exceptions.ConnectionError:
            self.write({'state': 'disconnected'})
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Info'),
                    'message': _('Bridge is not running. Click "Get QR Code" to start the bridge.'),
                    'type': 'info',
                }
            }
        except Exception as e:
            _logger.error("WhatsApp Bridge connection check error: %s", str(e))
            self.write({'state': 'disconnected'})
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Connection check failed: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_disconnect(self):
        """Disconnect from WhatsApp"""
        self.ensure_one()
        try:
            response = requests.post(f"{self.bridge_url}:{self.bridge_port}/logout")
            if response.status_code == 200:
                self.write({
                    'state': 'disconnected',
                    'qr_code': False
                })
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Disconnected from WhatsApp'),
                        'type': 'success',
                    }
                }
            raise UserError(_('Failed to disconnect from WhatsApp Bridge'))
        except Exception as e:
            _logger.error("WhatsApp Bridge disconnect error: %s", str(e))
            raise UserError(_('Could not disconnect from WhatsApp: %s') % str(e))
    
    def action_refresh_qr_code(self):
        """Refresh QR code without restarting bridge"""
        self.ensure_one()
        try:
            response = requests.get(f"{self.bridge_url}:{self.bridge_port}/api/qr", timeout=10)
            if response.status_code == 200:
                data = response.json()
                if 'qr_code' in data and data['qr_code']:
                    if self._generate_qr_image(data['qr_code']):
                        return {
                            'type': 'ir.actions.client',
                            'tag': 'display_notification',
                            'params': {
                                'title': _('QR Code Refreshed'),
                                'message': _('New QR Code generated! Scan it with WhatsApp.'),
                                'type': 'success',
                            }
                        }
                elif 'already_connected' in data.get('status', ''):
                    self.write({'state': 'connected', 'last_connection': fields.Datetime.now()})
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Already Connected'),
                            'message': _('WhatsApp is already connected!'),
                            'type': 'success',
                        }
                    }
            
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Could not get QR code. Bridge may not be running.'),
                    'type': 'warning',
                }
            }
        except Exception as e:
            _logger.error("QR code refresh error: %s", str(e))
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to refresh QR code: %s') % str(e),
                    'type': 'danger',
                }
            }
    
    def _generate_qr_image(self, qr_data):
        """Generate QR code image from data"""
        try:
            if qrcode:
                import io
                import base64
                
                qr = qrcode.QRCode(
                    version=1,
                    error_correction=qrcode.constants.ERROR_CORRECT_L,
                    box_size=8,
                    border=4,
                )
                qr.add_data(qr_data)
                qr.make(fit=True)
                
                img = qr.make_image(fill_color="black", back_color="white")
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                qr_image = base64.b64encode(buffer.getvalue())
                
                self.write({'qr_code': qr_image})
                return True
            else:
                _logger.warning("qrcode library not available")
                return False
        except Exception as e:
            _logger.error("QR code generation error: %s", str(e))
            return False
    
    def action_sync_messages(self):
        """Load messages from bridge SQLite database"""
        self.ensure_one()
        try:
            message_model = self.env['whatsapp.message']
            loaded_count = message_model.sync_messages_from_bridge()
            
            if loaded_count > 0:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Loaded %d messages from bridge database') % loaded_count,
                        'type': 'success',
                    }
                }
            else:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Info'),
                        'message': _('No new messages found in bridge database'),
                        'type': 'info',
                    }
                }
        except Exception as e:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to load messages: %s') % str(e),
                    'type': 'danger',
                }
            }

    def action_stop_bridge(self):
        """Stop the WhatsApp bridge process"""
        self.ensure_one()
        import subprocess
        import os
        
        is_docker = os.path.exists('/.dockerenv')
        
        if is_docker:
            bridge_script = '/mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh'
            try:
                result = subprocess.run(['/bin/bash', bridge_script, 'stop'], 
                                      capture_output=True, text=True, timeout=15)
                success = result.returncode == 0
            except Exception as e:
                _logger.error("Failed to stop Docker bridge: %s", str(e))
                success = False
        else:
            try:
                from odoo.addons.odoo_whatsapp_mcp.scripts import run_bridge
                success = run_bridge.stop_whatsapp_bridge()
            except ImportError:
                success = False
        
        if success:
            self.write({'state': 'disconnected', 'qr_code': False})
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('WhatsApp Bridge stopped'),
                    'type': 'success',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Info'),
                    'message': _('Bridge was not running or could not be stopped'),
                    'type': 'info',
                }
            }