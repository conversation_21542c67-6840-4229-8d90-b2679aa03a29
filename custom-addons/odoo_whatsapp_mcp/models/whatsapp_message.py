from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import json
import logging

_logger = logging.getLogger(__name__)

class WhatsAppMessage(models.Model):
    _name = 'whatsapp.message'
    _description = 'WhatsApp Message'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char('Name', compute='_compute_name', store=True)
    message_type = fields.Selection([
        ('text', 'Text'),
        ('image', 'Image'),
        ('document', 'Document'),
        ('audio', 'Audio'),
        ('video', 'Video')
    ], string='Type', required=True, default='text')
    direction = fields.Selection([
        ('outbound', 'Outbound'),
        ('inbound', 'Inbound')
    ], string='Direction', required=True, default='outbound')
    state = fields.Selection([
        ('draft', 'Draft'),
        ('sending', 'Sending'),
        ('sent', 'Sent'),
        ('delivered', 'Delivered'),
        ('read', 'Read'),
        ('failed', 'Failed')
    ], string='Status', default='draft', tracking=True)
    phone = fields.Char('Phone Number', required=True)
    message = fields.Text('Message Text')
    attachment = fields.Binary('Attachment')
    attachment_name = fields.Char('Attachment Name')
    whatsapp_message_id = fields.Char('WhatsApp Message ID', readonly=True)
    error_message = fields.Text('Error Message')
    company_id = fields.Many2one('res.company', string='Company',
                                default=lambda self: self.env.company)
    res_model = fields.Char('Related Document Model')
    res_id = fields.Integer('Related Document ID')
    conversation_id = fields.Many2one('whatsapp.conversation', 'Conversation', index=True)
    is_read = fields.Boolean('Read', default=False)
    read_date = fields.Datetime('Read Date')
    reply_to_id = fields.Many2one('whatsapp.message', 'Reply To')
    is_synced_from_channel = fields.Boolean('Synced from Channel', default=False, help="Message originated from discuss channel sync")

    @api.depends('message', 'create_date')
    def _compute_name(self):
        for record in self:
            if record.message:
                record.name = record.message[:64]
            else:
                record.name = f'Message {record.create_date or ""}'

    def _get_whatsapp_config(self):
        """Get active WhatsApp configuration"""
        config = self.env['whatsapp.config'].search([
            ('company_id', '=', self.env.company.id),
            ('is_active', '=', True),
            ('state', '=', 'connected')
        ], limit=1)
        _logger.debug("Active WhatsApp configuration: %s", config)
        if not config:
            raise UserError(_('No active WhatsApp configuration found'))
        return config

    def action_send_message(self):
        """Send message through WhatsApp Bridge"""
        self.ensure_one()
        if self.state != 'draft':
            raise UserError(_('Only draft messages can be sent'))

        config = self._get_whatsapp_config()
        self.write({'state': 'sending'})
        temp_file_path = None

        try:
            # Handle media files separately like the reference MCP server
            if self.attachment and self.attachment_name:
                import tempfile
                import base64
                import os
                
                # Create temp file with proper extension
                file_ext = os.path.splitext(self.attachment_name)[1] or '.bin'
                with tempfile.NamedTemporaryFile(delete=False, suffix=file_ext) as tmp_file:
                    tmp_file.write(base64.b64decode(self.attachment))
                    temp_file_path = tmp_file.name
                
                # Send file using media endpoint (like reference MCP)
                payload = {
                    'recipient': self.phone,
                    'media_path': temp_file_path
                }
            else:
                # Send text message
                payload = {
                    'recipient': self.phone,
                    'message': self.message or ''
                }

            response = requests.post(
                f"http://127.0.0.1:{config.bridge_port}/api/send",
                json=payload,
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    self.write({
                        'state': 'sent',
                        'whatsapp_message_id': f'sent_{self.id}_{fields.Datetime.now().timestamp()}'
                    })
                    # Post to chatter
                    msg_text = self.message or f"[{self.message_type}] {self.attachment_name}" if self.attachment_name else "Media file"
                    self.message_post(
                        body=f"✅ WhatsApp message sent to {self.phone}: {msg_text}",
                        subject="WhatsApp Message Sent"
                    )
                    
                    # Sync outbound message to channel if exists
                    if self.conversation_id and self.conversation_id.discuss_channel_id:
                        self._sync_message_to_channel(self)
                else:
                    error = data.get('message', 'Unknown error')
                    self.write({
                        'state': 'failed',
                        'error_message': error
                    })
                    raise UserError(_('Failed to send WhatsApp message: %s') % error)
            else:
                error = f'HTTP {response.status_code}'
                self.write({
                    'state': 'failed',
                    'error_message': error
                })
                raise UserError(_('Failed to send WhatsApp message: %s') % error)

        except Exception as e:
            self.write({
                'state': 'failed',
                'error_message': str(e)
            })
            _logger.error("WhatsApp message send error: %s", str(e))
            raise UserError(_('Could not send WhatsApp message: %s') % str(e))
        finally:
            # Clean up temporary file
            if temp_file_path:
                try:
                    import os
                    os.unlink(temp_file_path)
                except Exception as cleanup_error:
                    _logger.warning("Failed to cleanup temp file %s: %s", temp_file_path, cleanup_error)

    @api.model
    def process_incoming_message(self, data):
        """Process incoming message from WhatsApp Bridge webhook"""
        try:
            # Get or create conversation
            conversation = self.env['whatsapp.conversation'].get_or_create_conversation(
                data.get('from')
            )
            
            message = self.create({
                'message_type': data.get('type', 'text'),
                'direction': 'inbound',
                'phone': data.get('from'),
                'message': data.get('message'),
                'whatsapp_message_id': data.get('message_id'),
                'conversation_id': conversation.id,
                'state': 'delivered'
            })

            if data.get('attachment'):
                message.write({
                    'attachment': data['attachment'],
                    'attachment_name': data.get('filename', 'attachment')
                })

            # Update conversation
            conversation.write({
                'last_message_date': fields.Datetime.now(),
                'unread_count': conversation.unread_count + 1
            })

            # Try to link message to existing records
            if data.get('from'):
                self._link_message_to_records(message, data['from'])

            # Trigger real-time notification
            self._notify_new_message(message)
            
            # Sync to discuss channel if exists
            if conversation.discuss_channel_id:
                self._sync_message_to_channel(message)
            
            # Also trigger conversation channel sync (but don't sync channel-originated messages)
            if not message.is_synced_from_channel:
                conversation._sync_messages_to_channel()

            return message
        except Exception as e:
            _logger.error("Error processing incoming WhatsApp message: %s", str(e))
            return False

    def _link_message_to_records(self, message, phone):
        """Link incoming message to existing records based on phone number"""
        # Search for matching partner
        partner = self.env['res.partner'].search([
            '|',
            ('phone', '=', phone),
            ('mobile', '=', phone)
        ], limit=1)

        if partner:
            # Link message to partner
            message.write({
                'res_model': 'res.partner',
                'res_id': partner.id
            })

            # Create message in partner's chatter
            partner.message_post(
                body=f"WhatsApp: {message.message}",
                subject=_('WhatsApp Message from %s') % phone,
                message_type='comment',
                subtype_xmlid='mail.mt_comment'
            )
            
            _logger.info("Linked WhatsApp message to partner: %s", partner.name)

    def action_test_send(self):
        """Test action to send message immediately"""
        self.ensure_one()
        return self.action_send_message()

    def _sync_message_to_channel(self, message):
        """Sync WhatsApp message to discuss channel"""
        if not message.conversation_id or not message.conversation_id.discuss_channel_id:
            return
            
        # Skip if already synced or originated from channel
        if message.res_model == 'discuss.channel' or message.is_synced_from_channel:
            return
            
        channel = message.conversation_id.discuss_channel_id
        direction_icon = "📤" if message.direction == 'outbound' else "📥"
        sender = "You" if message.direction == 'outbound' else (
            message.conversation_id.partner_id.name or message.phone
        )
        
        # Format message content
        if message.message:
            content = message.message
        elif message.attachment_name:
            content = f"[{message.message_type.title()}: {message.attachment_name}]"
        else:
            content = f"[{message.message_type.title()}]"
        
        # Post message to channel
        channel.message_post(
            body=f"{direction_icon} {sender}: {content}",
            message_type='comment',
            subtype_xmlid='mail.mt_comment'
        )
        
        # Mark message as synced
        message.write({
            'res_model': 'discuss.channel',
            'res_id': channel.id
        })
        
        _logger.debug("Synced message %s to channel %s", message.id, channel.name)

    def _notify_new_message(self, message):
        """Send notification using Odoo's built-in notification system"""
        try:
            # Use Odoo's mail notification system
            if message.conversation_id and message.conversation_id.partner_id:
                partner = message.conversation_id.partner_id
                partner.message_post(
                    body=f"New WhatsApp message: {message.message}",
                    subject=f"WhatsApp from {message.phone}",
                    message_type='notification',
                    subtype_xmlid='mail.mt_comment'
                )
        except Exception as e:
            _logger.warning("Failed to send notification: %s", str(e))

    @api.model
    def get_unread_count(self):
        """Get unread message count for notifications"""
        try:
            return self.search_count([('is_read', '=', False), ('direction', '=', 'inbound')])
        except Exception:
            # Return 0 if column doesn't exist yet (during migration)
            return 0

    @api.model
    def auto_sync_messages_cron(self):
        """Cron job to automatically sync messages from bridge database"""
        try:
            loaded_count = self.sync_messages_from_bridge()
            if loaded_count > 0:
                _logger.info("Auto-sync loaded %d new messages", loaded_count)
            return loaded_count
        except Exception as e:
            _logger.error("Auto-sync failed: %s", str(e))
            return 0

    @api.model
    def sync_messages_from_bridge(self):
        """Load messages from bridge SQLite database"""
        import sqlite3
        import os
        
        try:
            # Dynamic path detection for bridge messages database
            module_path = os.path.dirname(os.path.dirname(__file__))
            bridge_db_path = os.path.join(module_path, 'bridge', 'store', 'messages.db')
            
            _logger.info("Looking for bridge database at: %s", bridge_db_path)
            
            if not os.path.exists(bridge_db_path):
                _logger.warning("Bridge messages database not found at: %s", bridge_db_path)
                _logger.info("Module path detected as: %s", module_path)
                return 0
            
            conn = sqlite3.connect(bridge_db_path)
            cursor = conn.cursor()
            
            # Fetch messages from bridge database
            cursor.execute("""
                SELECT m.id, m.chat_jid, m.sender, m.content, m.timestamp, 
                       m.is_from_me, m.media_type, m.filename
                FROM messages m
                ORDER BY m.timestamp DESC
                LIMIT 100
            """)
            
            messages = cursor.fetchall()
            loaded_count = 0
            
            for msg_data in messages:
                msg_id, chat_jid, sender, content, timestamp, is_from_me, media_type, filename = msg_data
                
                # Skip if message already exists
                existing = self.search([('whatsapp_message_id', '=', msg_id)], limit=1)
                if existing:
                    continue
                
                # Extract phone number from JID
                phone = chat_jid.split('@')[0] if '@' in chat_jid else chat_jid
                if not phone.startswith('+'):
                    phone = '+' + phone
                
                # Create conversation
                conversation = self.env['whatsapp.conversation'].get_or_create_conversation(phone)
                
                # Create message
                message = self.create({
                    'whatsapp_message_id': msg_id,
                    'phone': phone,
                    'message': content or '',
                    'message_type': media_type or 'text',
                    'direction': 'outbound' if is_from_me else 'inbound',
                    'conversation_id': conversation.id,
                    'state': 'delivered',
                    'attachment_name': filename,
                    'create_date': timestamp
                })
                
                # Link to partner if exists
                if not is_from_me:
                    self._link_message_to_records(message, phone)
                
                # Sync to channel if exists
                if conversation.discuss_channel_id:
                    self._sync_message_to_channel(message)
                
                loaded_count += 1
            
            conn.close()
            _logger.info("Loaded %d messages from bridge database", loaded_count)
            return loaded_count
            
        except Exception as e:
            _logger.error("Error loading messages from bridge database: %s", str(e))
            return 0

    @api.model
    def create_test_message(self, phone, message_text):
        """Create and send a test message"""
        conversation = self.env['whatsapp.conversation'].get_or_create_conversation(phone)
        message = self.create({
            'phone': phone,
            'message': message_text,
            'message_type': 'text',
            'direction': 'outbound',
            'conversation_id': conversation.id
        })
        message.action_send_message()
        return message