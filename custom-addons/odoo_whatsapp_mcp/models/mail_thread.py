from odoo import models, api

class MailThread(models.AbstractModel):
    _inherit = 'mail.thread'

    def _whatsapp_send_message(self, phone, message):
        """Send WhatsApp message from any record with chatter"""
        WhatsAppMessage = self.env['whatsapp.message']
        
        # Create and send WhatsApp message
        whatsapp_msg = WhatsAppMessage.create({
            'phone': phone,
            'message': message,
            'message_type': 'text',
            'direction': 'outbound',
            'res_model': self._name,
            'res_id': self.id,
        })
        
        whatsapp_msg.action_send_message()
        
        # Post in chatter
        self.message_post(
            body=f"WhatsApp message sent to {phone}: {message}",
            subject="WhatsApp Message Sent",
            message_type='comment'
        )
        
        return whatsapp_msg