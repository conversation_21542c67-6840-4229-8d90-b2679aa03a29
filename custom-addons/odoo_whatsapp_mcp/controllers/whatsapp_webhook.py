import json
import logging
from odoo import http, _
from odoo.http import request

_logger = logging.getLogger(__name__)

class WhatsAppWebhook(http.Controller):

    @http.route('/whatsapp/webhook', type='http', auth='none', methods=['POST'], csrf=False)
    def whatsapp_webhook(self, **kwargs):
        """Webhook endpoint for receiving messages from WhatsApp Bridge"""
        try:
            # Handle JSON data from request body
            import json
            data = json.loads(request.httprequest.data.decode('utf-8'))
            _logger.info("Received WhatsApp webhook: %s", data)
            
            # Process incoming message
            message_model = request.env['whatsapp.message'].sudo()
            result = message_model.process_incoming_message(data)
            
            response_data = {
                'status': 'success' if result else 'error',
                'message': 'Message processed' if result else 'Failed to process message'
            }
            
            return request.make_response(
                json.dumps(response_data),
                headers=[('Content-Type', 'application/json')]
            )
                
        except Exception as e:
            _logger.error("WhatsApp webhook error: %s", str(e))
            import json
            return request.make_response(
                json.dumps({'status': 'error', 'message': str(e)}),
                headers=[('Content-Type', 'application/json')]
            )

    @http.route('/whatsapp/status', type='http', auth='none', methods=['GET'])
    def whatsapp_status(self, **kwargs):
        """Status endpoint for checking webhook connectivity"""
        import json
        response_data = {
            'status': 'active',
            'webhook_url': '/whatsapp/webhook',
            'message': 'WhatsApp webhook is active'
        }
        return request.make_response(
            json.dumps(response_data),
            headers=[('Content-Type', 'application/json')]
        )