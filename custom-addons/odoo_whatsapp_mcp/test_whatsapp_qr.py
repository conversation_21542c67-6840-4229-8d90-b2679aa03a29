#!/usr/bin/env python3
"""
Test script to verify WhatsApp QR code functionality
"""
import requests
import time

def test_whatsapp_bridge():
    """Test WhatsApp bridge API and QR code generation"""
    bridge_url = "http://localhost:8082"
    
    print("🔍 Testing WhatsApp Bridge API...")
    
    # Test status endpoint
    try:
        response = requests.get(f"{bridge_url}/api/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Bridge Status: {status}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Bridge not responding: {e}")
        return False
    
    # Test QR code endpoint
    try:
        print("📱 Getting QR code...")
        response = requests.get(f"{bridge_url}/api/qr", timeout=10)
        if response.status_code == 200:
            data = response.json()
            if 'qr_code' in data and data['qr_code']:
                print("✅ QR Code data received!")
                print(f"📋 QR Code length: {len(data['qr_code'])} characters")
                print(f"🔗 QR Code preview: {data['qr_code'][:50]}...")
                
                # Test QR code generation
                try:
                    import qrcode
                    import io
                    import base64
                    
                    qr = qrcode.QRCode(version=1, box_size=8, border=4)
                    qr.add_data(data['qr_code'])
                    qr.make(fit=True)
                    
                    img = qr.make_image(fill_color="black", back_color="white")
                    buffer = io.BytesIO()
                    img.save(buffer, format='PNG')
                    qr_image = base64.b64encode(buffer.getvalue())
                    
                    print(f"✅ QR Code image generated! Size: {len(qr_image)} bytes")
                    return True
                    
                except Exception as e:
                    print(f"❌ QR Code generation failed: {e}")
                    return False
                    
            elif 'already_connected' in data.get('status', ''):
                print("✅ WhatsApp already connected!")
                return True
            else:
                print(f"❌ Invalid QR response: {data}")
                return False
        else:
            print(f"❌ QR request failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ QR code request failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 WhatsApp Bridge QR Code Test")
    print("=" * 40)
    
    success = test_whatsapp_bridge()
    
    print("=" * 40)
    if success:
        print("✅ All tests passed! QR code functionality is working.")
        print("\n📱 Next steps:")
        print("1. Go to Odoo > WhatsApp > Configuration")
        print("2. Create/Edit a WhatsApp config")
        print("3. Click 'Start Bridge & Get QR Code'")
        print("4. Scan the QR code with WhatsApp")
    else:
        print("❌ Tests failed. Check bridge status and logs.")
        print("\n🔧 Troubleshooting:")
        print("1. Restart bridge: docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart")
        print("2. Check logs: docker exec odoo18-stack tail -f /mnt/extra-addons/odoo_whatsapp_mcp/bridge/bridge.log")