#!/bin/bash
# Get QR code URL from WhatsApp bridge API

echo "🔄 Getting QR code from API..."

# Check if bridge is running
if ! docker exec odoo18-stack curl -s http://localhost:8082/api/status > /dev/null 2>&1; then
    echo "⚠️  Bridge not responding, restarting..."
    docker exec odoo18-stack /mnt/extra-addons/odoo_whatsapp_mcp/scripts/docker_bridge_start.sh restart
    sleep 5
fi

# Get QR code from API
echo "📱 Fetching QR code data..."
QR_DATA=$(docker exec odoo18-stack curl -s http://localhost:8082/api/qr)

if echo "$QR_DATA" | grep -q "qr_code"; then
    QR_CODE=$(echo "$QR_DATA" | docker exec -i odoo18-stack jq -r '.qr_code' 2>/dev/null)
    
    if [ "$QR_CODE" != "null" ] && [ -n "$QR_CODE" ]; then
        echo ""
        echo "✅ QR Code Data Retrieved!"
        echo "================================================================="
        echo "📋 QR Code String (copy this):"
        echo "$QR_CODE"
        echo "================================================================="
        echo ""
        echo "🌐 Online QR Code Generators:"
        echo "1. https://www.qr-code-generator.com/"
        echo "2. https://qr.io/"
        echo "3. https://www.qrcode-monkey.com/"
        echo ""
        echo "📱 Instructions:"
        echo "1. Copy the QR code string above"
        echo "2. Go to any online QR code generator"
        echo "3. Paste the string and generate QR code"
        echo "4. Scan with WhatsApp > Settings > Linked Devices > Link a Device"
        echo "================================================================="
    else
        echo "❌ Invalid QR code data received"
    fi
elif echo "$QR_DATA" | grep -q "already_connected"; then
    echo "✅ Already connected to WhatsApp!"
else
    echo "❌ Could not get QR code from API"
    echo "Response: $QR_DATA"
fi