/** @odoo-module **/

import { Component } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";

export class WhatsAppWidget extends Component {
    setup() {
        this.rpc = useService("rpc");
        this.notification = useService("notification");
    }

    async sendWhatsAppMessage(phone, message) {
        try {
            const result = await this.rpc("/web/dataset/call_kw", {
                model: "whatsapp.message",
                method: "create",
                args: [{
                    phone: phone,
                    message: message,
                    message_type: "text",
                    direction: "outbound"
                }],
                kwargs: {}
            });
            
            if (result) {
                await this.rpc("/web/dataset/call_kw", {
                    model: "whatsapp.message",
                    method: "action_send_message",
                    args: [result],
                    kwargs: {}
                });
                
                this.notification.add("WhatsApp message sent successfully", {
                    type: "success"
                });
            }
        } catch (error) {
            this.notification.add("Failed to send WhatsApp message", {
                type: "danger"
            });
        }
    }
}

WhatsAppWidget.template = "odoo_whatsapp_mcp.WhatsAppWidget";

registry.category("components").add("WhatsAppWidget", WhatsAppWidget);