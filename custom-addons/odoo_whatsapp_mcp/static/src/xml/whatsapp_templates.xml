<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">
    
    <t t-name="WhatsAppMessageBubble" owl="1">
        <div t-attf-class="whatsapp-message-bubble #{props.direction === 'outbound' ? 'outbound' : 'inbound'}">
            <div class="message-content">
                <t t-esc="props.message"/>
            </div>
            <div class="message-meta">
                <span class="message-time" t-esc="props.time"/>
                <span t-if="props.direction === 'outbound'" t-attf-class="message-status #{props.state}">
                    <i t-if="props.state === 'sent'" class="fa fa-check" title="Sent"/>
                    <i t-if="props.state === 'delivered'" class="fa fa-check-double" title="Delivered"/>
                    <i t-if="props.state === 'read'" class="fa fa-check-double text-primary" title="Read"/>
                    <i t-if="props.state === 'failed'" class="fa fa-exclamation-triangle text-danger" title="Failed"/>
                </span>
            </div>
        </div>
    </t>

    <t t-name="WhatsAppConversationItem" owl="1">
        <div t-attf-class="whatsapp-conversation-item #{props.unread_count > 0 ? 'unread' : ''}">
            <div class="conversation-avatar">
                <img t-if="props.partner_avatar" t-att-src="props.partner_avatar" alt="Avatar"/>
                <div t-if="!props.partner_avatar" class="default-avatar">
                    <i class="fa fa-user"/>
                </div>
            </div>
            <div class="conversation-content">
                <div class="conversation-header">
                    <span class="conversation-name" t-esc="props.name"/>
                    <span class="conversation-time" t-esc="props.last_message_time"/>
                </div>
                <div class="conversation-preview">
                    <span t-esc="props.last_message_preview"/>
                </div>
            </div>
            <div t-if="props.unread_count > 0" class="unread-badge">
                <span t-esc="props.unread_count"/>
            </div>
        </div>
    </t>

</templates>