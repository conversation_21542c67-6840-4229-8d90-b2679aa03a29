/* WhatsApp Message Management Styles */

.whatsapp-message-bubble {
    max-width: 70%;
    margin: 8px 0;
    padding: 8px 12px;
    border-radius: 12px;
    position: relative;
    word-wrap: break-word;
}

.whatsapp-message-bubble.inbound {
    background-color: #f1f1f1;
    margin-right: auto;
    border-bottom-left-radius: 4px;
}

.whatsapp-message-bubble.outbound {
    background-color: #dcf8c6;
    margin-left: auto;
    border-bottom-right-radius: 4px;
}

.message-content {
    margin-bottom: 4px;
    line-height: 1.4;
}

.message-meta {
    font-size: 11px;
    color: #666;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    gap: 4px;
}

.message-status i {
    font-size: 12px;
}

.whatsapp-conversation-item {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s;
}

.whatsapp-conversation-item:hover {
    background-color: #f8f9fa;
}

.whatsapp-conversation-item.unread {
    background-color: #fff3cd;
}

.conversation-avatar {
    width: 48px;
    height: 48px;
    border-radius: 50%;
    margin-right: 12px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #e9ecef;
}

.conversation-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.default-avatar {
    color: #6c757d;
    font-size: 20px;
}

.conversation-content {
    flex: 1;
    min-width: 0;
}

.conversation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;
}

.conversation-name {
    font-weight: 600;
    color: #212529;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.conversation-time {
    font-size: 12px;
    color: #6c757d;
    white-space: nowrap;
}

.conversation-preview {
    color: #6c757d;
    font-size: 14px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.unread-badge {
    background-color: #25d366;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
    margin-left: 8px;
    flex-shrink: 0;
}

.whatsapp-thread-container {
    height: 400px;
    overflow-y: auto;
    padding: 16px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.whatsapp-reply-area {
    margin-top: 16px;
    padding: 16px;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
}

.whatsapp-reply-input {
    width: 100%;
    min-height: 60px;
    border: 1px solid #ced4da;
    border-radius: 20px;
    padding: 12px 16px;
    resize: none;
    font-family: inherit;
}

.whatsapp-reply-input:focus {
    outline: none;
    border-color: #25d366;
    box-shadow: 0 0 0 0.2rem rgba(37, 211, 102, 0.25);
}

.whatsapp-send-button {
    background-color: #25d366;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 20px;
    margin-top: 8px;
    float: right;
}

.whatsapp-send-button:hover {
    background-color: #128c7e;
}

@media (max-width: 768px) {
    .whatsapp-message-bubble {
        max-width: 85%;
    }
    
    .conversation-avatar {
        width: 40px;
        height: 40px;
    }
    
    .whatsapp-thread-container {
        height: 300px;
    }
}