package main

import (
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strings"
	"syscall"
	"time"

	_ "github.com/mattn/go-sqlite3"
	"github.com/mdp/qrterminal"
	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store/sqlstore"
	"go.mau.fi/whatsmeow/types/events"
	"go.mau.fi/whatsmeow/types"
	waProto "go.mau.fi/whatsmeow/binary/proto"
	waLog "go.mau.fi/whatsmeow/util/log"
)

var (
	client *whatsmeow.Client
	logger waLog.Logger
	messageStore *MessageStore
)

// MessageStore handles database operations for messages
type MessageStore struct {
	db *sql.DB
}

// Initialize message store
func NewMessageStore() (*MessageStore, error) {
	// Open SQLite database for messages
	db, err := sql.Open("sqlite3", "file:store/messages.db?_foreign_keys=on")
	if err != nil {
		return nil, fmt.Errorf("failed to open message database: %v", err)
	}

	// Create tables if they don't exist
	_, err = db.Exec(`
		CREATE TABLE IF NOT EXISTS chats (
			jid TEXT PRIMARY KEY,
			name TEXT,
			last_message_time TIMESTAMP
		);
		
		CREATE TABLE IF NOT EXISTS messages (
			id TEXT,
			chat_jid TEXT,
			sender TEXT,
			content TEXT,
			timestamp TIMESTAMP,
			is_from_me BOOLEAN,
			media_type TEXT,
			filename TEXT,
			url TEXT,
			media_key BLOB,
			file_sha256 BLOB,
			file_enc_sha256 BLOB,
			file_length INTEGER,
			PRIMARY KEY (id, chat_jid),
			FOREIGN KEY (chat_jid) REFERENCES chats(jid)
		);
	`)
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("failed to create tables: %v", err)
	}

	return &MessageStore{db: db}, nil
}

// Close the database connection
func (store *MessageStore) Close() error {
	return store.db.Close()
}

// Store a chat in the database
func (store *MessageStore) StoreChat(jid, name string, lastMessageTime time.Time) error {
	_, err := store.db.Exec(
		"INSERT OR REPLACE INTO chats (jid, name, last_message_time) VALUES (?, ?, ?)",
		jid, name, lastMessageTime,
	)
	return err
}

// Store a message in the database
func (store *MessageStore) StoreMessage(id, chatJID, sender, content string, timestamp time.Time, isFromMe bool,
	mediaType, filename, url string, mediaKey, fileSHA256, fileEncSHA256 []byte, fileLength uint64) error {
	// Only store if there's actual content or media
	if content == "" && mediaType == "" {
		return nil
	}

	_, err := store.db.Exec(
		`INSERT OR REPLACE INTO messages 
		(id, chat_jid, sender, content, timestamp, is_from_me, media_type, filename, url, media_key, file_sha256, file_enc_sha256, file_length) 
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
		id, chatJID, sender, content, timestamp, isFromMe, mediaType, filename, url, mediaKey, fileSHA256, fileEncSHA256, fileLength,
	)
	return err
}

type StatusResponse struct {
	Connected bool   `json:"connected"`
	Status    string `json:"status"`
	Message   string `json:"message"`
}

type SendResponse struct {
	Success bool   `json:"success"`
	Message string `json:"message"`
}

type SendRequest struct {
	Recipient string `json:"recipient"`
	Message   string `json:"message"`
	MediaPath string `json:"media_path"`
}

func main() {
	logger = waLog.Stdout("Client", "INFO", true)
	logger.Infof("Starting WhatsApp client...")

	// Create database connection for storing session data
	dbLog := waLog.Stdout("Database", "INFO", true)

	// Create directory for database if it doesn't exist
	if err := os.MkdirAll("store", 0755); err != nil {
		logger.Errorf("Failed to create store directory: %v", err)
		return
	}

	container, err := sqlstore.New("sqlite3", "file:store/whatsapp.db?_foreign_keys=on", dbLog)
	if err != nil {
		logger.Errorf("Failed to connect to database: %v", err)
		return
	}

	// Get device store - This contains session information
	deviceStore, err := container.GetFirstDevice()
	if err != nil {
		if err == sql.ErrNoRows {
			// No device exists, create one
			deviceStore = container.NewDevice()
			logger.Infof("Created new device")
		} else {
			logger.Errorf("Failed to get device: %v", err)
			return
		}
	}

	// Create client instance
	client = whatsmeow.NewClient(deviceStore, logger)
	if client == nil {
		logger.Errorf("Failed to create WhatsApp client")
		return
	}

	// Initialize message store
	messageStore, err = NewMessageStore()
	if err != nil {
		logger.Errorf("Failed to initialize message store: %v", err)
		return
	}
	defer messageStore.Close()

	// Setup event handling
	client.AddEventHandler(func(evt interface{}) {
		switch v := evt.(type) {
		case *events.Message:
			handleMessage(v, logger)
		case *events.HistorySync:
			handleHistorySync(v, logger)
		case *events.Connected:
			logger.Infof("Connected to WhatsApp")
		case *events.LoggedOut:
			logger.Warnf("Device logged out, please scan QR code to log in again")
		}
	})

	// Start HTTP server
	go startHTTPServer()

	// Connect to WhatsApp
	if client.Store.ID == nil {
		// No ID stored, this is a new client, need to pair with phone
		qrChan, _ := client.GetQRChannel(context.Background())
		err = client.Connect()
		if err != nil {
			logger.Errorf("Failed to connect: %v", err)
			return
		}

		// Print QR code for pairing with phone
		for evt := range qrChan {
			if evt.Event == "code" {
				fmt.Println("\n🔗 SCAN THIS QR CODE WITH YOUR WHATSAPP APP:")
				fmt.Println(strings.Repeat("=", 60))
				qrterminal.GenerateHalfBlock(evt.Code, qrterminal.L, os.Stdout)
				fmt.Println(strings.Repeat("=", 60))
				fmt.Println("📱 Instructions:")
				fmt.Println("1. Open WhatsApp on your phone")
				fmt.Println("2. Go to Settings > Linked Devices")
				fmt.Println("3. Tap 'Link a Device'")
				fmt.Println("4. Scan the QR code above")
				fmt.Println(strings.Repeat("=", 60))
			} else if evt.Event == "success" {
				fmt.Println("\n✅ Successfully connected and authenticated!")
				break
			}
		}
	} else {
		// Already logged in, try to reconnect with retry
		logger.Infof("Found existing session, attempting to reconnect...")
		for i := 0; i < 3; i++ {
			err = client.Connect()
			if err == nil {
				logger.Infof("Successfully reconnected using existing session")
				break
			}
			logger.Warnf("Reconnection attempt %d failed: %v", i+1, err)
			time.Sleep(time.Duration(i+1) * 2 * time.Second)
		}
		
		if err != nil {
			logger.Errorf("Failed to reconnect after 3 attempts, session may be expired")
			// Don't return, let it continue to generate QR code
		}
	}

	// Wait a moment for connection to stabilize
	time.Sleep(2 * time.Second)

	if !client.IsConnected() {
		logger.Errorf("Failed to establish stable connection")
		return
	}

	fmt.Println("\n✓ Connected to WhatsApp!")
	fmt.Println("🌐 REST server running on :8082")

	// Create a channel to keep the main goroutine alive
	exitChan := make(chan os.Signal, 1)
	signal.Notify(exitChan, syscall.SIGINT, syscall.SIGTERM)

	// Wait for termination signal
	<-exitChan

	fmt.Println("Disconnecting...")
	// Disconnect client
	client.Disconnect()
}

func startHTTPServer() {
	// Status endpoint
	http.HandleFunc("/api/status", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		
		connected := client != nil && client.IsConnected()
		status := StatusResponse{
			Connected: connected,
			Status:    "running",
		}
		
		if connected {
			status.Message = "Connected to WhatsApp"
		} else {
			status.Message = "Bridge running but not connected. Please scan QR code."
		}
		
		json.NewEncoder(w).Encode(status)
	})

	// QR code endpoint
	http.HandleFunc("/api/qr", func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		
		if client == nil {
			json.NewEncoder(w).Encode(map[string]string{"error": "Client not initialized"})
			return
		}
		
		if client.IsConnected() {
			json.NewEncoder(w).Encode(map[string]string{"status": "already_connected"})
			return
		}
		
		// Force new QR code generation
		qrChan, _ := client.GetQRChannel(context.Background())
		go func() {
			client.Connect()
		}()
		
		select {
		case evt := <-qrChan:
			if evt.Event == "code" {
				json.NewEncoder(w).Encode(map[string]string{"qr_code": evt.Code})
			} else {
				json.NewEncoder(w).Encode(map[string]string{"status": evt.Event})
			}
		case <-time.After(10 * time.Second):
			json.NewEncoder(w).Encode(map[string]string{"error": "timeout"})
		}
	})

	// Send message endpoint
	http.HandleFunc("/api/send", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != http.MethodPost {
			http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
			return
		}

		var req SendRequest
		if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
			http.Error(w, "Invalid request", http.StatusBadRequest)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		
		if client == nil || !client.IsConnected() {
			response := SendResponse{
				Success: false,
				Message: "Not connected to WhatsApp",
			}
			json.NewEncoder(w).Encode(response)
			return
		}

		// Parse recipient number
		recipientJID, err := parsePhoneNumber(req.Recipient)
		if err != nil {
			response := SendResponse{
				Success: false,
				Message: fmt.Sprintf("Invalid phone number: %v", err),
			}
			json.NewEncoder(w).Encode(response)
			return
		}

		var msg *waProto.Message
		
		// Check if this is a media message
		if req.MediaPath != "" {
			// Send media file
			msg, err = createMediaMessage(req.MediaPath)
			if err != nil {
				response := SendResponse{
					Success: false,
					Message: fmt.Sprintf("Failed to prepare media: %v", err),
				}
				json.NewEncoder(w).Encode(response)
				return
			}
		} else {
			// Send text message
			msg = &waProto.Message{
				Conversation: &req.Message,
			}
		}

		_, err = client.SendMessage(context.Background(), recipientJID, msg)
		if err != nil {
			response := SendResponse{
				Success: false,
				Message: fmt.Sprintf("Failed to send message: %v", err),
			}
			json.NewEncoder(w).Encode(response)
			return
		}

		msgType := "text"
		if req.MediaPath != "" {
			msgType = "media"
		}
		
		response := SendResponse{
			Success: true,
			Message: fmt.Sprintf("%s message sent to %s", msgType, req.Recipient),
		}
		json.NewEncoder(w).Encode(response)
	})

	logger.Infof("HTTP server starting on :8082")
	if err := http.ListenAndServe(":8082", nil); err != nil {
		logger.Errorf("HTTP server error: %v", err)
	}
}

func parsePhoneNumber(phone string) (types.JID, error) {
	// Remove any non-digit characters
	digits := strings.ReplaceAll(phone, "+", "")
	digits = strings.ReplaceAll(digits, "-", "")
	digits = strings.ReplaceAll(digits, " ", "")
	
	// Ensure it's a valid phone number format
	if len(digits) < 10 {
		return types.JID{}, fmt.Errorf("phone number too short")
	}
	
	// Create WhatsApp JID
	return types.NewJID(digits, types.DefaultUserServer), nil
}

func createMediaMessage(mediaPath string) (*waProto.Message, error) {
	// Check if file exists
	if _, err := os.Stat(mediaPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("media file not found: %s", mediaPath)
	}

	// Read file data
	data, err := os.ReadFile(mediaPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read media file: %v", err)
	}

	// Upload media to WhatsApp servers
	uploaded, err := client.Upload(context.Background(), data, whatsmeow.MediaDocument)
	if err != nil {
		return nil, fmt.Errorf("failed to upload media: %v", err)
	}

	// Get filename from path
	filename := strings.Split(mediaPath, "/")
	name := filename[len(filename)-1]

	// Detect MIME type from file extension
	mimeType := "application/octet-stream"
	if strings.HasSuffix(strings.ToLower(mediaPath), ".pdf") {
		mimeType = "application/pdf"
	} else if strings.HasSuffix(strings.ToLower(mediaPath), ".jpg") || strings.HasSuffix(strings.ToLower(mediaPath), ".jpeg") {
		mimeType = "image/jpeg"
	} else if strings.HasSuffix(strings.ToLower(mediaPath), ".png") {
		mimeType = "image/png"
	}

	// Create document message
	msg := &waProto.Message{
		DocumentMessage: &waProto.DocumentMessage{
			URL:           &uploaded.URL,
			Mimetype:      &mimeType,
			Title:         &name,
			FileName:      &name,
			FileSHA256:    uploaded.FileSHA256,
			FileLength:    &uploaded.FileLength,
			MediaKey:      uploaded.MediaKey,
			FileEncSHA256: uploaded.FileEncSHA256,
			DirectPath:    &uploaded.DirectPath,
		},
	}

	return msg, nil
}

// Extract text content from a message
func extractTextContent(msg *waProto.Message) string {
	if msg == nil {
		return ""
	}

	// Try to get text content
	if text := msg.GetConversation(); text != "" {
		return text
	} else if extendedText := msg.GetExtendedTextMessage(); extendedText != nil {
		return extendedText.GetText()
	}

	return ""
}

// Extract media info from a message
func extractMediaInfo(msg *waProto.Message) (mediaType string, filename string, url string, mediaKey []byte, fileSHA256 []byte, fileEncSHA256 []byte, fileLength uint64) {
	if msg == nil {
		return "", "", "", nil, nil, nil, 0
	}

	// Check for image message
	if img := msg.GetImageMessage(); img != nil {
		return "image", "image_" + time.Now().Format("20060102_150405") + ".jpg",
			img.GetURL(), img.GetMediaKey(), img.GetFileSHA256(), img.GetFileEncSHA256(), img.GetFileLength()
	}

	// Check for video message
	if vid := msg.GetVideoMessage(); vid != nil {
		return "video", "video_" + time.Now().Format("20060102_150405") + ".mp4",
			vid.GetURL(), vid.GetMediaKey(), vid.GetFileSHA256(), vid.GetFileEncSHA256(), vid.GetFileLength()
	}

	// Check for audio message
	if aud := msg.GetAudioMessage(); aud != nil {
		return "audio", "audio_" + time.Now().Format("20060102_150405") + ".ogg",
			aud.GetURL(), aud.GetMediaKey(), aud.GetFileSHA256(), aud.GetFileEncSHA256(), aud.GetFileLength()
	}

	// Check for document message
	if doc := msg.GetDocumentMessage(); doc != nil {
		filename := doc.GetFileName()
		if filename == "" {
			filename = "document_" + time.Now().Format("20060102_150405")
		}
		return "document", filename,
			doc.GetURL(), doc.GetMediaKey(), doc.GetFileSHA256(), doc.GetFileEncSHA256(), doc.GetFileLength()
	}

	return "", "", "", nil, nil, nil, 0
}

// Handle regular incoming messages
func handleMessage(msg *events.Message, logger waLog.Logger) {
	// Save message to database
	chatJID := msg.Info.Chat.String()
	sender := msg.Info.Sender.User

	// Get chat name
	name := getChatName(msg.Info.Chat, sender)

	// Update chat in database
	err := messageStore.StoreChat(chatJID, name, msg.Info.Timestamp)
	if err != nil {
		logger.Warnf("Failed to store chat: %v", err)
	}

	// Extract text content
	content := extractTextContent(msg.Message)

	// Extract media info
	mediaType, filename, url, mediaKey, fileSHA256, fileEncSHA256, fileLength := extractMediaInfo(msg.Message)

	// Skip if there's no content and no media
	if content == "" && mediaType == "" {
		return
	}

	// Store message in database
	err = messageStore.StoreMessage(
		msg.Info.ID,
		chatJID,
		sender,
		content,
		msg.Info.Timestamp,
		msg.Info.IsFromMe,
		mediaType,
		filename,
		url,
		mediaKey,
		fileSHA256,
		fileEncSHA256,
		fileLength,
	)

	if err != nil {
		logger.Warnf("Failed to store message: %v", err)
	} else {
		// Log message reception
		timestamp := msg.Info.Timestamp.Format("2006-01-02 15:04:05")
		direction := "←"
		if msg.Info.IsFromMe {
			direction = "→"
		}

		// Log based on message type
		if mediaType != "" {
			fmt.Printf("[%s] %s %s: [%s: %s] %s\n", timestamp, direction, sender, mediaType, filename, content)
		} else if content != "" {
			fmt.Printf("[%s] %s %s: %s\n", timestamp, direction, sender, content)
		}
	}
}

// Handle history sync events
func handleHistorySync(historySync *events.HistorySync, logger waLog.Logger) {
	fmt.Printf("Received history sync event with %d conversations\n", len(historySync.Data.Conversations))

	syncedCount := 0
	for _, conversation := range historySync.Data.Conversations {
		// Parse JID from the conversation
		if conversation.ID == nil {
			continue
		}

		chatJID := *conversation.ID

		// Try to parse the JID
		jid, err := types.ParseJID(chatJID)
		if err != nil {
			logger.Warnf("Failed to parse JID %s: %v", chatJID, err)
			continue
		}

		// Get appropriate chat name
		name := getChatName(jid, "")

		// Process messages
		messages := conversation.Messages
		if len(messages) > 0 {
			// Update chat with latest message timestamp
			latestMsg := messages[0]
			if latestMsg == nil || latestMsg.Message == nil {
				continue
			}

			// Get timestamp from message info
			timestamp := time.Time{}
			if ts := latestMsg.Message.GetMessageTimestamp(); ts != 0 {
				timestamp = time.Unix(int64(ts), 0)
			} else {
				continue
			}

			messageStore.StoreChat(chatJID, name, timestamp)

			// Store messages
			for _, msg := range messages {
				if msg == nil || msg.Message == nil {
					continue
				}

				// Extract text content
				var content string
				if msg.Message.Message != nil {
					content = extractTextContent(msg.Message.Message)
				}

				// Extract media info
				var mediaType, filename, url string
				var mediaKey, fileSHA256, fileEncSHA256 []byte
				var fileLength uint64

				if msg.Message.Message != nil {
					mediaType, filename, url, mediaKey, fileSHA256, fileEncSHA256, fileLength = extractMediaInfo(msg.Message.Message)
				}

				// Skip messages with no content and no media
				if content == "" && mediaType == "" {
					continue
				}

				// Determine sender
				var sender string
				isFromMe := false
				if msg.Message.Key != nil {
					if msg.Message.Key.FromMe != nil {
						isFromMe = *msg.Message.Key.FromMe
					}
					if !isFromMe && msg.Message.Key.Participant != nil && *msg.Message.Key.Participant != "" {
						sender = *msg.Message.Key.Participant
					} else if isFromMe {
						sender = client.Store.ID.User
					} else {
						sender = jid.User
					}
				} else {
					sender = jid.User
				}

				// Store message
				msgID := ""
				if msg.Message.Key != nil && msg.Message.Key.ID != nil {
					msgID = *msg.Message.Key.ID
				}

				// Get message timestamp
				timestamp := time.Time{}
				if ts := msg.Message.GetMessageTimestamp(); ts != 0 {
					timestamp = time.Unix(int64(ts), 0)
				} else {
					continue
				}

				err = messageStore.StoreMessage(
					msgID,
					chatJID,
					sender,
					content,
					timestamp,
					isFromMe,
					mediaType,
					filename,
					url,
					mediaKey,
					fileSHA256,
					fileEncSHA256,
					fileLength,
				)
				if err != nil {
					logger.Warnf("Failed to store history message: %v", err)
				} else {
					syncedCount++
				}
			}
		}
	}

	fmt.Printf("History sync complete. Stored %d messages.\n", syncedCount)
}

// Get chat name based on JID
func getChatName(jid types.JID, sender string) string {
	if jid.Server == "g.us" {
		// This is a group chat
		groupInfo, err := client.GetGroupInfo(jid)
		if err == nil && groupInfo.Name != "" {
			return groupInfo.Name
		} else {
			return fmt.Sprintf("Group %s", jid.User)
		}
	} else {
		// This is an individual contact
		contact, err := client.Store.Contacts.GetContact(jid)
		if err == nil && contact.FullName != "" {
			return contact.FullName
		} else if sender != "" {
			return sender
		} else {
			return jid.User
		}
	}
}

