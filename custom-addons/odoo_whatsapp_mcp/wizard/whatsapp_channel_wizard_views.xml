<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- WhatsApp Channel Creation Wizard -->
        <record id="view_whatsapp_channel_wizard_form" model="ir.ui.view">
            <field name="name">Create WhatsApp Channel</field>
            <field name="model">whatsapp.channel.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <sheet>
                        <div class="oe_title">
                            <h1>Create WhatsApp Channel</h1>
                            <p>Create a discuss channel linked to a WhatsApp conversation</p>
                        </div>
                        
                        <div class="alert alert-info" role="status" invisible="not has_existing_conversation">
                            <i class="fa fa-info-circle" title="Information"/> 
                            This contact already has a WhatsApp conversation.
                            <button name="action_open_existing_conversation" type="object" 
                                    string="Open Existing" class="btn btn-link"/>
                        </div>
                        
                        <group>
                            <group>
                                <field name="partner_id" 
                                       options="{'no_create': True, 'no_edit': True}"
                                       placeholder="Select contact with phone number..."/>
                                <field name="phone" readonly="1"/>
                                <field name="channel_name"/>
                            </group>
                            <group>
                                <field name="auto_create_channel"/>
                                <field name="has_existing_conversation" column_invisible="1"/>
                                <field name="existing_conversation_id" column_invisible="1"/>
                            </group>
                        </group>
                        
                        <group string="Channel Members">
                            <field name="member_ids" widget="many2many_tags" 
                                   placeholder="Select users who can access this channel..."/>
                        </group>
                    </sheet>
                    <footer>
                        <button name="action_create_channel" type="object" 
                                string="Create Channel" class="btn-primary"/>
                        <button string="Cancel" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>

        <!-- Action to open wizard -->
        <record id="action_whatsapp_channel_wizard" model="ir.actions.act_window">
            <field name="name">Create WhatsApp Channel</field>
            <field name="res_model">whatsapp.channel.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>

    </data>
</odoo>