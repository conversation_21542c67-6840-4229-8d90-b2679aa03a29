from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class WhatsAppChannelWizard(models.TransientModel):
    _name = 'whatsapp.channel.wizard'
    _description = 'WhatsApp Channel Creation Wizard'

    partner_id = fields.Many2one(
        'res.partner', 
        'Contact',
        required=True,
        domain="[('mobile', '!=', False)]",
        help="Select contact with mobile number for WhatsApp channel"
    )
    phone = fields.Char(
        'mobile Number',
        related='partner_id.mobile',
        readonly=True
    )
    channel_name = fields.Char(
        'Channel Name',
        compute='_compute_channel_name',
        store=True,
        help="Name for the discuss channel"
    )
    member_ids = fields.Many2many(
        'res.users',
        'whatsapp_wizard_users_rel',
        'wizard_id',
        'user_id',
        string='Channel Members',
        default=lambda self: [(6, 0, [self.env.user.id])],
        help="Users who will have access to this WhatsApp channel"
    )
    auto_create_channel = fields.Boolean(
        'Auto Create Channel',
        default=True,
        help="Automatically create discuss channel for future conversations"
    )
    existing_conversation_id = fields.Many2one(
        'whatsapp.conversation',
        'Existing Conversation',
        compute='_compute_existing_conversation'
    )
    has_existing_conversation = fields.Boolean(
        'Has Existing Conversation',
        compute='_compute_existing_conversation'
    )

    @api.depends('partner_id')
    def _compute_channel_name(self):
        for record in self:
            if record.partner_id:
                record.channel_name = f"WhatsApp: {record.partner_id.name}"
            else:
                record.channel_name = "WhatsApp Channel"

    @api.depends('partner_id')
    def _compute_existing_conversation(self):
        for record in self:
            if record.partner_id and record.partner_id.phone:
                conversation = self.env['whatsapp.conversation'].search([
                    '|',
                    ('phone', '=', record.partner_id.phone),
                    ('mobile', '=', record.partner_id.phone)
                ], limit=1)
                record.existing_conversation_id = conversation.id if conversation else False
                record.has_existing_conversation = bool(conversation)
            else:
                record.existing_conversation_id = False
                record.has_existing_conversation = False

    def action_create_channel(self):
        """Create WhatsApp channel for selected contact"""
        self.ensure_one()
        
        if not self.partner_id.mobile:
            raise UserError(_('Selected contact must have a mobile number'))
            
        phone = self.partner_id.mobile
        
        # Get or create conversation
        conversation = self.env['whatsapp.conversation'].get_or_create_conversation(phone)
        
        # Update conversation settings
        conversation.write({
            'partner_id': self.partner_id.id,
            'auto_create_channel': self.auto_create_channel,
            'channel_members': [(6, 0, self.member_ids.ids)]
        })
        
        # Create or get discuss channel
        if conversation.discuss_channel_id:
            channel = conversation.discuss_channel_id
            # Add new members using proper method
            new_partner_ids = self.member_ids.mapped('partner_id.id')
            channel.add_members(partner_ids=new_partner_ids)
        else:
            channel = conversation._create_discuss_channel()
            
        return {
            'type': 'ir.actions.act_window',
            'name': 'WhatsApp Channel',
            'res_model': 'discuss.channel',
            'res_id': channel.id,
            'view_mode': 'form',
            'target': 'current'
        }

    def action_open_existing_conversation(self):
        """Open existing WhatsApp conversation"""
        self.ensure_one()
        if not self.existing_conversation_id:
            raise UserError(_('No existing conversation found'))
            
        return {
            'type': 'ir.actions.act_window',
            'name': 'WhatsApp Conversation',
            'res_model': 'whatsapp.conversation',
            'res_id': self.existing_conversation_id.id,
            'view_mode': 'form',
            'target': 'current'
        }