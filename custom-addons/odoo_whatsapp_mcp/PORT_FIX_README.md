# WhatsApp Bridge Port Fix

## Issue
The WhatsApp bridge was configured to use port 8080, but <PERSON><PERSON> was already using this port, causing connection errors:
```
HTTPConnectionPool(host='localhost', port=8080): Max retries exceeded with url: /api/qr (Caused by NewConnectionError...)
```

## Solution Applied
Changed the bridge port from 8080 to 8082 to avoid conflicts.

## Files Modified

### 1. Bridge Configuration
- **File**: `bridge/production_bridge.go`
- **Changes**: Updated HTTP server to listen on port 8082
- **Status**: ✅ Complete - Bridge rebuilt and tested

### 2. Odoo Configuration Model
- **File**: `models/whatsapp_config.py`
- **Changes**: Updated default bridge_port from 8080 to 8082
- **Status**: ✅ Complete

### 3. Bridge Testing Script
- **File**: `scripts/run_bridge.py`
- **Changes**: Updated test_bridge_connection default port to 8082
- **Status**: ✅ Complete

### 4. Bridge Supervisor
- **File**: `scripts/bridge_supervisor.py`
- **Changes**: Fixed bridge directory path and log location
- **Status**: ✅ Complete

## Verification Steps

### 1. Bridge Status
```bash
# Check if bridge is running
lsof -i :8082

# Test API endpoints
curl http://localhost:8082/api/status
curl http://localhost:8082/api/qr
```

### 2. Current Status
- ✅ Bridge is running on port 8082
- ✅ API endpoints are responding
- ✅ QR code is being generated
- ✅ Ready for WhatsApp connection

## Next Steps for Users

### For New Installations
- No action needed - new configurations will use port 8082 by default

### For Existing Installations
1. **Update existing configurations**:
   - Go to WhatsApp > Configuration in Odoo
   - Edit your configuration
   - Change "Bridge Port" from 8080 to 8082
   - Save the configuration

2. **Or run the update script** (from Odoo shell):
   ```python
   configs = env['whatsapp.config'].search([('bridge_port', '=', 8080)])
   configs.write({'bridge_port': 8082})
   ```

### Testing the Fix
1. Go to WhatsApp > Configuration in Odoo
2. Click "Get QR Code" button
3. Should see success message instead of connection error
4. Check server logs for QR code display
5. Use "Check Connection" to verify bridge status

## Bridge Management

### Start Bridge Manually
```bash
cd /path/to/odoo_whatsapp_mcp/bridge
./bridge
```

### Check Bridge Status
```bash
curl http://localhost:8082/api/status
```

### Stop Bridge
```bash
pkill bridge
```

## Troubleshooting

### If Port 8082 is Also Busy
1. Find available port: `lsof -i :8083`
2. Update `production_bridge.go` with new port
3. Rebuild: `go build -o bridge production_bridge.go`
4. Update Odoo configuration accordingly

### If Bridge Won't Start
1. Check Go installation: `go version`
2. Check dependencies: `go mod tidy`
3. Check permissions: `ls -la bridge`
4. Check logs: `tail -f bridge.log`

## Files Created
- `scripts/update_port.py` - Helper script to update existing configurations
- `PORT_FIX_README.md` - This documentation

---
**Status**: ✅ Issue Resolved - Bridge running on port 8082
**Date**: July 3, 2025