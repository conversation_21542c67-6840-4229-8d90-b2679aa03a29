from odoo import http, _
from odoo.http import request
from odoo.addons.iap.tools import iap_tools
from odoo.addons.vpcs_message_enhancer.controllers.main import PromptEnhancerController
from odoo.exceptions import UserError, AccessError

DEFAULT_OLG_ENDPOINT = "https://olg.api.odoo.com"

class AITranslatorController(PromptEnhancerController):
    
    @http.route('/vpcs_ai_translator/get_active_languages', type='json', auth='user')
    def get_active_languages(self):
        """Returns list of active languages in the database"""
        Lang = request.env['res.lang']
        active_langs = Lang.get_installed()
        return [{'code': lang[0], 'name': lang[1]} for lang in active_langs]

    @http.route('/vpcs_ai_translator/translate_text', type='json', auth='user')
    def translate_text(self, text, target_lang):
        """Translate text to target language using OLG API"""
        if not text or not text.strip():
            return {'error': 'Empty text'}
        try:
            IrConfigParameter = request.env['ir.config_parameter'].sudo()
            olg_api_endpoint = IrConfigParameter.get_param(
                'web_editor.olg_api_endpoint', DEFAULT_OLG_ENDPOINT
            )
            database_id = IrConfigParameter.get_param('database.uuid')
            
            # Create translation prompt
            prompt = f"Translate the following text to {target_lang}. Keep the same tone and formatting:\n\n{text}"
            
            response = iap_tools.iap_jsonrpc(
                olg_api_endpoint + '/api/olg/1/chat',
                params={
                    'prompt': prompt,
                    'conversation_history': [],
                    'database_id': database_id,
                },
                timeout=30,
            )
            
            if response['status'] == 'success':
                translated_text = response['content']
            elif response['status'] == 'error_prompt_too_long':
                raise UserError(_("Text is too long to translate. Please try with shorter text."))
            elif response['status'] == 'limit_call_reached':
                raise UserError(_("Translation limit reached. Please try again later."))
            else:
                raise UserError(_("Translation failed. Please try again later."))
                
            return {'translated_text': translated_text, 'success': True}
            
        except AccessError:
            raise AccessError(_("Translation service is currently unavailable."))