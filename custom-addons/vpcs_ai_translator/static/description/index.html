<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>VPCS AI Translator</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        .feature-section {
            margin-bottom: 30px;
            padding: 20px;
            background: #f9f9f9;
            border-radius: 8px;
        }
        .feature-title {
            color: #5a4f7f;
            font-size: 1.5em;
            margin-bottom: 15px;
        }
        .feature-description {
            margin-bottom: 15px;
        }
        .feature-list {
            list-style-type: none;
            padding-left: 20px;
        }
        .feature-list li {
            margin-bottom: 10px;
            position: relative;
        }
        .feature-list li:before {
            content: "•";
            color: #5a4f7f;
            font-weight: bold;
            position: absolute;
            left: -20px;
        }
        .user-guide {
            margin-top: 40px;
            padding: 20px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .guide-title {
            color: #5a4f7f;
            font-size: 1.5em;
            margin-bottom: 20px;
        }
        .guide-step {
            margin-bottom: 20px;
            padding: 15px;
            background: #f5f5f5;
            border-radius: 5px;
        }
        .step-number {
            font-weight: bold;
            color: #5a4f7f;
        }
        .note {
            background: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
        }
        .screenshot {
            max-width: 100%;
            height: auto;
            margin: 20px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .video-section {
            margin: 40px 0;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
            width: 100%;
            box-sizing: border-box;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            max-width: 1000px;
            margin: 20px auto;
            overflow: hidden;
            padding-top: 56.25%; 
            margin-bottom: -100px;
        }
        
        .video-container video {
            position: absolute;
            top: -60px;
            left: 0;
            width: 100%;
            height: auto;
            object-fit: fill;
        }
        
        .video-title {
            color: #5a4f7f;
            font-size: 1.8em;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .video-description {
            text-align: center;
            color: #666;
            margin-bottom: 20px;
            font-size: 1.1em;
            max-width: 800px;
            margin-left: auto;
            margin-right: auto;
        }

        /* Responsive adjustments */
        @media screen and (max-width: 768px) {
            .video-section {
                padding: 15px;
                margin: 20px 0;
            }
            
            .video-title {
                font-size: 1.5em;
            }
            
            .video-description {
                font-size: 1em;
                padding: 0 15px;
            }
        }

        @media screen and (max-width: 480px) {
            .video-section {
                padding: 10px;
            }
            
            .video-title {
                font-size: 1.3em;
            }
            
            .video-container {
                margin: 10px auto;
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>VPCS AI Translator</h1>
        <p>Translate your messages with AI</p>
    </div>

    <!-- Video Section -->
    <div class="video-section">
        <h2 class="video-title">Watch How It Works</h2>
        <p class="video-description">See VPCS AI Transaltor in action - Translating messages in real-time</p>
        <div class="video-container">
            <video controls style="width:1000px">
                <source src="translate.webm" type="video/webm">
                Your browser does not support the video tag.
            </video>
        </div>
    </div>

    <div class="feature-section">
        <h2 class="feature-title">Features</h2>
        <div class="feature-description">
            The VPCS AI Translator module depends on VPCS Message Enhancer and adds an AI-powered Translating feature to your Odoo messages in any active languages in odoo database.
        </div>
        <ul class="feature-list">
            <li>AI-powered message Transaltor</li>
            <li>One-click translate button</li>
            <li>Works in multiple locations:
                <ul>
                    <li>ChatGPT window</li>
                    <li>Send message field in chatter</li>
                    <li>Log note field in chatter</li>
                </ul>
            </li>
            <li>AI-Powered Translation: Translates any text entered into the message box using AI.</li>
            <li>Real-Time Translation: Translates input text without page refresh, enhancing user experience.</li>
            <li>Minimal UI Impact: Easy to use interface</li>
        </ul>
    </div>

    <div class="user-guide">
        <h2 class="guide-title">User Guide</h2>
        
        <div class="guide-step">
            <span class="step-number">Pre-requisites:</span> 
            <ul>
                <li>Ensure the module (VPCS Message Enhancer) is installed and working.</li>
                <li>Install AI Translation Module which depends on the VPCS Message Enhancer module.</li>
            </ul>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 1:</span> Activate Languages in Settings
            <ul>
                <li>Go to Settings → Translations → Languages.</li>
                <li>Ensure the languages you want to use for translation are active.</li>
                <li>If needed, click "Activate" on the desired languages.</li>
            </ul>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 2:</span> Access the Translation Feature
            <p>The translation button (translate languages icon) appears in the following locations:</p>
            <ul>
                <li>ChatGPT window - Top right corner of the input field</li>
                <li>Send message field - Top right corner of the message input</li>
                <li>Log note field - Top right corner of the note input</li>
            </ul>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 3:</span> Write Your Message
            <p>Type your message in any of the supported fields. The Translate button will be visible as a translate language icon in the top right corner.</p>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 4:</span> Click on Translate Button
            <ul>
                <li>Click the Translate Button</li>
                <li>A searchable dropdown will appear.</li>
                <ul>
                    <li>This dropdown allows you to search and select the target language.</li>
                    <li>It includes all active languages from the databse (from Step 1).</li>
                </ul>
            </ul>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 5:</span> Search & Select a Language
            <ul>
                <li>Start typing the language name (e.g., "Spanish", "German") in the search bar:</li>
                <li>The dropdown will filter to show matching languages.</li>
                <li>Click on the desired language from the list.</li>
            </ul>
        </div>

        <div class="guide-step">
            <span class="step-number">Step 6:</span> Get Translated Text Instantly
            <ul>
                <li>The message you typed will be translated instantly using an AI backend.</li>
                <li>The translated message will replace the original message in the input box.</li>
            </ul>
        </div>

        <div class="note">
            <strong>Note:</strong> The translation feature works best with clear, well-structured messages. For best results, write your message first, then use the Translate button to improve it.
        </div>
    </div>

    <footer>
            <h2>Support & Contact</h2>
            <p>For questions, issues, or customization requests, our team is ready to help:</p>
            <div class="contact-info">
                <div class="contact-item">
                    <span>📧</span>
                    <a href="mailto:<EMAIL>"><EMAIL></a>
                </div>
                <div class="contact-item">
                    <span>🌐</span>
                    <a href="http://vperfectcs.com/" target="_blank">http://vperfectcs.com/</a>
                </div>
            </div>
            <p>This module is available for purchase and is protected by copyright law.</p>
            <p class="copyright">© 2025 VperfectCS. All rights reserved.</p>
        </footer>
</body>
</html>
