/** @odoo-module **/

import { rpc } from "@web/core/network/rpc";

const DEBUG = true;

function log(message) {
    if (DEBUG) {
        console.log("[prompt translation] " + message);
    }
}

async function addTranslateButton(enhanceBtn) {
    if (!enhanceBtn || enhanceBtn.parentNode.querySelector('.o-translate-btn-wrapper')) return;
    log("Adding translation button to element: " + (enhanceBtn.className || 'unknown'));
    const wrapper = document.createElement('div');
    wrapper.className = 'o-translate-btn-wrapper';
    wrapper.style.cssText = `
        position: absolute;
        right: 40px;
        top: 5px;
        z-index: 9999;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
    `;

    const translateBtn = document.createElement('button');
    translateBtn.className = 'btn btn-secondary o-translate-btn';
    translateBtn.innerHTML = '<i class="fa fa-language"></i>';
    translateBtn.title = 'Translate your message';
    translateBtn.style.cssText = `
        background-color: #5a4f7f !important;
        color: white;
        padding: 3px 8px;
        border: none;
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
    `;

    const dropdown = document.createElement('div');
    dropdown.className = 'o-language-dropdown';
    dropdown.style.cssText = `
        min-width: 200px;
        margin-top: 1.5px;
        padding: 8px;
        border-radius: 6px;
        border: 1px solid #5a4f7f !important;
        background-color: #fff;
        box-shadow: 0 2px 6px #5a4f7f !important;
        font-size: 14px;
        color: #333;
        font-family: 'Segoe UI', sans-serif;
        display: none;
        cursor: default;
        transition: all 0.3s ease;
        overflow: hidden;
        max-height: 300px;
        overflow-y: auto;
    `;

    const searchInput = document.createElement('input');
    searchInput.type = 'text';
    searchInput.placeholder = 'Search language...';
    searchInput.style.cssText = `
        width: 100%;
        padding: 6px 10px;
        margin-bottom: 6px;
        border-radius: 4px;
        border: 1px solid #ccc;
        font-size: 14px;
    `;

    const dropdownList = document.createElement('div');
    dropdownList.style.maxHeight = '200px';
    dropdownList.style.overflowY = 'auto';

    dropdown.appendChild(searchInput);
    dropdown.appendChild(dropdownList);

    const textarea = enhanceBtn.parentNode.querySelector('textarea');
    if (!textarea) return;

    const container = textarea.parentElement;
    container.style.position = 'relative';
    container.style.paddingRight = '50px';
    
    translateBtn.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        const isVisible = dropdown.style.display === 'block';
        dropdown.style.display = isVisible ? 'none' : 'block';

        if (!isVisible) {
            const outsideClickListener = (event) => {
                if (!dropdown.contains(event.target) && !translateBtn.contains(event.target)) {
                    dropdown.style.display = 'none';
                    document.removeEventListener('click', outsideClickListener);
                }
            };
            document.addEventListener('click', outsideClickListener);
        }
    });

    try {
        const languages = await rpc('/vpcs_ai_translator/get_active_languages');
        languages.forEach(lang => {
            const option = document.createElement('div');
            option.textContent = lang.name;
            option.dataset.code = lang.code;
            option.style.cssText = `
                padding: 6px 10px;
                cursor: pointer;
            `;
            option.addEventListener('click', async () => {
                if (!textarea.value.trim()) {
                    alert("Please enter a message to translate.");
                    dropdown.style.display = 'none';
                    return;
                }

                dropdown.style.display = 'none';
                showNotification(`Translating to ${lang.name}...`);
                try {
                    const result = await rpc('/vpcs_ai_translator/translate_text', {
                        text: textarea.value,
                        target_lang: lang.name,
                    });

                    if (result?.success) {
                        textarea.value = result.translated_text;
                        textarea.focus();
                        textarea.dispatchEvent(new Event('input', { bubbles: true }));
                        showNotification("Translation complete!");
                    } else {
                        showNotification("Translation failed.");
                    }
                } catch (err) {
                    console.error("Translation error", err);
                    showNotification("Translation service failed.");
                }
            });
            dropdownList.appendChild(option);
        });
    } catch (err) {
        console.error("Could not load language list", err);
    }

    searchInput.addEventListener('input', () => {
        const filter = searchInput.value.toLowerCase();
        const options = dropdownList.querySelectorAll('div');
        options.forEach(opt => {
            const text = opt.textContent.toLowerCase();
            opt.style.display = text.includes(filter) ? '' : 'none';
        });
    });

    wrapper.appendChild(translateBtn);
    wrapper.appendChild(dropdown);
    enhanceBtn.parentNode.appendChild(wrapper);
}

function showNotification(message, duration = 4000) {
    let notification = document.createElement("div");
    notification.className = "o-enhance-notification";
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: green;
        color: white;
        padding: 10px 15px;
        border-radius: 5px;
        font-size: 14px;
        z-index: 1000;
        box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.2);
        transition: opacity 0.5s;
    `;
    notification.innerText = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        notification.style.opacity = "0";
        setTimeout(() => notification.remove(), 500);
    }, duration);
}

function initTranslateButtonObserver() {
    log("Initializing transaltor..");
    const observer = new MutationObserver(() => {
        const enhanceButtons = document.querySelectorAll('.o-enhance-prompt-btn');
        enhanceButtons.forEach(btn => addTranslateButton(btn));
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true,
    });

    setTimeout(() => {
        document.querySelectorAll('.o-enhance-prompt-btn').forEach(btn => addTranslateButton(btn));
    }, 100);
}

if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initTranslateButtonObserver);
} else {
    initTranslateButtonObserver();
}