{
    'name': 'Odoo Hugging Face Gradio MCP',
    'version': '1.0',
    'category': 'Sales/CRM',
    'summary': 'Integrates Odoo with Hugging Face Gradio MCP for AI-powered sales order creation via live chat.',
    'depends': [
        'base',
        'mail',
        'im_livechat',
        'sale_management',
        'vpcs_llm_provider_huggingface',
        'odoo_huggingface_mcp',
        'sales_team', # Added this dependency for group_sale_salesman
    ],
    'data': [
        'security/ir.model.access.csv',
        'security/gradio_mcp_security.xml',
        'views/gradio_mcp_config_views.xml',
        'views/gradio_mcp_tool_views.xml',
        'views/gradio_mcp_processor_views.xml',
        'views/gradio_mcp_wizard_views.xml',
        'views/gradio_mcp_dashboard_views.xml',
        'views/gradio_mcp_agent_views.xml',
        'views/gradio_mcp_livechat_views.xml',
        'data/gradio_mcp_config_data.xml',
        # Other data files will be added here as they are created
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
}