<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="gradio_mcp_config_default" model="gradio.mcp.config">
            <field name="name">Default Gradio MCP Configuration</field>
            <field name="mcp_server_url">https://vpcsinfo-gradio-mcp-odoo.hf.space/gradio_api/mcp</field>
        </record>

        <record id="ir_cron_gradio_mcp_connection_health" model="ir.cron">
            <field name="name">Gradio MCP: Connection Health Check</field>
            <field name="model_id" ref="model_gradio_mcp_config"/>
            <field name="state">code</field>
            <field name="code">model._check_connection_health()</field>
            <field name="interval_number">1</field>
            <field name="interval_type">hours</field>
            
        </record>
    </data>
</odoo>