# Odoo Hugging Face Gradio MCP Integration

This Odoo module integrates with a Hugging Face Gradio-based MCP (Multi-Agent Communication Protocol) server to enable AI-powered sales order creation and other functionalities directly within Odoo, including live chat integration.

## Features

*   **Gradio MCP Server Integration:** Connects Odoo to a remote or local Gradio MCP server.
*   **AI Agent for Sales Orders:** Leverages a Pydantic AI agent to process natural language requests for sales orders.
*   **Product and Customer Lookup:** AI agent can search for products and customers in Odoo using MCP tools.
*   **Sales Order Creation:** AI agent can create sales orders with line items, including pricing and tax calculations.
*   **Conversation Management:** Manages multi-turn conversations with context and state tracking.
*   **Live Chat Integration:** Seamlessly integrates with Odoo's `im_livechat` module for real-time AI-powered sales assistance.
*   **Monitoring Dashboard:** Provides a dashboard to monitor AI agent activity, MCP tool usage, and logs.
*   **Configuration Wizard:** Easy setup and management of MCP server connection and API keys.
*   **Robust Error Handling:** Includes retry mechanisms and informative error messages.

## Installation

1.  Clone this repository into your Odoo `custom-addons` directory.
2.  Ensure all dependencies are installed:
    ```bash
    pip install -r /path/to/odoo_huggingface_gradio_mcp/requirements.txt
    ```
3.  Start Odoo and update your modules list.
4.  Install the `Odoo Hugging Face Gradio MCP` module.

## Configuration

After installation, configure the module:

1.  Go to `Gradio MCP` -> `Setup Wizard`.
2.  Enter the `MCP Server URL`. The default is `https://vpcsinfo-gradio-mcp-odoo.hf.space/gradio_api/mcp`.
3.  (Optional) Enter an `API Key` if your MCP server requires authentication.
4.  Click `Test Connection` to verify the connection to the MCP server.
5.  Click `Save Configuration`.

## Usage

### AI Agent Conversations

*   You can monitor AI agent conversations and their states by navigating to `Gradio MCP` -> `Conversations`.

### MCP Tools

*   To import or view the available MCP tools from your configured server, go to `Gradio MCP` -> `Tools`.
*   Click `Import Tools from MCP` to fetch the latest tools.

### Tool Executions

*   Monitor the execution of MCP tools by navigating to `Gradio MCP` -> `Tool Executions`.

### Dashboard

*   View a summary of AI agent activity and MCP tool usage on the dashboard: `Gradio MCP` -> `Dashboard`.

### Live Chat Integration

*   Once configured, the AI agent can be integrated with Odoo's live chat. When a customer initiates a chat, the AI agent can assist with sales order creation based on natural language input.

## Natural Language Commands (Examples)

Here are some example commands you can use to interact with the AI agent:

*   **Create Sales Order:**
    *   "I want to create an order for 2 laptops."
    *   "Can you make an order for 5 mouses?"

*   **Search Product:**
    *   "Search product laptop."
    *   "What is the price of a keyboard?"

*   **Find Customer:**
    *   "Find customer John Doe."
    *   "What is the email of Jane Smith?"

*   **Confirm/Cancel Order:**
    *   (After agent proposes an order) "Yes" or "No"

## Development

### Module Structure

```
odoo_huggingface_gradio_mcp/
├── __init__.py
├── __manifest__.py
├── models/
│   ├── __init__.py
│   ├── gradio_mcp_agent.py          # Main Pydantic AI agent logic
│   ├── gradio_mcp_client.py         # Gradio MCP client wrapper for API calls
│   ├── gradio_mcp_config.py         # Configuration model for MCP server settings
│   ├── gradio_mcp_processor.py      # Model for managing and monitoring tool executions
│   ├── gradio_mcp_wizard.py         # Transient model for configuration setup wizard
│   ├── gradio_mcp_livechat.py       # Extends discuss.channel for live chat integration
│   └── gradio_mcp_chatbot.py        # Extends chatbot.script for sales order steps
├── data/
│   ├── gradio_mcp_config_data.xml   # Default configuration data
│   └── system_parameters.xml        # (Optional) System parameters
├── views/
│   ├── gradio_mcp_config_views.xml  # Views for configuration settings
│   ├── gradio_mcp_tool_views.xml    # Views for displaying MCP tools
│   ├── gradio_mcp_processor_views.xml # Views for tool execution monitoring
│   ├── gradio_mcp_wizard_views.xml  # Views for the setup wizard
│   ├── gradio_mcp_dashboard_views.xml # Views for the dashboard
│   └── gradio_mcp_agent_views.xml   # Views for AI agent conversations
├── security/
│   └── ir.model.access.csv          # Access rights definitions
├── controllers/
│   ├── __init__.py
│   └── gradio_mcp_controller.py     # (Optional) Web controllers for external communication
├── tests/
│   ├── __init__.py
│   ├── test_gradio_mcp_client.py    # Unit tests for MCP client
│   ├── test_gradio_mcp_agent.py     # Unit tests for AI agent logic
│   ├── test_gradio_mcp_processor.py # Unit tests for tool processor
│   ├── test_gradio_mcp_config.py    # Unit tests for configuration
│   ├── test_gradio_mcp_tool.py      # Unit tests for tool definition
│   ├── test_gradio_mcp_wizard.py    # Unit tests for wizard
│   ├── test_gradio_mcp_livechat.py  # Unit tests for live chat integration
│   └── test_gradio_mcp_chatbot.py   # Unit tests for chatbot extension
└── requirements.txt                 # Python dependencies
```

## License

LGPL-3
