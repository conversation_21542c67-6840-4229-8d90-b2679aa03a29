id,name,model_id:id,group_id:id,perm_read,perm_write,perm_create,perm_unlink
access_gradio_mcp_config,gradio.mcp.config.access,model_gradio_mcp_config,base.group_system,1,1,1,1
access_gradio_mcp_tool,gradio.mcp.tool.access,model_gradio_mcp_tool,base.group_system,1,1,1,1
access_gradio_mcp_processor,gradio.mcp.processor.access,model_gradio_mcp_processor,base.group_system,1,1,1,1
access_gradio_mcp_wizard,gradio.mcp.wizard.access,model_gradio_mcp_wizard,base.group_system,1,1,1,1
access_gradio_mcp_dashboard,gradio.mcp.dashboard.access,model_gradio_mcp_dashboard,base.group_system,1,0,0,0
access_gradio_mcp_agent,gradio.mcp.agent.access,model_gradio_mcp_agent,base.group_system,1,1,1,1
access_discuss_channel_ai_fields,discuss.channel.ai.fields.access,model_discuss_channel,base.group_user,1,1,0,0
access_chatbot_script_sales_fields,chatbot.script.sales.fields.access,model_chatbot_script,base.group_user,1,1,0,0