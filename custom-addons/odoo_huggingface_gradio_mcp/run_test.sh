#!/bin/bash

# Simple test runner for Gradio MCP integration

echo "🚀 Setting up test environment..."

# Install requirements
pip install -r requirements_test.txt

# Set environment variables (modify as needed)
export ODOO_URL="http://localhost:8069"
export ODOO_DB="llmdb18"
export ODOO_USERNAME="admin"
export ODOO_PASSWORD="admin"

echo "🔧 Environment configured:"
echo "   ODOO_URL: $ODOO_URL"
echo "   ODOO_DB: $ODOO_DB"
echo "   ODOO_USERNAME: $ODOO_USERNAME"

echo ""
echo "🧪 Running Gradio MCP test..."
echo ""
echo "# Set your HuggingFace token if needed for private spaces:"
echo "# export HUGGINGFACE_TOKEN=hf_your_token_here"
echo ""
python test_gradio_mcp.py