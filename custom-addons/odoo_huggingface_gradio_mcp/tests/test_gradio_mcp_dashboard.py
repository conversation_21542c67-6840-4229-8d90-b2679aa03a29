from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json

class TestGradioMcpDashboard(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpDashboard, self).setUp(*args, **kwargs)
        self.dashboard_model = self.env['gradio.mcp.dashboard']
        self.agent_model = self.env['gradio.mcp.agent']
        self.processor_model = self.env['gradio.mcp.processor']
        self.tool_model = self.env['gradio.mcp.tool']

        # Create some dummy data for testing computations
        self.agent1 = self.agent_model.create({'current_state': 'initial'})
        self.agent2 = self.agent_model.create({'current_state': 'gathering_info'})
        self.agent3 = self.agent_model.create({'current_state': 'completed'})

        self.tool1 = self.tool_model.create({'name': 'tool1'})
        self.tool2 = self.tool_model.create({'name': 'tool2'})

        self.processor1 = self.processor_model.create({
            'name': 'Proc1', 'tool_id': self.tool1.id, 'execution_status': 'completed', 'execution_log': 'Log1'
        })
        self.processor2 = self.processor_model.create({
            'name': 'Proc2', 'tool_id': self.tool2.id, 'execution_status': 'failed', 'execution_log': 'Log2'
        })
        self.processor3 = self.processor_model.create({
            'name': 'Proc3', 'tool_id': self.tool1.id, 'execution_status': 'completed', 'execution_log': 'Log3'
        })

    def test_compute_dashboard_data(self):
        dashboard = self.dashboard_model.create({})
        
        self.assertEqual(dashboard.total_conversations, 3)
        self.assertEqual(dashboard.active_conversations, 1) # agent2 is 'gathering_info'
        self.assertEqual(dashboard.total_tool_calls, 3)
        self.assertEqual(dashboard.successful_tool_calls, 2)
        self.assertEqual(dashboard.failed_tool_calls, 1)

        # Check latest logs content (order by create_date desc)
        expected_logs_part = [
            f"[{self.processor3.create_date}] Proc3 - Status: completed - Log: Log3",
            f"[{self.processor2.create_date}] Proc2 - Status: failed - Log: Log2",
            f"[{self.processor1.create_date}] Proc1 - Status: completed - Log: Log1",
        ]
        # We only check if parts of the expected logs are present, as datetime formatting might vary slightly
        # and there might be other logs from test setup/teardown
        for expected_log in expected_logs_part:
            self.assertIn(expected_log.split('] ')[1], dashboard.latest_logs)
