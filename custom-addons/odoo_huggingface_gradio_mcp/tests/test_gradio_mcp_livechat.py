from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json

class TestGradioMcpLivechat(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpLivechat, self).setUp(*args, **kwargs)
        self.channel_model = self.env['discuss.channel']
        self.agent_model = self.env['gradio.mcp.agent']
        self.partner_model = self.env['res.partner']
        self.sale_order_model = self.env['sale.order']
        self.product_product_model = self.env['product.product'] # Added for product creation

        self.test_partner = self.partner_model.create({'name': 'Test Customer', 'email': '<EMAIL>'})
        self.test_channel = self.channel_model.create({
            'channel_partner_ids': [(4, self.test_partner.id)],
            'channel_type': 'livechat',
            'name': 'Test Livechat Channel',
        })
        self.test_channel._activate_ai_agent() # Activate AI agent for the channel

        # Create dummy products for testing
        self.product_laptop = self.product_product_model.create({'name': 'Laptop', 'list_price': 1200.0})
        self.product_mouse = self.product_product_model.create({'name': 'Mouse', 'list_price': 25.0})

        # Create a dummy config for MCP client
        self.env['gradio.mcp.config'].create({
            'name': 'Test MCP Config',
            'mcp_server_url': 'http://mock-mcp-server.com',
        })

    def test_activate_ai_agent(self):
        # Already activated in setUp, so just re-verify
        self.assertTrue(self.test_channel.ai_agent_active)

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_agent.GradioMcpAgent.process_message')
    def test_process_ai_message(self, mock_process_message):
        mock_process_message.return_value = "AI response"
        
        response = self.test_channel._process_ai_message("User message")
        self.assertEqual(response, "AI response")
        mock_process_message.assert_called_once_with("User message")

    def test_create_sales_order_from_chat(self):
        order_data = {
            'partner_id': self.test_partner.id,
            'order_lines': [
                {'product_id': self.product_laptop.id, 'product_uom_qty': 2, 'price_unit': 1200.0},
            ]
        }
        
        with patch.object(self.sale_order_model, 'create') as mock_sale_order_create:
            mock_sale_order_create.return_value = MagicMock(id=999, name='SO001')
            
            order = self.test_channel._create_sales_order_from_chat(order_data)
            
            self.assertIsNotNone(order)
            self.assertEqual(self.test_channel.sales_order_draft_id.id, 999)
            mock_sale_order_create.assert_called_once()

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_agent.GradioMcpAgent._call_mcp_tools')
    @patch('odoo.addons.mail.models.mail_channel.MailChannel.message_post')
    def test_end_to_end_sales_order_creation_livechat(self, mock_message_post, mock_call_mcp_tools):
        # Mock MCP tool calls
        mock_call_mcp_tools.side_effect = [
            # For search_partner (initial identification)
            {'id': self.test_partner.id, 'name': self.test_partner.name, 'email': self.test_partner.email},
            # For search_product (Laptop)
            {'id': self.product_laptop.id, 'name': self.product_laptop.name, 'price': self.product_laptop.list_price},
            # For get_product_pricing (Laptop)
            {'price': self.product_laptop.list_price},
            # For create_order_generic
            {'order_id': 'SO007', 'status': 'created'}
        ]

        # Simulate initial message from customer
        self.test_channel._message_post_after_hook(MagicMock(body="Hello, my name is Test Customer, email: <EMAIL>", author_id=MagicMock(id=100)), {})
        
        # Verify initial greeting and state transition
        mock_message_post.assert_called_with(body="Hello! I'm here to help you create your order. May I have your name and email address?", message_type='comment', subtype_xmlid='mail.mt_comment')
        # Access the agent instance from the channel. This assumes a specific way the agent is linked.
        # In a real scenario, you might need to search for the agent record associated with this channel.
        # For simplicity in test, we assume the agent is created and linked to the channel's context.
        # This line might need adjustment based on how the agent is truly managed per channel.
        # For now, let's assume the agent is created and its state is updated globally or via a specific link.
        # A more robust way would be to pass the agent record to the _process_customer_message or retrieve it.
        # For this test, we'll directly check the agent model's state if it's a singleton or globally managed.
        # If the agent is per-channel, we need to ensure the correct agent instance is being checked.
        # Given the current agent model is AbstractModel, it's likely a singleton or managed via context.
        # Let's assume for now that the agent's state is updated on the single agent record.
        agent_instance = self.agent_model.search([], limit=1) # Assuming a single agent instance for simplicity
        self.assertEqual(agent_instance.current_state, 'awaiting_customer_id')

        # Simulate customer providing details and asking to create order
        mock_message_post.reset_mock() # Reset mock for next AI response
        self.test_channel._message_post_after_hook(MagicMock(body="I want to create order for 2 laptops", author_id=MagicMock(id=100)), {})
        
        # Verify AI response and state transition
        mock_message_post.assert_called_with(body=f"Thank you {self.test_partner.name}! I found your account. What products are you interested in today?", message_type='comment', subtype_xmlid='mail.mt_comment')
        self.assertEqual(agent_instance.current_state, 'gathering_info')

        # Simulate customer confirming order
        mock_message_post.reset_mock()
        self.test_channel._message_post_after_hook(MagicMock(body="Yes, that is correct", author_id=MagicMock(id=100)), {})

        # Verify final AI response and sales order creation
        mock_message_post.assert_called_with(body=f"Order created successfully! Result: {{'order_id': 'SO007', 'status': 'created'}}. You'll receive a confirmation email shortly. Is there anything else?", message_type='comment', subtype_xmlid='mail.mt_comment')
        self.assertEqual(agent_instance.current_state, 'completed')

        # Verify sales order was attempted to be created (check mock_call_mcp_tools for create_order_generic)
        # This requires inspecting the calls to mock_call_mcp_tools
        create_order_call_args = None
        for call in mock_call_mcp_tools.call_args_list:
            if call.args[0] == 'create_order_generic':
                create_order_call_args = call.args[1]
                break
        
        self.assertIsNotNone(create_order_call_args)
        self.assertEqual(create_order_call_args['partner_id'], self.test_partner.id)
        self.assertEqual(len(create_order_call_args['order_lines']), 1)
        self.assertEqual(create_order_call_args['order_lines'][0]['product_id'], self.product_laptop.id)
        self.assertEqual(create_order_call_args['order_lines'][0]['product_uom_qty'], 2)
        self.assertEqual(create_order_call_args['order_lines'][0]['price_unit'], 1200.0)