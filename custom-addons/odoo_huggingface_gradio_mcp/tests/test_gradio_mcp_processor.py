from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json

class TestGradioMcpProcessor(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpProcessor, self).setUp(*args, **kwargs)
        self.processor = self.env['gradio.mcp.processor']
        self.config = self.env['gradio.mcp.config'].create({
            'name': 'Test Config',
            'mcp_server_url': 'http://test-mcp-server.com',
        })
        self.tool = self.env['gradio.mcp.tool'].create({
            'name': 'test_tool',
            'description': 'A test tool',
            'parameters': '{}',
        })

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient.execute_tool')
    def test_execute_mcp_tool_success(self, mock_execute_tool):
        mock_execute_tool.return_value = {"status": "success", "data": "tool_output"}
        
        processor_record = self.processor.create({
            'name': 'Test Process',
            'tool_id': self.tool.id,
            'input_parameters': json.dumps({"param1": "value1"}),
        })
        
        result = processor_record.execute_mcp_tool()
        self.assertTrue(result)
        self.assertEqual(processor_record.execution_status, 'completed')
        self.assertEqual(json.loads(processor_record.output_result), {"status": "success", "data": "tool_output"})
        self.assertEqual(processor_record.execution_log, "Tool executed successfully.")
        mock_execute_tool.assert_called_once_with(self.config.mcp_server_url, 'test_tool', {"param1": "value1"})

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient.execute_tool')
    def test_execute_mcp_tool_invalid_json(self, mock_execute_tool):
        processor_record = self.processor.create({
            'name': 'Test Process Invalid JSON',
            'tool_id': self.tool.id,
            'input_parameters': "invalid json",
        })
        
        result = processor_record.execute_mcp_tool()
        self.assertFalse(result)
        self.assertEqual(processor_record.execution_status, 'failed')
        self.assertIn("Invalid JSON in Input Parameters.", processor_record.execution_log)
        mock_execute_tool.assert_not_called()

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient.execute_tool')
    def test_execute_mcp_tool_execution_failure(self, mock_execute_tool):
        mock_execute_tool.side_effect = Exception("MCP tool error")
        
        processor_record = self.processor.create({
            'name': 'Test Process Execution Failure',
            'tool_id': self.tool.id,
            'input_parameters': "{}",
        })
        
        result = processor_record.execute_mcp_tool()
        self.assertFalse(result)
        self.assertEqual(processor_record.execution_status, 'failed')
        self.assertIn("MCP tool error", processor_record.execution_log)
        mock_execute_tool.assert_called_once()

    def test_execute_mcp_tool_no_config(self):
        self.config.unlink() # Remove the config
        
        processor_record = self.processor.create({
            'name': 'Test Process No Config',
            'tool_id': self.tool.id,
            'input_parameters': "{}",
        })
        
        result = processor_record.execute_mcp_tool()
        self.assertFalse(result)
        self.assertEqual(processor_record.execution_status, 'failed')
        self.assertIn("No Gradio MCP configuration found", processor_record.execution_log)
