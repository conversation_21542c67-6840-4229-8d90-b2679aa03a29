from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json

class TestGradioMcpAgent(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpAgent, self).setUp(*args, **kwargs)
        self.agent = self.env['gradio.mcp.agent'].create({'id': 1}) # Create a dummy agent record
        self.config = self.env['gradio.mcp.config'].create({
            'name': 'Test Config',
            'mcp_server_url': 'http://test-mcp-server.com',
        })

    def test_initial_state(self):
        self.assertEqual(self.agent.current_state, 'initial')
        self.assertEqual(json.loads(self.agent.conversation_history), [])

    def test_process_message_initial_state_create_order(self):
        response = self.agent.process_message("I want to create order")
        self.assertEqual(self.agent.current_state, 'gathering_info')
        self.assertEqual(response, "Okay, I can help you create an order. What product are you interested in?")
        history = json.loads(self.agent.conversation_history)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]['content'], "I want to create order")
        self.assertEqual(history[1]['content'], "Okay, I can help you create an order. What product are you interested in?")

    def test_process_message_initial_state_general_query(self):
        response = self.agent.process_message("Hello there!")
        self.assertEqual(self.agent.current_state, 'initial')
        self.assertEqual(response, "Hello! How can I assist you today?")
        history = json.loads(self.agent.conversation_history)
        self.assertEqual(len(history), 2)
        self.assertEqual(history[0]['content'], "Hello there!")
        self.assertEqual(history[1]['content'], "Hello! How can I assist you today?")

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_agent.GradioMcpAgent._call_mcp_tools')
    def test_process_message_gathering_info_success(self, mock_call_mcp_tools):
        self.agent.write({'current_state': 'gathering_info'})
        mock_call_mcp_tools.side_effect = [
            {'id': 10, 'name': 'Laptop', 'price': 1200.0}, # For search_product
            {'order_id': 'SO001'} # For create_sales_order
        ]
        
        response = self.agent.process_message("I want 2 laptops")
        self.assertEqual(self.agent.current_state, 'confirming_order')
        self.assertIn("I\'m about to create an order for 2 Laptop(s) at $1200.0 each. Is this correct? (Yes/No)", response)
        self.assertNotEqual(json.loads(self.agent.temp_order_data), {})

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_agent.GradioMcpAgent._call_mcp_tools')
    def test_process_message_gathering_info_product_not_found(self, mock_call_mcp_tools):
        self.agent.write({'current_state': 'gathering_info'})
        mock_call_mcp_tools.return_value = None # Product not found
        
        response = self.agent.process_message("I want 5 unknown_product")
        self.assertEqual(self.agent.current_state, 'gathering_info') # Stays in gathering_info
        self.assertIn("Sorry, I couldn\'t find unknown_product. Please specify a valid product.", response)
        self.assertEqual(json.loads(self.agent.temp_order_data), {})

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_agent.GradioMcpAgent._call_mcp_tools')
    def test_process_message_confirming_order_yes(self, mock_call_mcp_tools):
        self.agent.write({
            'current_state': 'confirming_order',
            'temp_order_data': json.dumps({
                'product_id': 10, 'product_name': 'Laptop', 'quantity': 2, 'price_unit': 1200.0, 'partner_id': 1
            })
        })
        mock_call_mcp_tools.return_value = {'order_id': 'SO001'}
        
        response = self.agent.process_message("Yes")
        self.assertEqual(self.agent.current_state, 'completed')
        self.assertIn("Order created successfully! Result: {'order_id': 'SO001'}. Is there anything else?", response)
        self.assertEqual(json.loads(self.agent.temp_order_data), {})

    def test_process_message_confirming_order_no(self):
        self.agent.write({
            'current_state': 'confirming_order',
            'temp_order_data': json.dumps({
                'product_id': 10, 'product_name': 'Laptop', 'quantity': 2, 'price_unit': 1200.0, 'partner_id': 1
            })
        })
        
        response = self.agent.process_message("No")
        self.assertEqual(self.agent.current_state, 'initial')
        self.assertIn("Okay, I\'ve cancelled the order. Is there anything else I can help you with?", response)
        self.assertEqual(json.loads(self.agent.temp_order_data), {})

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_agent.GradioMcpAgent._call_mcp_tools')
    def test_process_message_search_product(self, mock_call_mcp_tools):
        self.agent.write({'current_state': 'search_product'})
        mock_call_mcp_tools.return_value = {'id': 20, 'name': 'Keyboard', 'price': 50.0}
        
        response = self.agent.process_message("Keyboard")
        self.assertEqual(self.agent.current_state, 'initial')
        self.assertIn("Found product: Keyboard (ID: 20, Price: $50.0)", response)

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_agent.GradioMcpAgent._call_mcp_tools')
    def test_process_message_find_customer(self, mock_call_mcp_tools):
        self.agent.write({'current_state': 'find_customer'})
        mock_call_mcp_tools.return_value = {'id': 1, 'name': 'John Doe', 'email': '<EMAIL>', 'phone': '12345'}
        
        response = self.agent.process_message("John Doe")
        self.assertEqual(self.agent.current_state, 'initial')
        self.assertIn("Found customer: John Doe (Email: <EMAIL>, Phone: 12345)", response)
