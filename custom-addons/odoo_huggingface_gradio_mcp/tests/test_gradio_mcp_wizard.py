from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json

class TestGradioMcpWizard(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpWizard, self).setUp(*args, **kwargs)
        self.wizard_model = self.env['gradio.mcp.wizard']
        self.config_model = self.env['gradio.mcp.config']
        
        # Ensure no existing config interferes with default_get
        self.config_model.search([]).unlink()

    def test_default_get_no_config(self):
        wizard = self.wizard_model.create({})
        self.assertEqual(wizard.mcp_server_url, '') # Default from field definition
        self.assertIsNone(wizard.api_key)
        self.assertEqual(wizard.connection_status, 'disconnected')

    def test_default_get_with_existing_config(self):
        self.config_model.create({
            'name': 'Existing Config',
            'mcp_server_url': 'http://existing-server.com',
            'api_key': 'existing_key',
            'connection_status': 'connected',
        })
        wizard = self.wizard_model.create({})
        self.assertEqual(wizard.mcp_server_url, 'http://existing-server.com')
        self.assertEqual(wizard.api_key, 'existing_key')
        self.assertEqual(wizard.connection_status, 'connected')

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient._initialize_connection')
    def test_action_test_connection_success(self, mock_initialize_connection):
        mock_initialize_connection.return_value = True
        wizard = self.wizard_model.create({
            'mcp_server_url': 'http://test-server.com',
            'api_key': 'test_key',
        })
        
        result = wizard.action_test_connection()
        self.assertEqual(wizard.connection_status, 'connected')
        mock_initialize_connection.assert_called_once_with('http://test-server.com')
        self.assertEqual(result['type'], 'ir.actions.act_window')
        self.assertEqual(result['res_id'], wizard.id)

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient._initialize_connection')
    def test_action_test_connection_failure(self, mock_initialize_connection):
        mock_initialize_connection.return_value = False
        wizard = self.wizard_model.create({
            'mcp_server_url': 'http://fail-server.com',
        })
        
        result = wizard.action_test_connection()
        self.assertEqual(wizard.connection_status, 'error')
        mock_initialize_connection.assert_called_once_with('http://fail-server.com')
        self.assertEqual(result['type'], 'ir.actions.act_window')
        self.assertEqual(result['res_id'], wizard.id)

    def test_action_save_configuration_create_new(self):
        wizard = self.wizard_model.create({
            'mcp_server_url': 'http://new-server.com',
            'api_key': 'new_key',
        })
        
        result = wizard.action_save_configuration()
        self.assertEqual(result['type'], 'ir.actions.act_window_close')
        
        config = self.config_model.search([])
        self.assertEqual(len(config), 1)
        self.assertEqual(config.mcp_server_url, 'http://new-server.com')
        self.assertEqual(config.api_key, 'new_key')

    def test_action_save_configuration_update_existing(self):
        existing_config = self.config_model.create({
            'name': 'Existing Config',
            'mcp_server_url': 'http://old-server.com',
            'api_key': 'old_key',
        })
        
        wizard = self.wizard_model.create({
            'mcp_server_url': 'http://updated-server.com',
            'api_key': 'updated_key',
        })
        
        result = wizard.action_save_configuration()
        self.assertEqual(result['type'], 'ir.actions.act_window_close')
        
        config = self.config_model.search([])
        self.assertEqual(len(config), 1) # Still one config
        self.assertEqual(config.mcp_server_url, 'http://updated-server.com')
        self.assertEqual(config.api_key, 'updated_key')
        self.assertEqual(config.name, 'Existing Config') # Name should remain the same
