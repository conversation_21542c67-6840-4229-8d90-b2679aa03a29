from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import requests

class TestGradioMcpClient(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpClient, self).setUp(*args, **kwargs)
        self.client = self.env['gradio.mcp.client']
        self.config = self.env['gradio.mcp.config'].create({
            'name': 'Test Config',
            'mcp_server_url': 'http://test-mcp-server.com',
        })

    @patch('requests.get')
    def test_initialize_connection_success(self, mock_get):
        mock_get.return_value.raise_for_status = MagicMock()
        mock_get.return_value.status_code = 200
        
        result = self.client._initialize_connection(self.config.mcp_server_url)
        self.assertTrue(result)
        mock_get.assert_called_once_with(self.config.mcp_server_url, timeout=5)
        self.assertEqual(self.config.connection_status, 'connected')

    @patch('requests.get')
    def test_initialize_connection_failure(self, mock_get):
        mock_get.side_effect = requests.exceptions.RequestException("Connection error")
        
        result = self.client._initialize_connection(self.config.mcp_server_url)
        self.assertFalse(result)
        mock_get.assert_called_once_with(self.config.mcp_server_url, timeout=5)
        self.assertEqual(self.config.connection_status, 'error')

    @patch('requests.post')
    def test_send_request_success(self, mock_post):
        mock_post.return_value.raise_for_status = MagicMock()
        mock_post.return_value.json.return_value = {"status": "success", "data": "test_data"}
        
        result = self.client._send_request(self.config.mcp_server_url, "test_endpoint", {"key": "value"})
        self.assertEqual(result, {"status": "success", "data": "test_data"})
        mock_post.assert_called_once()

    @patch('requests.post')
    def test_send_request_failure(self, mock_post):
        mock_post.side_effect = requests.exceptions.RequestException("Post error")
        
        with self.assertRaises(requests.exceptions.RequestException):
            self.client._send_request(self.config.mcp_server_url, "test_endpoint", {"key": "value"})
        mock_post.assert_called_once()

    def test_handle_response_success(self):
        response_data = {"status": "success", "data": {"result": "ok"}}
        result = self.client._handle_response(response_data)
        self.assertEqual(result, {"result": "ok"})

    def test_handle_response_failure(self):
        response_data = {"status": "error", "message": "Something went wrong"}
        with self.assertRaisesRegex(Exception, "MCP Server Error: Something went wrong"):
            self.client._handle_response(response_data)

    def test_execute_tool(self):
        with patch.object(self.client, '_send_request') as mock_send_request, \
             patch.object(self.client, '_handle_response') as mock_handle_response:
            
            mock_send_request.return_value = {"status": "success", "data": "tool_output"}
            mock_handle_response.return_value = "tool_output"

            result = self.client.execute_tool(self.config.mcp_server_url, "my_tool", {"param": "value"})
            self.assertEqual(result, "tool_output")
            mock_send_request.assert_called_once_with(self.config.mcp_server_url, "execute_tool", {'tool_name': 'my_tool', 'params': {'param': 'value'}})
            mock_handle_response.assert_called_once_with({"status": "success", "data": "tool_output"})

    def test_get_tools(self):
        with patch.object(self.client, '_send_request') as mock_send_request, \
             patch.object(self.client, '_handle_response') as mock_handle_response:
            
            mock_send_request.return_value = {"status": "success", "data": [{"name": "tool1"}, {"name": "tool2"}]}
            mock_handle_response.return_value = [{"name": "tool1"}, {"name": "tool2"}]

            result = self.client.get_tools(self.config.mcp_server_url)
            self.assertEqual(result, [{"name": "tool1"}, {"name": "tool2"}])
            mock_send_request.assert_called_once_with(self.config.mcp_server_url, "get_tools", {})
            mock_handle_response.assert_called_once_with({"status": "success", "data": [{"name": "tool1"}, {"name": "tool2"}]})