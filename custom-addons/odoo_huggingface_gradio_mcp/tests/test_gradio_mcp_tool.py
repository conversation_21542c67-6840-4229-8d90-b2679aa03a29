from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json

class TestGradioMcpTool(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpTool, self).setUp(*args, **kwargs)
        self.tool_model = self.env['gradio.mcp.tool']
        self.config = self.env['gradio.mcp.config'].create({
            'name': 'Test Config',
            'mcp_server_url': 'http://test-mcp-server.com',
        })

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient.get_tools')
    def test_import_tools_from_mcp_new_tools(self, mock_get_tools):
        mock_get_tools.return_value = [
            {'name': 'tool_a', 'description': 'Desc A', 'parameters': {'param_a': 'str'}},
            {'name': 'tool_b', 'description': 'Desc B', 'parameters': {'param_b': 'int'}},
        ]
        
        result = self.tool_model.import_tools_from_mcp()
        self.assertTrue(result)
        self.assertEqual(self.tool_model.search_count([]), 2)
        
        tool_a = self.tool_model.search([('name', '=', 'tool_a')])
        self.assertEqual(tool_a.description, 'Desc A')
        self.assertEqual(json.loads(tool_a.parameters), {'param_a': 'str'})

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient.get_tools')
    def test_import_tools_from_mcp_update_existing_tools(self, mock_get_tools):
        self.tool_model.create({
            'name': 'tool_a',
            'description': 'Old Desc A',
            'parameters': '{"old_param": "val"}',
        })
        
        mock_get_tools.return_value = [
            {'name': 'tool_a', 'description': 'New Desc A', 'parameters': {'new_param': 'bool'}},
        ]
        
        result = self.tool_model.import_tools_from_mcp()
        self.assertTrue(result)
        self.assertEqual(self.tool_model.search_count([]), 1) # Still one tool
        
        tool_a = self.tool_model.search([('name', '=', 'tool_a')])
        self.assertEqual(tool_a.description, 'New Desc A')
        self.assertEqual(json.loads(tool_a.parameters), {'new_param': 'bool'})

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient.get_tools')
    def test_import_tools_from_mcp_no_config(self, mock_get_tools):
        self.config.unlink() # Remove the config
        
        result = self.tool_model.import_tools_from_mcp()
        self.assertFalse(result)
        mock_get_tools.assert_not_called()

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient.get_tools')
    def test_import_tools_from_mcp_client_error(self, mock_get_tools):
        mock_get_tools.side_effect = Exception("Client error")
        
        result = self.tool_model.import_tools_from_mcp()
        self.assertFalse(result)
        self.assertEqual(self.tool_model.search_count([]), 0) # No tools created/updated
