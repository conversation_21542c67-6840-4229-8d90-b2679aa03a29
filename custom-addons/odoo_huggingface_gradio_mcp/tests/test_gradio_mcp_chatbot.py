from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
import json

class TestGradioMcpChatbot(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpChatbot, self).setUp(*args, **kwargs)
        self.chatbot_script_model = self.env['chatbot.script']
        self.pricelist_model = self.env['product.pricelist']

        self.test_pricelist = self.pricelist_model.create({'name': 'Test Pricelist'})
        self.test_chatbot = self.chatbot_script_model.create({
            'title': 'Test Chatbot',
            'enable_sales_orders': False,
        })

    def test_create_chatbot_script(self):
        self.assertFalse(self.test_chatbot.enable_sales_orders)
        self.assertIsNone(self.test_chatbot.default_pricelist_id)

    def test_create_chatbot_script_with_sales_enabled(self):
        chatbot = self.chatbot_script_model.create({
            'title': 'Sales Chatbot',
            'enable_sales_orders': True,
            'default_pricelist_id': self.test_pricelist.id,
        })
        self.assertTrue(chatbot.enable_sales_orders)
        self.assertEqual(chatbot.default_pricelist_id, self.test_pricelist)

    @patch('odoo.addons.im_livechat.models.chatbot_script.ChatbotScriptStep.create')
    def test_create_sales_order_steps(self, mock_create_step):
        self.test_chatbot.enable_sales_orders = True
        self.test_chatbot._create_sales_order_steps()
        
        # This is a basic check. In a real scenario, you'd check the arguments passed to create
        # and the number of calls based on your _create_sales_order_steps implementation.
        # For now, since the method is a placeholder, we just check if it was called.
        mock_create_step.assert_called()
