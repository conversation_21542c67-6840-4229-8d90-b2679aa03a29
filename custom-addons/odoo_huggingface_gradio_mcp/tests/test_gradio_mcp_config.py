from odoo.tests.common import TransactionCase
from unittest.mock import patch, MagicMock
from datetime import datetime

class TestGradioMcpConfig(TransactionCase):

    def setUp(self, *args, **kwargs):
        super(TestGradioMcpConfig, self).setUp(*args, **kwargs)
        self.config_model = self.env['gradio.mcp.config']
        self.config = self.config_model.create({
            'name': 'Test Config',
            'mcp_server_url': 'http://test-mcp-server.com',
        })

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient._initialize_connection')
    def test_test_connection_success(self, mock_initialize_connection):
        mock_initialize_connection.return_value = True
        
        result = self.config.test_connection()
        self.assertEqual(self.config.connection_status, 'connected')
        self.assertIsNotNone(self.config.last_connection_test)
        self.assertEqual(result['params']['message'], 'Connection test successful!')
        self.assertEqual(result['params']['type'], 'success')
        mock_initialize_connection.assert_called_once_with(self.config.mcp_server_url)

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient._initialize_connection')
    def test_test_connection_failure(self, mock_initialize_connection):
        mock_initialize_connection.return_value = False
        
        result = self.config.test_connection()
        self.assertEqual(self.config.connection_status, 'error')
        self.assertIsNotNone(self.config.last_connection_test)
        self.assertEqual(result['params']['message'], 'Connection test failed!')
        self.assertEqual(result['params']['type'], 'danger')
        mock_initialize_connection.assert_called_once_with(self.config.mcp_server_url)

    @patch('odoo.addons.odoo_huggingface_gradio_mcp.models.gradio_mcp_client.GradioMcpClient._initialize_connection')
    def test_check_connection_health_cron(self, mock_initialize_connection):
        # Create another config for cron to check multiple
        self.config_model.create({
            'name': 'Another Config',
            'mcp_server_url': 'http://another-mcp-server.com',
        })
        
        mock_initialize_connection.side_effect = [True, False] # First success, second failure
        
        self.config_model._check_connection_health()
        
        # Check first config
        self.assertEqual(self.config.connection_status, 'connected')
        self.assertIsNotNone(self.config.last_connection_test)

        # Check second config
        another_config = self.config_model.search([('name', '=', 'Another Config')])
        self.assertEqual(another_config.connection_status, 'error')
        self.assertIsNotNone(another_config.last_connection_test)
        
        self.assertEqual(mock_initialize_connection.call_count, 2)
