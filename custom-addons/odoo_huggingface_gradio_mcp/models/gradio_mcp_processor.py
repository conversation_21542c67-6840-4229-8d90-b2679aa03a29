from odoo import models, fields, api
import logging
import json

_logger = logging.getLogger(__name__)

class GradioMcpProcessor(models.AbstractModel):
    _name = 'gradio.mcp.processor'
    _description = 'Gradio MCP Tool Processor'

    name = fields.Char(string='Process Name', required=True)
    tool_id = fields.Many2one('gradio.mcp.tool', string='MCP Tool', required=True)
    input_parameters = fields.Text(string='Input Parameters (JSON)', help="JSON input for the MCP tool")
    output_result = fields.Text(string='Output Result (JSON)', readonly=True)
    execution_status = fields.Selection([
        ('pending', 'Pending'),
        ('in_progress', 'In Progress'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ], string='Status', default='pending', readonly=True)
    execution_log = fields.Text(string='Execution Log', readonly=True)
    
    def execute_mcp_tool(self):
        self.ensure_one()
        self.execution_status = 'in_progress'
        self.execution_log = ''
        self.output_result = ''
        
        config = self.env['gradio.mcp.config'].search([], limit=1)
        if not config or not config.mcp_server_url:
            self.execution_status = 'failed'
            self.execution_log = "Error: No Gradio MCP configuration found or server URL is not set."
            _logger.error(self.execution_log)
            return False

        client = self.env['gradio.mcp.client']
        try:
            params = {}
            if self.input_parameters:
                try:
                    params = json.loads(self.input_parameters)
                except json.JSONDecodeError:
                    self.execution_status = 'failed'
                    self.execution_log = "Error: Invalid JSON in Input Parameters."
                    _logger.error(self.execution_log)
                    return False

            _logger.info(f"Executing MCP tool '{self.tool_id.name}' with parameters: {params}")
            result = client.execute_tool(config.mcp_server_url, self.tool_id.name, params)
            
            self.output_result = json.dumps(result, indent=4)
            self.execution_status = 'completed'
            self.execution_log = "Tool executed successfully."
            _logger.info(f"Tool '{self.tool_id.name}' completed. Result: {self.output_result}")
            return True
        except Exception as e:
            self.execution_status = 'failed'
            self.execution_log = f"Tool execution failed: {e}"
            _logger.error(f"Tool '{self.tool_id.name}' failed: {e}")
            return False
        finally:
            self.env.cr.commit() # Commit changes to update status and logs
