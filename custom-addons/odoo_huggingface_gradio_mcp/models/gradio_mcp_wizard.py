from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class GradioMcpWizard(models.TransientModel):
    _name = 'gradio.mcp.wizard'
    _description = 'Gradio MCP Configuration Wizard'

    mcp_server_url = fields.Char(string='MCP Server URL', required=True)
    api_key = fields.Char(string='API Key')
    connection_status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connected', 'Connected'),
        ('error', 'Error'),
    ], string='Connection Status', default='disconnected', readonly=True)

    @api.model
    def default_get(self, fields):
        res = super(GradioMcpWizard, self).default_get(fields)
        config = self.env['gradio.mcp.config'].search([], limit=1)
        if config:
            res['mcp_server_url'] = config.mcp_server_url
            res['api_key'] = config.api_key
            res['connection_status'] = config.connection_status
        return res

    def action_test_connection(self):
        self.ensure_one()
        client = self.env['gradio.mcp.client']
        try:
            if client._initialize_connection(self.mcp_server_url):
                self.connection_status = 'connected'
            else:
                self.connection_status = 'error'
        except Exception as e:
            _logger.error(f"Error testing connection to MCP server: {e}")
            self.connection_status = 'error'
        return {
            'type': 'ir.actions.act_window',
            'res_model': 'gradio.mcp.wizard',
            'view_mode': 'form',
            'res_id': self.id,
            'target': 'new',
            'context': self.env.context,
        }

    def action_save_configuration(self):
        self.ensure_one()
        try:
            config = self.env['gradio.mcp.config'].search([], limit=1)
            if config:
                config.write({
                    'mcp_server_url': self.mcp_server_url,
                    'api_key': self.api_key,
                })
            else:
                self.env['gradio.mcp.config'].create({
                    'name': 'Default Gradio MCP Configuration',
                    'mcp_server_url': self.mcp_server_url,
                    'api_key': self.api_key,
                })
            return {'type': 'ir.actions.act_window_close'}
        except Exception as e:
            _logger.error(f"Error saving MCP configuration: {e}")
            # Optionally, display a notification to the user
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'message': f'Failed to save configuration: {e}',
                    'type': 'danger',
                    'sticky': True,
                }
            }