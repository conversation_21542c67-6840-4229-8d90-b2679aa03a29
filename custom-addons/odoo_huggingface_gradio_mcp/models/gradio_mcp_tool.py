from odoo import models, fields, api
import json
import logging

_logger = logging.getLogger(__name__)

class GradioMcpTool(models.Model):
    _name = 'gradio.mcp.tool'
    _description = 'Gradio MCP Tool Definition'

    name = fields.Char(string='Tool Name', required=True, index=True)
    description = fields.Text(string='Description')
    parameters = fields.Text(string='Parameters (JSON)', help="JSON schema of the tool's parameters")
    
    _sql_constraints = [
        ('name_uniq', 'unique (name)', 'Tool name must be unique!'),
    ]

    def import_tools_from_mcp(self):
        """
        Imports tools from the configured Gradio MCP server.
        This method will be called from a button in the UI or a cron job.
        """
        config = self.env['gradio.mcp.config'].search([], limit=1)
        if not config or not config.mcp_server_url:
            _logger.warning("No Gradio MCP configuration found or server URL is not set.")
            return False

        client = self.env['gradio.mcp.client']
        try:
            tools_data = client.get_tools(config.mcp_server_url)
            if not tools_data:
                _logger.info("No tools found on the MCP server.")
                return False

            # Optimization: Fetch all existing tool names once
            existing_tools = self.search([])
            existing_tool_map = {tool.name: tool for tool in existing_tools}

            for tool_info in tools_data:
                tool_name = tool_info.get('name')
                tool_description = tool_info.get('description')
                tool_parameters = json.dumps(tool_info.get('parameters', {}), indent=4)

                existing_tool = existing_tool_map.get(tool_name) # Check against the map
                if existing_tool:
                    existing_tool.write({
                        'description': tool_description,
                        'parameters': tool_parameters,
                    })
                    _logger.info(f"Updated existing MCP tool: {tool_name}")
                else:
                    self.create({
                        'name': tool_name,
                        'description': tool_description,
                        'parameters': tool_parameters,
                    })
                    _logger.info(f"Created new MCP tool: {tool_name}")
            return True
        except Exception as e:
            _logger.error(f"Failed to import tools from MCP server: {e}")
            return False