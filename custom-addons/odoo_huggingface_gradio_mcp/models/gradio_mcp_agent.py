from odoo import models, fields, api
import logging
import json
import re

_logger = logging.getLogger(__name__)

class GradioMcpAgent(models.AbstractModel):
    _name = 'gradio.mcp.agent'
    _description = 'Gradio MCP AI Agent'

    conversation_history = fields.Text(string='Conversation History', default='[]') # Stored as JSON string
    current_state = fields.Char(string='Current State', default='initial')
    # New fields to store temporary order data for confirmation
    temp_order_data = fields.Text(string='Temporary Order Data', default='{}') # Stored as JSON string

    def _initialize_ai_agent(self, llm_provider):
        # TODO: Implement logic to initialize AI agent with LLM provider
        _logger.info(f"Initializing AI agent with LLM provider: {llm_provider}")
        pass

    def _receive_user_input(self, user_input):
        # TODO: Implement logic to receive user input
        _logger.info(f"Received user input: {user_input}")
        pass

    def _call_mcp_tools(self, tool_name, params):
        # TODO: Implement logic to call MCP tools based on input
        _logger.info(f"Calling MCP tool: {tool_name} with params: {params}")
        client = self.env['gradio.mcp.client']
        # Assuming a default config exists for now
        config = self.env['gradio.mcp.config'].search([], limit=1)
        if not config:
            _logger.error("No Gradio MCP configuration found.")
            return False
        return client.execute_tool(config.mcp_server_url, tool_name, params)

    def _generate_response(self, agent_output):
        # TODO: Implement logic to generate responses
        _logger.info(f"Generating response from agent output: {agent_output}")
        pass

    def process_message(self, user_input, llm_provider='default'):
        self.ensure_one() # Ensure this method is called on a single record

        # Load conversation history
        history = json.loads(self.conversation_history)
        history.append({'role': 'user', 'content': user_input})
        
        response_content = ""
        temp_order = json.loads(self.temp_order_data)

        try:
            if self.current_state == 'initial':
                # Session Initialization
                response_content = "Hello! I'm here to help you create your order. May I have your name and email address?"
                self.current_state = 'awaiting_customer_id'
            elif self.current_state == 'awaiting_customer_id':
                # Customer Identification
                customer_name_match = re.search(r'Name:\s*(.*?)(?:,\s*Email:|$)', user_input, re.IGNORECASE)
                customer_email_match = re.search(r'Email:\s*(\S+)', user_input, re.IGNORECASE)
                
                customer_name = customer_name_match.group(1).strip() if customer_name_match else None
                customer_email = customer_email_match.group(1).strip() if customer_email_match else None

                if customer_name or customer_email:
                    customer_info = None
                    if customer_email:
                        customer_info = self._call_mcp_tools('search_partner', {'email': customer_email})
                    elif customer_name:
                        customer_info = self._call_mcp_tools('search_partner', {'name': customer_name})
                    
                    if customer_info:
                        temp_order['partner_id'] = customer_info.get('id')
                        temp_order['partner_name'] = customer_info.get('name')
                        self.temp_order_data = json.dumps(temp_order)
                        self.current_state = 'gathering_info'
                        response_content = f"Thank you {customer_info.get('name')}! I found your account. What products are you interested in today?"
                    else:
                        response_content = "I couldn't find an account with that information. Could you please provide a different email or name, or would you like to proceed as a new customer?"
                else:
                    response_content = "Please provide your name and email address so I can identify you."

            elif self.current_state == 'gathering_info':
                # Product Selection and Order Configuration
                product_matches = re.findall(r'(\d+)\s+([a-zA-Z0-9\s]+)', user_input)
                order_lines = []
                
                for quantity_str, product_name_raw in product_matches:
                    quantity = int(quantity_str)
                    product_name = product_name_raw.strip()

                    product_info = self._call_mcp_tools('search_product', {'name': product_name})
                    
                    if product_info and product_info.get('id'):
                        product_id = product_info['id']
                        pricing_info = self._call_mcp_tools('get_product_pricing', {'product_id': product_id})
                        price = pricing_info.get('price', 0.0) if pricing_info else 0.0
                        order_lines.append({
                            'product_id': product_id,
                            'product_name': product_info.get('name'),
                            'quantity': quantity,
                            'price_unit': price,
                        })
                    else:
                        response_content = f"Sorry, I couldn't find {product_name}. Please specify a valid product."
                        break # Exit if any product is not found
                
                if order_lines and not response_content: # If products found and no error message yet
                    temp_order['order_lines'] = order_lines
                    self.temp_order_data = json.dumps(temp_order)
                    self.current_state = 'confirming_order'
                    
                    order_summary = ", ".join([f"{line['quantity']} {line['product_name']}(s) at ${line['price_unit']} each" for line in order_lines])
                    response_content = f"I'm about to create an order for: {order_summary}. Is this correct? (Yes/No)"
                elif not response_content: # No products matched and no error message
                    response_content = "I'm sorry, I didn't understand. Could you please specify the product and quantity?"

            elif self.current_state == 'confirming_order':
                if "yes" in user_input.lower():
                    try:
                        params = {
                            'partner_id': temp_order['partner_id'],
                            'order_lines': [{'product_id': line['product_id'], 'product_uom_qty': line['quantity'], 'price_unit': line['price_unit']} for line in temp_order['order_lines']]
                        }
                        tool_result = self._call_mcp_tools('create_order_generic', params)
                        response_content = f"Order created successfully! Result: {tool_result}. You'll receive a confirmation email shortly. Is there anything else?"
                        self.current_state = 'completed'
                        self.temp_order_data = '{}' # Clear temporary data
                    except Exception as e:
                        _logger.error(f"Error during sales order creation: {e}")
                        response_content = "I apologize, but I encountered an error while creating the sales order. Please try again later."
                elif "no" in user_input.lower():
                    response_content = "Okay, I've cancelled the order. Is there anything else I can help you with?"
                    self.current_state = 'initial'
                    self.temp_order_data = '{}' # Clear temporary data
                else:
                    response_content = "Please confirm with 'Yes' or 'No'."
            elif self.current_state == 'search_product':
                try:
                    product_name = user_input
                    product_info = self._call_mcp_tools('search_product', {'name': product_name})
                    if product_info:
                        response_content = f"Found product: {product_info.get('name')} (ID: {product_info.get('id')}, Price: ${product_info.get('price')})"
                    else:
                        response_content = f"Could not find any product matching '{product_name}'."
                except Exception as e:
                    _logger.error(f"Error during product search: {e}")
                    response_content = "I apologize, but I encountered an error while searching for the product. Please try again later."
                self.current_state = 'initial'
            elif self.current_state == 'find_customer':
                try:
                    customer_query = user_input
                    customer_info = self._call_mcp_tools('search_partner', {'query': customer_query})
                    if customer_info:
                        response_content = f"Found customer: {customer_info.get('name')} (Email: {customer_info.get('email')}, Phone: {customer_info.get('phone')})"
                    else:
                        response_content = f"Could not find any customer matching '{customer_query}'."
                except Exception as e:
                    _logger.error(f"Error during customer search: {e}")
                    response_content = "I apologize, but I encountered an error while searching for the customer. Please try again later."
                self.current_state = 'initial'
            elif self.current_state == 'completed':
                response_content = "Is there anything else I can help you with?"
                self.current_state = 'initial'

        except json.JSONDecodeError as e:
            _logger.error(f"Corrupted conversation history or temporary order data: {e}")
            response_content = "I'm sorry, I encountered an internal error. My memory might be corrupted. Please start a new conversation."
            self.conversation_history = '[]'
            self.temp_order_data = '{}'
            self.current_state = 'initial'
        except Exception as e:
            _logger.critical(f"An unhandled critical error occurred in GradioMcpAgent: {e}")
            response_content = "I'm sorry, I encountered a critical internal error. Please contact support."
            # Optionally, trigger an automated alert here
            # self.env['mail.activity'].create(...) or send email

        history.append({'role': 'agent', 'content': response_content})
        self.conversation_history = json.dumps(history)
        
        self.write({'conversation_history': self.conversation_history, 'current_state': self.current_state, 'temp_order_data': self.temp_order_data})

        return response_content
