import requests
import json
import logging
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

from odoo import models, fields, api

_logger = logging.getLogger(__name__)

class GradioMcpClient(models.AbstractModel):
    _name = 'gradio.mcp.client'
    _description = 'Gradio MCP Client'

    def _initialize_connection(self, server_url):
        try:
            # Attempt to connect to the server
            response = requests.get(server_url, timeout=5)
            response.raise_for_status()  # Raise an HTTPError for bad responses (4xx or 5xx)
            self.env.cr.commit() # Commit the transaction to ensure the connection is established
            _logger.info(f"Successfully connected to Gradio MCP server at {server_url}")
            return True
        except requests.exceptions.RequestException as e:
            # Handle connection errors (e.g., network issues, server not reachable)
            self.env.cr.rollback() # Rollback the transaction on error
            _logger.error(f"Failed to connect to Gradio MCP server at {server_url}: {e}")
            return False

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10),
           retry=retry_if_exception_type(requests.exceptions.RequestException))
    def _send_request(self, server_url, endpoint, payload):
        headers = {'Content-Type': 'application/json'}
        try:
            response = requests.post(f"{server_url}/{endpoint}", headers=headers, data=json.dumps(payload), timeout=30)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            _logger.error(f"Error sending request to MCP server: {e}")
            raise

    def _handle_response(self, response):
        if response.get('status') == 'success':
            return response.get('data')
        else:
            error_message = response.get('message', 'Unknown error from MCP server')
            _logger.error(f"MCP server returned an error: {error_message}")
            raise Exception(f"MCP Server Error: {error_message}")

    def _handle_error(self, error):
        _logger.error(f"An error occurred in MCP client: {error}")
        raise error

    def execute_tool(self, server_url, tool_name, params):
        try:
            payload = {
                'tool_name': tool_name,
                'params': params
            }
            response = self._send_request(server_url, "execute_tool", payload)
            return self._handle_response(response)
        except Exception as e:
            self._handle_error(e)

    def get_tools(self, server_url):
        try:
            # The /get_tools endpoint typically returns a list of tool metadata
            response = self._send_request(server_url, "get_tools", {})
            tools_data = self._handle_response(response)
            _logger.info(f"Discovered MCP tools: {tools_data}")
            return tools_data
        except Exception as e:
            self._handle_error(e)
