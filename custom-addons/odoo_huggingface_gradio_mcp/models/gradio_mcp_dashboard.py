from odoo import models, fields, api
import logging
import json

_logger = logging.getLogger(__name__)

class GradioMcpDashboard(models.TransientModel):
    _name = 'gradio.mcp.dashboard'
    _description = 'Gradio MCP Dashboard'

    total_conversations = fields.Integer(string='Total Conversations', compute='_compute_dashboard_data')
    active_conversations = fields.Integer(string='Active Conversations', compute='_compute_dashboard_data')
    total_tool_calls = fields.Integer(string='Total Tool Calls', compute='_compute_dashboard_data')
    successful_tool_calls = fields.Integer(string='Successful Tool Calls', compute='_compute_dashboard_data')
    failed_tool_calls = fields.Integer(string='Failed Tool Calls', compute='_compute_dashboard_data')
    latest_logs = fields.Text(string='Latest Logs', compute='_compute_dashboard_data')

    @api.depends('id') # Dummy dependency to trigger computation
    def _compute_dashboard_data(self):
        for rec in self:
            # Compute total conversations and active conversations
            conversation_counts = self.env['gradio.mcp.agent'].read_group(
                domain=[],
                fields=['current_state'],
                groupby=['current_state'],
                lazy=False
            )
            total_conversations = sum(group['current_state_count'] for group in conversation_counts)
            active_conversations = sum(group['current_state_count'] for group in conversation_counts if group['current_state'] not in ['initial', 'completed'])

            rec.total_conversations = total_conversations
            rec.active_conversations = active_conversations

            # Compute tool call statistics
            processor_counts = self.env['gradio.mcp.processor'].read_group(
                domain=[],
                fields=['execution_status'],
                groupby=['execution_status'],
                lazy=False
            )
            total_calls = sum(group['execution_status_count'] for group in processor_counts)
            successful_calls = next((group['execution_status_count'] for group in processor_counts if group['execution_status'] == 'completed'), 0)
            failed_calls = next((group['execution_status_count'] for group in processor_counts if group['execution_status'] == 'failed'), 0)
            
            rec.total_tool_calls = total_calls
            rec.successful_tool_calls = successful_calls
            rec.failed_tool_calls = failed_calls

            # Fetch latest logs (e.g., from gradio.mcp.processor)
            latest_processor_logs = self.env['gradio.mcp.processor'].search([], order='create_date desc', limit=10)
            logs_list = []
            for log in latest_processor_logs:
                logs_list.append(f"[{log.create_date}] {log.name} - Status: {log.execution_status} - Log: {log.execution_log or 'N/A'}")
            rec.latest_logs = "\n".join(logs_list) if logs_list else "No recent logs."