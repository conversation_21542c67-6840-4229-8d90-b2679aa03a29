from odoo import models, fields, api
import logging

_logger = logging.getLogger(__name__)

class GradioMcpConfig(models.Model):
    _name = 'gradio.mcp.config'
    _description = 'Gradio MCP Configuration'

    name = fields.Char(string='Configuration Name', required=True)
    mcp_server_url = fields.Char(string='MCP Server URL', required=True,
                                 default='https://vpcsinfo-gradio-mcp-odoo.hf.space/gradio_api/mcp')
    api_key = fields.Char(string='API Key', groups='base.group_system')
    connection_status = fields.Selection([
        ('disconnected', 'Disconnected'),
        ('connecting', 'Connecting'),
        ('connected', 'Connected'),
        ('error', 'Error'),
    ], string='Connection Status', default='disconnected', readonly=True)
    last_connection_test = fields.Datetime(string='Last Connection Test', readonly=True)
    
    def test_connection(self):
        # This method will be called from the UI to test the connection
        # It will use the GradioMcpClient to test the connection
        self.ensure_one()
        client = self.env['gradio.mcp.client']
        self.connection_status = 'connecting'
        self.last_connection_test = fields.Datetime.now()
        if client._initialize_connection(self.mcp_server_url):
            self.connection_status = 'connected'
        else:
            self.connection_status = 'error'
        self.env.cr.commit() # Commit the transaction to update the status in the UI
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'message': 'Connection test successful!' if self.connection_status == 'connected' else 'Connection test failed!',
                'type': 'success' if self.connection_status == 'connected' else 'danger',
                'sticky': False,
            }
        }

    @api.model
    def _check_connection_health(self):
        """Cron job method to periodically check connection health"""
        _logger.info("Running Gradio MCP connection health check...")
        configs = self.search([])
        for config in configs:
            _logger.info(f"Checking connection for config: {config.name}")
            client = self.env['gradio.mcp.client']
            if client._initialize_connection(config.mcp_server_url):
                config.connection_status = 'connected'
                _logger.info(f"Connection to {config.mcp_server_url} is healthy.")
            else:
                config.connection_status = 'error'
                _logger.warning(f"Connection to {config.mcp_server_url} failed.")
            config.last_connection_test = fields.Datetime.now()
        self.env.cr.commit()