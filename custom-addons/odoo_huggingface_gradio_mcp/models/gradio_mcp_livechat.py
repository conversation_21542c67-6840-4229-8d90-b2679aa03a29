from odoo import models, fields, api
import logging
from odoo.tools import html2plaintext

_logger = logging.getLogger(__name__)

class DiscussChannel(models.Model):
    _inherit = 'discuss.channel'
    
    # AI Agent Integration
    ai_agent_active = fields.<PERSON><PERSON><PERSON>('AI Agent Active', default=False)
    ai_agent_context = fields.Text('AI Agent Context')
    mcp_session_id = fields.Char('MCP Session ID')
    sales_order_draft_id = fields.Many2one('sale.order', 'Draft Sales Order')
    
    # AI Agent Methods
    def _activate_ai_agent(self):
        """Activate AI agent for this channel"""
        self.ensure_one()
        _logger.info(f"Activating AI agent for channel {self.id}")
        self.ai_agent_active = True
        # TODO: Add logic to initialize AI agent session if needed
        
    def _process_ai_message(self, message):
        """Process message through AI agent"""
        self.ensure_one()
        _logger.info(f"Processing AI message for channel {self.id}: {message}")
        ai_agent = self.env['gradio.mcp.agent']
        try:
            response = ai_agent.process_message(message)
            return response
        except Exception as e:
            _logger.error(f"Error processing AI message for channel {self.id}: {e}")
            # Send a user-friendly message back to the live chat
            return "I'm sorry, I'm having trouble processing your request right now. Please try again later or contact a human agent."
        
    def _create_sales_order_from_chat(self, order_data):
        """Create sales order from chat conversation"""
        self.ensure_one()
        _logger.info(f"Creating sales order from chat for channel {self.id} with data: {order_data}")
        SaleOrder = self.env['sale.order']
        try:
            order = SaleOrder.create({
                'partner_id': self.partner_id.id if self.partner_id else False,
                'state': 'draft',
                # Add other fields from order_data
            })
            self.sales_order_draft_id = order.id
            return order
        except Exception as e:
            _logger.error(f"Error creating sales order from chat for channel {self.id}: {e}")
            # Send a user-friendly message back to the live chat
            return "I'm sorry, I encountered an error while trying to create the sales order. Please try again later."

    @api.model
    def _message_post_after_hook(self, message, msg_vals):
        """Process messages through AI agent when active"""
        res = super(DiscussChannel, self)._message_post_after_hook(message, msg_vals)
        
        # Check if the message is from a live chat channel and AI agent is active
        if message.model == 'discuss.channel' and message.res_id and message.res_id == self.id and self.channel_type == 'livechat' and self.ai_agent_active:
            # Ensure the message is from a customer, not the AI agent itself
            if message.author_id != self.env.ref('base.partner_root'): # Assuming partner_root is the AI agent's partner
                self._process_customer_message(message)
        return res

    def _process_customer_message(self, message):
        """Process customer message and generate AI response"""
        self.ensure_one()
        customer_input = html2plaintext(message.body) # Extract message content
        
        # Get AI agent response
        ai_response = self._process_ai_message(customer_input)
        
        # Post AI response back to the chat
        if ai_response:
            self.message_post(body=ai_response, message_type='comment', subtype_xmlid='mail.mt_comment')
