#!/usr/bin/env python3
"""
Test Gradio MCP server using Gradio Client
"""

import asyncio
import json
from gradio_client import Client

class GradioOdooTester:
    def __init__(self, server_url: str = "http://127.0.0.1:7861"):
        self.server_url = server_url
        self.client = None
    
    def connect_client(self):
        """Connect to Gradio server"""
        try:
            self.client = Client(self.server_url)
            print("✅ Connected to Gradio server")
            return True
        except Exception as e:
            print(f"❌ Failed to connect to Gradio server: {e}")
            return False
    
    def test_connection(self):
        """Test Odoo connection"""
        print("🔌 Testing Odoo Connection...")
        try:
            result = self.client.predict(api_name="/connect_odoo")
            print(f"✅ Connection result: {result}")
            return True
        except Exception as e:
            print(f"❌ Connection failed: {e}")
            return False
    
    def test_model_fields(self, model_name: str = "res.partner"):
        """Test model field inspection"""
        print(f"📊 Testing model fields for {model_name}...")
        try:
            result = self.client.predict(model_name, api_name="/get_model_fields")
            if isinstance(result, dict) and "error" not in result:
                field_count = len(result)
                print(f"✅ Found {field_count} fields for {model_name}")
                return True
            else:
                print(f"❌ Model fields test failed: {result}")
                return False
        except Exception as e:
            print(f"❌ Model fields test failed: {e}")
            return False
    
    def test_search_read(self, model_name: str = "res.partner", domain: str = "[]", limit: int = 5):
        """Test search and read operations"""
        print(f"🔍 Testing search_read for {model_name}...")
        try:
            result = self.client.predict(model_name, domain, limit, api_name="/search_read")
            if isinstance(result, list):
                print(f"✅ Found {len(result)} records")
                return result
            else:
                print(f"❌ Search read failed: {result}")
                return []
        except Exception as e:
            print(f"❌ Search read failed: {e}")
            return []
    
    def test_order_creation(self):
        """Test sales order creation"""
        print("🛒 Testing sales order creation...")
        
        # First, find a customer
        customers = self.test_search_read("res.partner", "[['is_company', '=', True]]", 1)
        if not customers:
            print("❌ No customers found for order creation")
            return False
        
        customer_id = customers[0]['id']
        print(f"📋 Using customer ID: {customer_id}")
        
        # Find products
        products = self.test_search_read("product.product", "[['sale_ok', '=', True]]", 2)
        
        if not products:
            print("❌ No products found for order creation")
            return False
        
        # Create order lines with customer
        order_lines = []
        for i, product in enumerate(products[:2]):
            if isinstance(product, dict) and 'id' in product:
                order_lines.append({
                    "product_id": product["id"],
                    "product_uom_qty": i + 1,
                    "price_unit": 10.0 + (i * 5)
                })
        
        if not order_lines:
            print("❌ Could not create order lines")
            return False
        
        # Create the order with customer
        try:
            result = self.client.predict(
                json.dumps(order_lines),
                "Test order from Gradio client",
                api_name="/create_order"
            )
            
            if isinstance(result, dict) and "error" in result:
                print(f"❌ Order creation failed: {result['error']}")
                return False
            else:
                print(f"✅ Order created successfully: {result}")
                return True
        except Exception as e:
            print(f"❌ Order creation failed: {e}")
            return False
    
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🚀 Starting Odoo MCP Server Tests...\n")
        
        # Connect to client
        if not self.connect_client():
            return
        
        results = {
            "connection": False,
            "model_fields": False,
            "search_read": False,
            "order_creation": False
        }
        
        # Test 1: Connection
        results["connection"] = self.test_connection()
        print()
        
        if not results["connection"]:
            print("❌ Cannot proceed without connection")
            return results
        
        # Test 2: Model fields
        results["model_fields"] = self.test_model_fields()
        print()
        
        # Test 3: Search and read
        search_results = self.test_search_read()
        results["search_read"] = len(search_results) > 0
        print()
        
        # Test 4: Order creation
        results["order_creation"] = self.test_order_creation()
        print()
        
        # Summary
        passed = sum(results.values())
        total = len(results)
        print(f"📋 Test Summary: {passed}/{total} tests passed")
        
        for test_name, passed in results.items():
            status = "✅ PASS" if passed else "❌ FAIL"
            print(f"  {test_name}: {status}")
        
        return results

def main():
    """Main test runner"""
    tester = GradioOdooTester()
    tester.run_all_tests()

if __name__ == "__main__":
    main()