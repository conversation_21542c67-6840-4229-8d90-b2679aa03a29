<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="gradio_mcp_wizard_form_view" model="ir.ui.view">
        <field name="name">gradio.mcp.wizard.form.view</field>
        <field name="model">gradio.mcp.wizard</field>
        <field name="arch" type="xml">
            <form string="Gradio MCP Configuration Wizard">
                <sheet>
                    <group>
                        <field name="mcp_server_url"/>
                        <field name="api_key" password="True"/>
                        <field name="connection_status"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_test_connection" type="object" string="Test Connection" class="oe_highlight"/>
                    <button name="action_save_configuration" type="object" string="Save Configuration" class="oe_highlight"/>
                    <button string="Cancel" class="oe_link" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <act_window id="action_gradio_mcp_wizard"
        name="Gradio MCP Configuration"
        res_model="gradio.mcp.wizard"
        view_mode="form"
        target="new"
        />

    <menuitem id="gradio_mcp_wizard_menu"
              name="Setup Wizard"
              parent="gradio_mcp_menu_root"
              action="action_gradio_mcp_wizard"
              sequence="0"/>
</odoo>