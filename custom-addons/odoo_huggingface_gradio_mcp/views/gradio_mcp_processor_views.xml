<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="gradio_mcp_processor_view_tree" model="ir.ui.view">
        <field name="name">gradio.mcp.processor.view.tree</field>
        <field name="model">gradio.mcp.processor</field>
        <field name="arch" type="xml">
            <list string="MCP Tool Executions">
                <field name="name"/>
                <field name="tool_id"/>
                <field name="execution_status"/>
            </list>
        </field>
    </record>

    <record id="gradio_mcp_processor_view_form" model="ir.ui.view">
        <field name="name">gradio.mcp.processor.view.form</field>
        <field name="model">gradio.mcp.processor</field>
        <field name="arch" type="xml">
            <form string="MCP Tool Execution">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="tool_id"/>
                        <field name="input_parameters" widget="json_editor"/>
                        <field name="output_result" widget="json_editor"/>
                        <field name="execution_status"/>
                        <field name="execution_log"/>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="gradio_mcp_processor_action" model="ir.actions.act_window">
        <field name="name">MCP Tool Executions</field>
        <field name="res_model">gradio.mcp.processor</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="gradio_mcp_processor_menu"
              name="Tool Executions"
              parent="gradio_mcp_menu_root"
              action="gradio_mcp_processor_action"
              sequence="30"/>
</odoo>