<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="gradio_mcp_tool_view_tree" model="ir.ui.view">
        <field name="name">gradio.mcp.tool.view.tree</field>
        <field name="model">gradio.mcp.tool</field>
        <field name="arch" type="xml">
            <list string="Gradio MCP Tools">
                <field name="name"/>
                <field name="description"/>
            </list>
        </field>
    </record>

    <record id="gradio_mcp_tool_view_form" model="ir.ui.view">
        <field name="name">gradio.mcp.tool.view.form</field>
        <field name="model">gradio.mcp.tool</field>
        <field name="arch" type="xml">
            <form string="Gradio MCP Tool">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="description"/>
                        <field name="parameters" widget="json_editor"/>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="gradio_mcp_tool_action" model="ir.actions.act_window">
        <field name="name">Gradio MCP Tools</field>
        <field name="res_model">gradio.mcp.tool</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="gradio_mcp_tools_menu"
              name="Tools"
              parent="gradio_mcp_menu_root"
              action="gradio_mcp_tool_action"
              sequence="20"/>
</odoo>