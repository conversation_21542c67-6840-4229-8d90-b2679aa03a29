<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="gradio_mcp_dashboard_view_form" model="ir.ui.view">
        <field name="name">gradio.mcp.dashboard.view.form</field>
        <field name="model">gradio.mcp.dashboard</field>
        <field name="arch" type="xml">
            <form string="Gradio MCP Dashboard" create="false" edit="false">
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button class="oe_stat_button" icon="fa-comments" type="action" name="%(gradio_mcp_agent_action)d">
                            <field name="total_conversations" widget="statinfo" string="Total Conversations"/>
                        </button>
                        <button class="oe_stat_button" icon="fa-commenting" type="action" name="%(gradio_mcp_agent_action)d">
                            <field name="active_conversations" widget="statinfo" string="Active Conversations"/>
                        </button>
                        <button class="oe_stat_button" icon="fa-cogs" type="action" name="%(gradio_mcp_processor_action)d">
                            <field name="total_tool_calls" widget="statinfo" string="Total Tool Calls"/>
                        </button>
                        <button class="oe_stat_button" icon="fa-check-circle" type="action" name="%(gradio_mcp_processor_action)d">
                            <field name="successful_tool_calls" widget="statinfo" string="Successful Calls"/>
                        </button>
                        <button class="oe_stat_button" icon="fa-times-circle" type="action" name="%(gradio_mcp_processor_action)d">
                            <field name="failed_tool_calls" widget="statinfo" string="Failed Calls"/>
                        </button>
                    </div>
                    <group>
                        <field name="latest_logs" nolabel="1" readonly="1" class="oe_read_only"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="gradio_mcp_dashboard_action" model="ir.actions.act_window">
        <field name="name">Gradio MCP Dashboard</field>
        <field name="res_model">gradio.mcp.dashboard</field>
        <field name="view_mode">form</field>
        <field name="target">main</field>
    </record>

    <menuitem id="gradio_mcp_dashboard_menu"
              name="Dashboard"
              parent="gradio_mcp_menu_root"
              action="gradio_mcp_dashboard_action"
              sequence="1"/>
</odoo>