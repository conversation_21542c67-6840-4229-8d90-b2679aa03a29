<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="gradio_mcp_config_view_form" model="ir.ui.view">
        <field name="name">gradio.mcp.config.view.form</field>
        <field name="model">gradio.mcp.config</field>
        <field name="arch" type="xml">
            <form string="Gradio MCP Configuration">
                <sheet>
                    <group>
                        <field name="name"/>
                        <field name="mcp_server_url"/>
                        <field name="api_key" password="True"/>
                        <field name="connection_status"/>
                        <field name="last_connection_test"/>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="gradio_mcp_config_view_tree" model="ir.ui.view">
        <field name="name">gradio.mcp.config.view.tree</field>
        <field name="model">gradio.mcp.config</field>
        <field name="arch" type="xml">
            <list string="Gradio MCP Configurations">
                <field name="name"/>
                <field name="mcp_server_url"/>
                <field name="connection_status"/>
            </list>
        </field>
    </record>

    <record id="gradio_mcp_config_action" model="ir.actions.act_window">
        <field name="name">Gradio MCP Configurations</field>
        <field name="res_model">gradio.mcp.config</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="gradio_mcp_menu_root"
              name="Gradio MCP"
              sequence="100"/>

    <menuitem id="gradio_mcp_config_menu"
              name="Configuration"
              parent="gradio_mcp_menu_root"
              action="gradio_mcp_config_action"
              sequence="10"/>
</odoo>