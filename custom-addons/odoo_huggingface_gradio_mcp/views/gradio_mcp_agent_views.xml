<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="gradio_mcp_agent_view_tree" model="ir.ui.view">
        <field name="name">gradio.mcp.agent.view.tree</field>
        <field name="model">gradio.mcp.agent</field>
        <field name="arch" type="xml">
            <list string="AI Agent Conversations">
                <field name="id"/>
                <field name="current_state"/>
            </list>
        </field>
    </record>

    <record id="gradio_mcp_agent_view_form" model="ir.ui.view">
        <field name="name">gradio.mcp.agent.view.form</field>
        <field name="model">gradio.mcp.agent</field>
        <field name="arch" type="xml">
            <form string="AI Agent Conversation">
                <sheet>
                    <group>
                        <field name="current_state"/>
                        <field name="conversation_history" widget="json_editor"/>
                        <field name="temp_order_data" widget="json_editor"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="gradio_mcp_agent_action" model="ir.actions.act_window">
        <field name="name">AI Agent Conversations</field>
        <field name="res_model">gradio.mcp.agent</field>
        <field name="view_mode">tree,form</field>
    </record>

    <menuitem id="gradio_mcp_agent_menu"
              name="Conversations"
              parent="gradio_mcp_menu_root"
              action="gradio_mcp_agent_action"
              sequence="40"/>
</odoo>