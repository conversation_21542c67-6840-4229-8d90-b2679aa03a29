<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Create a sequence for law agent chats -->
        <record id="law_agent_chat_sequence" model="ir.sequence">
            <field name="name">Law Agent Chat Sequence</field>
            <field name="code">law.agent.chat</field>
            <field name="prefix">CHAT/</field>
            <field name="padding">4</field>
        </record>

        <!-- Sample Law Agent Chats -->
        <record id="law_agent_chat_1" model="law.agent.chat">
            <field name="name">Property Rights Consultation</field>
            <field name="agent_type">general</field>
            <field name="use_web_search" eval="True"/>
            <field name="use_document_search" eval="True"/>
        </record>

        <record id="law_agent_chat_2" model="law.agent.chat">
            <field name="name">Gujarat Land Dispute</field>
            <field name="agent_type">gujarat</field>
            <field name="use_web_search" eval="True"/>
            <field name="use_document_search" eval="True"/>
        </record>

        <record id="law_agent_chat_3" model="law.agent.chat">
            <field name="name">Civil Contract Review</field>
            <field name="agent_type">civil</field>
            <field name="use_web_search" eval="True"/>
            <field name="use_document_search" eval="True"/>
        </record>

        <record id="law_agent_chat_4" model="law.agent.chat">
            <field name="name">Land Acquisition Query</field>
            <field name="agent_type">land</field>
            <field name="use_web_search" eval="True"/>
            <field name="use_document_search" eval="True"/>
        </record>

        <!-- Sample Law Agent Messages -->
        <record id="law_agent_message_1_1" model="law.agent.message">
            <field name="chat_id" ref="law_agent_chat_1"/>
            <field name="content">What are the property rights for ancestral property in India?</field>
            <field name="is_user" eval="True"/>
        </record>

        <record id="law_agent_message_1_2" model="law.agent.message">
            <field name="chat_id" ref="law_agent_chat_1"/>
            <field name="content">In India, ancestral property rights are governed by personal laws that vary based on religion. Under Hindu law, ancestral property is property inherited up to four generations of male lineage, and the right to such property accrues by birth. The Hindu Succession (Amendment) Act, 2005 grants equal rights to daughters in ancestral property. 

For Muslims, property rights are governed by Islamic law, where inheritance follows specific shares defined by the Quran. Christians and Parsis follow the Indian Succession Act, which has different provisions.

Key points about ancestral property in India:
1. Hindu Undivided Family (HUF) property is jointly owned by all members
2. Male and female heirs have equal rights since 2005
3. Property can be partitioned through mutual consent or court decree
4. Tax implications differ for ancestral vs. self-acquired property
5. State-specific laws may apply in addition to central laws

**Sources:**
1. Hindu Succession Act, 1956 (as amended in 2005)
2. Supreme Court judgment in Vineeta Sharma vs. Rakesh Sharma (2020)
3. Indian Succession Act, 1925</field>
            <field name="is_user" eval="False"/>
        </record>

        <record id="law_agent_message_2_1" model="law.agent.message">
            <field name="chat_id" ref="law_agent_chat_2"/>
            <field name="content">ગુજરાતમાં જમીન વિવાદ માટે કયા કાયદા લાગુ પડે છે?</field>
            <field name="is_user" eval="True"/>
        </record>

        <record id="law_agent_message_2_2" model="law.agent.message">
            <field name="chat_id" ref="law_agent_chat_2"/>
            <field name="content">ગુજરાતમાં જમીન વિવાદ માટે નીચેના કાયદાઓ લાગુ પડે છે:

1. ગુજરાત જમીન મહેસૂલ અધિનિયમ, 1879: આ કાયદો જમીન રેકોર્ડ, મહેસૂલ વસૂલાત અને જમીન સંબંધિત વિવાદોના નિરાકરણ માટેની પ્રક્રિયાઓ નિર્ધારિત કરે છે.

2. ગુજરાત ટેનન્સી એક્ટ: આ કાયદો ભાડૂઆત અને જમીન માલિક વચ્ચેના સંબંધોને નિયંત્રિત કરે છે.

3. ગુજરાત પંચાયત અધિનિયમ: ગ્રામીણ વિસ્તારોમાં જમીન વિવાદોના નિરાકરણ માટે પંચાયતોની ભૂમિકા નિર્ધારિત કરે છે.

4. ગુજરાત લેન્ડ ગ્રેબિંગ (પ્રોહિબિશન) એક્ટ, 2020: સરકારી જમીન પર અતિક્રમણને રોકવા માટેનો કાયદો.

5. ભારતીય રજિસ્ટ્રેશન એક્ટ, 1908: જમીન વ્યવહારોના રજિસ્ટ્રેશન માટેની પ્રક્રિયાઓ નિર્ધારિત કરે છે.

જમીન વિવાદોના નિરાકરણ માટે, તમે નીચેના પગલાં લઈ શકો છો:
- મામલતદાર/તાલુકા વિકાસ અધિકારી સમક્ષ ફરિયાદ નોંધાવી શકો છો
- જિલ્લા કલેક્ટર સમક્ષ અપીલ કરી શકો છો
- ગુજરાત રેવન્યુ ટ્રિબ્યુનલમાં કેસ દાખલ કરી શકો છો
- જરૂર પડે તો હાઈકોર્ટમાં જઈ શકો છો

**સ્ત્રોત:**
1. ગુજરાત જમીન મહેસૂલ અધિનિયમ, 1879
2. ગુજરાત લેન્ડ ગ્રેબિંગ (પ્રોહિબિશન) એક્ટ, 2020
3. ગુજરાત રેવન્યુ ટ્રિબ્યુનલ</field>
            <field name="is_user" eval="False"/>
        </record>

        <record id="law_agent_message_3_1" model="law.agent.message">
            <field name="chat_id" ref="law_agent_chat_3"/>
            <field name="content">What are the essential elements of a valid contract under Indian Contract Act?</field>
            <field name="is_user" eval="True"/>
        </record>

        <record id="law_agent_message_3_2" model="law.agent.message">
            <field name="chat_id" ref="law_agent_chat_3"/>
            <field name="content">Under the Indian Contract Act, 1872, a valid contract must contain the following essential elements:

1. **Offer and Acceptance**: There must be a lawful offer by one party and a lawful acceptance of that offer by the other party. The acceptance must be absolute and unqualified (Section 7).

2. **Intention to Create Legal Relationship**: Both parties must intend to create a legally binding relationship.

3. **Lawful Consideration**: Something of value must be exchanged between the parties. Consideration may be past, present, or future, but it must be lawful (Sections 2(d), 23, 25).

4. **Capacity of Parties (Competency)**: All parties must be legally competent to enter into a contract. This means they must:
   - Be of the age of majority (18 years)
   - Be of sound mind
   - Not be disqualified by any law (Sections 11, 12)

5. **Free Consent**: Consent must be free and not obtained by coercion, undue influence, fraud, misrepresentation, or mistake (Sections 13-22).

6. **Lawful Object**: The purpose or object of the agreement must be lawful (Section 23).

7. **Certainty and Possibility of Performance**: The terms of the agreement must be certain and not vague. The agreement must be capable of being performed (Sections 29, 56).

8. **Not Expressly Declared Void**: The agreement should not be one that the law declares void (Sections 24-30).

9. **Legal Formalities**: In some cases, specific legal formalities like writing, registration, etc., may be required.

If any of these elements is missing, the contract may be void, voidable, or unenforceable.

**Sources:**
1. Indian Contract Act, 1872
2. Mohiri Bibi v. Dharmodas Ghose (1903)
3. Balfour v. Balfour (1919)
4. Carlill v. Carbolic Smoke Ball Co. (1893)</field>
            <field name="is_user" eval="False"/>
        </record>

        <!-- Sample Case Analyses -->
        <record id="law_agent_case_analysis_1" model="law.agent.case.analysis">
            <field name="name">Property Inheritance Dispute</field>
            <field name="case_number">CIV/2025/1234</field>
            <field name="case_type">property</field>
            <field name="language">en</field>
            <field name="use_online_search" eval="True"/>
            <field name="case_text">Case involves a dispute between siblings over ancestral property. The father passed away in 2022 without a will, leaving behind a house and agricultural land. The eldest son claims exclusive right to the property based on traditional practices, while the two daughters claim equal share under the Hindu Succession (Amendment) Act, 2005. The property has been in the family for three generations. The eldest son has been managing the agricultural land for the past 10 years and claims that he has made significant improvements to it using his own funds. The sisters have been living in different cities and have not contributed to the maintenance of the property. The case was filed in the civil court in 2023.</field>
            <field name="state">draft</field>
        </record>

        <record id="law_agent_case_analysis_2" model="law.agent.case.analysis">
            <field name="name">Contract Breach Analysis</field>
            <field name="case_number">COM/2025/5678</field>
            <field name="case_type">civil</field>
            <field name="language">en</field>
            <field name="use_online_search" eval="True"/>
            <field name="case_text">A software development company entered into a contract with a client to develop a custom ERP system. The contract specified delivery in three phases over 12 months with payment milestones after each phase. After completing the first phase and receiving payment, the company delivered the second phase three months late. The client refused to pay for the second phase, citing the delay and claiming that the delivered modules did not meet the specifications outlined in the contract. The development company argues that the delay was caused by the client's repeated changes to requirements and slow response to queries. The client has now hired another company to complete the project and is suing for breach of contract, seeking damages for the amount paid for phase one, additional costs for hiring the new company, and business losses due to the delay. The contract includes a clause limiting liability to the value of the contract.</field>
            <field name="state">draft</field>
        </record>

        <!-- Sample Document Searches -->
        <record id="law_agent_document_search_1" model="law.agent.document.search">
            <field name="name">Property rights in Gujarat</field>
            <field name="language">en</field>
            <field name="max_results">5</field>
        </record>

        <record id="law_agent_document_search_2" model="law.agent.document.search">
            <field name="name">Civil Procedure Code limitation periods</field>
            <field name="language">en</field>
            <field name="max_results">5</field>
        </record>

        <!-- Sample Document Results -->
        <record id="law_agent_document_result_1_1" model="law.agent.document.result">
            <field name="search_id" ref="law_agent_document_search_1"/>
            <field name="document">Gujarat Land Revenue Code.pdf</field>
            <field name="page">42</field>
            <field name="score">0.89</field>
            <field name="content">Chapter VI - Rights in Land
Section 102. Rights of occupants:
(1) An occupant shall be entitled to the use and occupation of his land for the period, if any, to which his tenure is limited, or if the period is unlimited, or a survey settlement has been extended to the land, in perpetuity, conditionally on the payment of the amounts due on account of the land revenue for the same, according to the provisions of this Act, or of any rules made under this Act, or of any other law for the time being in force, and on the fulfillment of any other terms or conditions lawfully annexed to his tenure.
(2) The occupant shall be entitled to all trees naturally growing on the land.
(3) The occupant shall be entitled to all minerals in the land, except those minerals which are specified in the rules made by the State Government in this behalf to be reserved by the State Government.</field>
        </record>

        <record id="law_agent_document_result_1_2" model="law.agent.document.result">
            <field name="search_id" ref="law_agent_document_search_1"/>
            <field name="document">Gujarat Tenancy Act.pdf</field>
            <field name="page">15</field>
            <field name="score">0.78</field>
            <field name="content">Section 43. Rights of tenants in respect of dwelling houses:
(1) If a tenant has built or acquired a dwelling house on his landlord's land and has been residing therein, he shall not be evicted from such dwelling house (with the materials and the site thereof and the land immediately appurtenant thereto and necessary for its enjoyment) unless the landlord proves that he has provided the tenant with alternative accommodation.
(2) The alternative accommodation shall be adequate and suitable to the tenant's requirements, including those of his family and shall be in the same village or in any other place where the tenant agrees to go.</field>
        </record>

        <record id="law_agent_document_result_2_1" model="law.agent.document.result">
            <field name="search_id" ref="law_agent_document_search_2"/>
            <field name="document">Civil Procedure Code.pdf</field>
            <field name="page">156</field>
            <field name="score">0.92</field>
            <field name="content">Article 137. Period of limitation for applications for which no period of limitation is provided elsewhere in this division: Three years from when the right to apply accrues.

Article 138. For the execution of a decree granting a mandatory injunction: Three years from the date of the decree or where a date is fixed for performance, such date.

Article 136. For the execution of any decree (other than a decree granting a mandatory injunction) or order of any civil court: Twelve years from when the decree or order becomes enforceable.</field>
        </record>

        <!-- Server URL Configuration -->
        <record id="law_agent_server_url" model="ir.config_parameter">
            <field name="key">law_agent.server_url</field>
            <field name="value">http://host.docker.internal:8000</field>
        </record>

        <record id="law_agent_enable_web_search" model="ir.config_parameter">
            <field name="key">law_agent.enable_web_search</field>
            <field name="value">True</field>
        </record>

        <record id="law_agent_enable_document_search" model="ir.config_parameter">
            <field name="key">law_agent.enable_document_search</field>
            <field name="value">True</field>
        </record>

        <record id="law_agent_enable_opik_tracing" model="ir.config_parameter">
            <field name="key">law_agent.enable_opik_tracing</field>
            <field name="value">False</field>
        </record>
    </data>
</odoo>
