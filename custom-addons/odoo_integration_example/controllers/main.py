from odoo import http, _
from odoo.http import request
import json
import logging

_logger = logging.getLogger(__name__)

class LawAgentController(http.Controller):
    
    @http.route('/law_agent/chat', type='json', auth='user')
    def chat(self, question, agent_type='general', use_web_search=True, use_document_search=True, chat_id=None):
        """
        Chat with the law agent.
        
        This endpoint can be called from JavaScript to integrate the law agent
        into custom Odoo views and widgets.
        """
        try:
            # If chat_id is provided, use that chat
            if chat_id:
                chat = request.env['law.agent.chat'].browse(int(chat_id))
                if not chat.exists():
                    return {'error': _('Chat not found')}
            else:
                # Create a new chat
                chat = request.env['law.agent.chat'].create({
                    'name': question[:30] + '...' if len(question) > 30 else question,
                    'agent_type': agent_type,
                    'use_web_search': use_web_search,
                    'use_document_search': use_document_search,
                })
            
            # Ask the question
            response = chat.action_ask_question(question)
            
            return {
                'success': True,
                'chat_id': chat.id,
                'response': response
            }
        
        except Exception as e:
            _logger.exception("Error in law agent chat")
            return {
                'success': False,
                'error': str(e)
            }
    
    @http.route('/law_agent/analyze_case', type='json', auth='user')
    def analyze_case(self, case_data):
        """
        Analyze a legal case.
        
        This endpoint can be called from JavaScript to integrate case analysis
        into custom Odoo views and widgets.
        """
        try:
            # Create a new case analysis
            case = request.env['law.agent.case.analysis'].create({
                'name': case_data.get('case_title', 'Untitled Case'),
                'case_number': case_data.get('case_number', ''),
                'case_text': case_data.get('case_text', ''),
                'case_type': case_data.get('case_type', 'property'),
                'language': case_data.get('language', 'en'),
                'use_online_search': case_data.get('use_online_search', True),
            })
            
            # Analyze the case
            case.action_analyze()
            
            return {
                'success': True,
                'case_id': case.id,
                'background_analysis': case.background_analysis,
                'legal_issues': case.legal_issues,
                'evidence_analysis': case.evidence_analysis,
                'legal_principles': case.legal_principles,
                'judgment_prediction': case.judgment_prediction,
                'full_analysis': case.full_analysis
            }
        
        except Exception as e:
            _logger.exception("Error in law agent case analysis")
            return {
                'success': False,
                'error': str(e)
            }
    
    @http.route('/law_agent/search_documents', type='json', auth='user')
    def search_documents(self, query, language=None, max_results=10):
        """
        Search for documents using the law agent.
        
        This endpoint can be called from JavaScript to integrate document search
        into custom Odoo views and widgets.
        """
        try:
            # Create a new document search
            search = request.env['law.agent.document.search'].create({
                'name': query,
                'language': language,
                'max_results': max_results,
            })
            
            # Perform the search
            search.action_search()
            
            # Format the results
            results = []
            for result in search.result_ids:
                results.append({
                    'document': result.document,
                    'page': result.page,
                    'score': result.score,
                    'content': result.content,
                })
            
            return {
                'success': True,
                'search_id': search.id,
                'results': results
            }
        
        except Exception as e:
            _logger.exception("Error in law agent document search")
            return {
                'success': False,
                'error': str(e)
            }
