import logging
import requests
from odoo.tests.common import TransactionCase
from odoo.exceptions import UserError

_logger = logging.getLogger(__name__)

class TestMCPConnection(TransactionCase):
    """Test the connection to the MCP server."""

    def setUp(self):
        super(TestMCPConnection, self).setUp()
        self.server_url = self.env['ir.config_parameter'].sudo().get_param('law_agent.server_url', 'http://host.docker.internal:8000')
        self.chat = self.env.ref('odoo_integration_example.law_agent_chat_1')
        
    def test_server_connection(self):
        """Test that we can connect to the MCP server."""
        try:
            response = requests.get(f"{self.server_url}/health", timeout=5)
            response.raise_for_status()
            _logger.info(f"Successfully connected to MCP server at {self.server_url}")
            self.assertTrue(True, "Connection to MCP server successful")
        except requests.exceptions.RequestException as e:
            _logger.error(f"Error connecting to MCP server: {str(e)}")
            self.fail(f"Failed to connect to MCP server: {str(e)}")
    
    def test_chat_endpoint(self):
        """Test the chat endpoint."""
        try:
            response = requests.post(
                f"{self.server_url}/chat",
                json={
                    'query': "What is the Indian Contract Act?",
                    'agent_type': self.chat.agent_type,
                    'use_web_search': self.chat.use_web_search,
                    'use_document_search': self.chat.use_document_search
                },
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            _logger.info(f"Successfully received response from chat endpoint: {result}")
            self.assertTrue('response' in result, "Response should contain 'response' key")
        except requests.exceptions.RequestException as e:
            _logger.error(f"Error calling chat endpoint: {str(e)}")
            self.fail(f"Failed to call chat endpoint: {str(e)}")
    
    def test_analyze_case_endpoint(self):
        """Test the analyze_case endpoint."""
        case = self.env.ref('odoo_integration_example.law_agent_case_analysis_1')
        try:
            case_data = {
                'case_id': case.id,
                'case_title': case.name,
                'case_number': case.case_number,
                'case_text': case.case_text,
                'case_type': case.case_type,
                'language': case.language,
                'use_online_search': case.use_online_search,
                'enable_tracing': False
            }
            
            response = requests.post(
                f"{self.server_url}/analyze_case",
                json={'case_data': case_data},
                timeout=60
            )
            response.raise_for_status()
            result = response.json()
            _logger.info(f"Successfully received response from analyze_case endpoint: {result}")
            self.assertTrue('background_analysis' in result, "Response should contain 'background_analysis' key")
        except requests.exceptions.RequestException as e:
            _logger.error(f"Error calling analyze_case endpoint: {str(e)}")
            self.fail(f"Failed to call analyze_case endpoint: {str(e)}")
    
    def test_search_documents_endpoint(self):
        """Test the search_documents endpoint."""
        search = self.env.ref('odoo_integration_example.law_agent_document_search_1')
        try:
            payload = {
                'query': search.name,
                'max_results': search.max_results,
                'enable_tracing': False
            }
            
            if search.language:
                payload['language'] = search.language
                
            response = requests.post(
                f"{self.server_url}/search_documents",
                json=payload,
                timeout=30
            )
            response.raise_for_status()
            results = response.json()
            _logger.info(f"Successfully received response from search_documents endpoint: {results}")
            self.assertTrue(isinstance(results, list), "Response should be a list of results")
        except requests.exceptions.RequestException as e:
            _logger.error(f"Error calling search_documents endpoint: {str(e)}")
            self.fail(f"Failed to call search_documents endpoint: {str(e)}")
