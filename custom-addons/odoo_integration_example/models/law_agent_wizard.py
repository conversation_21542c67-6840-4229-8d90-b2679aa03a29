from odoo import models, fields, api, _

class LawAgentQuestionWizard(models.TransientModel):
    _name = 'law.agent.question.wizard'
    _description = 'Law Agent Question Wizard'

    chat_id = fields.Many2one('law.agent.chat', string='Chat', required=True)
    question = fields.Text('Question', required=True)

    def action_submit_question(self):
        """Submit the question to the law agent."""
        self.ensure_one()
        return self.chat_id.action_ask_question(self.question)
