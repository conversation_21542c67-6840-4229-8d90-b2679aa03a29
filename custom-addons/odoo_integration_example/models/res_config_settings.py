from odoo import api, fields, models

class ResConfigSettings(models.TransientModel):
    _inherit = 'res.config.settings'

    law_agent_server_url = fields.Char(
        string='Law Agent Server URL',
        config_parameter='law_agent.server_url',
        default='http://localhost:8000',
        help='URL of the MCP Law Agents server'
    )

    law_agent_enable_web_search = fields.Boolean(
        string='Enable Web Search',
        config_parameter='law_agent.enable_web_search',
        default=True,
        help='Enable web search for law agent queries'
    )

    law_agent_enable_document_search = fields.Boolean(
        string='Enable Document Search',
        config_parameter='law_agent.enable_document_search',
        default=True,
        help='Enable document search for law agent queries'
    )

    # Opik Tracing Configuration
    law_agent_enable_opik_tracing = fields.Boolean(
        string='Enable Opik Tracing',
        config_parameter='law_agent.enable_opik_tracing',
        default=False,
        help='Enable Opik tracing for LangGraph flows'
    )

    law_agent_opik_api_key = fields.Char(
        string='Opik API Key',
        config_parameter='law_agent.opik_api_key',
        help='API key for Comet Opik tracing'
    )

    law_agent_opik_workspace = fields.Char(
        string='Opik Workspace',
        config_parameter='law_agent.opik_workspace',
        help='Workspace name for Comet Opik tracing'
    )
