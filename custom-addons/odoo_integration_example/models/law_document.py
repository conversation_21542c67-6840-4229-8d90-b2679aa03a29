from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import logging
import base64
import binascii
import os

_logger = logging.getLogger(__name__)

class LawDocument(models.Model):
    _name = 'law.document'
    _description = 'Law Document'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char('Name', required=True)
    file = fields.Binary('Document File', attachment=True, required=True)
    file_name = fields.Char('File Name', required=True)
    file_type = fields.Selection([
        ('pdf', 'PDF'),
        ('txt', 'Text'),
        ('docx', 'Word Document')
    ], string='File Type', compute='_compute_file_type', store=True)
    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati'),
        ('hi', 'Hindi')
    ], string='Language', default='en', required=True)
    description = fields.Text('Description')
    page_count = fields.Integer('Page Count', default=0)
    file_size = fields.Integer('File Size', compute='_compute_file_size', store=True)
    indexed = fields.Boolean('Indexed', default=False, help="Whether the document has been indexed in the vector store")
    active = fields.Boolean(default=True)

    @api.depends('file_name')
    def _compute_file_type(self):
        """Compute the file type based on the file name extension."""
        for record in self:
            if record.file_name:
                file_ext = os.path.splitext(record.file_name)[1].lower()
                if file_ext == '.pdf':
                    record.file_type = 'pdf'
                elif file_ext == '.txt':
                    record.file_type = 'txt'
                elif file_ext == '.docx':
                    record.file_type = 'docx'
                else:
                    record.file_type = False
            else:
                record.file_type = False

    @api.depends('file')
    def _compute_file_size(self):
        """Compute the file size in bytes."""
        for record in self:
            if not record.file:
                record.file_size = 0
                continue

            try:
                # Make sure the base64 string is properly padded
                # Base64 strings should have a length that's a multiple of 4
                file_data = record.file
                # Add padding if needed
                padding_needed = len(file_data) % 4
                if padding_needed:
                    file_data += b'=' * (4 - padding_needed) if isinstance(file_data, bytes) else '=' * (4 - padding_needed)

                # Decode and get the length
                record.file_size = len(base64.b64decode(file_data))
            except (binascii.Error, TypeError) as e:
                _logger.warning(f"Error computing file size for document {record.id} - {record.name}: {str(e)}")
                # Set a default size to prevent UI errors
                record.file_size = 0

    def action_index_document(self):
        """Index the document in the vector store."""
        self.ensure_one()

        if not self.file:
            raise UserError(_("No file attached to index."))

        # Get the server URL from config
        ICPSudo = self.env['ir.config_parameter'].sudo()
        server_url = ICPSudo.get_param('law_agent.server_url', 'http://localhost:8000')

        try:
            # Decode the file content with proper error handling
            try:
                # Make sure the base64 string is properly padded
                file_data = self.file
                # Add padding if needed
                padding_needed = len(file_data) % 4
                if padding_needed:
                    file_data += b'=' * (4 - padding_needed) if isinstance(file_data, bytes) else '=' * (4 - padding_needed)

                file_content = base64.b64decode(file_data)
            except (binascii.Error, TypeError) as e:
                raise UserError(_("Invalid file data. The file may be corrupted. Error: %s") % str(e))

            # Create form data with file
            files = {'file': (self.file_name, file_content)}

            # Call the law agent server
            response = requests.post(
                f"{server_url}/upload_document",
                files=files,
                timeout=300  # 5 minutes timeout for large file uploads
            )

            response.raise_for_status()
            result = response.json()

            if result.get('success', False):
                self.indexed = True
                self.message_post(body=_("Document indexed successfully."))
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Document indexed successfully.'),
                        'sticky': False,
                        'type': 'success',
                    }
                }
            else:
                error_msg = result.get('error', _('Unknown error'))
                self.message_post(body=_("Failed to index document: %s") % error_msg)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Failed to index document: %s') % error_msg,
                        'sticky': True,
                        'type': 'danger',
                    }
                }

        except requests.exceptions.RequestException as e:
            error_msg = f"Error communicating with Law Agent server: {str(e)}"
            _logger.error(error_msg)
            self.message_post(body=_("Error: %s") % error_msg)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Error communicating with Law Agent server: %s') % str(e),
                    'sticky': True,
                    'type': 'danger',
                }
            }

    def _fix_file_padding(self, file_data):
        """Fix base64 padding for a file.

        Args:
            file_data: The base64 encoded file data

        Returns:
            tuple: (fixed_data, is_fixed, error_message)
        """
        if not file_data:
            return file_data, False, "No file data"

        try:
            # Try to decode the file to check if it's valid
            base64.b64decode(file_data)
            return file_data, False, "File is already valid"
        except (binascii.Error, TypeError) as e:
            # File is corrupted, try to fix the padding
            try:
                # Add padding if needed
                padding_needed = len(file_data) % 4
                if padding_needed:
                    fixed_data = file_data + ('=' * (4 - padding_needed))
                else:
                    # Try removing padding and re-adding it
                    fixed_data = file_data.rstrip('=')
                    padding_needed = len(fixed_data) % 4
                    if padding_needed:
                        fixed_data += '=' * (4 - padding_needed)

                # Test if it's now valid
                base64.b64decode(fixed_data)

                # If we get here, the fix worked
                return fixed_data, True, "Fixed padding"
            except (binascii.Error, TypeError) as e2:
                # Still corrupted, can't fix automatically
                return file_data, False, f"Could not fix: {str(e2)}"

    def action_fix_file(self):
        """Fix corrupted file for the current document."""
        self.ensure_one()

        if not self.file:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No File'),
                    'message': _('No file to fix.'),
                    'type': 'warning',
                }
            }

        fixed_data, is_fixed, message = self._fix_file_padding(self.file)

        if is_fixed:
            self.file = fixed_data
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('File fixed successfully.'),
                    'type': 'success',
                }
            }
        else:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Fix Needed'),
                    'message': _(message),
                    'type': 'info',
                }
            }

    def action_fix_corrupted_files(self):
        """Fix corrupted files in the database."""
        # If called from a form view with a single record, use action_fix_file instead
        if len(self) == 1:
            return self.action_fix_file()

        corrupted_docs = []
        fixed_docs = []

        for record in self.search([]):
            if not record.file:
                continue

            fixed_data, is_fixed, _ = self._fix_file_padding(record.file)

            if is_fixed:
                record.file = fixed_data
                fixed_docs.append(record.id)
            elif fixed_data != record.file:  # File was corrupted but couldn't be fixed
                corrupted_docs.append(record.id)

        # Return a message with the results
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('File Check Complete'),
                'message': _('%s corrupted files found, %s files fixed.') % (len(corrupted_docs), len(fixed_docs)),
                'sticky': True,
                'type': 'info',
            }
        }

    def action_update_vector_store(self):
        """Update the vector store with all documents."""
        # Get the server URL from config
        ICPSudo = self.env['ir.config_parameter'].sudo()
        server_url = ICPSudo.get_param('law_agent.server_url', 'http://localhost:8000')

        try:
            # Call the law agent server
            response = requests.post(
                f"{server_url}/update_vector_store",
                params={"force_recreate": False},
                timeout=300  # 5 minutes timeout for vector store update
            )

            response.raise_for_status()
            result = response.json()

            if result.get('success', False):
                # Mark all documents as indexed
                self.search([]).write({'indexed': True})

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Vector store updated successfully. Document count: %s') % result.get('document_count', 0),
                        'sticky': False,
                        'type': 'success',
                    }
                }
            else:
                error_msg = result.get('error', _('Unknown error'))
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Failed to update vector store: %s') % error_msg,
                        'sticky': True,
                        'type': 'danger',
                    }
                }

        except requests.exceptions.RequestException as e:
            error_msg = f"Error communicating with Law Agent server: {str(e)}"
            _logger.error(error_msg)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Error communicating with Law Agent server: %s') % str(e),
                    'sticky': True,
                    'type': 'danger',
                }
            }
