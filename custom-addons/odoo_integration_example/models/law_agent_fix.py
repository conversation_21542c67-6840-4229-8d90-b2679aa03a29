from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import logging
import os

_logger = logging.getLogger(__name__)

class LawAgentCaseAnalysis(models.Model):
    _inherit = 'law.agent.case.analysis'
    
    def action_analyze(self):
        """Analyze the case using the law agent."""
        self.ensure_one()

        if self.state != 'draft':
            raise UserError(_("Only draft cases can be analyzed."))

        self.state = 'analyzing'

        # Set up Opik tracing if enabled
        opik_enabled = self._setup_opik_environment()

        # Get the server URL from config
        ICPSudo = self.env['ir.config_parameter'].sudo()
        server_url = ICPSudo.get_param('law_agent.server_url', 'http://localhost:8000')

        try:
            # Prepare the case data
            case_data = {
                'case_id': str(self.id),  # Convert ID to string to avoid 422 error
                'case_title': self.name,
                'case_number': self.case_number or '',  # Ensure not None
                'case_text': self.case_text,
                'case_type': self.case_type,
                'language': self.language,
                'use_online_search': self.use_online_search,
                'enable_tracing': self._get_opik_tracing_enabled()
            }

            # Call the law agent server
            _logger.info(f"Sending case analysis request to {server_url}/analyze_case with case: {self.name}")
            
            response = requests.post(
                f"{server_url}/analyze_case",
                json={'case_data': case_data},
                timeout=120
            )
            response.raise_for_status()
            result = response.json()

            # Update the case with the analysis results
            self.write({
                'background_analysis': result.get('background_analysis', ''),
                'legal_issues': result.get('legal_issues', ''),
                'evidence_analysis': result.get('evidence_analysis', ''),
                'legal_principles': result.get('legal_principles', ''),
                'judgment_prediction': result.get('judgment_prediction', ''),
                'full_analysis': result.get('full_analysis', ''),
                'state': 'done'
            })

            # Post a message in the chatter
            self.message_post(
                body=_("Case analysis completed successfully."),
                subject=_("Case Analysis")
            )

            return True

        except requests.exceptions.RequestException as e:
            error_msg = f"Error communicating with Law Agent server: {str(e)}"
            _logger.error(error_msg)
            
            # Log more detailed error information
            if hasattr(e, 'response') and e.response is not None:
                _logger.error(f"Response status code: {e.response.status_code}")
                try:
                    error_detail = e.response.json()
                    _logger.error(f"Error details: {error_detail}")
                except:
                    _logger.error(f"Response text: {e.response.text}")

            self.write({
                'state': 'error'
            })

            # Post an error message in the chatter
            self.message_post(
                body=error_msg,
                subject=_("Case Analysis Error")
            )

            raise UserError(error_msg)
