from odoo import models, fields, api, _
from odoo.exceptions import UserError
import requests
import logging
import json
import os

_logger = logging.getLogger(__name__)

class LawAgentChat(models.Model):
    _name = 'law.agent.chat'
    _description = 'Law Agent Chat'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char('Name', required=True, default=lambda self: _('New Chat'))
    user_id = fields.Many2one('res.users', string='User', default=lambda self: self.env.user, required=True)
    agent_type = fields.Selection([
        ('general', 'General Legal'),
        ('gujarat', 'Gujarat Law'),
        ('land', 'Land Law'),
        ('civil', 'Civil Law')
    ], string='Agent Type', default='general', required=True)
    use_web_search = fields.Boolean('Use Web Search', default=True)
    use_document_search = fields.Boolean('Use Document Search', default=True)
    conversation = fields.One2many('law.agent.message', 'chat_id', string='Conversation')
    active = fields.Boolean(default=True)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('name', _('New Chat')) == _('New Chat'):
                vals['name'] = self.env['ir.sequence'].next_by_code('law.agent.chat') or _('New Chat')
        return super(LawAgentChat, self).create(vals_list)

    def _get_opik_tracing_enabled(self):
        """Check if Opik tracing is enabled in settings"""
        return self.env['ir.config_parameter'].sudo().get_param('law_agent.enable_opik_tracing', 'False').lower() == 'true'

    def _setup_opik_environment(self):
        """Set up Opik environment variables if tracing is enabled"""
        if not self._get_opik_tracing_enabled():
            return False

        # Get Opik API key and workspace from settings
        api_key = self.env['ir.config_parameter'].sudo().get_param('law_agent.opik_api_key', '')
        workspace = self.env['ir.config_parameter'].sudo().get_param('law_agent.opik_workspace', '')

        if not api_key or not workspace:
            _logger.warning("Opik tracing is enabled but API key or workspace is not set")
            return False

        # Set environment variables for Opik
        os.environ["OPIK_API_KEY"] = api_key
        os.environ["OPIK_WORKSPACE"] = workspace

        _logger.info("Opik environment variables set up successfully")
        return True

    def action_ask_question(self, question):
        """Ask a question to the law agent."""
        self.ensure_one()

        # Create a user message
        self.env['law.agent.message'].create({
            'chat_id': self.id,
            'content': question,
            'is_user': True,
        })

        # Set up Opik tracing if enabled
        opik_enabled = self._setup_opik_environment()

        # Get the server URL from config
        ICPSudo = self.env['ir.config_parameter'].sudo()
        server_url = ICPSudo.get_param('law_agent.server_url', 'http://localhost:8000')

        # First try with current settings
        try:
            # Call the law agent server
            response = requests.post(
                f"{server_url}/chat",
                json={
                    'query': question,
                    'agent_type': self.agent_type,
                    'use_web_search': self.use_web_search,
                    'use_document_search': self.use_document_search
                },
                timeout=60
            )
            response.raise_for_status()
            result = response.json()
        except requests.exceptions.RequestException as e:
            # If there's an error and web search is enabled, try again with web search disabled
            if self.use_web_search:
                _logger.warning(f"Error with web search enabled, trying again with web search disabled: {str(e)}")
                try:
                    response = requests.post(
                        f"{server_url}/chat",
                        json={
                            'query': question,
                            'agent_type': self.agent_type,
                            'use_web_search': False,  # Disable web search
                            'use_document_search': self.use_document_search
                        },
                        timeout=60
                    )
                    response.raise_for_status()
                    result = response.json()

                    # Add a note about web search being disabled
                    if 'response' in result:
                        result['response'] += "\n\n*Note: Web search was disabled due to technical issues.*"
                except requests.exceptions.RequestException as e2:
                    # If it still fails, use the original error
                    return self._handle_request_error(e)
            else:
                # If web search is already disabled, handle the error
                return self._handle_request_error(e)

        # Create an agent message with the response
        sources_text = ""
        if result.get('sources'):
            sources_text = "\n\n**Sources:**\n"
            for i, source in enumerate(result['sources'], 1):
                title = source.get('title', 'Untitled')
                url = source.get('url', '')
                sources_text += f"{i}. {title}"
                if url:
                    sources_text += f" - {url}"
                sources_text += "\n"

        self.env['law.agent.message'].create({
            'chat_id': self.id,
            'content': result.get('response', 'No response') + sources_text,
            'is_user': False,
        })

        return result.get('response', 'No response')

    def _handle_request_error(self, e):
        """Handle request error and create an error message."""
        error_msg = f"Error communicating with Law Agent server: {str(e)}"
        _logger.error(error_msg)

        # Create an error message
        self.env['law.agent.message'].create({
            'chat_id': self.id,
            'content': error_msg,
            'is_user': False,
        })

        raise UserError(error_msg)


class LawAgentMessage(models.Model):
    _name = 'law.agent.message'
    _description = 'Law Agent Message'
    _order = 'create_date asc'

    chat_id = fields.Many2one('law.agent.chat', string='Chat', required=True, ondelete='cascade')
    content = fields.Text('Content', required=True)
    is_user = fields.Boolean('Is User Message', default=False)
    create_date = fields.Datetime('Created on', readonly=True)


class LawAgentCaseAnalysis(models.Model):
    _name = 'law.agent.case.analysis'
    _description = 'Law Agent Case Analysis'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _order = 'create_date desc'

    name = fields.Char('Name', required=True)
    case_number = fields.Char('Case Number')
    case_text = fields.Text('Case Text', required=True)
    case_type = fields.Selection([
        ('property', 'Property'),
        ('civil', 'Civil'),
        ('criminal', 'Criminal'),
        ('family', 'Family'),
        ('corporate', 'Corporate'),
        ('other', 'Other')
    ], string='Case Type', default='property', required=True)
    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati'),
        ('hi', 'Hindi')
    ], string='Language', default='en', required=True)
    use_online_search = fields.Boolean('Use Online Search', default=True)

    background_analysis = fields.Text('Background Analysis', readonly=True)
    legal_issues = fields.Text('Legal Issues', readonly=True)
    evidence_analysis = fields.Text('Evidence Analysis', readonly=True)
    legal_principles = fields.Text('Legal Principles', readonly=True)
    judgment_prediction = fields.Text('Judgment Prediction', readonly=True)
    full_analysis = fields.Text('Full Analysis', readonly=True)

    state = fields.Selection([
        ('draft', 'Draft'),
        ('analyzing', 'Analyzing'),
        ('done', 'Completed'),
        ('error', 'Error')
    ], string='Status', default='draft', tracking=True)

    def _get_opik_tracing_enabled(self):
        """Check if Opik tracing is enabled in settings"""
        return self.env['ir.config_parameter'].sudo().get_param('law_agent.enable_opik_tracing', 'False').lower() == 'true'

    def _setup_opik_environment(self):
        """Set up Opik environment variables if tracing is enabled"""
        if not self._get_opik_tracing_enabled():
            return False

        # Get Opik API key and workspace from settings
        api_key = self.env['ir.config_parameter'].sudo().get_param('law_agent.opik_api_key', '')
        workspace = self.env['ir.config_parameter'].sudo().get_param('law_agent.opik_workspace', '')

        if not api_key or not workspace:
            _logger.warning("Opik tracing is enabled but API key or workspace is not set")
            return False

        # Set environment variables for Opik
        os.environ["OPIK_API_KEY"] = api_key
        os.environ["OPIK_WORKSPACE"] = workspace

        _logger.info("Opik environment variables set up successfully")
        return True

    def action_analyze(self):
        """Analyze the case using the law agent."""
        self.ensure_one()

        if self.state != 'draft':
            raise UserError(_("Only draft cases can be analyzed."))

        self.state = 'analyzing'

        # Set up Opik tracing if enabled
        opik_enabled = self._setup_opik_environment()

        # Get the server URL from config
        ICPSudo = self.env['ir.config_parameter'].sudo()
        server_url = ICPSudo.get_param('law_agent.server_url', 'http://localhost:8000')

        try:
            # Prepare the case data
            case_data = {
                'case_id': self.id,
                'case_title': self.name,
                'case_number': self.case_number,
                'case_text': self.case_text,
                'case_type': self.case_type,
                'language': self.language,
                'use_online_search': self.use_online_search,
                'enable_tracing': self._get_opik_tracing_enabled()
            }

            # Call the law agent server
            response = requests.post(
                f"{server_url}/analyze_case",
                json={'case_data': case_data},
                timeout=120
            )
            response.raise_for_status()
            result = response.json()

            # Update the case with the analysis results
            self.write({
                'background_analysis': result.get('background_analysis', ''),
                'legal_issues': result.get('legal_issues', ''),
                'evidence_analysis': result.get('evidence_analysis', ''),
                'legal_principles': result.get('legal_principles', ''),
                'judgment_prediction': result.get('judgment_prediction', ''),
                'full_analysis': result.get('full_analysis', ''),
                'state': 'done'
            })

            # Post a message in the chatter
            self.message_post(
                body=_("Case analysis completed successfully."),
                subject=_("Case Analysis")
            )

            return True

        except requests.exceptions.RequestException as e:
            error_msg = f"Error communicating with Law Agent server: {str(e)}"
            _logger.error(error_msg)

            self.write({
                'state': 'error'
            })

            # Post an error message in the chatter
            self.message_post(
                body=error_msg,
                subject=_("Case Analysis Error")
            )

            raise UserError(error_msg)


class LawAgentDocumentSearch(models.Model):
    _name = 'law.agent.document.search'
    _description = 'Law Agent Document Search'
    _order = 'create_date desc'

    name = fields.Char('Search Query', required=True)
    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati'),
        ('hi', 'Hindi')
    ], string='Language', default='en')
    max_results = fields.Integer('Max Results', default=10)
    result_ids = fields.One2many('law.agent.document.result', 'search_id', string='Results')

    def _get_opik_tracing_enabled(self):
        """Check if Opik tracing is enabled in settings"""
        return self.env['ir.config_parameter'].sudo().get_param('law_agent.enable_opik_tracing', 'False').lower() == 'true'

    def _setup_opik_environment(self):
        """Set up Opik environment variables if tracing is enabled"""
        if not self._get_opik_tracing_enabled():
            return False

        # Get Opik API key and workspace from settings
        api_key = self.env['ir.config_parameter'].sudo().get_param('law_agent.opik_api_key', '')
        workspace = self.env['ir.config_parameter'].sudo().get_param('law_agent.opik_workspace', '')

        if not api_key or not workspace:
            _logger.warning("Opik tracing is enabled but API key or workspace is not set")
            return False

        # Set environment variables for Opik
        os.environ["OPIK_API_KEY"] = api_key
        os.environ["OPIK_WORKSPACE"] = workspace

        _logger.info("Opik environment variables set up successfully")
        return True

    def action_search(self):
        """Search for documents using the law agent."""
        self.ensure_one()

        # Set up Opik tracing if enabled
        opik_enabled = self._setup_opik_environment()

        # Get the server URL from config
        ICPSudo = self.env['ir.config_parameter'].sudo()
        server_url = ICPSudo.get_param('law_agent.server_url', 'http://localhost:8000')

        try:
            # Prepare the search payload
            payload = {
                'query': self.name,
                'max_results': self.max_results,
                'enable_tracing': self._get_opik_tracing_enabled()
            }

            if self.language:
                payload['language'] = self.language

            # Call the law agent server
            response = requests.post(
                f"{server_url}/search_documents",
                json=payload,
                timeout=30
            )
            response.raise_for_status()

            # Parse the response
            try:
                results = response.json()

                # Handle case where results is not a list
                if not isinstance(results, list):
                    _logger.warning(f"Unexpected response format: {type(results)}")
                    if isinstance(results, dict) and 'results' in results:
                        # Some APIs return {'results': [...]} format
                        results = results['results']
                    elif isinstance(results, dict) and 'documents' in results:
                        # Some APIs return {'documents': [...]} format
                        results = results['documents']
                    else:
                        # Convert to list with single item
                        results = [results]
            except ValueError as e:
                # Handle case where response is not valid JSON
                _logger.warning(f"Invalid JSON response: {str(e)}")
                results = [response.text]

            # Delete existing results
            self.result_ids.unlink()

            # Create new results
            for result in results:
                # Handle different response formats
                if isinstance(result, dict):
                    # If result is a dictionary, use get method
                    self.env['law.agent.document.result'].create({
                        'search_id': self.id,
                        'document': result.get('document', 'Unknown'),
                        'page': result.get('page', 0),
                        'score': result.get('score', 0.0),
                        'content': result.get('content', '')
                    })
                elif isinstance(result, str):
                    # If result is a string, use it as content
                    self.env['law.agent.document.result'].create({
                        'search_id': self.id,
                        'document': 'Text Result',
                        'page': 0,
                        'score': 1.0,
                        'content': result
                    })
                else:
                    # For any other type, convert to string
                    _logger.warning(f"Unexpected result type: {type(result)}")
                    self.env['law.agent.document.result'].create({
                        'search_id': self.id,
                        'document': 'Unknown Format',
                        'page': 0,
                        'score': 0.5,
                        'content': str(result)
                    })

            return True

        except requests.exceptions.RequestException as e:
            error_msg = f"Error communicating with Law Agent server: {str(e)}"
            _logger.error(error_msg)

            # Create a result with the error message
            self.env['law.agent.document.result'].create({
                'search_id': self.id,
                'document': 'Error',
                'page': 0,
                'score': 0.0,
                'content': f"Error: {str(e)}\n\nPlease check the server URL and make sure the MCP server is running."
            })

            # Show a user-friendly error message
            self.env.user.notify_warning(
                title='Document Search Error',
                message=f"Could not connect to the Law Agent server. Please check the server URL and make sure the server is running.\n\nError: {str(e)}",
                sticky=True
            )

            return False


class LawAgentDocumentResult(models.Model):
    _name = 'law.agent.document.result'
    _description = 'Law Agent Document Search Result'
    _order = 'score desc'

    search_id = fields.Many2one('law.agent.document.search', string='Search', required=True, ondelete='cascade')
    document = fields.Char('Document', required=True)
    page = fields.Integer('Page', default=0)
    score = fields.Float('Relevance Score', default=0.0)
    content = fields.Text('Content')
