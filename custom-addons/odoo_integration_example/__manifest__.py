{
    'name': 'MCP Law Agents Integration',
    'version': '1.0',
    'category': 'Legal',
    'summary': 'Integrate Odoo with MCP Law Agents for legal AI assistance',
    'description': """
MCP Law Agents Integration
=========================

This module integrates Odoo with the MCP Law Agents server to provide:

* Legal question answering
* Case analysis
* Document search
* Web search for legal information
* Opik tracing for LangGraph flows

The module adds AI-powered legal assistance to various Odoo models and includes
integration with Comet Opik for tracing and monitoring LangGraph flows.
    """,
    'author': 'VPerfectCS',
    'website': 'https://www.vperfectcs.com',
    'depends': ['base', 'mail', 'web'],
    'data': [
        'security/ir.model.access.csv',
        'views/law_agent_wizard_views.xml',
        'views/law_agent_views.xml',
        'views/law_document_views.xml',
        'views/res_config_settings_views.xml',
    ],
    'demo': [
        'data/law_agent_demo_data.xml',
    ],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
}
