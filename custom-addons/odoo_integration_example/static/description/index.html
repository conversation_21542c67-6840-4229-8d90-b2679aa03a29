<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">MCP Law Agents Integration</h2>
        <h3 class="oe_slogan">AI-powered legal assistance for Odoo</h3>
        <div class="oe_demo oe_picture oe_screenshot">
            <img src="banner.png">
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Key Features</h2>
        <div class="oe_span6">
            <div class="oe_demo oe_picture oe_screenshot">
                <img src="chat.png">
            </div>
        </div>
        <div class="oe_span6">
            <p class="oe_mt32 text-justify">
                <ul>
                    <li><strong>Legal Question Answering:</strong> Ask legal questions and get AI-powered answers</li>
                    <li><strong>Case Analysis:</strong> Analyze legal cases with AI assistance</li>
                    <li><strong>Document Search:</strong> Search for relevant legal documents</li>
                    <li><strong>Web Search:</strong> Search the web for legal information</li>
                    <li><strong>Multiple Agent Types:</strong> Specialized agents for different legal domains</li>
                </ul>
            </p>
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Law Agent Chat</h2>
        <div class="oe_span6">
            <p class="oe_mt32 text-justify">
                The Law Agent Chat feature allows users to ask legal questions and get AI-powered answers. The chat interface is intuitive and easy to use, with support for different agent types:
                <ul>
                    <li><strong>General Legal:</strong> General legal questions</li>
                    <li><strong>Gujarat Law:</strong> Questions specific to Gujarat state laws</li>
                    <li><strong>Land Law:</strong> Questions about land and property laws</li>
                    <li><strong>Civil Law:</strong> Questions about civil procedure and litigation</li>
                </ul>
                The chat history is saved for future reference, and users can share chats with colleagues.
            </p>
        </div>
        <div class="oe_span6">
            <div class="oe_demo oe_picture oe_screenshot">
                <img src="chat_detail.png">
            </div>
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Case Analysis</h2>
        <div class="oe_span6">
            <div class="oe_demo oe_picture oe_screenshot">
                <img src="case_analysis.png">
            </div>
        </div>
        <div class="oe_span6">
            <p class="oe_mt32 text-justify">
                The Case Analysis feature allows users to analyze legal cases with AI assistance. Users can input case details and get a comprehensive analysis including:
                <ul>
                    <li><strong>Background Analysis:</strong> Overview of the case background</li>
                    <li><strong>Legal Issues:</strong> Identification of key legal issues</li>
                    <li><strong>Evidence Analysis:</strong> Analysis of evidence presented</li>
                    <li><strong>Legal Principles:</strong> Relevant legal principles and precedents</li>
                    <li><strong>Judgment Prediction:</strong> Prediction of possible outcomes</li>
                </ul>
                The analysis is saved for future reference and can be shared with colleagues.
            </p>
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Document Search</h2>
        <div class="oe_span6">
            <p class="oe_mt32 text-justify">
                The Document Search feature allows users to search for relevant legal documents. The search is powered by advanced semantic chunking and vector search technology, providing highly relevant results. Users can:
                <ul>
                    <li><strong>Search by Query:</strong> Enter a natural language query</li>
                    <li><strong>Filter by Language:</strong> Filter results by language (English, Gujarati, Hindi)</li>
                    <li><strong>View Document Excerpts:</strong> See relevant excerpts from documents</li>
                    <li><strong>Sort by Relevance:</strong> Results are sorted by relevance score</li>
                </ul>
                The search results can be saved for future reference.
            </p>
        </div>
        <div class="oe_span6">
            <div class="oe_demo oe_picture oe_screenshot">
                <img src="document_search.png">
            </div>
        </div>
    </div>
</section>

<section class="oe_container oe_dark">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Technical Information</h2>
        <div class="oe_span12">
            <p class="oe_mt32 text-justify">
                <strong>Server Requirements:</strong>
                <ul>
                    <li>The module requires a running MCP Law Agents server</li>
                    <li>The server URL can be configured in the module settings</li>
                    <li>The server should be accessible from the Odoo server</li>
                </ul>
                
                <strong>Integration Options:</strong>
                <ul>
                    <li>REST API: The module communicates with the MCP Law Agents server via REST API</li>
                    <li>Widget: A JavaScript widget is provided for embedding the chat interface in custom views</li>
                    <li>Controllers: Custom controllers are provided for JavaScript integration</li>
                </ul>
                
                <strong>Configuration:</strong>
                <ul>
                    <li>Server URL: Configure the URL of the MCP Law Agents server</li>
                    <li>Web Search: Enable/disable web search for law agent queries</li>
                    <li>Document Search: Enable/disable document search for law agent queries</li>
                </ul>
            </p>
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">Contact Information</h2>
        <div class="oe_span12">
            <p class="oe_mt32 text-center">
                <strong>VPerfectcs</strong><br/>
                Email: <EMAIL><br/>
                Website: https://www.vperfectcs.com<br/>
            </p>
        </div>
    </div>
</section>
