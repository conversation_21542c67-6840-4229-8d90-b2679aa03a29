# MCP Law Agents Integration for Odoo

This module integrates Odoo with the MCP Law Agents server to provide AI-powered legal assistance.

## Features

* **Legal Question Answering**: Ask legal questions and get AI-powered answers
* **Case Analysis**: Analyze legal cases with AI assistance
* **Document Search**: Search for legal documents using AI
* **Web Search**: Search the web for legal information
* **Opik Tracing**: Monitor and trace LangGraph flows with Comet Opik

## Installation

1. Install the module in your Odoo instance
2. Configure the MCP Law Agents server URL in the settings
3. Enable/disable web search and document search as needed
4. Configure Opik tracing if desired
5. Load demo data if you want to test with sample records

### Docker Configuration

If you're running Odoo in Docker and your MCP server is on the host machine, use the following URL in the settings:

```
http://host.docker.internal:8000
```

This special DNS name allows Docker containers to access services running on the host machine.

## Configuration

### Server Configuration

1. Go to **Law Agent > Configuration > Settings**
2. Set the **Server URL** to the URL of your MCP Law Agents server
3. Enable/disable **Web Search** and **Document Search** as needed

### Opik Tracing Configuration

1. Go to **Law Agent > Configuration > Settings**
2. Enable **Opik Tracing** to use Comet Opik for tracing LangGraph flows
3. Enter your **Opik API Key** and **Workspace** from your Comet account

## Usage

### Law Agent Chat

1. Go to **Law Agent > Chats**
2. Create a new chat
3. Select the agent type (General Legal, Gujarat Law, Land Law, Civil Law)
4. Click **Ask Question** to ask a legal question
5. The agent will respond with an answer and sources

### Case Analysis

1. Go to **Law Agent > Case Analyses**
2. Create a new case analysis
3. Enter the case details
4. Click **Analyze Case** to analyze the case
5. The agent will provide a comprehensive analysis of the case

### Document Search

1. Go to **Law Agent > Document Search**
2. Create a new document search
3. Enter your search query
4. Click **Search Documents** to search for relevant documents
5. The agent will return a list of relevant documents

## Opik Tracing

When Opik tracing is enabled, the module will:

1. Set up the Opik environment variables for each request
2. Enable tracing for the LangGraph flows
3. Send tracing data to your Comet workspace

You can view the traces in the Comet dashboard:

1. Go to https://www.comet.com/
2. Navigate to your workspace
3. Click on "Opik" in the sidebar
4. You should see your traces listed there

## Technical Information

The module uses the following endpoints from the MCP Law Agents server:

* `/chat`: For asking questions to the law agent
* `/analyze_case`: For analyzing legal cases
* `/search_documents`: For searching legal documents

When Opik tracing is enabled, the module adds the `enable_tracing` parameter to the requests to enable tracing on the server side.

## Requirements

* Odoo 18.0
* MCP Law Agents server
* Comet account (for Opik tracing)

## Demo Data

The module includes demo data to help you test the functionality without having to create records manually. The demo data includes:

* Sample Law Agent Chats with conversation history
* Sample Case Analyses
* Sample Document Searches with results
* Preconfigured server URL for Docker environments

To load the demo data, install the module with demo data enabled.

## Testing

### Automated Tests

The module includes automated tests to verify the connection to the MCP server. To run the tests:

```bash
odoo-bin -d your_database --test-enable --test-tags=odoo_integration_example
```

### Manual Testing Scripts

#### Direct API Testing

You can use the provided script to test the connection to the MCP server directly:

```bash
python3 /path/to/odoo_integration_example/scripts/test_mcp_server.py
```

This script will test the connection to the MCP server and verify that all endpoints are working correctly.

#### Docker Environment Testing

If you're running Odoo in Docker, you can use the provided shell scripts to test the connection:

**Full Test (with web search):**
```bash
./odoo_integration_example/scripts/test_docker.sh
```

**Basic Test (without web search):**
```bash
./odoo_integration_example/scripts/test_docker_basic.sh
```

You can also specify a custom server URL for either script:

```bash
./odoo_integration_example/scripts/test_docker.sh http://your-mcp-server:8000
./odoo_integration_example/scripts/test_docker_basic.sh http://your-mcp-server:8000
```

**Note:** If you encounter issues with the web search functionality (e.g., Node.js permission errors), use the basic test script which doesn't rely on web search.

#### Odoo API Testing

You can also test the connection from within the Odoo environment using the Odoo API:

```bash
odoo-bin shell -d your_database < /path/to/odoo_integration_example/scripts/odoo_test_mcp.py
```

This script will use the Odoo API to test the connection to the MCP server and verify that the integration is working correctly.

## OWL Component Implementation

This module uses Odoo's OWL (Odoo Web Library) framework for building UI components. The main component is `LawAgentChat`, which provides a chat interface for interacting with the law agent.

### Component Structure

```
odoo_integration_example/
├── static/
│   ├── src/
│   │   ├── components/
│   │   │   ├── law_agent_chat/
│   │   │   │   ├── law_agent_chat.js       # Main OWL component
│   │   │   │   ├── law_agent_chat.xml      # Component template
│   │   │   │   └── law_agent_chat_registry.js  # Component registration
│   │   ├── js/
│   │   │   └── law_agent_chat_widget.js    # Legacy widget (deprecated)
│   │   └── xml/
│   │       └── law_agent_chat_templates.xml # Legacy templates (deprecated)
```

### Using the Component in Views

To use the Law Agent Chat component in your views:

```xml
<field name="description" widget="law_agent_chat" options="{
    'agent_type': 'general',
    'use_web_search': true,
    'use_document_search': true
}"/>
```

Available options:
- `agent_type`: The type of law agent to use (general, gujarat, land, civil)
- `use_web_search`: Whether to use web search (true/false)
- `use_document_search`: Whether to use document search (true/false)
- `chat_id`: The ID of an existing chat to continue (optional)

### Migration from Legacy Widget

The module includes both the modern OWL component and the legacy widget for backward compatibility. The legacy widget is deprecated and will be removed in future versions.

If you're using the legacy widget, you should migrate to the OWL component:

1. Update your view definitions to use the new widget name
2. Update any JavaScript code that interacts with the widget
3. Test thoroughly to ensure compatibility

## Technical Details

### OWL Component Features

- Modern OWL component architecture following Odoo 18.0 guidelines
- Proper use of props, hooks, and services
- State management with useState
- Lifecycle hooks with onWillStart and onMounted
- Event handling with t-on-* directives
- Responsive UI design for chat interface

### Model Implementation

- Use of `@api.model_create_multi` for batch creation
- Proper inheritance of mail thread and activity mixin
- Standardized field definitions with proper defaults
- Comprehensive error handling and logging
- Robust handling of different response formats
- Graceful fallback for web search failures
- User-friendly error messages

#### Batch Creation Example

```python
@api.model_create_multi
def create(self, vals_list):
    for vals in vals_list:
        if vals.get('name', _('New Chat')) == _('New Chat'):
            vals['name'] = self.env['ir.sequence'].next_by_code('law.agent.chat') or _('New Chat')
    return super(LawAgentChat, self).create(vals_list)
```

#### Robust Response Handling Example

```python
# Parse the response
try:
    results = response.json()

    # Handle case where results is not a list
    if not isinstance(results, list):
        if isinstance(results, dict) and 'results' in results:
            # Some APIs return {'results': [...]} format
            results = results['results']
        else:
            # Convert to list with single item
            results = [results]

    # Create new results
    for result in results:
        # Handle different response formats
        if isinstance(result, dict):
            # If result is a dictionary, use get method
            self.env['law.agent.document.result'].create({
                'search_id': self.id,
                'document': result.get('document', 'Unknown'),
                'content': result.get('content', '')
            })
        elif isinstance(result, str):
            # If result is a string, use it as content
            self.env['law.agent.document.result'].create({
                'search_id': self.id,
                'document': 'Text Result',
                'content': result
            })
except ValueError:
    # Handle case where response is not valid JSON
    results = [response.text]
```

### Asset Management

The module uses the following asset bundles:

```python
'assets': {
    'web.assets_backend': [
        # OWL Components (recommended)
        'odoo_integration_example/static/src/components/**/*.js',
        'odoo_integration_example/static/src/components/**/*.scss',
        # Legacy widgets (deprecated)
        'odoo_integration_example/static/src/js/law_agent_chat_widget.js',
    ],
    'web.assets_backend_xml': [
        'odoo_integration_example/static/src/components/**/*.xml',
    ],
},
```
