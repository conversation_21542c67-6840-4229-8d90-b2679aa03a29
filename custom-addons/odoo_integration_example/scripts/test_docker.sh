#!/bin/bash

# This script runs the test_mcp_server.py script in the Docker environment
# Usage: ./test_docker.sh [server_url]

# Default server URL
SERVER_URL=${1:-"http://host.docker.internal:8000"}

# Get the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"

# Get the Docker container name or ID
CONTAINER_NAME=$(docker ps --filter "name=odoo" --format "{{.Names}}" | head -n 1)

if [ -z "$CONTAINER_NAME" ]; then
    echo "Error: No running Odoo container found."
    exit 1
fi

echo "Using Odoo container: $CONTAINER_NAME"
echo "Using MCP server URL: $SERVER_URL"
echo "Running test script..."

# Run the test script in the Docker container
docker exec -e MCP_SERVER_URL="$SERVER_URL" "$CONTAINER_NAME" python3 /mnt/extra-addons/odoo_integration_example/scripts/test_mcp_server.py

# Check the exit code
if [ $? -eq 0 ]; then
    echo "Test completed successfully!"
else
    echo "Test failed!"
    exit 1
fi
