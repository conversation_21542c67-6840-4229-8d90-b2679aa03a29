# This script tests basic MCP server functionality without web search
# Usage: python3 test_mcp_basic.py

import requests
import sys
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
_logger = logging.getLogger(__name__)

def test_mcp_basic():
    """Test basic MCP server functionality without web search."""
    # Use a default server URL or get it from environment variable
    server_url = os.environ.get('MCP_SERVER_URL', 'http://host.docker.internal:8000')
    _logger.info(f"Using MCP server URL: {server_url}")
    _logger.info(f"Testing connection to MCP server at {server_url}")

    try:
        # Test the health endpoint
        _logger.info("1. Testing health endpoint...")
        response = requests.get(f"{server_url}/health", timeout=5)
        response.raise_for_status()
        health_data = response.json() if response.headers.get('content-type') == 'application/json' else {'status': response.status_code}
        _logger.info(f"✅ Successfully connected to MCP server at {server_url}")
        _logger.info(f"   Health status: {health_data}")
        
        # Test the chat endpoint with document search only (no web search)
        _logger.info("\n2. Testing chat endpoint with document search only...")
        test_query = "What is the Indian Contract Act?"
        _logger.info(f"   Sending query: '{test_query}'")
        
        response = requests.post(
            f"{server_url}/chat",
            json={
                'query': test_query,
                'agent_type': 'general',
                'use_web_search': False,  # Disable web search to avoid Brave Search issues
                'use_document_search': True
            },
            timeout=60  # Increase timeout for document search
        )
        response.raise_for_status()
        chat_result = response.json()
        
        # Extract and display a snippet of the response
        response_text = chat_result.get('response', '')
        response_snippet = response_text[:150] + '...' if len(response_text) > 150 else response_text
        _logger.info(f"✅ Successfully received response from chat endpoint")
        _logger.info(f"   Response snippet: '{response_snippet}'")
        
        _logger.info("\n✅ Basic tests passed! MCP server is working correctly.")
        return True
        
    except requests.exceptions.RequestException as e:
        _logger.error(f"❌ Error connecting to MCP server: {str(e)}")
        return False

# Run the test
if __name__ == "__main__":
    success = test_mcp_basic()
    if not success:
        sys.exit(1)  # Exit with error code
