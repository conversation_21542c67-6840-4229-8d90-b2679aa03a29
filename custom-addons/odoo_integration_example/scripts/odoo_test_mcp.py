# This script should be run from the Odoo shell
# Usage: odoo-bin shell -d your_database < odoo_test_mcp.py

import logging
import requests
import sys

_logger = logging.getLogger(__name__)

def test_mcp_connection_from_odoo():
    """Test the connection to the MCP server from Odoo."""
    # Get the server URL from the configuration
    server_url = env['ir.config_parameter'].sudo().get_param('law_agent.server_url', 'http://host.docker.internal:8000')
    print(f"Using MCP server URL: {server_url}")
    
    try:
        # Test the health endpoint
        print("1. Testing health endpoint...")
        response = requests.get(f"{server_url}/health", timeout=5)
        response.raise_for_status()
        print(f"✅ Successfully connected to MCP server at {server_url}")
        
        # Test with a sample chat
        print("\n2. Testing with a sample chat...")
        chat = env['law.agent.chat'].search([], limit=1)
        if not chat:
            print("No chat found, creating a new one...")
            chat = env['law.agent.chat'].create({
                'name': 'Test Chat',
                'agent_type': 'general',
                'use_web_search': True,
                'use_document_search': True,
            })
        
        print(f"Using chat: {chat.name} (ID: {chat.id})")
        
        # Send a test question
        question = "What is the Indian Contract Act?"
        print(f"Sending question: '{question}'")
        
        try:
            response = chat.action_ask_question(question)
            print(f"✅ Successfully received response from law agent")
            print(f"Response snippet: '{response[:150]}...' if len(response) > 150 else response")
            
            # Check if messages were created
            messages = env['law.agent.message'].search([('chat_id', '=', chat.id)])
            print(f"Chat now has {len(messages)} messages")
            
            return True
        except Exception as e:
            print(f"❌ Error asking question: {str(e)}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error connecting to MCP server: {str(e)}")
        return False

# Run the test
success = test_mcp_connection_from_odoo()
if not success:
    sys.exit(1)  # Exit with error code
