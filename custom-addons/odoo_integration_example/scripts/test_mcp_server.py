# This script can be run directly to test the connection to the MCP server
# Usage: python3 test_mcp_server.py

import requests
import sys
import logging
import os

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
_logger = logging.getLogger(__name__)

def test_mcp_connection():
    """Test the connection to the MCP server."""
    # Use a default server URL or get it from environment variable
    server_url = os.environ.get('MCP_SERVER_URL', 'http://host.docker.internal:8000')
    _logger.info(f"Using MCP server URL: {server_url}")
    _logger.info(f"Testing connection to MCP server at {server_url}")

    try:
        # Test the health endpoint
        _logger.info("1. Testing health endpoint...")
        response = requests.get(f"{server_url}/health", timeout=5)
        response.raise_for_status()
        health_data = response.json() if response.headers.get('content-type') == 'application/json' else {'status': response.status_code}
        _logger.info(f"✅ Successfully connected to MCP server at {server_url}")
        _logger.info(f"   Health status: {health_data}")

        # Test the chat endpoint
        _logger.info("\n2. Testing chat endpoint...")
        test_query = "What is the Indian Contract Act?"
        _logger.info(f"   Sending query: '{test_query}'")

        response = requests.post(
            f"{server_url}/chat",
            json={
                'query': test_query,
                'agent_type': 'general',
                'use_web_search': True,
                'use_document_search': True
            },
            timeout=30
        )
        response.raise_for_status()
        chat_result = response.json()

        # Extract and display a snippet of the response
        response_text = chat_result.get('response', '')
        response_snippet = response_text[:150] + '...' if len(response_text) > 150 else response_text
        _logger.info(f"✅ Successfully received response from chat endpoint")
        _logger.info(f"   Response snippet: '{response_snippet}'")

        # Test the analyze_case endpoint
        _logger.info("\n3. Testing analyze_case endpoint...")
        case_text = "This is a test case about a property dispute between neighbors over a boundary wall."
        _logger.info(f"   Analyzing case: '{case_text[:50]}...'")

        case_data = {
            'case_id': 1,
            'case_title': "Test Case",
            'case_number': "TEST/2025/001",
            'case_text': case_text,
            'case_type': 'civil',
            'language': 'en',
            'use_online_search': True,
            'enable_tracing': False
        }

        response = requests.post(
            f"{server_url}/analyze_case",
            json={'case_data': case_data},
            timeout=60
        )
        response.raise_for_status()
        analysis_result = response.json()

        # Extract and display a snippet of the analysis
        background = analysis_result.get('background_analysis', '')
        background_snippet = background[:150] + '...' if len(background) > 150 else background
        _logger.info(f"✅ Successfully received response from analyze_case endpoint")
        _logger.info(f"   Background analysis snippet: '{background_snippet}'")

        # Test the search_documents endpoint
        _logger.info("\n4. Testing search_documents endpoint...")
        search_query = "Property rights in India"
        _logger.info(f"   Searching for: '{search_query}'")

        payload = {
            'query': search_query,
            'max_results': 5,
            'language': 'en',
            'enable_tracing': False
        }

        response = requests.post(
            f"{server_url}/search_documents",
            json=payload,
            timeout=30
        )
        response.raise_for_status()
        search_results = response.json()

        # Display the number of results and a snippet of the first result
        num_results = len(search_results)
        _logger.info(f"✅ Successfully received response from search_documents endpoint")
        _logger.info(f"   Found {num_results} document results")

        if num_results > 0:
            first_result = search_results[0]
            document = first_result.get('document', 'Unknown')
            content = first_result.get('content', '')
            content_snippet = content[:150] + '...' if len(content) > 150 else content
            _logger.info(f"   First result - Document: '{document}'")
            _logger.info(f"   Content snippet: '{content_snippet}'")

        _logger.info("\n✅ All tests passed! MCP server is working correctly.")
        return True

    except requests.exceptions.RequestException as e:
        _logger.error(f"❌ Error connecting to MCP server: {str(e)}")
        return False

# Run the test
success = test_mcp_connection()
if not success:
    sys.exit(1)  # Exit with error code
