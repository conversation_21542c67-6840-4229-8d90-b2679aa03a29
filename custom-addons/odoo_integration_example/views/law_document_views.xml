<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Law Document Form View -->
    <record id="view_law_document_form" model="ir.ui.view">
        <field name="name">law.document.form</field>
        <field name="model">law.document</field>
        <field name="arch" type="xml">
            <form string="Law Document">
                <header>
                    <button name="action_index_document" string="Index Document" type="object" class="oe_highlight" invisible="indexed"/>
                    <button name="action_index_document" string="Re-Index Document" type="object" invisible="not indexed"/>
                    <button name="action_fix_file" string="Fix File" type="object" help="Try to fix corrupted file data"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Document Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="file" filename="file_name"/>
                            <field name="file_name" invisible="1"/>
                            <field name="file_type"/>
                            <field name="language"/>
                        </group>
                        <group>
                            <field name="file_size" widget="integer"/>
                            <field name="page_count" widget="integer"/>
                            <field name="indexed" widget="boolean"/>
                            <field name="active" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Description">
                            <field name="description" placeholder="Enter a description for this document..."/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Law Document list View -->
    <record id="view_law_document_list" model="ir.ui.view">
        <field name="name">law.document.list</field>
        <field name="model">law.document</field>
        <field name="arch" type="xml">
            <list string="Law Documents" decoration-success="indexed" decoration-danger="not indexed">
                <field name="name"/>
                <field name="file_type"/>
                <field name="language"/>
                <field name="file_size"/>
                <field name="page_count"/>
                <field name="indexed" widget="boolean"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <!-- Law Document Search View -->
    <record id="view_law_document_search" model="ir.ui.view">
        <field name="name">law.document.search</field>
        <field name="model">law.document</field>
        <field name="arch" type="xml">
            <search string="Search Law Documents">
                <field name="name"/>
                <field name="file_name"/>
                <field name="description"/>
                <filter string="PDF" name="pdf" domain="[('file_type', '=', 'pdf')]"/>
                <filter string="Text" name="txt" domain="[('file_type', '=', 'txt')]"/>
                <filter string="Word" name="docx" domain="[('file_type', '=', 'docx')]"/>
                <separator/>
                <filter string="English" name="en" domain="[('language', '=', 'en')]"/>
                <filter string="Gujarati" name="gu" domain="[('language', '=', 'gu')]"/>
                <filter string="Hindi" name="hi" domain="[('language', '=', 'hi')]"/>
                <separator/>
                <filter string="Indexed" name="indexed" domain="[('indexed', '=', True)]"/>
                <filter string="Not Indexed" name="not_indexed" domain="[('indexed', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="File Type" name="group_by_file_type" context="{'group_by': 'file_type'}"/>
                    <filter string="Language" name="group_by_language" context="{'group_by': 'language'}"/>
                    <filter string="Indexed" name="group_by_indexed" context="{'group_by': 'indexed'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Law Document Action -->
    <record id="action_law_document" model="ir.actions.act_window">
        <field name="name">Law Documents</field>
        <field name="res_model">law.document</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_law_document_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Upload your first legal document
            </p>
            <p>
                Upload PDF, TXT, or DOCX files to be indexed and used by the Law Agent.
            </p>
        </field>
    </record>

    <!-- Update Vector Store Action -->
    <record id="action_update_vector_store" model="ir.actions.server">
        <field name="name">Update Vector Store</field>
        <field name="model_id" ref="model_law_document"/>
        <field name="binding_model_id" ref="model_law_document"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = model.action_update_vector_store()</field>
    </record>

    <!-- Fix Corrupted Files Action -->
    <record id="action_fix_corrupted_files" model="ir.actions.server">
        <field name="name">Fix Corrupted Files</field>
        <field name="model_id" ref="model_law_document"/>
        <field name="binding_model_id" ref="model_law_document"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = model.action_fix_corrupted_files()</field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_law_document"
              name="Documents"
              parent="menu_law_agent_root"
              action="action_law_document"
              sequence="20"/>
</odoo>
