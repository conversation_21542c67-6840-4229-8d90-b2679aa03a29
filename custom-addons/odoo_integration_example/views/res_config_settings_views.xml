<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.inherit.law.agent</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="70"/>
        <field name="type">form</field>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//setting[@id='access_rights']" position="after">
                <setting id="law_agent_settings" string="Law Agent" help="Configure Law Agent settings">
                    <field name="law_agent_server_url" placeholder="http://localhost:8000"/>
                    <div class="content-group">
                        <div class="mt8">
                            <field name="law_agent_enable_web_search"/>
                            <label for="law_agent_enable_web_search" string="Enable Web Search"/>
                        </div>
                        <div class="mt8">
                            <field name="law_agent_enable_document_search"/>
                            <label for="law_agent_enable_document_search" string="Enable Document Search"/>
                        </div>
                    </div>
                </setting>

                <setting id="law_agent_opik_settings" string="Opik Tracing" help="Configure Opik tracing for LangGraph flows">
                    <field name="law_agent_enable_opik_tracing"/>
                    <div class="content-group" invisible="not law_agent_enable_opik_tracing">
                        <div class="mt8">
                            <label for="law_agent_opik_api_key" string="API Key"/>
                            <field name="law_agent_opik_api_key" password="True"/>
                        </div>
                        <div class="mt8">
                            <label for="law_agent_opik_workspace" string="Workspace"/>
                            <field name="law_agent_opik_workspace"/>
                        </div>
                    </div>
                </setting>
            </xpath>
        </field>
    </record>

    <record id="action_law_agent_config_settings" model="ir.actions.act_window">
        <field name="name">Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'odoo_integration_example'}</field>
    </record>

    <menuitem id="menu_law_agent_settings" name="Settings" parent="menu_law_agent_configuration" action="action_law_agent_config_settings" sequence="10"/>

</odoo>
