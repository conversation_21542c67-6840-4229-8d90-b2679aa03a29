<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Law Agent Message Views -->
    <record id="view_law_agent_message_list" model="ir.ui.view">
        <field name="name">law.agent.message.list</field>
        <field name="model">law.agent.message</field>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list string="Messages">
                <field name="create_date"/>
                <field name="is_user"/>
                <field name="content"/>
            </list>
        </field>
    </record>

    <record id="view_law_agent_message_form" model="ir.ui.view">
        <field name="name">law.agent.message.form</field>
        <field name="model">law.agent.message</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Message">
                <group>
                    <field name="is_user"/>
                    <field name="content"/>
                    <field name="create_date" readonly="1"/>
                </group>
            </form>
        </field>
    </record>

    <!-- Law Agent Chat Views -->
    <record id="view_law_agent_chat_form" model="ir.ui.view">
        <field name="name">law.agent.chat.form</field>
        <field name="model">law.agent.chat</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Law Agent Chat">
                <header>
                    <button name="%(action_law_agent_question_wizard)d" string="Ask Question" type="action" class="oe_highlight" context="{'default_chat_id': id}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Chat Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="user_id"/>
                            <field name="agent_type"/>
                        </group>
                        <group>
                            <field name="use_web_search"/>
                            <field name="use_document_search"/>
                            <field name="active" invisible="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Conversation">
                            <field name="conversation"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="view_law_agent_chat_list" model="ir.ui.view">
        <field name="name">law.agent.chat.list</field>
        <field name="model">law.agent.chat</field>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list string="Law Agent Chats">
                <field name="name"/>
                <field name="user_id"/>
                <field name="agent_type"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <record id="view_law_agent_chat_search" model="ir.ui.view">
        <field name="name">law.agent.chat.search</field>
        <field name="model">law.agent.chat</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search string="Search Law Agent Chats">
                <field name="name"/>
                <field name="user_id"/>
                <field name="agent_type"/>
                <filter string="My Chats" name="my_chats" domain="[('user_id', '=', uid)]"/>
                <filter string="General Legal" name="general" domain="[('agent_type', '=', 'general')]"/>
                <filter string="Gujarat Law" name="gujarat" domain="[('agent_type', '=', 'gujarat')]"/>
                <filter string="Land Law" name="land" domain="[('agent_type', '=', 'land')]"/>
                <filter string="Civil Law" name="civil" domain="[('agent_type', '=', 'civil')]"/>
                <group expand="0" string="Group By">
                    <filter string="User" name="user" context="{'group_by': 'user_id'}"/>
                    <filter string="Agent Type" name="agent_type" context="{'group_by': 'agent_type'}"/>
                    <filter string="Month" name="month" context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_law_agent_chat" model="ir.actions.act_window">
        <field name="name">Law Agent Chats</field>
        <field name="res_model">law.agent.chat</field>
        <field name="view_mode">list,form</field>
        <field name="context">{'search_default_my_chats': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Law Agent Chat
            </p>
            <p>
                Ask legal questions and get AI-powered answers.
            </p>
        </field>
    </record>

    <!-- Law Agent Case Analysis Views -->
    <record id="view_law_agent_case_analysis_form" model="ir.ui.view">
        <field name="name">law.agent.case.analysis.form</field>
        <field name="model">law.agent.case.analysis</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Case Analysis">
                <header>
                    <button name="action_analyze" string="Analyze Case" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                    <field name="state" widget="statusbar" statusbar_visible="draft,analyzing,done"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Case Title"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="case_number"/>
                            <field name="case_type"/>
                            <field name="language"/>
                        </group>
                        <group>
                            <field name="use_online_search"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Case Text">
                            <field name="case_text" placeholder="Enter the case details here..." readonly="state != 'draft'"/>
                        </page>
                        <page string="Analysis Results" invisible="state == 'draft'">
                            <group>
                                <field name="background_analysis" widget="html"/>
                                <field name="legal_issues" widget="html"/>
                                <field name="evidence_analysis" widget="html"/>
                                <field name="legal_principles" widget="html"/>
                                <field name="judgment_prediction" widget="html"/>
                            </group>
                        </page>
                        <page string="Full Analysis" invisible="state == 'draft'">
                            <field name="full_analysis" widget="html"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <record id="view_law_agent_case_analysis_list" model="ir.ui.view">
        <field name="name">law.agent.case.analysis.list</field>
        <field name="model">law.agent.case.analysis</field>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list string="Case Analyses">
                <field name="name"/>
                <field name="case_number"/>
                <field name="case_type"/>
                <field name="language"/>
                <field name="state"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <record id="view_law_agent_case_analysis_search" model="ir.ui.view">
        <field name="name">law.agent.case.analysis.search</field>
        <field name="model">law.agent.case.analysis</field>
        <field name="type">search</field>
        <field name="arch" type="xml">
            <search string="Search Case Analyses">
                <field name="name"/>
                <field name="case_number"/>
                <field name="case_type"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Analyzing" name="analyzing" domain="[('state', '=', 'analyzing')]"/>
                <filter string="Completed" name="done" domain="[('state', '=', 'done')]"/>
                <filter string="Error" name="error" domain="[('state', '=', 'error')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="state" context="{'group_by': 'state'}"/>
                    <filter string="Case Type" name="case_type" context="{'group_by': 'case_type'}"/>
                    <filter string="Language" name="language" context="{'group_by': 'language'}"/>
                    <filter string="Month" name="month" context="{'group_by': 'create_date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <record id="action_law_agent_case_analysis" model="ir.actions.act_window">
        <field name="name">Case Analyses</field>
        <field name="res_model">law.agent.case.analysis</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Case Analysis
            </p>
            <p>
                Analyze legal cases with AI assistance.
            </p>
        </field>
    </record>

    <!-- Law Agent Document Search Views -->
    <record id="view_law_agent_document_search_form" model="ir.ui.view">
        <field name="name">law.agent.document.search.form</field>
        <field name="model">law.agent.document.search</field>
        <field name="type">form</field>
        <field name="arch" type="xml">
            <form string="Document Search">
                <header>
                    <button name="action_search" string="Search Documents" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="name" placeholder="Search Query"/>
                            <field name="language"/>
                        </group>
                        <group>
                            <field name="max_results"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Search Results">
                            <field name="result_ids">
                                <list string="Results">
                                    <field name="document"/>
                                    <field name="page"/>
                                    <field name="score"/>
                                    <field name="content"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <record id="view_law_agent_document_search_list" model="ir.ui.view">
        <field name="name">law.agent.document.search.list</field>
        <field name="model">law.agent.document.search</field>
        <field name="type">list</field>
        <field name="arch" type="xml">
            <list string="Document Searches">
                <field name="name"/>
                <field name="language"/>
                <field name="max_results"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <record id="action_law_agent_document_search" model="ir.actions.act_window">
        <field name="name">Document Search</field>
        <field name="res_model">law.agent.document.search</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first Document Search
            </p>
            <p>
                Search for legal documents using AI.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_law_agent_root" name="Law Agent" sequence="100" web_icon="odoo_integration_example,static/description/icon.png"/>

    <menuitem id="menu_law_agent_chat" name="Chats" parent="menu_law_agent_root" action="action_law_agent_chat" sequence="10"/>
    <menuitem id="menu_law_agent_case_analysis" name="Case Analyses" parent="menu_law_agent_root" action="action_law_agent_case_analysis" sequence="20"/>
    <menuitem id="menu_law_agent_document_search" name="Document Search" parent="menu_law_agent_root" action="action_law_agent_document_search" sequence="30"/>

    <menuitem id="menu_law_agent_configuration" name="Configuration" parent="menu_law_agent_root" sequence="100"/>
</odoo>
