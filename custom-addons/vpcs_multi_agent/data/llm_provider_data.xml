<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="1">
    <!-- Groq Default Sql Agent Provider -->
    <record id="llm_provider_groq_default" model="llm.provider">
        <field name="name">Sql Agent-Groq</field>
        <field name="provider_type">groq</field>
        <field name="model">gemma2-9b-it</field>
        <field name="base_url">https://api.groq.com/v1</field>
        <field name="timeout">30</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
        <field name="is_sql_agent">true</field>
    </record>
    <!-- OpenAI Default Sql Agent Provider -->
    <record id="llm_provider_openai_default" model="llm.provider">
        <field name="name">SqlAgent-Openai</field>
        <field name="provider_type">openai</field>
        <field name="model">gpt-4o</field>
        <field name="base_url">https://api.openai.com/v1</field>
        <field name="timeout">60</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
        <field name="is_sql_agent">true</field>
    </record>
    <!-- Google Gemini Default Sql Agent Provider -->
    <record id="llm_provider_google_default" model="llm.provider">
        <field name="name">SqlAgent-Google</field>
        <field name="provider_type">google</field>
        <field name="model">gemini-1.5-flash</field>
        <field name="timeout">60</field>
        <field name="max_retries">3</field>
        <field name="retry_delay">1</field>
        <field name="active">true</field>
        <field name="is_sql_agent">true</field>
    </record>
</odoo>