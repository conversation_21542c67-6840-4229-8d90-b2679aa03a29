<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="partner_chatgpt" model="res.partner">
            <field name="name">VPCS AI Agent 🚀</field>
        </record>

        <record id="user_chatgpt" model="res.users">
            <field name="login">aiagent</field>
            <field name="password">aiagent</field>
            <field name="partner_id" ref="vpcs_multi_agent.partner_chatgpt"/>
            <field name="company_id" ref="base.main_company"/>
            <field name="company_ids" eval="[Command.link(ref('base.main_company'))]"/>
            <field name="groups_id" eval="[Command.link(ref('base.group_user'))]"/>
        </record>
    </data>
</odoo>