<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_sql_database_agent_tree" model="ir.ui.view">
        <field name="name">sql.database.agent.tree</field>
        <field name="model">sql.database.agent</field>
        <field name="arch" type="xml">
            <list>
                <field name="create_date"/>
                <field name="llm_wrapper"/>
                <!-- <field name="model_name"/> -->
                <field name="active" widget="boolean_toggle"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_sql_database_agent_form" model="ir.ui.view">
        <field name="name">sql.database.agent.form</field>
        <field name="model">sql.database.agent</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <widget name="web_ribbon" title="Archived" invisible="active"/>
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="create_date"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="llm_wrapper" options="{'no_create': True}" domain="[('is_sql_agent', '=', True)]"/>
                            <!-- <field name="model_name"/> -->
                        </group>
                        <group>
                            <field name="allowed_tables" widget="many2many_tags"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Query History" name="query_history">
                            <field name="message_ids"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_sql_database_agent_search" model="ir.ui.view">
        <field name="name">sql.database.agent.search</field>
        <field name="model">sql.database.agent</field>
        <field name="arch" type="xml">
            <search>
                <field name="create_date"/>
                <field name="llm_wrapper"/>
                <!-- <field name="model_name"/> -->
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <group expand="0" string="Group By">
                    <filter string="LLM Provider" name="group_by_provider" context="{'group_by': 'llm_wrapper'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_sql_database_agent" model="ir.actions.act_window">
        <field name="name">SQL Agents</field>
        <field name="res_model">sql.database.agent</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_sql_database_agent_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first SQL Agent!
            </p>
            <p>
                SQL Agents help you generate and execute SQL queries using natural language.
            </p>
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_sql_database_agent_root"
        name="SQL Agents"
        parent="vpcs_llm_provider.menu_llm_provider_configuration"
        sequence="20"/>

    <menuitem id="menu_sql_database_agent"
        name="Agents"
        parent="menu_sql_database_agent_root"
        action="action_sql_database_agent"
        sequence="10"/>
</odoo>
