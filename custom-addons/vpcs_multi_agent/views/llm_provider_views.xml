<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_llm_provider_form_inherited" model="ir.ui.view">
        <field name="name">llm.provider.form.inherited</field>
        <field name="model">llm.provider</field>
        <field name="inherit_id" ref="vpcs_llm_provider.view_llm_provider_form" />
        <field name="arch" type="xml">
            <field name="base_url" position="after">
                <field name="is_sql_agent" />
                <field name="is_chat_model" invisible="not is_sql_agent" />
                <field name="embedding_model" invisible="not is_sql_agent" />
                <field name="temperature" invisible="not is_sql_agent" />
                <field name="max_tokens" invisible="not is_sql_agent" />
                <field name="context_window" invisible="not is_sql_agent" />
            </field>
            <xpath expr="//notebook[last()]" position="inside">
                <page string="Sql Agent Instructions" name="sql_agent_instructions" 
                    invisible="not is_sql_agent">
                    <group>
                        <field name="agent_instructions" 
                            placeholder="You can add any example or instructions to the agent so agent will give you more accurate result" />
                    </group>
                </page>
            </xpath>
        </field>
    </record>
</odoo>
