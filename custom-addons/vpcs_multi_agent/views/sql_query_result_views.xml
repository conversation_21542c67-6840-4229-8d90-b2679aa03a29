<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Tree View -->
    <record id="view_sql_query_result_tree" model="ir.ui.view">
        <field name="name">sql.query.result.tree</field>
        <field name="model">sql.query.result</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="create_date"/>
                <field name="record_count"/>
                <field name="total_amount"/>
                <field name="result_summary"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_sql_query_result_form" model="ir.ui.view">
        <field name="name">sql.query.result.form</field>
        <field name="model">sql.query.result</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_validate" string="Validate" type="object" 
                            class="oe_highlight" invisible="state != 'draft'"/>
                    <button name="action_archive" string="Archive" type="object" 
                            invisible="state != 'validated'"/>
                    <button name="action_draft" string="Set to Draft" type="object" 
                            invisible="state not in ('validated', 'archived')"/>
                    <button name="action_view_table" 
                            string="View Table" 
                            type="object" 
                            class="oe_highlight"
                            invisible="not result_data"/>
                    <field name="state" widget="statusbar"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Query Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="create_date" readonly="1"/>
                            <field name="create_uid" readonly="1"/>
                            <field name="dashboard_url" widget="url" invisible="not dashboard_url"/>
                            <field name="llm_provider_id"/>
                        </group>
                        <group>
                            <field name="record_count"/>
                            <field name="total_amount"/>
                            <field name="result_summary"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Query">
                            <field name="query_text"/>
                        </page>
                        <page string="Results">
                            <field name="result_data"/>
                            <field name="result_summary"/>
                            <field name="dashboard_url" widget="url"/>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_sql_query_results" model="ir.actions.act_window">
        <field name="name">SQL Query Results</field>
        <field name="res_model">sql.query.result</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No query results found
            </p>
            <p>
                Use the SQL Query Generator to create new queries
            </p>
        </field>
    </record>

    <!-- Menu -->
    <menuitem id="menu_sql_query_results"
              name="SQL Query Results"
              parent="vpcs_llm_provider.menu_vpcs_llm_provider"
              action="action_sql_query_results"
              sequence="20"/>

    <!-- Search View -->
    <record id="view_sql_query_result_search" model="ir.ui.view">
        <field name="name">sql.query.result.search</field>
        <field name="model">sql.query.result</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="create_uid"/>
                <field name="company_id" groups="base.group_multi_company"/>
                <filter string="My Queries" name="my_queries" 
                        domain="[('create_uid', '=', uid)]"/>
                <filter string="Draft" name="draft" 
                        domain="[('state', '=', 'draft')]"/>
                <filter string="Validated" name="validated" 
                        domain="[('state', '=', 'validated')]"/>
                <filter string="Archived" name="archived" 
                        domain="[('state', '=', 'archived')]"/>
                <group expand="0" string="Group By">
                    <filter string="Status" name="group_by_state" 
                            context="{'group_by': 'state'}"/>
                    <filter string="Created by" name="group_by_create_uid" 
                            context="{'group_by': 'create_uid'}"/>
                </group>
            </search>
        </field>
    </record>
</odoo>
