<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <template id="query_table_template" name="Query Results Table">
        <t t-call="website.layout">
            <t t-set="title" t-value="page_name"/>
            
            <!-- CSS Assets -->
            <link rel="stylesheet" type="text/css" href="/web/static/lib/bootstrap/css/bootstrap.min.css"/>
            
            <!-- Custom CSS for better responsiveness -->
            <style>
                /* Card layout for mobile */
                .mobile-card {
                    display: none;
                }
                
                /* Desktop table styling */
                .desktop-table {
                    display: block;
                    width: 100%;
                    overflow-x: auto;
                }
                
                /* Table with many columns */
                .wide-table {
                    min-width: 100%;
                    width: auto;
                }
                
                /* For tables with many columns */
                @media (min-width: 768px) {
                    .desktop-table {
                        overflow-x: auto;
                        -webkit-overflow-scrolling: touch;
                    }
                    .wide-table th, .wide-table td {
                        white-space: nowrap;
                        min-width: 150px;
                    }
                    /* Special styling for first column to keep it visible */
                    .wide-table th:first-child, .wide-table td:first-child {
                        position: sticky;
                        left: 0;
                        background-color: #f8f9fa;
                        z-index: 1;
                        border-right: 2px solid #dee2e6;
                    }
                    /* Shadow for sticky column */
                    .wide-table td:first-child {
                        background-color: #ffffff;
                    }
                }
                
                /* Mobile layout */
                @media (max-width: 767px) {
                    .mobile-card {
                        display: block;
                        margin-bottom: 15px;
                        border: 1px solid #dee2e6;
                        border-radius: 0.25rem;
                    }
                    .mobile-card-header {
                        background-color: #f8f9fa;
                        padding: 8px 12px;
                        border-bottom: 1px solid #dee2e6;
                        font-weight: bold;
                    }
                    .mobile-card-body {
                        padding: 12px;
                    }
                    .mobile-card-item {
                        display: flex;
                        border-bottom: 1px solid #f0f0f0;
                        padding: 6px 0;
                    }
                    .mobile-card-item:last-child {
                        border-bottom: none;
                    }
                    .mobile-card-label {
                        width: 40%;
                        font-weight: 500;
                        color: #666;
                    }
                    .mobile-card-value {
                        width: 60%;
                        word-break: break-word;
                    }
                    .desktop-table {
                        display: none;
                    }
                }
                
                /* Common styles */
                .pagination-controls {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    margin-top: 15px;
                }
                .badge {
                    padding: 8px 12px;
                    font-size: 0.9rem;
                }
                @media (max-width: 767px) {
                    .header-info {
                        flex-direction: column;
                        align-items: flex-start !important;
                    }
                    .header-badges {
                        margin-top: 10px;
                    }
                    .pagination-controls {
                        flex-direction: column;
                        align-items: flex-start;
                    }
                    .pagination-buttons {
                        margin-top: 10px;
                        width: 100%;
                        overflow-x: auto;
                    }
                }
            </style>
            
            <div class="container-fluid py-3">
                <!-- Header Section -->
                <div class="row mb-4">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center header-info">
                            <div>
                                <h2 class="mb-1" t-esc="query.name"/>
                                <p class="text-muted mb-0">
                                    Created on <t t-esc="query.create_date"/> by <t t-esc="query.create_uid.name"/>
                                </p>
                            </div>
                            <div class="header-badges">
                                <span class="badge bg-primary me-2">
                                    <t t-esc="len(data)"/> Records
                                </span>
                                <t t-if="query.total_amount">
                                    <span class="badge bg-success">
                                        Total: <t t-esc="query.total_amount"/>
                                    </span>
                                </t>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Error Message -->
                <t t-if="error">
                    <div class="alert alert-danger" role="alert">
                        <i class="fa fa-exclamation-triangle me-2"/> <t t-esc="error"/>
                    </div>
                </t>

                <!-- Data Table -->
                <t t-if="not error and data">
                    <div class="card shadow-sm">
                        <div class="card-body px-md-4 py-md-3 px-2 py-2">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <span class="text-muted">
                                        Showing <strong><t t-esc="(current_page-1) * page_size + 1"/></strong> to 
                                        <strong><t t-esc="min(current_page * page_size, len(data))"/></strong> of 
                                        <strong><t t-esc="len(data)"/></strong> entries
                                    </span>
                                </div>
                            </div>
                            
                            <!-- Table (Desktop View) -->
                            <div class="desktop-table">
                                <table class="table table-striped table-bordered wide-table">
                                    <thead class="table-light">
                                        <tr>
                                            <t t-foreach="columns" t-as="column">
                                                <th t-esc="column"/>
                                            </t>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <t t-set="start_idx" t-value="(current_page-1) * page_size"/>
                                        <t t-set="end_idx" t-value="min(start_idx + page_size, len(data))"/>
                                        <t t-foreach="data[start_idx:end_idx]" t-as="row">
                                            <tr>
                                                <t t-foreach="columns" t-as="column">
                                                    <td>
                                                        <t t-if="isinstance(row.get(column), (int, float))" t-set="value" t-value="'{:,.2f}'.format(row.get(column))"/>
                                                        <t t-elif="isinstance(row.get(column), (datetime.date, datetime.datetime))" t-set="value" t-value="row.get(column).strftime('%Y-%m-%d %H:%M:%S')"/>
                                                        <t t-elif="row.get(column) == 'N/A' or row.get(column) is None or row.get(column) == ''" t-set="value" t-value="'-'"/>
                                                        <t t-else="" t-set="value" t-value="row.get(column)"/>
                                                        <span t-esc="value" t-att-title="value"/>
                                                    </td>
                                                </t>
                                            </tr>
                                        </t>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- Card Layout (Mobile View) -->
                            <div class="mobile-cards">
                                <t t-set="start_idx" t-value="(current_page-1) * page_size"/>
                                <t t-set="end_idx" t-value="min(start_idx + page_size, len(data))"/>
                                <t t-foreach="data[start_idx:end_idx]" t-as="row" t-key="row_index">
                                    <div class="mobile-card">
                                        <div class="mobile-card-header">
                                            <t t-if="'name' in columns">
                                                <t t-esc="row.get('name')"/>
                                            </t>
                                            <t t-elif="'id' in columns">
                                                ID: <t t-esc="row.get('id')"/>
                                            </t>
                                            <t t-else="">
                                                Record #<t t-esc="start_idx + row_index + 1"/>
                                            </t>
                                        </div>
                                        <div class="mobile-card-body">
                                            <t t-foreach="columns" t-as="column">
                                                <div class="mobile-card-item">
                                                    <div class="mobile-card-label">
                                                        <t t-esc="column"/>
                                                    </div>
                                                    <div class="mobile-card-value">
                                                        <t t-if="isinstance(row.get(column), (int, float))" t-set="value" t-value="'{:,.2f}'.format(row.get(column))"/>
                                                        <t t-elif="isinstance(row.get(column), (datetime.date, datetime.datetime))" t-set="value" t-value="row.get(column).strftime('%Y-%m-%d %H:%M:%S')"/>
                                                        <t t-elif="row.get(column) == 'N/A' or row.get(column) is None or row.get(column) == ''" t-set="value" t-value="'-'"/>
                                                        <t t-else="" t-set="value" t-value="row.get(column)"/>
                                                        <span t-esc="value"/>
                                                    </div>
                                                </div>
                                            </t>
                                        </div>
                                    </div>
                                </t>
                            </div>
                            
                            <!-- Simple server-side pagination buttons -->
                            <div class="pagination-controls">
                                <div>
                                    <form t-attf-action="/web/query/table/#{query.id}" method="get" class="form-inline">
                                        <label for="page_size" class="me-2">Show</label>
                                        <select name="page_size" id="page_size" class="form-select form-select-sm me-2" onchange="this.form.submit()">
                                            <option value="10" t-att-selected="page_size == 10">10</option>
                                            <option value="25" t-att-selected="page_size == 25">25</option>
                                            <option value="50" t-att-selected="page_size == 50">50</option>
                                            <option value="100" t-att-selected="page_size == 100">100</option>
                                        </select>
                                        <!-- Keep track of current page when changing page size -->
                                        <input type="hidden" name="page" t-att-value="1"/>
                                        <span>entries</span>
                                    </form>
                                </div>
                                
                                <div class="pagination-buttons">
                                    <t t-if="total_pages > 1">
                                        <nav aria-label="Page navigation">
                                            <ul class="pagination pagination-sm mb-0">
                                                <li t-att-class="'page-item ' + (current_page == 1 and 'disabled' or '')">
                                                    <a class="page-link" t-attf-href="/web/query/table/#{query.id}?page=#{current_page-1}&amp;page_size=#{page_size}">Previous</a>
                                                </li>
                                                
                                                <!-- First page -->
                                                <li t-att-class="'page-item ' + (current_page == 1 and 'active' or '')">
                                                    <a class="page-link" t-attf-href="/web/query/table/#{query.id}?page=1&amp;page_size=#{page_size}">1</a>
                                                </li>
                                                
                                                <!-- Ellipsis if needed -->
                                                <t t-if="current_page > 3">
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                </t>
                                                
                                                <!-- Pages around current -->
                                                <t t-foreach="range(max(2, current_page - 1), min(current_page + 2, total_pages))" t-as="page">
                                                    <li t-att-class="'page-item ' + (page == current_page and 'active' or '')">
                                                        <a class="page-link" t-attf-href="/web/query/table/#{query.id}?page=#{page}&amp;page_size=#{page_size}" t-esc="page"/>
                                                    </li>
                                                </t>
                                                
                                                <!-- Ellipsis if needed -->
                                                <t t-if="current_page &lt; total_pages - 2">
                                                    <li class="page-item disabled">
                                                        <span class="page-link">...</span>
                                                    </li>
                                                </t>
                                                
                                                <!-- Last page if not already shown -->
                                                <t t-if="total_pages > 1 and current_page &lt; total_pages - 1">
                                                    <li t-att-class="'page-item ' + (current_page == total_pages and 'active' or '')">
                                                        <a class="page-link" t-attf-href="/web/query/table/#{query.id}?page=#{total_pages}&amp;page_size=#{page_size}" t-esc="total_pages"/>
                                                    </li>
                                                </t>
                                                
                                                <li t-att-class="'page-item ' + (current_page == total_pages and 'disabled' or '')">
                                                    <a class="page-link" t-attf-href="/web/query/table/#{query.id}?page=#{current_page+1}&amp;page_size=#{page_size}">Next</a>
                                                </li>
                                            </ul>
                                        </nav>
                                    </t>
                                </div>
                            </div>
                        </div>
                    </div>
                </t>

                <!-- No Data Message -->
                <t t-if="not error and not data">
                    <div class="alert alert-info" role="alert">
                        <i class="fa fa-info-circle me-2"/> No data available
                    </div>
                </t>
            </div>
        </t>
    </template>
</odoo> 