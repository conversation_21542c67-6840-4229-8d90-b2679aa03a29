# Odoo 18 Coding Standards

## Global Rules for All Future Code Updates

### 1. View Changes
- **Replace `attrs` with direct attributes**: Use direct syntax instead of `attrs` dictionary
- **Simplified `invisible` syntax**: Use `invisible="field == value"` instead of `invisible="[('field', '=', value)]"`
- **Replace `<tree>` with `<list>`**: All tree views must use `<list>` tag
- **Use `<chatter>` tag**: Replace chatter-related groups with single `<chatter>` tag

### 2. Field Definitions
- Use proper field types for Odoo 18
- Follow new widget naming conventions
- Use `readonly` instead of `attrs` for readonly fields
- **Replace `widget="ace"` with `widget="text"`**: ACE editor widget options changed in Odoo 18

### 3. JavaScript (Odoo 18 Format)
- **Use service registry instead of patching**: Create services with `registry.category("services").add()`
- **Avoid WebClient patching**: Don't patch core components like WebClient
- **Use proper module imports**: `import { registry } from "@web/core/registry"`
- **Service dependencies**: Declare dependencies like `["rpc", "notification"]`
- **Proper service structure**: Use `start(env, { rpc, notification })` format
- **Module declaration**: Always start with `/** @odoo-module **/`

### 4. XML Templates (QWeb)
- **No empty `t-else` attributes**: Use `t-if="!condition"` instead of `t-else`
- **Proper attribute values**: All attributes must have values in XML
- **Icon titles required**: All FontAwesome icons need `title` attributes
- **Proper XML escaping**: Use `&lt;`, `&gt;`, `&amp;`, `&quot;`, `&apos;`
- **Valid OWL syntax**: Use `owl="1"` for OWL components
- **Template naming**: Use descriptive template names with module prefix

### 5. Security
- Always define proper access rights
- Use appropriate groups for permissions

### 5. Data Files
- Use `noupdate="1"` for demo data
- Proper XML formatting and validation
- **Cron Jobs (ir.cron)**: Remove deprecated `numbercall` and `doall` fields in Odoo 18

## Examples

### Old (Odoo 17 and below):
```xml
<tree string="Records">
    <field name="name" attrs="{'invisible': [('state', '=', 'draft')]}"/>
</tree>

<group>
    <group>
        <field name="message_follower_ids" widget="mail_followers"/>
        <field name="activity_ids" widget="mail_activity"/>
    </group>
    <group>
        <field name="message_ids" widget="mail_thread"/>
    </group>
</group>

<field name="parameters" widget="ace" options="{'mode': 'json'}"/>
```

### New (Odoo 18):
```xml
<list string="Records">
    <field name="name" invisible="state == 'draft'"/>
</list>

<chatter/>

<field name="parameters" widget="text" placeholder="Enter JSON parameters here..."/>
```

### JavaScript Service Example (Odoo 18):
```javascript
/** @odoo-module **/

import { registry } from "@web/core/registry";
import { _t } from "@web/core/l10n/translation";

const myCustomService = {
    dependencies: ["orm", "notification"],
    
    start(env, { orm, notification }) {
        return {
            async doSomething() {
                const result = await orm.call(
                    "my.model",
                    "my_method",
                    []
                );
                return result;
            }
        };
    },
};

registry.category("services").add("my_custom_service", myCustomService);
```

### XML Template Examples (Odoo 18):
```xml
<!-- Wrong -->
<div t-else class="default-avatar">
    <i class="fa fa-user"/>
</div>

<!-- Correct -->
<div t-if="!props.partner_avatar" class="default-avatar">
    <i class="fa fa-user" title="User Avatar"/>
</div>

<!-- Wrong -->
<span>Price > 100</span>

<!-- Correct -->
<span>Price &gt; 100</span>
```

## XML Validation Tools

### Online RelaxNG Validator
Use https://www.liquid-technologies.com/online-relaxng-validator to validate XML files:

1. **XML Content**: Copy your Odoo XML file content
2. **RelaxNG Schema**: Use Odoo 18 schema from https://github.com/odoo/odoo/blob/18.0/odoo/import_xml.rng
3. **Validate**: Check for schema compliance before deployment

### Common XML Validation Errors
- **"Element odoo has extra content"**: Missing `<data>` wrapper
- **Missing required attributes**: Icons need `title` attributes
- **Invalid nesting**: Check proper XML structure

### Reference
- Blog: https://www.linkedin.com/pulse/nasty-assertion-error-element-odoo-has-extra-content-malekinejad/
- Schema: https://github.com/odoo/odoo/blob/18.0/odoo/import_xml.rng

## XML Special Characters Handling

### Required XML Entity Escaping
- **`<` (less than)**: Use `&lt;`
- **`>` (greater than)**: Use `&gt;`
- **`&` (ampersand)**: Use `&amp;`
- **`"` (double quote)**: Use `&quot;`
- **`'` (single quote)**: Use `&apos;`

### Examples
```xml
<!-- Wrong -->
<li>Go to Settings > Linked Devices</li>
<small>Click "Refresh" if needed</small>
<field domain="[('amount', '>', 100)]"/>

<!-- Correct -->
<li>Go to Settings &gt; Linked Devices</li>
<small>Click &quot;Refresh&quot; if needed</small>
<field domain="[('amount', '&gt;', 100)]"/>
```

### Bootstrap Alert Requirements
- All `alert` classes must have proper ARIA roles:
  - `role="alert"` for critical alerts
  - `role="alertdialog"` for interactive alerts
  - `role="status"` for status updates
- Use `alert-link` class for links within alerts

## File Organization Standards

### Separate Files by Purpose
- **Views**: Create separate files for different view types
  - `model_name_views.xml` - Form, list, search views
  - `model_name_menu_views.xml` - Menu items and actions
  - `model_name_wizard_views.xml` - Wizard views (if any)
- **Data**: Keep data files for initial data only
  - `module_data.xml` - Default records, demo data
  - Use `noupdate="1"` for data that shouldn't be updated
- **Security**: Separate security files
  - `ir.model.access.csv` - Access rights
  - `security_groups.xml` - Security groups (if any)

### File Naming Conventions
- Use snake_case for file names
- Include model name in view files: `whatsapp_message_views.xml`
- Menu files: `module_menu_views.xml` or `model_menu_views.xml`
- Wizard files: `model_wizard_views.xml`

### Manifest File Organization
```python
'data': [
    'security/ir.model.access.csv',        # Security first
    'views/model_views.xml',               # Views
    'views/model_menu_views.xml',          # Menus
    'wizard/model_wizard_views.xml',       # Wizards
    'data/module_data.xml',                # Data last
],
```

## Mandatory Checks Before Code Commit
1. ✅ No `attrs` usage - replaced with direct attributes
2. ✅ Simplified `invisible` syntax - use `field == value` instead of `[('field', '=', value)]`
3. ✅ No `<tree>` tags - replaced with `<list>`
4. ✅ Chatter properly implemented with `<chatter>` tag
5. ✅ No `widget="ace"` with options - use `widget="text"` instead
6. ✅ All JS files registered in manifest
7. ✅ Proper access rights defined
8. ✅ XML validated against Odoo 18 RelaxNG schema
9. ✅ All FontAwesome icons have `title` attributes
10. ✅ Proper `<data>` wrapper in all XML files
11. ✅ **XML special characters properly escaped** (`&lt;`, `&gt;`, `&amp;`, `&quot;`, `&apos;`)
12. ✅ **Bootstrap alerts have proper ARIA roles** (`role="alert"`, `role="alertdialog"`, `role="status"`)
13. ✅ **No emoji or special Unicode characters in XML strings**
14. ✅ **Separate menu files from data files** - Menu items in `*_menu_views.xml`
15. ✅ **Data files only for initial data** - Use `noupdate="1"` for reference data
16. ✅ **No empty t-else attributes** - Use `t-if="!condition"` instead of `t-else`
17. ✅ **XML template validation** - All templates must be valid XML with proper attributes
18. ✅ **Cron jobs without deprecated fields** - No `numbercall` or `doall` fields in `ir.cron` records