from pyexpat.errors import messages
from odoo.exceptions import UserError, ValidationError
from odoo import api, fields, models, _
import logging
from odoo.addons.vpcs_multi_agent.controllers.main import MultiAIChatbotController  


_logger = logging.getLogger(__name__)


class CommunicationChannel(models.Model):
    _inherit = 'discuss.channel'

    provider = fields.Many2one(
        'llm.provider',
        string='Provider', 
        help="Please select llm provider.", 
        domain="[('is_sql_agent', '=', True)]",
        )
    
    def _notify_thread(self, message, msg_vals=False, **kwargs):
        rdata = super(CommunicationChannel, self)._notify_thread(message, msg_vals=msg_vals, **kwargs)
        chatgpt_channel_id = self.env.ref('vpcs_multi_agent.channel_chatgpt')
        user_chatgpt = self.env.ref("vpcs_multi_agent.user_chatgpt")
        partner_chatgpt = self.env.ref("vpcs_multi_agent.partner_chatgpt")
        author_id = msg_vals.get('author_id')
        chatgpt_name = str(partner_chatgpt.name or '') + ', '
        prompt = msg_vals.get('body')
        selected_model = self.provider.id if self.provider else None
        print ('___ prompt : ', prompt);
        print ('___ chatgpt_channel_id : ', chatgpt_channel_id);
        print ('___ partner_chatgpt : ', partner_chatgpt);
        print ('___ chatgpt_name : ', chatgpt_name);
        print ('___ user_chatgpt : ', user_chatgpt);
        print ('___ author_id : ', author_id);
        print ('___ selected_model : ', selected_model);

        if not prompt:
            return rdata
        try:
            if author_id != partner_chatgpt.id and (chatgpt_name in msg_vals.get('record_name', '') or 'ChatGPT,' in msg_vals.get('record_name', '')) and self.channel_type == 'chat':
                res = self._get_chatgpt_response(prompt=prompt, model=selected_model)
                self.with_user(user_chatgpt).message_post(body=res, message_type='comment', subtype_xmlid='mail.mt_comment')
            elif author_id != partner_chatgpt.id and msg_vals.get('model', '') == 'discuss.channel' and msg_vals.get('res_id', 0) == chatgpt_channel_id.id:
                res = self._get_chatgpt_response(prompt=prompt, model=selected_model)
                chatgpt_channel_id.with_user(user_chatgpt).message_post(body=res, message_type='comment', subtype_xmlid='mail.mt_comment')

        except Exception as e:
            # Log or handle exceptions more specifically
            _logger.error("Error in ChatGPT response: %s", e)
            pass

        return rdata

    def _get_chatgpt_response(self, prompt, model):
        """
        Calls the ChatGPT controller method directly to get the response.
        No logic is duplicated here; it uses the same controller logic.
        """
        try:
            controller = MultiAIChatbotController()

            response = controller.generate_text(
                prompt=prompt,
                model=model,
                conversation_history=[]
            )
            return response
        except Exception as e:
            _logger.error("Failed to get ChatGPT response: %s", e)
            return "I'm sorry, I couldn't generate a response at the moment."

