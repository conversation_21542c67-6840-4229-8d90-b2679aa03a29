from odoo import models, fields, api
import json
import uuid
import datetime
from odoo.http import request


class SQLQueryResult(models.Model):
    _name = "sql.query.result"
    _description = "SQL Query Results Dashboard"
    _order = "create_date desc"
    _inherit = ["mail.thread", "mail.activity.mixin"]

    name = fields.Char("Query Name", required=True, tracking=True)
    company_id = fields.Many2one(
        "res.company",
        string="Company",
        required=True,
        default=lambda self: self.env.company,
    )
    query_text = fields.Text("SQL Query", tracking=True)
    result_data = fields.Text("Result Data", help="JSON formatted result data")
    result_summary = fields.Text("Summary", compute="_compute_summary", store=True)
    total_amount = fields.Float(
        "Total Amount", compute="_compute_total_amount", store=True
    )
    record_count = fields.Integer(
        "Record Count", compute="_compute_record_count", store=True
    )
    state = fields.Selection(
        [("draft", "Draft"), ("validated", "Validated"), ("archived", "Archived")],
        string="Status",
        default="draft",
        tracking=True,
    )
    create_date = fields.Datetime("Created On", readonly=True)
    create_uid = fields.Many2one("res.users", string="Created by", readonly=True)
    dashboard_url = fields.Char("Dashboard URL", readonly=True)
    llm_provider_id = fields.Many2one("llm.provider", string="LLM Provider")

    @api.depends("result_data")
    def _compute_summary(self):
        for record in self:
            try:
                data = json.loads(record.result_data or "[]")
                if data:
                    record.result_summary = f"Found {len(data)} records"
                else:
                    record.result_summary = "No data"
            except:
                record.result_summary = "Invalid data format"

    @api.depends("result_data")
    def _compute_total_amount(self):
        for record in self:
            try:
                data = json.loads(record.result_data or "[]")
                record.total_amount = sum(
                    float(item.get("amount_due", 0)) for item in data
                )
            except:
                record.total_amount = 0.0

    @api.depends("result_data")
    def _compute_record_count(self):
        for record in self:
            try:
                data = json.loads(record.result_data or "[]")
                record.record_count = len(data)
            except:
                record.record_count = 0

    def create_dashboard_json(self, results, column_names):
        """Create a spreadsheet dashboard from query results"""
        dashboard_id = str(uuid.uuid4())

        dashboard = {
            "version": 12,
            "sheets": [
                {
                    "id": dashboard_id,
                    "name": "Query Results",
                    "colNumber": len(column_names) + 1,
                    "rowNumber": len(results) + 5,
                    "cells": {},
                    "cols": {},
                    "rows": {},
                }
            ],
        }

        # Add column headers
        for col_idx, col_name in enumerate(column_names):
            col_letter = chr(65 + col_idx)
            dashboard["sheets"][0]["cells"][f"{col_letter}1"] = {
                "content": col_name,
                "style": 1,
                "border": 1,
            }

            dashboard["sheets"][0]["cols"][str(col_idx)] = {"size": 150}

        # Add data cells
        for row_idx, row in enumerate(results, start=2):
            for col_idx, value in enumerate(row):
                col_letter = chr(65 + col_idx)
                cell_value = str(value) if value is not None else ""

                if isinstance(value, (datetime.date, datetime.datetime)):
                    cell_value = value.isoformat()

                dashboard["sheets"][0]["cells"][f"{col_letter}{row_idx}"] = {
                    "content": cell_value,
                    "style": 2,
                }

        # Add pivot table configuration for numeric columns
        numeric_cols = []
        for col_idx, col_name in enumerate(column_names):
            if any(isinstance(row[col_idx], (int, float)) for row in results):
                numeric_cols.append(col_name)

        if numeric_cols:
            dashboard["pivots"] = {
                "1": {
                    "colGroupBys": [],
                    "measures": numeric_cols,
                    "model": "sql.query.result",
                    "domain": [],
                    "context": {"group_by": []},
                }
            }

            # Add chart for numeric data
            dashboard["sheets"][0]["figures"] = [
                {
                    "id": str(uuid.uuid4()),
                    "tag": "chart",
                    "data": {
                        "type": "bar",
                        "dataSets": [
                            {
                                "label": numeric_cols[0],
                                "data": [
                                    row[column_names.index(numeric_cols[0])]
                                    for row in results
                                    if isinstance(
                                        row[column_names.index(numeric_cols[0])],
                                        (int, float),
                                    )
                                ],
                            }
                        ],
                    },
                    "position": {"x": 0, "y": len(results) + 3},
                }
            ]

        return dashboard

    def _create_and_get_dashboard_url(self, dashboard_data):
        """Create dashboard and return its URL"""
        dashboard = (
            self.env["spreadsheet.dashboard"]
            .sudo()
            .create(
                {
                    "name": f"Query Results - {self.name} - {fields.Datetime.now().strftime('%Y-%m-%d %H:%M')}",
                    "spreadsheet_data": json.dumps(dashboard_data),
                    "dashboard_group_id": self.env.ref(
                        "spreadsheet_dashboard.spreadsheet_dashboard_group_finance"
                    ).id,
                }
            )
        )

        base_url = (
            self.env["ir.config_parameter"].sudo().get_param("web.base.url").rstrip("/")
        )
        company_id = self.env.company.id
        menu = self.env.ref(
            "spreadsheet_dashboard.spreadsheet_dashboard_menu_dashboard",
            raise_if_not_found=False,
        )
        menu_id = menu.id if menu else ""
        action_id = self.env.ref("spreadsheet_dashboard.ir_actions_dashboard_action").id

        relative_url = f"/web#dashboard_id={dashboard.id}&cids={company_id}&action={action_id}&menu_id={menu_id}"
        return f"{base_url}{relative_url}"

    def action_validate(self):
        """Validate the query result and generate dashboard"""
        for record in self:
            try:
                # Parse the result data
                results = json.loads(record.result_data or "[]")
                if not results:
                    continue

                # Extract column names from the first result
                column_names = list(results[0].keys()) if results else []

                # Create dashboard
                dashboard_data = record.create_dashboard_json(
                    results=[list(r.values()) for r in results],
                    column_names=column_names,
                )

                # Generate dashboard URL
                dashboard_url = record._create_and_get_dashboard_url(dashboard_data)

                # Update record
                record.write({"state": "validated", "dashboard_url": dashboard_url})

                # Post message in chatter
                record.message_post(
                    body=f"Query validated and dashboard created. <a href='{dashboard_url}'>View Dashboard</a>",
                    message_type="notification",
                )

                return {
                    "type": "ir.actions.client",
                    "tag": "display_notification",
                    "params": {
                        "title": "Success",
                        "message": "Query validated and dashboard created",
                        "sticky": False,
                        "type": "success",
                        "next": {
                            "type": "ir.actions.act_url",
                            "url": dashboard_url,
                            "target": "new",
                        },
                    },
                }

            except Exception as e:
                record.message_post(
                    body=f"Error validating query: {str(e)}",
                    message_type="notification",
                )
                return {
                    "type": "ir.actions.client",
                    "tag": "display_notification",
                    "params": {
                        "title": "Error",
                        "message": str(e),
                        "sticky": True,
                        "type": "danger",
                    },
                }

    def action_archive(self):
        """Archive the query result"""
        self.write({"state": "archived"})

    def action_draft(self):
        """Set the query result back to draft"""
        self.write({"state": "draft"})

    def name_get(self):
        """Custom name display"""
        result = []
        for record in self:
            name = f"{record.name} ({record.create_date.strftime('%Y-%m-%d')})"
            result.append((record.id, name))
        return result

    def action_view_table(self):
        """Open the table view of the query results"""
        self.ensure_one()
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/query/table/{self.id}',
            'target': 'new',
            'name': f'Query Results - {self.name}'
        }
