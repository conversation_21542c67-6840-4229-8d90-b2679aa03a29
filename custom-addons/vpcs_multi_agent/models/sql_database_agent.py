# -*- coding: utf-8 -*-
from odoo import models, fields, api, tools, _

# Updated imports for LlamaIndex
from llama_index.core.base.llms.types import (
    CompletionResponse,
)
from llama_index.core.llms import CustomLLM
from typing import Optional, List, Any, Dict, Set  # Added Set to imports
from pydantic import ConfigDict, BaseModel
from llama_index.core.query_pipeline import QueryPipeline
from llama_index.core.query_pipeline.components import FunctionComponent
from dataclasses import dataclass
import json
import logging
from typing import Dict, List, Any, Optional
import traceback
import re
import sqlparse
import pandas as pd
from decimal import Decimal
import datetime


_logger = logging.getLogger(__name__)


@dataclass
class LLMMetadata:
    model_name: str
    temperature: float
    max_tokens: int
    is_chat_model: bool
    context_window: int
    model_type: str
    num_output: int = -1  # Add this field with default value -1


class OdooLLMWrapper(CustomLLM, BaseModel):
    """Wrapper for LLM integration with Odoo"""

    model_config = ConfigDict(arbitrary_types_allowed=True)

    llm_provider: Any
    model_name: str
    temperature: float
    max_tokens: int = 2048  # Keep the default value
    is_chat_model: bool
    context_window: int
    env: Any  # Add env field
    allowed_tables: Optional[Set[str]] = None  # Add this field
    table_schemas: Optional[Dict[str, Any]] = None  # Add this field
    synced_tables: Set[str] = set()
    table_records_count: Dict[str, int] = {}

    def __init__(self, **data):
        super().__init__(**data)
        _logger.info("OdooLLMWrapper initialized with model: %s", self.model_name)

    @property
    def metadata(self) -> LLMMetadata:
        """Return metadata for the LLM model."""
        return LLMMetadata(
            model_name=self.model_name,
            temperature=self.temperature,
            max_tokens=self.max_tokens,
            is_chat_model=self.is_chat_model,
            context_window=self.context_window,
            model_type="custom",
        )

    def _extract_tables_from_query(self, query: str) -> set:
        """Extract table names from natural language query"""
        query = query.lower()
        if any(kw in query for kw in ["manufacturing", "production", "work order"]):
            query = (
                query.replace("manufacturing", "mrp_production")
                .replace("production", "mrp_production")
                .replace("work order", "mrp_workorder")
            )
        if any(kw in query for kw in ["sale", "sales", "sale order"]):
            query = (
                query.replace("sale", "sale_order")
                .replace("sales", "sale_order")
                .replace("sale order", "sale_order")
            )
        if any(kw in query for kw in ["project", "task"]):
            query = query.replace("project", "project_project").replace(
                "task", "project_task"
            )
        if any(
            kw in query for kw in ["account", "invoice", "invoices", "account move"]
        ):
            query = (
                query.replace("account", "account_move")
                .replace("invoice", "account_move")
                .replace("invoices", "account_move")
                .replace("account move", "account_move")
            )

        ir_model = self.env["ir.model"]

        # Get all available models
        models = ir_model.search([])
        mentioned_tables = set()

        for model in models:
            table_name = model.model.replace(".", "_")
            if table_name in query:
                mentioned_tables.add(table_name)
        return mentioned_tables

    def predict(self, prompt: str, **kwargs: Any) -> str:
        """Predict completion for the prompt."""
        try:
            # Extract relevant tables from the prompt
            needed_tables = self._extract_tables_from_query(str(prompt))

            # Generate optimized query plan
            query_plan = self._generate_query_plan(prompt, needed_tables)

            # Format prompt with query plan and context
            formatted_prompt = self._format_prompt_with_plan(prompt, query_plan)

            # Generate response using the LLM
            response = self.llm_provider.generate_chat_completion(formatted_prompt)
            print("Formatted Prompt:", formatted_prompt)
            return response.text if hasattr(response, "text") else str(response)

        except Exception as e:
            _logger.error(
                "Error in predict: %s\nTraceback: %s", str(e), traceback.format_exc()
            )
            raise

    def _get_table_schemas(self, tables: Set[str]) -> Dict[str, Dict[str, Any]]:
        """Retrieve schema information for specified tables."""
        schemas = {}
        for table in tables:
            try:
                # Get the model associated with the table
                model_name = table.replace("_", ".")
                if not self.env["ir.model"]._get(model_name):
                    continue

                model = self.env[model_name]
                fields_info = model.fields_get()

                # Collect field information
                table_schema = {}
                for field_name, field_data in fields_info.items():
                    field_type = field_data.get("type", "unknown")
                    field_relation = field_data.get("relation", "")
                    field_string = field_data.get("string", field_name)

                    # Validate if the field type is compatible with JSONB operations
                    is_jsonb = field_type in ["json", "jsonb"]

                    table_schema[field_name] = {
                        "type": field_type,
                        "relation": field_relation,
                        "string": field_string,
                        "is_jsonb": is_jsonb,
                    }

                schemas[table] = table_schema

            except Exception as e:
                _logger.warning(f"Failed to get schema for table {table}: {e}")
                continue
        return schemas

    def _generate_query_plan(self, prompt: str, tables: Set[str]) -> Dict[str, Any]:
        """Generate optimized query plan"""
        try:
            # Get table schemas and relationships
            schemas = self._get_table_schemas(tables)
            relationships = self._get_table_relationships(tables)

            # ✅ Ensure JSONB compatibility
            for table, schema in schemas.items():
                for field, info in schema.items():
                    if not info.get("is_jsonb", False) and "->>" in prompt:
                        # Remove JSONB operator if the field is not JSONB
                        prompt = prompt.replace(f"{field}->>", field)
            # Build query plan
            plan = {
                "tables": list(tables),
                "schemas": schemas,
                "relationships": relationships,
                "suggested_joins": self._suggest_joins(tables, relationships),
            }

            return plan
        except Exception as e:
            _logger.error("Error generating query plan: %s", str(e))
            return {}

    def _get_table_relationships(self, tables: Set[str]) -> List[Dict[str, Any]]:
        """Identify relationships between tables based on foreign keys."""
        relationships = []
        try:
            for table in tables:
                table_name = table.replace("_", ".")
                model_name = self.env["ir.model"]._get(table_name)

                if not model_name:
                    _logger.warning(f"No model found for table: {table}")
                    continue

                actual_model_name = model_name.model
                model = self.env[actual_model_name]
                fields_info = model.fields_get()

                # Find relation fields
                for field_name, field_data in fields_info.items():
                    if field_data.get("relation") and field_data.get("type") in [
                        "many2one",
                        "one2many",
                        "many2many",
                    ]:
                        related_model = field_data.get("relation")
                        related_table = related_model.replace(".", "_")

                        if related_table in tables:
                            relationships.append(
                                {
                                    "from_table": table,
                                    "to_table": related_table,
                                    "from_field": field_name,
                                    "relation_type": field_data.get("type"),
                                }
                            )
        except Exception as e:
            _logger.error(f"Error getting table relationships: {str(e)}")

        return relationships

    def _suggest_joins(
        self, tables: Set[str], relationships: List[Dict[str, Any]]
    ) -> List[str]:
        """Suggest SQL joins based on identified relationships."""
        suggested_joins = []

        for rel in relationships:
            from_table = rel["from_table"]
            to_table = rel["to_table"]
            from_field = rel["from_field"]
            relation_type = rel["relation_type"]

            if relation_type == "many2one":
                suggested_joins.append(
                    f"{from_table} JOIN {to_table} ON {from_table}.{from_field} = {to_table}.id"
                )
            elif relation_type == "one2many":
                # For one2many, the foreign key is on the "many" side
                suggested_joins.append(
                    f"{from_table} JOIN {to_table} ON {from_table}.id = {to_table}.{from_field}"
                )
            elif relation_type == "many2many":
                # For many2many, we need to find the relation table
                # This is a simplified approach
                rel_table = f"rel_{from_table}_{to_table}"
                suggested_joins.append(
                    f"{from_table} JOIN {rel_table} ON {from_table}.id = {rel_table}.{from_table}_id "
                    f"JOIN {to_table} ON {rel_table}.{to_table}_id = {to_table}.id"
                )

        return suggested_joins

    def _get_cache_key(self, query: str) -> str:
        """Generate cache key from query"""
        import hashlib

        return f"sql_query:{hashlib.md5(query.encode()).hexdigest()}"

    def _get_cached_result(self, cache_key: str) -> Optional[str]:
        """Get cached query result"""
        return self._query_cache.get(cache_key)

    def _cache_result(self, cache_key: str, result: str):
        """Cache query result"""
        self._query_cache[cache_key] = result

    def _format_prompt_with_plan(self, prompt: str, query_plan: Dict[str, Any]) -> str:
        """Format prompt with query plan"""
        try:
            tables = query_plan.get("tables", [])
            tables_str = ", ".join(tables) if tables else "No tables identified"

            suggested_joins = query_plan.get("suggested_joins", [])
            joins_str = (
                ", ".join(suggested_joins)
                if suggested_joins
                else "No suggested joins available"
            )

            schemas = query_plan.get("schemas", {})
            schemas_str = (
                json.dumps(schemas, indent=4)
                if schemas
                else "No schema details available"
            )

            return f"""
🔍 **Query Context:**
- **Tables Involved:** {tables_str}
- **Suggested Joins:** {joins_str}

🛠️ **Table Schemas:**
{schemas_str}

🚦 **Optimization Guidelines:**
- ✅ Use **efficient table joins** to avoid redundant data.
- ✅ Apply **necessary filters** to reduce the result set.
- ✅ Follow **security constraints** by only accessing authorized data.
- ✅ Optimize for **performance** by using indexed fields and avoiding large scans.

📝 **User Query:**
{prompt}

⚙️ **Expected Output:**
- SQL query with **accurate joins and filters**
- Optimized for **performance and readability**
- Clear handling of **null values** and potential errors
"""
        except Exception as e:
            _logger.error(f"Error formatting prompt: {str(e)}")
            return str(prompt)

    def complete(self, prompt: str, **kwargs: Any) -> CompletionResponse:
        """Complete the prompt."""
        try:
            response = self.predict(prompt, **kwargs)
            return CompletionResponse(text=response)
        except Exception as e:
            _logger.error(
                "Error in complete: %s\nTraceback: %s", str(e), traceback.format_exc()
            )
            raise

    def _sync_specific_tables(self, tables: Set[str]):
        """Sync specific tables from Odoo"""
        for table in tables:
            if table not in self.synced_tables:
                try:
                    # Check if table exists and has records
                    count = self._get_table_record_count(table)
                    if count > 0:
                        model_name = table.replace("_", ".")
                        if self.env["ir.model"]._get(model_name):
                            self.synced_tables.add(table)
                            self.table_records_count[table] = count
                            _logger.info(f"Synced table {table} with {count} records")
                except Exception as e:
                    _logger.warning(f"Failed to sync table {table}: {e}")

    def _get_table_record_count(self, table: str) -> int:
        """Get record count for a table"""
        try:
            # Using Odoo's ORM to get the record count
            model_name = table.replace("_", ".")
            if model_name in self.env:
                return self.env[model_name].search_count([])
            else:
                # Fallback to direct SQL if model not found
                self.env.cr.execute(f"SELECT COUNT(*) FROM {table}")
                result = self.env.cr.fetchone()
                return result[0] if result else 0
        except Exception as e:
            _logger.error(f"Failed to get record count for table {table}: {str(e)}")
            return 0

    def _sync_tables_for_query(self, query: str) -> Set[str]:
        """Sync only tables needed for specific query"""
        needed_tables = self._extract_tables_from_query(str(query))
        self._sync_specific_tables(needed_tables)
        return needed_tables

    def stream_complete(self, prompt: str, **kwargs: Any) -> CompletionResponse:
        """Stream completion - fallback to regular completion."""
        return self.complete(prompt, **kwargs)

    @property
    def _model_kwargs(self) -> Dict[str, Any]:
        """Get model configuration."""
        return {
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
        }


class SQLDatabaseAgent(models.Model):
    _name = "sql.database.agent"
    _description = "SQL Database Agent"
    _rec_name = "create_date"
    _order = "id desc"
    _inherit = ["mail.thread", "mail.activity.mixin"]

    llm_wrapper = fields.Many2one(
        "llm.provider",
        string="LLM Provider",
        required=True,
        default=lambda self: self.env["llm.provider"].search(
            [("is_sql_agent", "=", True)], limit=1
        ),
    )
    allowed_tables = fields.Many2many(
        "ir.model",
        string="Allowed Tables",
        domain="[('transient', '=', False)]",
        tracking=True,
    )
    active = fields.Boolean(string="Active", default=True)
    schema_info = fields.Text(string="Schema Information", default="")

    # Class variable for pipeline storage
    _pipeline_store = {}

    @api.model
    def _get_pipelines(self):
        """Get or initialize pipelines for the current instance"""
        if not self._pipeline_store.get(self.id):
            self._pipeline_store[self.id] = {
                "extraction_pipeline": None,
                "query_generation_pipeline": None,
                "validation_pipeline": None,
                "llm_wrapper": None,
            }
        return self._pipeline_store[self.id]

    def setup_pipelines(self, llm_wrapper: OdooLLMWrapper):
        """Setup query pipelines for the three-step process"""
        try:
            pipelines = self._get_pipelines()

            # Store pipelines
            pipelines["llm_wrapper"] = llm_wrapper
            pipelines["extraction_pipeline"] = self._create_extraction_pipeline()
            pipelines["query_generation_pipeline"] = (
                self._create_query_generation_pipeline()
            )
            pipelines["validation_pipeline"] = self._create_validation_pipeline()

            _logger.info("All pipelines successfully initialized")

            return pipelines

        except Exception as e:
            _logger.error(
                "Pipeline initialization failed: %s\nTraceback: %s",
                str(e),
                traceback.format_exc(),
            )
            raise

    def _create_extraction_pipeline(self) -> QueryPipeline:
        """Create pipeline for extracting tables and fields from natural language query"""
        extraction_pipeline = QueryPipeline(verbose=True)
        llm_wrapper = self._pipeline_store[self.id]["llm_wrapper"]
        extraction_prompt = """
            Given the following schema information:
            {schema_info}

            And this natural language query:
            {query}

            Extract the following information:
            1. Which tables are needed
            2. Which fields should be selected
            3. What conditions should be applied
            
            Return the result strictly as a JSON object in this exact format:
            {{
                "tables": ["table1", "table2"],
                "fields": ["field1", "field2"],
                "conditions": ["condition1", "condition2"]
            }}
        """

        def extract_completion(query: str) -> str:
            """Extract text from completion response"""
            response = llm_wrapper.complete(
                extraction_prompt.format(schema_info=self.schema_info, query=query)
            )
            return response.text if hasattr(response, "text") else str(response)

        extract_component = FunctionComponent(fn=extract_completion)
        parse_json_component = FunctionComponent(
            fn=lambda result: self._parse_extraction_result(result)
        )

        extraction_pipeline.add_modules(
            {"extract": extract_component, "parse_json": parse_json_component}
        )

        extraction_pipeline.add_chain(["extract", "parse_json"])

        return extraction_pipeline

    def _parse_extraction_result(self, result: str) -> Dict:
        """Parse the extraction result from JSON format"""
        _logger.info("Parsing extraction result: %s", result)

        # Return empty default if no result
        if not result:
            _logger.warning("Empty extraction result")
            return {"tables": [], "fields": [], "conditions": []}

        # If result is already a dict, return it with default values
        if isinstance(result, dict):
            return {
                "tables": result.get("tables", []),
                "fields": result.get("fields", []),
                "conditions": result.get("conditions", []),
            }

        try:
            # First, try to find a JSON structure
            json_pattern = r"```json\s*([\s\S]*?)\s*```|\{[\s\S]*\}"
            json_matches = re.findall(json_pattern, result, re.DOTALL)

            if not json_matches:
                _logger.warning("No JSON structure found in result")
            else:
                # Take the first match (tuple if markdown, string if plain JSON)
                potential_json = (
                    json_matches[0][0]
                    if isinstance(json_matches[0], tuple)
                    else json_matches[0]
                )
                try:
                    # Clean the JSON
                    cleaned_json = potential_json.strip()
                    _logger.info("Cleaned JSON: %s", cleaned_json)

                    # Parse directly (assumes LLM uses double quotes correctly)
                    parsed = json.loads(cleaned_json)
                    _logger.info("Parsed JSON: %s", parsed)

                    if all(key in parsed for key in ["tables", "fields", "conditions"]):
                        return {
                            "tables": parsed.get("tables", []),
                            "fields": parsed.get("fields", []),
                            "conditions": parsed.get("conditions", []),
                        }
                    _logger.warning("JSON does not contain all required keys")
                except json.JSONDecodeError as e:
                    _logger.warning(
                        f"JSON parsing failed: {str(e)}, attempting to fix quotes"
                    )
                    # Fix quotes manually if LLM used single quotes or malformed quotes
                    cleaned_json = (
                        potential_json.replace(
                            "'", '"'
                        )  # Replace single quotes with double quotes
                        .replace('\\"', '"')  # Handle escaped quotes
                        .replace("\n", " ")  # Remove newlines
                    )
                    try:
                        parsed = json.loads(cleaned_json)
                        if all(
                            key in parsed for key in ["tables", "fields", "conditions"]
                        ):
                            return {
                                "tables": parsed.get("tables", []),
                                "fields": parsed.get("fields", []),
                                "conditions": parsed.get("conditions", []),
                            }
                    except json.JSONDecodeError as e2:
                        _logger.warning(
                            f"Second JSON parsing attempt failed: {str(e2)}"
                        )
            # If we get here, try to extract information from SQL
            if "SELECT" in result.upper() and "FROM" in result.upper():
                tables = []
                fields = []
                conditions = []

                # Extract table names
                from_pattern = r"FROM\s+([^\sWHERE;]+)"
                table_matches = re.findall(from_pattern, result, re.IGNORECASE)
                if table_matches:
                    tables = [
                        t.strip(" AS").split(" AS")[0].strip()
                        for t in table_matches[0].split(",")
                    ]

                # Extract fields
                select_pattern = r"SELECT\s+(.+?)\s+FROM"
                field_matches = re.findall(
                    select_pattern, result, re.IGNORECASE | re.DOTALL
                )
                if field_matches:
                    fields = [f.strip() for f in field_matches[0].split(",")]

                # Extract conditions
                where_pattern = r"WHERE\s+(.+?)(;|$)"
                condition_matches = re.findall(
                    where_pattern, result, re.IGNORECASE | re.DOTALL
                )
                if condition_matches:
                    conditions = [condition_matches[0][0].strip()]

                return {"tables": tables, "fields": fields, "conditions": conditions}

            _logger.warning("Could not parse result as JSON or SQL")
            return {"tables": [], "fields": [], "conditions": []}

        except Exception as e:
            _logger.error(
                "Error parsing extraction result: %s\nResult: %s", str(e), result
            )
            return {"tables": [], "fields": [], "conditions": []}

    def _create_query_generation_pipeline(self) -> QueryPipeline:
        """Create pipeline for generating SQL query"""
        query_pipeline = QueryPipeline(verbose=True)
        llm_wrapper = self._pipeline_store[self.id]["llm_wrapper"]
        generation_prompt = """
        Given the following schema information and tables:
        {schema_info}
        
        Generate a SQL query for this request: {query}

        Critical instructions:
        - The schema information provided above ({schema_info}) is the definitive source of truth for this task.
        - You MUST base your SQL query EXCLUSIVELY on the tables, columns, and relationships explicitly listed in the schema_info.
        - Do NOT assume the existence of any tables, columns, or relationships that are not mentioned in the schema_info.
        
        Important rules:
        1. Always use the full table name in column references (e.g., 'product_template.name' not 'pp.name').
        2. Only use tables and columns that exist in the schema.
        3. When joining tables, ensure the relationships are correct.
        4. For product-related queries:
            - The 'product_product' table does not have a 'name' field.
            - Use 'product_template.name' instead, joining 'product_product' with 'product_template' using 'product_product.product_tmpl_id = product_template.id'.
        5. For user/employee related queries:
            - The name field is stored in res_partner table
            - Always join res_users with res_partner using res_users.partner_id = res_partner.id
            - Use res_partner.name instead of res_users.name
        6. For fields marked 'json' or 'jsonb' in schema_info:
            - Use '->>' for text comparisons (e.g., 'table_name.column_name->>''en_US'' = ''value''').
            - Default to 'en_US' unless a language code is specified in the query.
        7. For time period filters:
            - Use PostgreSQL syntax: 'CURRENT_DATE - INTERVAL ''N unit''' (e.g., 'CURRENT_DATE - INTERVAL ''1 month''').
            - Do not use 'DATE('now', '-N unit')'; it’s invalid.
            - Supported units: 'day', 'week', 'month', 'year'.
            - Examples: 
                - Last 1 month: 'CURRENT_DATE - INTERVAL ''1 month'''
                - Last 3 days: 'CURRENT_DATE - INTERVAL ''3 days'''
        8. For computed fields (marked with 'computed'):
            - Avoid using computed fields directly in SELECT or WHERE clauses as they are not stored in the database.
            - Use related stored fields or joins if possible, or assume they are available only via Odoo ORM.
        9. For invoicing-related queries:
            - Use account_move table instead of account_invoice
            - Use account_move.move_type = 'out_invoice' for customer invoices
            - Use account_move.state = 'posted' for confirmed invoices
            - Use account_move.date for date filtering
        10. For manufacturing-related queries (e.g., containing 'manufacturing', 'production', 'work order'):
            - Use 'mrp_production' for manufacturing orders and 'mrp_workorder' for work orders.
        11. For inventory queries (e.g., 'inventory', 'stock', 'units'):
            - Use 'stock_quant' for quantities, 'product_product' for products, 'product_template' for names.
            - Join 'product_product' with 'product_template' via 'product_tmpl_id'.
            - Use '<' for low inventory thresholds and '>' for high inventory thresholds based on the query.

            Examples:
            - Query: "Which products have low inventory (less than 10 units)?"
                SQL: SELECT pp.default_code, pt.name FROM product_product AS pp JOIN product_template AS pt ON pp.product_tmpl_id = pt.id WHERE pp.id IN (SELECT product_id FROM stock_quant WHERE quantity < 10)
            - Query: "Which products have low inventory (less than 5 units)?"
                SQL: SELECT pp.default_code, pt.name FROM product_product AS pp JOIN product_template AS pt ON pp.product_tmpl_id = pt.id WHERE pp.id IN (SELECT product_id FROM stock_quant WHERE quantity < 5)
            - Query: "Which products have high inventory (more than 100 units)?"
                SQL: SELECT pp.default_code, pt.name FROM product_product AS pp JOIN product_template AS pt ON pp.product_tmpl_id = pt.id WHERE pp.id IN (SELECT product_id FROM stock_quant WHERE quantity > 100)
            - Query: "Which products have high inventory (more than 50 units)?"
                SQL: SELECT pp.default_code, pt.name FROM product_product AS pp JOIN product_template AS pt ON pp.product_tmpl_id = pt.id WHERE pp.id IN (SELECT product_id FROM stock_quant WHERE quantity > 50)
        Return only the SQL query without any explanation.
        """

        def fix_user_references(sql: str) -> str:
            """Fix references to res_users fields that are actually in res_partner"""
            if "res_users" in sql and "res_users.name" in sql:
                # Add join with res_partner if not present
                if "JOIN res_partner" not in sql:
                    sql = sql.replace(
                        "FROM res_users",
                        "FROM res_users JOIN res_partner ON res_users.partner_id = res_partner.id",
                    )
                    sql = sql.replace(
                        "JOIN res_users",
                        "JOIN res_users JOIN res_partner ON res_users.partner_id = res_partner.id",
                    )
                # Replace res_users.name with res_partner.name
                sql = sql.replace("res_users.name", "res_partner.name")
            return sql

        def fix_date_intervals(sql: str) -> str:
            """Fix PostgreSQL date interval expressions"""

            sql = re.sub(
                r"DATE\('now',\s*'-(\d+)\s*(\w+)'\)",
                r"CURRENT_DATE - INTERVAL '\1 \2'",
                sql,
            )

            sql = re.sub(
                r"DATE_TRUNC\('(-?\d+)\s+([^']+)',\s*([^)]+)\)",
                lambda m: f"{m.group(3)} - INTERVAL '{m.group(1).strip('-')} {m.group(2)}'",
                sql,
            )
            sql = re.sub(
                r"([^ ]+)\s*([+-])\s*INTERVAL\s+'([^']+)'",
                lambda m: f"{m.group(1)} {m.group(2)} INTERVAL '{m.group(3)}'",
                sql,
            )

            # Replace DATE('now()') or DATE('now') with CURRENT_DATE
            sql = sql.replace("DATE('now()')", "CURRENT_DATE")
            sql = sql.replace("DATE('now')", "CURRENT_DATE")

            # Fix now() - interval expressions
            sql = re.sub(
                r"now\(\)\s*-\s*INTERVAL\s+'(\d+)\s+([^']+)'",
                r"now() - INTERVAL '\1 \2'",
                sql,
            )

            return sql

        def fix_legacy_table_names(sql: str) -> str:
            """Fix legacy table names to match current Odoo version"""
            replacements = {
                "account_invoice": "account_move",
                "account_invoice_line": "account_move_line",
                "invoice_date": "date",
                "state = 'posted'": "state = 'posted' AND move_type = 'out_invoice'",
            }

            fixed_sql = sql
            for old, new in replacements.items():
                fixed_sql = fixed_sql.replace(old, new)

            return fixed_sql

        def generate_sql(extraction_result: Dict) -> str:
            """Generate SQL query based on extracted information"""
            query_text = extraction_result.get("query", "")

            # Check if schema_info is empty or invalid, and use extraction tables as fallback
            schema_info = extraction_result.get("schema_info", "")

            if not schema_info or schema_info == "No tables identified in the query.":
                extracted_tables = extraction_result.get("extraction", {}).get(
                    "tables", []
                )
                if extracted_tables:
                    schema_info = self._get_schema_info(set(extracted_tables))
                    _logger.info(
                        f"Regenerated schema_info from extracted tables: {schema_info}"
                    )
            self.schema_info = schema_info
            response = llm_wrapper.complete(
                generation_prompt.format(schema_info=self.schema_info, query=query_text)
            )
            sql = response.text if hasattr(response, "text") else str(response)
            sql = fix_legacy_table_names(sql)
            sql = fix_date_intervals(sql)
            sql = fix_user_references(sql)
            return sql

        # Add components to pipeline
        generate_component = FunctionComponent(fn=generate_sql)
        query_pipeline.add_modules({"generate": generate_component})
        query_pipeline.add_chain(["generate"])

        return query_pipeline

    def _create_validation_pipeline(self) -> QueryPipeline:
        """Create pipeline for validating and optimizing SQL query"""
        validation_pipeline = QueryPipeline(verbose=True)
        llm_wrapper = self._pipeline_store[self.id]["llm_wrapper"]

        def validate_and_optimize_sql(sql: str) -> str:
            """Validate and optimize SQL query"""
            # Create validation prompt
            prompt = f"""
            Given this SQL query:
            {sql}
            
            And this schema information:
            {self.schema_info}
            
            Validate and optimize the SQL query based EXCLUSIVELY on the provided schema_info. Follow these steps:
            1. Check that all referenced tables exist in the schema_info. Remove or replace invalid tables.
            2. All field references are valid
            3. Ensure JOINs use correct relationships (e.g., foreign keys like 'project_id') as specified in schema_info.
            4. Conditions use PostgreSQL syntax
            5. Date functions are PostgreSQL compatible
            6. For JSON/JSONB fields (marked as 'json' or 'jsonb' in schema_info):
                - Replace direct equality (e.g., 'table_name.column_name = ''value''') with 'table_name.column_name->>''en_US'' = ''value''' for any field marked 'json' or 'jsonb'.
                - Use 'en_US' as the default language code unless specified otherwise in the query.
                - Apply this correction universally across all tables and columns.
            7. Optimize the query by:
                - Removing redundant conditions or joins.
                - Simplifying expressions where possible.
                - Ensuring proper indexing fields (e.g., 'id') are used in JOINs or WHERE clauses if available.
            8. If a field or table is not in schema_info, exclude it from the query rather than guessing.
            9. For JSON/JSONB fields (marked as 'json' or 'jsonb'):
                - Replace direct equality (e.g., 'table.column = ''value''') with 'table.column->>''en_US'' = ''value'''.
            10. For manufacturing-related queries (e.g., 'manufacturing' in original request):
                - Use 'mrp_production' and 'mrp_workorder' if available in schema_info.
            Return only the optimized SQL query without any explanation.
            """

            # Get completion and extract text
            response = llm_wrapper.complete(prompt)
            return response.text if hasattr(response, "text") else str(response)

        def format_sql(sql: str) -> str:
            """Format and clean SQL query"""
            try:
                # Remove any markdown formatting
                import re

                sql_match = re.search(r"```sql\n(.*?)\n```", sql, re.DOTALL)
                if sql_match:
                    sql = sql_match.group(1)

                # Clean whitespace
                sql = sql.strip()

                # Convert date function to PostgreSQL syntax
                sql = sql.replace("date('now'", "CURRENT_DATE")

                # Format using sqlparse
                formatted_sql = sqlparse.format(
                    sql, reindent=True, keyword_case="upper", identifier_case="lower"
                )

                return formatted_sql
            except Exception as e:
                _logger.warning(f"SQL formatting error: {e}")
                return sql

        # Create pipeline components
        validate_component = FunctionComponent(fn=validate_and_optimize_sql)

        format_component = FunctionComponent(fn=format_sql)

        # Add components to pipeline
        validation_pipeline.add_modules(
            {"validate": validate_component, "format": format_component}
        )

        # Set up pipeline flow
        validation_pipeline.add_chain(["validate", "format"])

        return validation_pipeline

    def _get_llm_wrapper(self):
        """Get LLM wrapper instance"""
        self.ensure_one()
        return OdooLLMWrapper(
            llm_provider=self.llm_wrapper,
            model_name=self.llm_wrapper.model,
            temperature=self.llm_wrapper.temperature,
            max_tokens=self.llm_wrapper.max_tokens,
            is_chat_model=self.llm_wrapper.is_chat_model,
            context_window=self.llm_wrapper.context_window,
            env=self.env,
        )

    def _get_schema_info(self, tables: Set[str]) -> str:
        if not tables:
            return "No tables identified in the query."

        schema_info = []
        for table in tables:
            try:
                # Query database schema for stored fields
                query = """
                    SELECT column_name, data_type
                    FROM information_schema.columns
                    WHERE table_name = %s
                """
                self.env.cr.execute(query, (table,))
                db_columns = {
                    col["column_name"]: col["data_type"]
                    for col in self.env.cr.dictfetchall()
                }
                # Get the model associated with the table
                table_name = table.replace("_", ".")  # Conversion step
                model_id = self.env["ir.model"]._get(table_name)
                if not model_id:
                    continue
                model_name = model_id.model
                model = self.env[model_name]
                fields_info = model.fields_get()

                # Collect field information
                schema_info.append(f"\nTable: {table}")
                for field_name, field_data in fields_info.items():
                    # field_type = field_data.get('type', 'unknown')
                    db_type = db_columns.get(
                        field_name, field_data.get("type", "unknown")
                    )
                    field_relation = field_data.get("relation", "")
                    field_string = field_data.get("string", field_name)
                    is_stored = field_name in db_columns and field_data.get(
                        "store", True
                    )
                    is_computed = field_data.get(
                        "store", True
                    ) is False or field_data.get("compute", False)

                    if not is_stored or is_computed:
                        continue
                    field_desc = f"  - {field_name} ({db_type})"

                    if field_relation:
                        field_desc += f" -> {field_relation}"
                    field_desc += f": {field_string}"
                    schema_info.append(field_desc)

            except Exception as e:
                _logger.warning(f"Failed to get schema for table {table}: {e}")
                continue
        result = "\n\n".join(schema_info)
        return result

    def _execute_query(self, sql_query: str) -> Dict[str, Any]:
        """Execute SQL query and return results"""
        try:
            # First attempt: Execute the query directly
            try:
                self.env.cr.execute(sql_query)
                records = self.env.cr.dictfetchall()
                _logger.info("Direct query execution successful.")
            except Exception as direct_error:
                _logger.warning(f"Direct execution failed: {str(direct_error)}. Falling back to validation pipeline.")
                self.env.cr.rollback()

                # Validate and optimize query using pipeline only if direct execution fails
                validation_pipeline = self._pipeline_store[self.id]["validation_pipeline"]
                validated_query = validation_pipeline.run(sql_query)
                if not validated_query.strip():
                    raise ValueError("Empty SQL query after validation")
                
                # Execute the validated query
                self.env.cr.execute(validated_query)
                records = self.env.cr.dictfetchall()
                _logger.info(f"Validated query executed: {validated_query}")
                sql_query = validated_query  # Update sql_query to reflect what was executed

            # Convert records to DataFrame and handle special data types
            data = []
            for record in records:
                row_dict = {}
                for col, val in record.items():
                    if isinstance(val, Decimal):
                        row_dict[col] = float(val)
                    elif isinstance(val, datetime.datetime):
                        row_dict[col] = val.isoformat()
                    elif isinstance(val, datetime.date):
                        row_dict[col] = val.isoformat()
                    else:
                        row_dict[col] = val
                data.append(row_dict)

            df = pd.DataFrame(data)
            _logger.info(f"Query result: {df}")
            return {
                "success": True,
                "sql": sql_query,  # Return the query that was actually executed
                "data": df,
                "metadata": {
                    "row_count": len(df),
                    "columns": list(df.columns) if not df.empty else [],
                    "model": self.llm_wrapper.model,
                    "timestamp": fields.Datetime.now(),
                },
            }

        except ValueError as ve:
            _logger.error(f"Validation error: {str(ve)}")
            return {
                "success": False,
                "error": f"Validation error: {str(ve)}",
                "metadata": {
                    "model": self.llm_wrapper.model,
                    "timestamp": fields.Datetime.now(),
                },
            }
        except Exception as e:
            _logger.error(f"Query execution error: {str(e)}")
            return {
                "success": False,
                "error": f"Query execution error: {str(e)}",
                "metadata": {
                    "model": self.llm_wrapper.model,
                    "timestamp": fields.Datetime.now(),
                },
            }
    def _optimize_query(self, query: str, pipelines) -> Dict[str, Any]:
        """Optimize a natural language query into SQL"""
        try:
            # Step 1: Extract tables and fields
            extraction_pipeline = pipelines["extraction_pipeline"]
            extraction_result = extraction_pipeline.run(query)

            if not extraction_result or not extraction_result.get("tables"):
                return {
                    "success": False,
                    "error": "No relevant tables identified",
                    "metadata": {},
                }

            # Step 2: Sync required tables and update schema info
            needed_tables = pipelines["llm_wrapper"]._sync_tables_for_query(query)

            self.schema_info = self._get_schema_info(needed_tables)

            # Step 3: Generate and validate SQL
            generation_input = {
                "query": query,
                "extraction": extraction_result,
                "schema_info": self.schema_info,
            }
            query_generation_pipeline = pipelines["query_generation_pipeline"]
            if query_generation_pipeline:
                sql_query = query_generation_pipeline.run(generation_input)

            if not sql_query:
                return {
                    "success": False,
                    "error": "Failed to generate SQL query",
                    "metadata": {},
                }
            self.schema_info = ""
            result = self._execute_query(sql_query)

            return result

        except Exception as e:
            _logger.error(f"Query optimization error: {str(e)}")
            return {"success": False, "error": str(e), "metadata": {}}

    def _process_query(self, query: str, pipelines) -> tuple:
        """Process a natural language query and return formatted results"""
        try:
            # Get optimized query
            optimized_query = self._optimize_query(query, pipelines)

            if not optimized_query["success"]:
                # return (
                #     "Error occurred",
                #     f"<div class='error'>{optimized_query['error']}</div>",
                #     None,
                #     optimized_query.get("metadata", {}),
                # )
                return {"success": False, "error": optimized_query['error']}

            return optimized_query

        except Exception as e:
            _logger.error(f"Query optimization error----: {str(e)}")
            return {"success": False, "error": str(e), "metadata": {}}

    def generate_sql_query(self, prompt: str) -> Dict[str, Any]:
        """Generate SQL query using LLM"""
        _logger.info("Generating SQL query for prompt: %s", prompt)
        self.ensure_one()

        try:
            llm_wrapper = self._get_llm_wrapper()
            pipelines = self.setup_pipelines(llm_wrapper)

            process_query = self._process_query(prompt, pipelines)
            _logger.info("Process query result: %s", process_query)

            if not process_query or not isinstance(process_query, dict):
                return {
                    "success": False,
                    "error": "Failed to process the query",
                    "metadata": {
                        "model": llm_wrapper.model_name,
                        "timestamp": fields.Datetime.now(),
                    },
                }

            # If process_query was successful, return the complete result
            if process_query.get("success"):
                return {
                    "success": True,
                    "query": process_query.get("sql", ""),
                    "data": process_query.get("data"),
                    "metadata": {
                        "model": llm_wrapper.model_name,
                        "timestamp": fields.Datetime.now(),
                        "row_count": process_query.get("metadata", {}).get(
                            "row_count", 0
                        ),
                        "columns": process_query.get("metadata", {}).get("columns", []),
                    },
                }

            # If process_query failed, return error
            return {
                "success": False,
                "error": process_query.get("error", "Unknown error occurred"),
                "metadata": {
                    "model": llm_wrapper.model_name,
                    "timestamp": fields.Datetime.now(),
                },
            }

        except Exception as e:
            _logger.error(
                "Error generating SQL query: %s\nTraceback: %s",
                str(e),
                traceback.format_exc(),
            )
            return {
                "success": False,
                "error": str(e),
                "metadata": {
                    "model": getattr(llm_wrapper, "model_name", "unknown"),
                    "timestamp": fields.Datetime.now(),
                },
            }

    # @api.model
    # def clear_caches(self):
    #     """Clear all caches for this model"""
    #     return self._get_cached_result.clear_cache(self)
