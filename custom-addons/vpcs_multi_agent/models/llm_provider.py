from odoo import models, fields
import re
from odoo.exceptions import UserError
import logging
import os
from llama_index.embeddings.openai import OpenAIEmbedding

_logger = logging.getLogger(__name__)


class LLMProvider(models.Model):
    _inherit = "llm.provider"

    # Additional fields for multi-agent functionality
    is_sql_agent = fields.<PERSON><PERSON>an("SQL Agent")
    is_chat_model = fields.Boolean("Is Chat Model", default=True)
    agent_instructions = fields.Text(
        "SQL Agent Instructions",
        default="Generate SQL Query based on Odoo 17 Model Tables Compatible.",
        help="You can add any example or instructions to the agent so agent will give you more accurate result",
    )
    embedding_model = fields.Selection(
        [
            ("pgvector", "PostgreSQL Vector (Recommended)"),
            ("openai", "OpenAI Embedding"),
        ],
        string="Embedding Model",
        default="pgvector",
    )
    temperature = fields.Float("Temperature", default=0.7)
    max_tokens = fields.Integer("Max Tokens", default=2048)
    context_window = fields.Integer("Context Window", default=4096)

    def as_llm_config(self):
        """Return provider configuration as a dictionary for LLM wrapper"""
        self.ensure_one()
        return {
            "model_name": self.model,
            "temperature": self.temperature,
            "max_tokens": self.max_tokens,
            "is_chat_model": self.is_chat_model,
            "generate": self.generate,
        }

    def setup_embedding_model(self):
        """Setup embedding model based on configuration"""
        self.ensure_one()

        if self.embedding_model == "pgvector":
            return None
        elif self.embedding_model == "openai":
            if not self.api_key:
                raise UserError("OpenAI API key is required for OpenAI embeddings")

            os.environ["OPENAI_API_KEY"] = self.api_key
            return OpenAIEmbedding(api_key=self.api_key, model="text-embedding-ada-002")
        return None

    def _format_sql_response(self, response):
        """Format the response to extract SQL query, removing thinking sections."""
        if not response:
            return None

        thinking_pattern = r"<think>.*?</think>"
        response = re.sub(thinking_pattern, "", response, flags=re.DOTALL)

        code_block_pattern = r"```(?:sql)?\s*(.*?)\s*```"
        matches = re.finditer(code_block_pattern, response, flags=re.DOTALL)
        for match in matches:
            sql = match.group(1).strip()
            if sql and "SELECT" in sql.upper():
                return sql

        sql_pattern = r"(?:SELECT\s+.*?FROM.*?)(?:;|\Z)"
        match = re.search(sql_pattern, response, re.DOTALL | re.IGNORECASE)
        if match:
            return match.group(0).strip().rstrip(";")

        return response

    def generate_chat_completion(self, prompt):
        """Generate chat completion while maintaining context."""
        self.ensure_one()

        if self.is_sql_agent and self.agent_instructions:
            context_prompt = f"{self.agent_instructions}\n\nUser Query: {prompt}"
        else:
            context_prompt = prompt

        # Use the inherited generate method from llm.provider
        response = super().generate(context_prompt)

        if self.is_sql_agent and response:
            response = self._format_sql_response(response)

        return response

    def generate(self, prompt, **kwargs):
        """Override generate method to use parent class implementation"""
        try:
            # Use the parent class generate method which handles all providers correctly
            return super().generate(prompt, **kwargs)
        except Exception as e:
            _logger.error("Error in multi-agent generation: %s", str(e))
            raise
