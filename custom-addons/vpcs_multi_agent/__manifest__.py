{
    'name': 'VPCS Multi Agent',
    'version': '********.0',
    'category': 'Productivity',
    'summary': 'Multi-Agent System for VPCS with VPCS AI AGENT channel',
    'description': """
        Multi-Agent System including SQL Query Generation and other AI capabilities.
        Features:
        - SQL Query Generation through natural language
        - Integration with multiple LLM providers (OpenAI, Anthropic, Groq)
        - Vector store support with PGVector
        - Automated table metadata extraction
        - Dynamic query generation with context awareness
        - Security-aware table access management
        - Integrated with Odoo's permission system
    """,
    'author': 'VPerfectCS',
    'website': 'https://www.vperfectcs.com',
    'depends': [
        "base", 
        "mail", 
        "web_editor", 
        "vpcs_llm_provider",
        "spreadsheet_dashboard",
        "website"
    ],
    'external_dependencies': {
        'python': [
            # Database and SQL
            'sqlalchemy>=1.4.0',
            'sqlparse',
            'psycopg2-binary',
            'pgvector',
            # LlamaIndex core and extensions
            'llama-index-core>=0.9.48',
            'llama-index-vector-stores-postgres>=0.1.2',
            'llama-index-embeddings-openai>=0.1.3',

            # LLM Providers
            'openai',
            'anthropic',
            'groq',
            'google-generativeai',
            # Utilities
            'python-dotenv',
            'pandas',
            'numpy',
        ],
        'bin': [],
    },
    'data': [
        'data/mail_channel_data.xml',
        'data/user_partner_data.xml',
        'security/security.xml',
        'security/ir.model.access.csv',
        'views/sql_database_agent_views.xml',
        'views/llm_provider_views.xml',
        'views/sql_query_result_views.xml',
        'views/query_table_template.xml',
        'views/mail_channel_views.xml',
    ],
    'images': ["static/description/banner.png"],
    'installable': True,
    'application': True,
    'auto_install': False,
    'license': 'LGPL-3',
    'price': 199.00,
    'currency': 'USD',
}
