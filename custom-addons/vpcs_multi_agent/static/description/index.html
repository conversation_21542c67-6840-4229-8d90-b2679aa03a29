<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h2 class="oe_slogan" style="color:#875A7B;">
            SQL Agent
        </h2>
        <h3 class="oe_slogan">
            This Odoo module uses AI to generate SQL queries from natural language input. <br>
            It's designed to help users build database queries without needing to know SQL.        </h3>
        <div style="margin-left: 20%; width: 60%; font-size: 18px">
            <h2>Dependent Modules @SQL Agent</h2>
            <ul style="font-size: 15px;">
                <li>vpcs_llm_provider</li>
            </ul>
            <h2>Features @SQL Agent</h2>
            <ul style="font-size: 15px;">
                <li>Natural Language to SQL Conversion</li>
                <li>LLM Integration with Multi-Model Support</li>
                <li>Interactive Dashboard in Odoo</li>
                <li>Result Rendering and History Logs</li>
            </ul>
        </div>
    </div>
</section>
<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            Open the LLM Provider Module in the Odoo
        </h4>
        <div class="oe_screenshot oe_demo">
            <img src="Odoo-LLM-Providers-SQL-1.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            Configure llm provider module in the Odoo
            <li> Navigate to - <i>llm provider module > configration > llm provider</i> </li>
                <li>For generate the API Key follow below steps:</li>
                <ul>
                    <li>Go to Google Cloud Console and Create the new project</li>
                    <li>Navigate to the Google Ai Studio go to the create API keys button and Generate the Key.</li>
                    <li>Create an API Key and paste it into the Odoo LLM Provider form view in the Odoo</li>
                </ul>
            </li>
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Odoo-gemini-2-0-flash-SQl-2.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            Go to the Discuss module, open the <b>Vpcs AI Agent</b> and click on setting for more configuration. 
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Odoo-Discuss-SQL-31.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            Click on channel.
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Odoo-widget-SQL-41.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            In the channel, Add LLM provider and also adjust channel member.
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Odoo-Discuss-SQL-51.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            Success message show in the Odoo vpcs ai agent channel, including Dashboard and Data Table links, using links directly open dashbord view and table view.
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Odoo-Discuss-SQl-61.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            The SQL query results menu shows the query and results, with links to view the full table in the Odoo dashboard and website.        
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Odoo-Query-Result-SQL-8.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            Clicking the Dashboard link displays the result in the Odoo Dashboard.
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Odoo-Dashboards-SQL-9.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container">
    <div class="oe_row oe_spaced">
        <h4 class="oe_slogan" style="color:#875A7B;">
            Clicking the View button displays the result on the Odoo Website.
        </h4>
        <div class="oe_demo oe_screenshot">
            <img src="Query-Results-Generate-SQL-10.png"
                style="max-width: 100%; height: auto; display: block;">
        </div>
    </div>
</section>

<section class="oe_container oe_dark" style="padding: 30px;">
    <div class="oe_spaced">
        <a href="http://www.vperfectcs.com" target="_blank">
            <h2 class="oe_slogan" style="color:#875A7B;">
                <img src="Vperfectcs.png" width="35%" height="50%">
            </h2>
        </a>
        <h3 class="oe_slogan">Veracious solutions to grow your business.</h3>
    </div>
    <div class="text-center">
        <a href="http://vperfectcs.com/about_us" target="_blank">
            <h2>About us</h2>
        </a>
        <div>
            <a href="http://www.vperfectcs.com/" target="_blank">
                Website
            </a> |
            <a href="http://www.vperfectcs.com/blog/our-blog-1" target="_blank">
                Blog
            </a> |
            <a href="http://www.vperfectcs.com/contactus" target="_blank">
                Contact us
            </a> |
            <a href="mailto:<EMAIL>"
                onClick="javascript:window.open('mailto:<EMAIL>', 'mail');event.preventDefault()"
                target="_blank">
                Request New Feature
            </a>
        </div>
    </div>
</section>
