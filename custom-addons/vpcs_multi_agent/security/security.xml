<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Security Groups -->
        <record id="group_sql_agent_user" model="res.groups">
            <field name="name">SQL Agent User</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <record id="group_sql_agent_manager" model="res.groups">
            <field name="name">SQL Agent Manager</field>
            <field name="category_id" ref="base.module_category_hidden"/>
            <field name="implied_ids" eval="[(4, ref('group_sql_agent_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>

        <!-- Record Rules -->
        <record id="sql_query_result_company_rule" model="ir.rule">
            <field name="name">SQL Query Result: Multi-Company Rule</field>
            <field name="model_id" ref="model_sql_query_result"/>
            <field name="global" eval="True"/>
            <field name="domain_force">['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>

        <record id="sql_query_result_user_rule" model="ir.rule">
            <field name="name">SQL Query Result: User Access Rule</field>
            <field name="model_id" ref="model_sql_query_result"/>
            <field name="groups" eval="[(4, ref('group_sql_agent_user'))]"/>
            <field name="domain_force">[('create_uid', '=', user.id)]</field>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>

        <record id="sql_query_result_manager_rule" model="ir.rule">
            <field name="name">SQL Query Result: Manager Access Rule</field>
            <field name="model_id" ref="model_sql_query_result"/>
            <field name="groups" eval="[(4, ref('group_sql_agent_manager'))]"/>
            <field name="domain_force">[(1, '=', 1)]</field>
        </record>
    </data>
</odoo>