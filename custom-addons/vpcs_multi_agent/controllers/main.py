import logging

_logger = logging.getLogger(__name__)

from odoo.http import request
from odoo import http, _
from odoo.exceptions import UserError, AccessError
# from odoo.addons.multi_ai_chatbot.controllers.main import EditorAIcontroller
import json
import datetime
import requests
from odoo.addons.iap.tools import iap_tools
import os
from odoo.http import request
from google import genai

DEFAULT_LIBRARY_ENDPOINT = "https://media-api.odoo.com"
DEFAULT_OLG_ENDPOINT = "https://olg.api.odoo.com"

class MultiAIChatbotController(http.Controller):
    def generate_response(self, prompt, model, conversation_history):
        try:
            if model:
                provider = (
                    request.env["llm.provider"].sudo().search([("id", "=", model)])
                )
                messages = [
                    {
                        "role": "system",
                        "content": provider.system_message
                        or "You are a helpful assistant.",
                    },
                    {"role": "user", "content": prompt},
                ]
                
                response = self._call_provider_api(provider, messages)
                print("\n\n============= Full Response: ", response)
                return response
            else:
                IrConfigParameter = request.env["ir.config_parameter"].sudo()
                olg_api_endpoint = IrConfigParameter.get_param(
                    "web_editor.olg_api_endpoint", DEFAULT_OLG_ENDPOINT
                )
                database_id = IrConfigParameter.get_param("database.uuid")
                response = iap_tools.iap_jsonrpc(
                    olg_api_endpoint + "/api/olg/1/chat",
                    params={
                        "prompt": prompt,
                        "conversation_history": conversation_history or [],
                        "database_id": database_id,
                    },
                    timeout=30,
                )
                if response["status"] == "success":
                    return response["content"]
                elif response["status"] == "error_prompt_too_long":
                    raise UserError(
                        _(
                            "Sorry, your prompt is too long. Try to say it in fewer words."
                        )
                    )
                elif response["status"] == "limit_call_reached":
                    raise UserError(
                        _(
                            "You have reached the maximum number of requests for this service. Try again later."
                        )
                    )
                else:
                    raise UserError(
                        _(
                            "Sorry, we could not generate a response. Please try again later."
                        )
                    )

        except AccessError:
            raise AccessError(_("Oops, it looks like our AI is unreachable!"))

    def _call_provider_api(self, provider, messages):
        """Call the appropriate provider API directly"""
        try:
            if provider.provider_type == "openai":
                return self._call_openai_api(provider, messages)
            elif provider.provider_type == "groq":
                return self._call_groq_api(provider, messages)
            elif provider.provider_type == "anthropic":
                return self._call_anthropic_api(provider, messages)
            elif provider.provider_type == "google":
                return self._call_google_api(provider, messages)
            else:
                raise UserError(_(f"Unsupported provider type: {provider.provider_type}"))
        except Exception as e:
            _logger.error(f"Provider API call failed: {str(e)}")
            raise UserError(_(f"Failed to generate response: {str(e)}"))

    def _call_openai_api(self, provider, messages):
        """Call OpenAI API directly"""
        headers = {
            "Authorization": f"Bearer {provider.api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": provider.model,
            "messages": messages,
            "temperature": 0.75
        }
        response = requests.post(
            "https://api.openai.com/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]

    def _call_groq_api(self, provider, messages):
        """Call Groq API directly"""
        headers = {
            "Authorization": f"Bearer {provider.api_key}",
            "Content-Type": "application/json"
        }
        data = {
            "model": provider.model,
            "messages": messages,
            "temperature": 0.75
        }
        response = requests.post(
            "https://api.groq.com/openai/v1/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"]

    def _call_anthropic_api(self, provider, messages):
        """Call Anthropic API directly"""
        headers = {
            "x-api-key": provider.api_key,
            "Content-Type": "application/json",
            "anthropic-version": "2023-06-01"
        }
        # Convert OpenAI format to Anthropic format
        system_message = ""
        user_messages = []
        for msg in messages:
            if msg["role"] == "system":
                system_message = msg["content"]
            else:
                user_messages.append(msg)
        
        data = {
            "model": provider.model,
            "max_tokens": 1024,
            "messages": user_messages,
            "temperature": 0.75
        }
        if system_message:
            data["system"] = system_message
            
        response = requests.post(
            "https://api.anthropic.com/v1/messages",
            headers=headers,
            json=data,
            timeout=30
        )
        response.raise_for_status()
        return response.json()["content"][0]["text"]

    def _call_google_api(self, provider, messages):
        """Call Google Gemini API using google-genai client"""
        try:
            # Extract system and user messages
            system_message = ""
            user_content = ""
            for msg in messages:
                if msg["role"] == "system":
                    system_message = msg["content"]
                elif msg["role"] == "user":
                    user_content = msg["content"]
            
            # Combine system and user messages for Gemini
            full_prompt = f"{system_message}\n\nUser: {user_content}" if system_message else user_content
            
            client = genai.Client(api_key=provider.api_key)
            response = client.models.generate_content(
                model=provider.model,
                contents=full_prompt,
            )
            return response.text
        except Exception as e:
            _logger.error(f"Google API call failed: {str(e)}")
            raise UserError(_(f"Failed to generate response from Google: {str(e)}"))

    def _format_query_results(self, results, query_structure):
        """Dynamically format query results based on column types and relationships"""
        formatted_results = []

        for row in results:
            formatted_row = {}
            for key, value in row.items():
                # Handle different field types
                if value is None:
                    formatted_row[key] = "N/A"
                    continue

                # Detect and format foreign key fields
                if key.endswith("_id"):
                    base_model = self._detect_model_from_field(key)
                    if base_model:
                        try:
                            record = request.env[base_model].sudo().browse(value)
                            display_name = (
                                record.display_name
                                if record.exists()
                                else f"Unknown ({value})"
                            )
                            formatted_row[key.replace("_id", "_name")] = display_name
                            formatted_row[key] = value  # Keep the ID as well
                        except Exception as e:
                            formatted_row[key] = value
                    continue

                # Format numeric values
                if isinstance(value, (float, int)):
                    if "amount" in key or "price" in key or "total" in key:
                        formatted_row[key] = f"{value:,.2f}"
                    else:
                        formatted_row[key] = f"{value:,}"
                    continue

                # Format dates
                if isinstance(value, (datetime.date, datetime.datetime)):
                    formatted_row[key] = value.strftime("%Y-%m-%d %H:%M:%S")
                    continue

                formatted_row[key] = value

            formatted_results.append(formatted_row)

        return formatted_results

    def _detect_model_from_field(self, field_name):
        """Detect the related model based on field name and common patterns"""
        common_models = {
            "partner_id": "res.partner",
            "user_id": "res.users",
            "company_id": "res.company",
            "currency_id": "res.currency",
            "country_id": "res.country",
            "state_id": "res.country.state",
            "product_id": "product.product",
            "category_id": "product.category",
            # Add more common mappings as needed
        }
        return common_models.get(field_name)

    def _format_response_text(self, formatted_results, query_structure):
        """Dynamically format response text based on result structure"""
        if not formatted_results:
            return "No results found."
        response_lines = ["✅ Successfully generated data. You can view it using the following links:"]
        for item in formatted_results:
            dashboard_url = item.get("dashboard_url")
            view_table = item.get("view_table")

            if dashboard_url and view_table:
                response_lines.append(f"📊 Dashboard Url: {dashboard_url}")
                response_lines.append(f"📋 Table View: {view_table}")

        return "\n".join(response_lines)

    def _get_sql_agent_for_provider(self, provider_id):
        """Get or create SQL agent for specific provider"""
        SqlAgent = request.env["sql.database.agent"].sudo()

        sql_agent = SqlAgent.search(
            [("llm_wrapper.id", "=", provider_id), ("active", "=", True)], limit=1
        )

        if not sql_agent:
            sql_agent = SqlAgent.create({"llm_wrapper": provider_id, "active": True})

        return sql_agent

    @http.route("/multi_ai_chatbot/generate_text", type="json", auth="user")
    def generate_text(self, prompt, model=None, conversation_history=None):
        try:
            if not model:
                return self.generate_response(prompt, model, conversation_history)

            provider = request.env["llm.provider"].sudo().search([("id", "=", model)])
            # If provider is not found or is not a SQL agent, use default behavior
            if not provider or not provider.is_sql_agent:
                return self.generate_response(prompt, model, conversation_history)

            # Get SQL agent
            sql_agent = self._get_sql_agent_for_provider(provider.id)
            response_text_error = ""
            count = 0
            max_retries = 3

            while count < max_retries:
                query_result = sql_agent.generate_sql_query(prompt)

                # If SQL generation fails, retry
                if not query_result.get("success"):
                    _logger.warning(
                        f"Query generation failed. Retrying... Attempt {count + 1} of {max_retries}"
                    )
                    response_text_error = query_result.get("error", "")
                    count += 1
                    continue

                # Extract the SQL query and metadata
                sql_query = query_result.get("query")
                query_structure = query_result.get(
                    "metadata", {}
                )  # Additional metadata about the query (model, timestamp)

                try:
                    # Try executing the SQL query
                    request.env.cr.execute(sql_query)
                    results = request.env.cr.dictfetchall()

                    # Format results
                    formatted_results = self._format_query_results(
                        results, query_structure
                    )

                    # Create result record
                    query_record = (
                        request.env["sql.query.result"]
                        .sudo()
                        .create(
                            {
                                "name": prompt[:64],
                                "query_text": sql_query,
                                "result_data": json.dumps(formatted_results),
                                "llm_provider_id": sql_agent.llm_wrapper.id,
                                "company_id": request.env.company.id,
                                "state": "draft",
                            }
                        )
                    )

                    # Automatically validate and create dashboard
                    query_record.action_validate()

                    # Add dashboard URL if available
                    if query_record.dashboard_url:
                        _logger.info(query_record.dashboard_url)
                        base_url = (
                            request.env["ir.config_parameter"].sudo().get_param("web.base.url").rstrip("/")
                        )
                        table_view_action = query_record.action_view_table()
                        table_view_url = table_view_action.get('url', '')
                        full_table_view_url = f"{base_url}{table_view_url}"
                        formatted_results.append(
                            {"dashboard_url": query_record.dashboard_url,
                             "view_table": full_table_view_url}
                        )
                    
                    # Generate response text
                    response_text = self._format_response_text(
                        formatted_results, query_structure
                    )
                    return response_text

                except Exception as e:
                    _logger.error(f"SQL execution failed: {str(e)}")
                    count += 1
                    _logger.warning(
                        f"SQL execution error. Retrying... Attempt {count} of {max_retries}"
                    )

            # If all retries fail
            # response_text = "Failed to execute the SQL query."
            return response_text_error

        except AccessError:
            raise AccessError(
                _(
                    "Access Error: Unable to reach the AI service. Please check your permissions and try again."
                )
            )
        except Exception as e:
            _logger.error("Error in generate_text: %s", str(e))
            return {"error": str(e)}

class QueryTableController(http.Controller):
    @http.route('/web/query/table/<int:query_id>', type='http', auth='user', website=True)
    def query_table_view(self, query_id, page=1, page_size=10, **kwargs):
        # Convert string parameters to integers
        try:
            page = int(page)
            page_size = int(page_size)
        except ValueError:
            page = 1
            page_size = 10
            
        # Ensure positive values
        page = max(1, page)
        page_size = max(1, min(100, page_size))  # Limit page size to 100 max
        
        query = request.env['sql.query.result'].browse(query_id)
        if not query.exists():
            return request.not_found()
            
        try:
            data = json.loads(query.result_data or '[]')
            if not data:
                return request.render('vpcs_multi_agent.query_table_template', {
                    'query': query,
                    'data': [],
                    'columns': [],
                    'error': 'No data available',
                    'page_name': f'Query Results - {query.name}',
                    'breadcrumbs': [
                        {'title': 'Home', 'url': '/'},
                        {'title': 'SQL Query Results', 'url': '/web#action=vpcs_multi_agent.action_sql_query_results'},
                        {'title': query.name, 'url': '#'}
                    ],
                    'current_page': page,
                    'page_size': page_size,
                    'total_pages': 1
                })
                
            columns = list(data[0].keys())
            
            # Calculate total pages
            total_pages = (len(data) + page_size - 1) // page_size
            
            # Adjust page if it's out of range
            if page > total_pages:
                page = total_pages
            
            return request.render('vpcs_multi_agent.query_table_template', {
                'query': query,
                'data': data,
                'columns': columns,
                'error': False,
                'page_name': f'Query Results - {query.name}',
                'breadcrumbs': [
                    {'title': 'Home', 'url': '/'},
                    {'title': 'SQL Query Results', 'url': '/web#action=vpcs_multi_agent.action_sql_query_results'},
                    {'title': query.name, 'url': '#'}
                ],
                'current_page': page,
                'page_size': page_size,
                'total_pages': total_pages
            })
        except Exception as e:
            return request.render('vpcs_multi_agent.query_table_template', {
                'query': query,
                'data': [],
                'columns': [],
                'error': str(e),
                'page_name': f'Query Results - {query.name}',
                'breadcrumbs': [
                    {'title': 'Home', 'url': '/'},
                    {'title': 'SQL Query Results', 'url': '/web#action=vpcs_multi_agent.action_sql_query_results'},
                    {'title': query.name, 'url': '#'}
                ],
                'current_page': 1,
                'page_size': page_size,
                'total_pages': 1
            })