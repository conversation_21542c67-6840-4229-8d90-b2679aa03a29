# LangGraph Tracing with Opik

This document explains how to use Opik for tracing and monitoring LangGraph agents in the Odoo Law Agents module.

## Overview

The Odoo Law Agents module integrates with [Opik](https://www.comet.com/docs/opik/tracing/integrations/langgraph/) to provide tracing and monitoring capabilities for LangGraph agents. This allows you to:

- Visualize the execution flow of LangGraph agents
- Monitor agent performance and behavior
- Debug issues in agent execution
- Analyze agent decision-making

## Setup

There are two ways to configure Opik tracing:

### Option 1: Configure in the Odoo Interface

1. Go to Law Agents > Law Agents
2. Select or create an agent
3. Go to the "Tracing Configuration" tab
4. Enable tracing and enter your Opik API key
5. Set the project name (default: "odoo_law_agents")

### Option 2: Configure with Environment Variables

Alternatively, you can set the following environment variables:

```bash
# Enable Opik tracing
export ENABLE_OPIK_TRACING=True

# Set your Opik API key
export OPIK_API_KEY=your_api_key_here
```

### Install Opik

Make sure the Opik package is installed:

```bash
pip install opik
```

You can get an API key by signing up at [Comet.com](https://www.comet.com/).

### Restart Odoo

Restart the Odoo server to apply the changes.

## Usage

Once configured, tracing will be automatically enabled for all LangGraph agent executions. You can view the traces in the Opik dashboard.

### Viewing Traces

1. Log in to your Comet.com account
2. Navigate to the Opik section
3. Select the "odoo_law_agents" project
4. View the traces for your agent executions

### Trace Information

Each trace includes:

- Node execution sequence
- Input and output states for each node
- Execution time for each node
- Decision points and routing conditions
- Error information (if any)

## Disabling Tracing

To disable tracing, set:

```bash
export ENABLE_OPIK_TRACING=False
```

## Troubleshooting

If you encounter issues with tracing:

1. Check that the Opik package is installed correctly
2. Verify that the environment variables are set correctly
3. Check the Odoo logs for any error messages related to Opik
4. Ensure your API key is valid and has the necessary permissions

### Known Issues

#### SentryHubDeprecationWarning

The Opik library may show a deprecation warning related to Sentry Hub usage:

```
SentryHubDeprecationWarning: `sentry_sdk.Hub` is deprecated and will be removed in a future major release.
```

This warning comes from the Opik library itself and not from our code. We've implemented warning suppression to prevent these messages from cluttering the logs. If you still see these warnings, you can safely ignore them as they don't affect the functionality of the tracing system.

## References

- [Opik Documentation](https://www.comet.com/docs/opik/tracing/integrations/langgraph/)
- [LangGraph Documentation](https://langchain-ai.github.io/langgraph/)
