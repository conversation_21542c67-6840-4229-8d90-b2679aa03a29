from odoo import models, fields, _, api
import logging
import os
from pathlib import Path

_logger = logging.getLogger(__name__)

# Import graph visualization utilities
try:
    # First try to import the Mermaid visualization utilities
    from ..tools.mermaid_visualization import save_graph_to_odoo_filestore, save_graph_to_folder
    HAS_GRAPH_VIZ = True
    GRAPH_VIZ_TYPE = "mermaid"
    _logger.info("Using Mermaid for graph visualization.")
except ImportError:
    try:
        # Fall back to GraphViz if Mermaid is not available
        from ..tools.graph_visualization import save_graph_to_odoo_filestore, save_graph_to_folder
        HAS_GRAPH_VIZ = True
        GRAPH_VIZ_TYPE = "graphviz"
        _logger.info("Using GraphViz for graph visualization.")
    except ImportError:
        _logger.warning("Graph visualization utilities not available.")
        HAS_GRAPH_VIZ = False
        GRAPH_VIZ_TYPE = None

class LawAgent(models.Model):
    _name = 'law.agent'
    _description = 'Law Agent'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(required=True, tracking=True)
    agent_type = fields.Selection([
        ('gujarat', 'Gujarat Law'),
        ('civil', 'Civil Law'),
        ('land', 'Land Law')
    ], required=True, tracking=True)
    active = fields.Boolean(default=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('active', 'Active'),
        ('inactive', 'Inactive')
    ], default='draft', tracking=True)
    company_id = fields.Many2one('res.company', required=True, default=lambda self: self.env.company)

    # Opik Tracing Configuration
    enable_tracing = fields.Boolean(string='Enable Opik Tracing', default=False,
                                   help='Enable tracing of agent execution using Opik')
    opik_api_key = fields.Char(string='Opik API Key',
                               help='API key for Opik tracing. If empty, the OPIK_API_KEY environment variable will be used.')
    opik_project_name = fields.Char(string='Opik Project Name', default='odoo_law_agents',
                                   help='Project name for Opik tracing')
    opik_workspace = fields.Char(string='Opik Workspace',
                                help='Workspace name for Opik tracing. If empty, the OPIK_WORKSPACE environment variable will be used.')

    # Graph Visualization
    graph_attachment_id = fields.Many2one('ir.attachment', string='Graph Visualization',
                                        help='Attachment containing the graph visualization')
    graph_download_url = fields.Char(string='Graph Download URL', compute='_compute_graph_download_url',
                                    help='URL to download the graph visualization')

    # Testing and Development Configuration
    use_mock = fields.Boolean(string='Use Mock Responses', default=False,
                             help='Use mock responses instead of calling the LLM API. Useful for testing and development.')
    debug_mode = fields.Boolean(string='Debug Mode', default=False,
                              help='Enable additional logging and debugging information')

    @api.depends('graph_attachment_id')
    def _compute_graph_download_url(self):
        """Compute the download URL for the graph visualization."""
        for record in self:
            if record.graph_attachment_id:
                record.graph_download_url = f'/web/content/{record.graph_attachment_id.id}?download=true'
            else:
                record.graph_download_url = False

    def generate_graph_visualization(self, format='png', save_to_folder=False, folder_path=None):
        """Generate a visualization of the agent's graph.

        Args:
            format: The output format (png, pdf, svg, etc.).
            save_to_folder: Whether to save the graph to a folder.
            folder_path: The path to the folder to save the graph in.

        Returns:
            A dictionary with information about the generated graph.
        """
        self.ensure_one()

        if not HAS_GRAPH_VIZ:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Graph visualization utilities not available. Please install required packages.'),
                    'sticky': False,
                    'type': 'danger',
                }
            }

        _logger.info(f"Using {GRAPH_VIZ_TYPE} for graph visualization.")

        # Import here to avoid import errors
        from ..langgraph.agent import create_agent

        try:
            # Create the agent graph
            agent_type = self.agent_type
            _logger.info(f"Generating graph for agent type: {agent_type}")
            # Set the agent type as an attribute on the agent object for visualization
            agent = create_agent(agent_type)
            # Add the agent_type as an attribute to the agent object
            if agent:
                agent.agent_type = agent_type

            if not agent:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Failed to create agent graph.'),
                        'sticky': False,
                        'type': 'danger',
                    }
                }

            # Generate a filename based on the agent name
            filename = f"{self.name.lower().replace(' ', '_')}_graph"

            # Save to folder if requested
            if save_to_folder:
                if not folder_path:
                    # Use a default folder in the user's home directory
                    home_dir = str(Path.home())
                    folder_path = os.path.join(home_dir, 'odoo_law_agents_graphs')

                # Save the graph to the folder
                # Check which visualization type we're using
                if GRAPH_VIZ_TYPE == "mermaid":
                    # Mermaid visualization doesn't use the format parameter
                    output_path = save_graph_to_folder(agent, folder_path, filename)
                else:
                    # GraphViz visualization uses the format parameter
                    output_path = save_graph_to_folder(agent, folder_path, filename, format)

                if not output_path:
                    return {
                        'type': 'ir.actions.client',
                        'tag': 'display_notification',
                        'params': {
                            'title': _('Error'),
                            'message': _('Failed to save graph to folder.'),
                            'sticky': False,
                            'type': 'danger',
                        }
                    }

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Success'),
                        'message': _('Graph saved to %s') % output_path,
                        'sticky': False,
                        'type': 'success',
                    }
                }

            # Save to Odoo filestore
            # Check which visualization type we're using
            if GRAPH_VIZ_TYPE == "mermaid":
                # Mermaid visualization doesn't use the format parameter
                result = save_graph_to_odoo_filestore(self.env, agent, filename)
            else:
                # GraphViz visualization uses the format parameter
                result = save_graph_to_odoo_filestore(self.env, agent, filename, format)

            if not result:
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _('Failed to save graph to Odoo filestore.'),
                        'sticky': False,
                        'type': 'danger',
                    }
                }

            # Update the attachment ID
            self.write({
                'graph_attachment_id': result['attachment_id'],
            })

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Graph visualization generated successfully.'),
                    'sticky': False,
                    'type': 'success',
                }
            }
        except Exception as e:
            _logger.error(f"Error generating graph visualization: {str(e)}")
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _('Failed to generate graph visualization: %s') % str(e),
                    'sticky': False,
                    'type': 'danger',
                }
            }

    def _process_query(self, query, language='en'):
        """Process a query using the LangGraph agent."""
        try:
            from ..langgraph.agent import process_query

            # Get the current user ID
            user_id = self.env.user.id

            # Check if debug mode is enabled - handle case when field doesn't exist yet
            debug_mode = False
            try:
                debug_mode = self.debug_mode
            except Exception:
                _logger.warning("debug_mode field not available yet")

            if debug_mode:
                _logger.info(f"Processing query in debug mode: {query[:100]}...")

            # Handle case when use_mock field doesn't exist yet
            use_mock = False
            try:
                use_mock = self.use_mock
            except Exception:
                _logger.warning("use_mock field not available yet")

            # Check if tracing is enabled - handle case when field doesn't exist yet
            enable_tracing = False
            try:
                enable_tracing = self.enable_tracing
                if enable_tracing:
                    _logger.info(f"Tracing enabled for agent: {self.name}")
                    _logger.info(f"Project name: {self.opik_project_name}")
                    _logger.info(f"Workspace: {self.opik_workspace if hasattr(self, 'opik_workspace') else 'Not set'}")

                    # Log API key status without revealing the key
                    if self.opik_api_key:
                        _logger.info("API key is provided.")
                    else:
                        _logger.warning("No API key provided in agent configuration.")
            except Exception as e:
                _logger.warning(f"Error checking tracing configuration: {str(e)}")

            # Process the query with agent configuration for tracing and mock mode
            result = process_query(
                query=query,
                language=language,
                user_id=user_id,
                agent_id=self.id,
                agent_model=self,  # Pass the agent model for tracing configuration
                env=self.env,  # Pass the environment directly
                use_mock=use_mock,  # Pass the mock mode flag
                self_ref=self  # Pass self reference for database access
            )

            # Log additional information in debug mode
            if debug_mode:  # Use the local variable we safely checked earlier
                _logger.info(f"Query type: {result.get('query_type', 'unknown')}")
                _logger.info(f"Processing time: {result.get('duration', 0):.2f} seconds")
                if 'warning' in result:
                    _logger.warning(f"Warning during processing: {result['warning']}")
                if 'error' in result and result['error']:
                    _logger.error(f"Error during processing: {result['error']}")

            # Return the full result dictionary instead of just the response string
            # This ensures that sources and other metadata are available to the caller
            _logger.info(f"Returning full result dictionary with {len(result.get('sources', []))} sources")
            return result
        except ImportError as e:
            error_msg = f"Error importing LangGraph agent: {str(e)}"
            _logger.error(error_msg)
            # Return a dictionary with error information
            return {
                'response': f"Error: LangGraph agent not available. {str(e)}",
                'error': error_msg,
                'sources': [],
                'duration': 0
            }
        except Exception as e:
            error_msg = f"Error processing query: {str(e)}"
            _logger.error(error_msg)
            # Return a dictionary with error information
            return {
                'response': f"Error processing query: {str(e)}",
                'error': error_msg,
                'sources': [],
                'duration': 0
            }