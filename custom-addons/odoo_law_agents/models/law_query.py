from odoo import models, fields, _, api
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class LawQuery(models.Model):
    _name = 'law.query'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Legal Query'
    _order = 'create_date desc'

    name = fields.Char(string='Query', required=True, tracking=True)
    user_id = fields.Many2one('res.users', string='User', default=lambda self: self.env.user, required=True, tracking=True)
    agent_id = fields.Many2one('law.agent', string='Agent', required=True, tracking=True)
    response = fields.Text(string='Response', tracking=True)
    acknowledgment_sent = fields.Boolean(string='Acknowledgment Sent', default=False, tracking=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ], default='draft', tracking=True)
    duration = fields.Float(string='Processing Time (sec)', readonly=True)
    company_id = fields.Many2one('res.company', required=True, default=lambda self: self.env.company)
    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati')
    ], string='Language', default='en', required=True)
    source_ids = fields.One2many('law.query.source', 'query_id', string='Sources')

    def process_query(self):
        """Process the query using the selected agent with LangGraph and RAG"""
        for record in self:
            record.write({'state': 'processing'})
            try:
                # Call the agent's process method
                start_time = fields.Datetime.now()

                # Process the query with the agent
                _logger.info(f"Processing query: '{record.name}' with agent: {record.agent_id.name} in language: {record.language}")
                result = record.agent_id._process_query(record.name, record.language)

                end_time = fields.Datetime.now()
                duration = (end_time - start_time).total_seconds()
                _logger.info(f"Query processing completed in {duration:.2f} seconds")

                # Extract response and sources if available
                if isinstance(result, dict):
                    # Log the complete result dictionary for debugging
                    _logger.info(f"Query result dictionary for query ID {record.id}:")
                    for key, value in result.items():
                        if key == 'sources':
                            _logger.info(f"  - sources: {len(result.get('sources', []))} source(s) found")
                            for i, source in enumerate(result.get('sources', []), 1):
                                source_type = source.get('type', 'unknown')
                                if source_type == 'document':
                                    _logger.info(f"    {i}. Document: {source.get('name', 'Unnamed')} (ID: {source.get('id')})")
                                elif source_type == 'web':
                                    _logger.info(f"    {i}. Web: {source.get('title', 'Untitled')} - {source.get('source', 'Unknown source')}")
                                else:
                                    _logger.info(f"    {i}. {source_type.capitalize()}: {source.get('name', 'Unnamed')}")
                        elif key == 'response':
                            # Log a truncated version of the response to avoid huge log entries
                            response_preview = str(value)[:200] + '...' if len(str(value)) > 200 else str(value)
                            _logger.info(f"  - response: {response_preview}")
                        else:
                            _logger.info(f"  - {key}: {value}")

                    # Extract the response text and sources
                    response_text = result.get('response', 'No response generated')
                    sources = result.get('sources', [])
                    error = result.get('error')
                    query_type = result.get('query_type', 'unknown')

                    # Log the query type if available
                    if query_type != 'unknown':
                        _logger.info(f"Query type detected: {query_type}")

                    if error:
                        _logger.error(f"Error processing query ID {record.id}: {error}")
                        record.write({
                            'response': f"Error: {error}",
                            'state': 'failed',
                            'duration': duration
                        })
                        return True

                    # Log source information
                    if sources:
                        _logger.info(f"Creating {len(sources)} source records for query ID {record.id}")
                    else:
                        _logger.info(f"No sources found for query ID {record.id}")

                    # Create source records
                    self._create_source_records(record.id, sources)
                else:
                    # If result is not a dictionary, it's just a string response
                    response_text = result
                    _logger.info(f"Query result is a simple string (not a dictionary) for query ID {record.id}")

                record.write({
                    'response': response_text,
                    'state': 'completed',
                    'duration': duration
                })
            except Exception as e:
                _logger.error(f"Exception processing query: {str(e)}")
                record.write({
                    'response': f"Error: {str(e)}",
                    'state': 'failed'
                })
        return True

    def _create_source_records(self, query_id, sources):
        """Create source records for the query"""
        if not sources:
            _logger.info(f"No sources to create for query ID {query_id}")
            return

        _logger.info(f"Creating {len(sources)} source records for query ID {query_id}")
        LawQuerySource = self.env['law.query.source']

        for i, source in enumerate(sources, 1):
            source_type = source.get('type', 'unknown')
            _logger.info(f"Processing source {i}/{len(sources)} of type '{source_type}' for query ID {query_id}")

            # Ensure source_type is one of the allowed values
            allowed_types = ['document', 'web', 'agent', 'civil_law', 'land_law', 'gujarat_law', 'mock', 'unknown']
            if source_type not in allowed_types:
                _logger.warning(f"Source type '{source_type}' is not in allowed types: {allowed_types}. Using 'unknown' instead.")
                source_type = 'unknown'

            vals = {
                'query_id': query_id,
                'source_type': source_type,
            }

            # Handle different source types
            if source_type == 'document':
                doc_name = source.get('name', 'Unknown Document')
                doc_id = source.get('id')
                vals.update({
                    'name': doc_name,
                    'document_id': doc_id,
                })
                _logger.info(f"  - Document source: {doc_name} (ID: {doc_id})")
            elif source_type == 'web':
                web_title = source.get('title', 'Unknown Web Source')
                web_url = source.get('link', '')
                web_source = source.get('source', '')
                vals.update({
                    'name': web_title,
                    'url': web_url,
                    'source': web_source,
                })
                _logger.info(f"  - Web source: {web_title} from {web_source}")
                if web_url:
                    _logger.info(f"    URL: {web_url[:100]}{'...' if len(web_url) > 100 else ''}")
            elif source_type in ['civil_law', 'land_law', 'gujarat_law', 'agent', 'mock']:
                # Handle specialized knowledge sources
                source_name = source.get('name', source.get('description', f"{source_type.replace('_', ' ').title()} Knowledge"))
                vals.update({
                    'name': source_name,
                })
                _logger.info(f"  - {source_type.replace('_', ' ').title()} source: {source_name}")
            else:
                # Handle unknown or other source types
                source_name = source.get('description', source.get('name', 'Unknown Source'))
                vals.update({
                    'name': source_name,
                })
                _logger.info(f"  - Other source ({source_type}): {source_name}")

            # Add relevance score if available
            if 'relevance' in source:
                vals['relevance'] = source.get('relevance', 0.0)
                _logger.info(f"  - Relevance score: {source.get('relevance', 0.0):.2f}")

            # Create the source record
            try:
                new_source = LawQuerySource.create(vals)
                _logger.info(f"  - Created source record ID {new_source.id} for query ID {query_id}")
            except Exception as e:
                _logger.error(f"Error creating source record for query ID {query_id}: {str(e)}")

        _logger.info(f"Completed creating source records for query ID {query_id}")

    def action_reset(self):
        """Reset the query to draft state"""
        for record in self:
            _logger.info(f"Resetting query ID {record.id} from state '{record.state}' to 'draft'")

            # Delete existing sources if any
            if record.source_ids:
                source_count = len(record.source_ids)
                _logger.info(f"Deleting {source_count} source records for query ID {record.id}")
                try:
                    record.source_ids.unlink()
                    _logger.info(f"Successfully deleted {source_count} source records for query ID {record.id}")
                except Exception as e:
                    _logger.error(f"Error deleting source records for query ID {record.id}: {str(e)}")

            # Reset the query
            try:
                record.write({
                    'state': 'draft',
                    'response': False,
                    'duration': 0,
                    'acknowledgment_sent': False
                })
                _logger.info(f"Successfully reset query ID {record.id} to draft state")
            except Exception as e:
                _logger.error(f"Error resetting query ID {record.id}: {str(e)}")

        return True

    def action_send_acknowledgment(self):
        """Send an acknowledgment message to the user."""
        self.ensure_one()
        if self.state != 'completed':
            raise UserError(_('You can only send acknowledgments for completed queries.'))

        if self.acknowledgment_sent:
            raise UserError(_('An acknowledgment has already been sent for this query.'))

        try:
            # Create a message in the chatter
            message = _("Thank you for your query. Your response has been processed and is now available.")
            if self.response:
                message += _('\n\nSummary: %s') % (self.response[:200] + '...' if len(self.response) > 200 else self.response)

            self.message_post(
                body=message,
                message_type='notification',
                subtype_xmlid='mail.mt_comment',
                partner_ids=[self.user_id.partner_id.id]
            )

            # Mark acknowledgment as sent
            self.write({'acknowledgment_sent': True})

            _logger.info(f"Acknowledgment sent for query ID {self.id} to user {self.user_id.name}")
            return {'type': 'ir.actions.client', 'tag': 'display_notification', 'params': {
                'title': _('Success'),
                'message': _('Acknowledgment sent successfully.'),
                'sticky': False,
                'type': 'success'
            }}
        except Exception as e:
            _logger.error(f"Error sending acknowledgment for query ID {self.id}: {str(e)}")
            return {'type': 'ir.actions.client', 'tag': 'display_notification', 'params': {
                'title': _('Error'),
                'message': _('Failed to send acknowledgment: %s') % str(e),
                'sticky': True,
                'type': 'danger'
            }}
