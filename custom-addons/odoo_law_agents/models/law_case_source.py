from odoo import models, fields, _

class LawCaseSource(models.Model):
    _name = 'law.case.source'
    _description = 'Law Case Source'
    
    name = fields.Char(string='Name', required=True)
    case_id = fields.Many2one('law.case', string='Case', required=True, ondelete='cascade')
    source_type = fields.Selection([
        ('document', 'Document'),
        ('web', 'Web Source'),
        ('agent', 'Agent Knowledge'),
        ('civil_law', 'Civil Law'),
        ('land_law', 'Land Law'),
        ('property_law', 'Property Law'),
        ('inheritance_law', 'Inheritance Law'),
        ('precedent', 'Legal Precedent'),
        ('statute', 'Statute'),
        ('mock', 'Mock Source'),
        ('unknown', 'Unknown')
    ], string='Source Type', required=True, default='unknown')
    document_id = fields.Many2one('law.document', string='Document')
    url = fields.Char(string='URL')
    source = fields.Char(string='Source')
    relevance = fields.Float(string='Relevance Score', default=0.0)
    excerpt = fields.Text(string='Excerpt', help="Relevant excerpt from the source")
    
    def action_open_source(self):
        """Open the source based on its type"""
        self.ensure_one()
        
        if self.source_type == 'document' and self.document_id:
            # Return action to open the document
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'law.document',
                'res_id': self.document_id.id,
                'view_mode': 'form',
                'target': 'current',
            }
        elif self.source_type == 'web' and self.url:
            # Return action to open the URL
            return {
                'type': 'ir.actions.act_url',
                'url': self.url,
                'target': 'new',
            }
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('No Source'),
                'message': _('No source available to open.'),
                'sticky': False,
                'type': 'warning',
            }
        }
