from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import os

_logger = logging.getLogger(__name__)

class LawDocument(models.Model):
    _name = 'law.document'
    _description = 'Law Document'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(required=True, tracking=True)
    # Legacy field for backward compatibility
    document_type = fields.Selection([
        ('act', 'Act'),
        ('judgment', 'Judgment'),
        ('regulation', 'Regulation'),
        ('land_law', 'Land Law'),
        ('civil_law', 'Civil Law')
    ], tracking=True)

    # New document type fields
    document_type_ids = fields.Many2many('document.type', string='Document Types', tracking=True)
    law_type_id = fields.Many2one('law.type', string='Law Type', tracking=True)
    category_id = fields.Many2one('document.category', string='Document Category', tracking=True)
    content = fields.Text(required=True)
    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati')
    ], required=True, default='en', tracking=True)

    # Original file storage
    original_file = fields.Binary(string='Original File', attachment=True, help='The original document file')
    original_filename = fields.Char(string='Original Filename')
    file_type = fields.Selection([
        ('pdf', 'PDF'),
        ('docx', 'DOCX'),
        ('txt', 'TXT'),
        ('other', 'Other')
    ], string='File Type', tracking=True)

    embedding_ids = fields.One2many('law.embedding', 'document_id', string='Embeddings')
    embedding_count = fields.Integer(compute='_compute_embedding_count', string='Embedding Count')
    has_embeddings = fields.Boolean(compute='_compute_embedding_count', string='Has Embeddings', search='_search_has_embeddings')
    company_id = fields.Many2one('res.company', required=True, default=lambda self: self.env.company)

    @api.depends('embedding_ids')
    def _compute_embedding_count(self):
        for record in self:
            record.embedding_count = len(record.embedding_ids)
            record.has_embeddings = record.embedding_count > 0

    @api.onchange('law_type_id', 'category_id')
    def _onchange_law_type_category(self):
        """Update document_type_ids when law_type_id or category_id changes."""
        if self.law_type_id and self.category_id:
            # Find the document type that matches the selected law type and category
            document_type = self.env['document.type'].search([
                ('law_type_id', '=', self.law_type_id.id),
                ('category_id', '=', self.category_id.id),
                ('active', '=', True)
            ], limit=1)

            if document_type:
                self.document_type_ids = [(4, document_type.id, 0)]

    @api.onchange('document_type_ids')
    def _onchange_document_type_ids(self):
        """Update legacy document_type field for backward compatibility."""
        if self.document_type_ids:
            # Get the first document type for backward compatibility
            document_type = self.document_type_ids[0]

            # Map to legacy document_type
            if document_type.law_type_id.code == 'land_law':
                self.document_type = 'land_law'
            elif document_type.law_type_id.code == 'civil_law':
                self.document_type = 'civil_law'
            elif document_type.category_id.code == 'act':
                self.document_type = 'act'
            elif document_type.category_id.code == 'judgment':
                self.document_type = 'judgment'
            elif document_type.category_id.code == 'regulation':
                self.document_type = 'regulation'

    def _search_has_embeddings(self, operator, value):
        """Search method for has_embeddings field."""
        # Find documents with embeddings
        documents_with_embeddings = self.env['law.embedding'].search([]).mapped('document_id')

        # Determine the domain based on the operator and value
        if (operator == '=' and value) or (operator == '!=' and not value):
            # Documents that have embeddings
            return [('id', 'in', documents_with_embeddings.ids)]
        else:
            # Documents that don't have embeddings
            return [('id', 'not in', documents_with_embeddings.ids)]

    def generate_embeddings(self):
        """Generate embeddings for the document (programmatic use)."""
        self.ensure_one()
        embedding_ids = []

        try:
            # Check if document has content
            if not self.content or not self.content.strip():
                _logger.error(f"Document {self.name} has no content to generate embeddings from")
                return []

            # Import the embedding service
            from ..services.embedding_service import EmbeddingService

            # Create the service and generate embeddings
            service = EmbeddingService(self.env)
            _logger.info(f"Starting embedding generation for document {self.name} using service")
            embedding_ids = service.create_embeddings(self)
            _logger.info(f"Embedding generation completed for document {self.name}, created {len(embedding_ids)} embeddings")

            return embedding_ids
        except ImportError as e:
            _logger.error(f"Import error when generating embeddings: {str(e)}")
            return []
        except Exception as e:
            _logger.error(f"Error generating embeddings for {self.name}: {str(e)}")
            return []

    def action_generate_embeddings(self):
        """Generate embeddings for the document (UI action)."""
        self.ensure_one()

        # Check if document has content
        if not self.content or not self.content.strip():
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Content'),
                    'message': _('Document %s has no content to generate embeddings from.') % self.name,
                    'sticky': False,
                    'type': 'warning',
                }
            }

        # Log document size but don't warn or limit
        _logger.info(f"Document {self.name} size: {len(self.content)} chars")

        try:
            # Log the start of embedding generation
            _logger.info(f"Starting embedding generation for document: {self.name} (ID: {self.id})")
            _logger.info(f"Document content length: {len(self.content)} characters")
            _logger.info(f"Document file type: {self.file_type or 'unknown'}")

            # Generate embeddings
            embedding_ids = self.generate_embeddings()

            if embedding_ids:
                _logger.info(f"Successfully generated {len(embedding_ids)} embeddings for document {self.name}")
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Embeddings Generated'),
                        'message': _('%s embeddings created for %s') % (len(embedding_ids), self.name),
                        'sticky': False,
                        'type': 'success',
                    }
                }
            else:
                _logger.warning(f"No embeddings were created for document {self.name}")
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('No Embeddings'),
                        'message': _('No embeddings were created for %s. Check the logs for errors.') % self.name,
                        'sticky': False,
                        'type': 'warning',
                    }
                }
        except ImportError as e:
            _logger.error(f"Import error when generating embeddings: {str(e)}")
            raise UserError(_('Sentence Transformers library is not installed. Please install it to generate embeddings.'))
        except Exception as e:
            _logger.error(f"Error generating embeddings for {self.name}: {str(e)}")
            # Return a more detailed error message
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error Generating Embeddings'),
                    'message': _('Error generating embeddings for %s: %s. Check the logs for more details.') % (self.name, str(e)),
                    'sticky': True,
                    'type': 'danger',
                }
            }

    def action_view_embeddings(self):
        """View embeddings for the document."""
        self.ensure_one()
        return {
            'name': _('Embeddings'),
            'type': 'ir.actions.act_window',
            'res_model': 'law.embedding',
            'view_mode': 'tree,form',
            'domain': [('document_id', '=', self.id)],
            'context': {'default_document_id': self.id},
        }

    def action_generate_embeddings_batch(self):
        """Generate embeddings for multiple documents at once."""
        if not self:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Documents Selected'),
                    'message': _('Please select at least one document.'),
                    'sticky': False,
                    'type': 'warning',
                }
            }

        # Count documents with and without content
        docs_with_content = self.filtered(lambda d: d.content and d.content.strip())
        docs_without_content = self - docs_with_content

        if not docs_with_content:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('No Content'),
                    'message': _('None of the selected documents have content to generate embeddings from.'),
                    'sticky': False,
                    'type': 'warning',
                }
            }

        # Process documents with content
        success_count = 0
        error_count = 0

        for doc in docs_with_content:
            try:
                embedding_ids = doc.generate_embeddings()
                if embedding_ids:
                    success_count += 1
                else:
                    error_count += 1
            except Exception as e:
                _logger.error(f"Error generating embeddings for document {doc.name}: {str(e)}")
                error_count += 1

        # Prepare message
        message_parts = []
        if success_count:
            message_parts.append(_('%s documents processed successfully') % success_count)
        if error_count:
            message_parts.append(_('%s documents failed') % error_count)
        if docs_without_content:
            message_parts.append(_('%s documents skipped (no content)') % len(docs_without_content))

        message = ', '.join(message_parts)

        # Return notification
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Embedding Generation Complete'),
                'message': message,
                'sticky': False,
                'type': 'success' if success_count > 0 and error_count == 0 else 'warning',
            }
        }

    @api.model
    def get_default_data_folder(self, language='en'):
        """Get the default data folder path for the given language.

        Args:
            language: The language code ('en' or 'gu').

        Returns:
            The path to the default data folder.
        """
        try:
            # Use Odoo's standard module tools
            from odoo.modules.module import get_module_path

            # Get the module path directly using Odoo's function
            module_path = get_module_path('odoo_law_agents')
            if not module_path:
                # Fallback method if the above doesn't work
                module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                _logger.debug(f"Using fallback method to find module path: {module_path}")
        except Exception as e:
            _logger.error(f"Error finding module path: {str(e)}")
            # Fallback method if the above doesn't work
            module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            _logger.info(f"Using fallback method to find module path: {module_path}")

        # Construct the path based on the language
        lang_folder = 'english' if language == 'en' else 'gujarati'
        folder_path = os.path.join(module_path, 'data', lang_folder)

        return folder_path

    @api.model
    def load_documents_from_folder(self, folder_path=None, document_type=None, language='en'):
        """Load documents from a folder and create embeddings."""
        if not folder_path:
            # Use the default data folder
            folder_path = self.get_default_data_folder(language)
            _logger.info(f"Using default data folder: {folder_path}")
        else:
            _logger.info(f"Using provided folder path: {folder_path}")

        # Check if the folder exists
        if not os.path.exists(folder_path):
            _logger.warning(f"Folder does not exist: {folder_path}")
            # Try to get the absolute path
            abs_path = os.path.abspath(folder_path)
            _logger.info(f"Absolute path: {abs_path}")
            if not os.path.exists(abs_path):
                _logger.warning(f"Absolute path does not exist either: {abs_path}")

        try:
            from ..services.embedding_service import EmbeddingService
            service = EmbeddingService(self.env)

            document_ids = []

            # If document_type is specified, load only that type
            if document_type:
                subfolder = os.path.join(folder_path, document_type)
                if os.path.exists(subfolder) and os.path.isdir(subfolder):
                    doc_ids = service.load_documents_from_folder(subfolder, document_type, language)
                    document_ids.extend(doc_ids)

                    # Generate embeddings for the loaded documents
                    if doc_ids:
                        for doc_id in doc_ids:
                            doc = self.browse(doc_id)
                            if doc.exists() and not doc.has_embeddings:
                                try:
                                    _logger.info(f"Generating embeddings for document: {doc.name}")
                                    doc.generate_embeddings()
                                except Exception as e:
                                    _logger.error(f"Error generating embeddings for document {doc.name}: {str(e)}")
            else:
                # Load all document types from subfolders
                for subfolder_name in os.listdir(folder_path):
                    subfolder = os.path.join(folder_path, subfolder_name)
                    if os.path.isdir(subfolder):
                        # Map the subfolder name to a valid document_type value
                        if subfolder_name == 'land_law':
                            doc_type = 'land_law'
                        elif subfolder_name == 'civil_law':
                            doc_type = 'civil_law'
                        else:
                            # Skip unknown document types
                            _logger.warning(f"Unknown document type folder: {subfolder_name}")
                            continue

                        doc_ids = service.load_documents_from_folder(subfolder, doc_type, language)
                        document_ids.extend(doc_ids)

                        # Generate embeddings for the loaded documents
                        if doc_ids:
                            for doc_id in doc_ids:
                                doc = self.browse(doc_id)
                                if doc.exists() and not doc.has_embeddings:
                                    try:
                                        _logger.info(f"Generating embeddings for document: {doc.name}")
                                        doc.generate_embeddings()
                                    except Exception as e:
                                        _logger.error(f"Error generating embeddings for document {doc.name}: {str(e)}")

            return document_ids
        except ImportError:
            _logger.error('Sentence Transformers library is not installed.')
            return []
        except Exception as e:
            _logger.error(f"Error loading documents: {str(e)}")
            return []