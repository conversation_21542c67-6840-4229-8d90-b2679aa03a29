from odoo import models, fields, api, _

class LawType(models.Model):
    _name = 'law.type'
    _description = 'Law Type'
    _order = 'sequence, name'

    name = fields.Char(string='Name', required=True)
    code = fields.Char(string='Code', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(default=True)

    _sql_constraints = [
        ('code_uniq', 'unique (code)', 'The code must be unique!')
    ]


class DocumentCategory(models.Model):
    _name = 'document.category'
    _description = 'Document Category'
    _order = 'sequence, name'

    name = fields.Char(string='Name', required=True)
    code = fields.Char(string='Code', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(default=True)

    _sql_constraints = [
        ('code_uniq', 'unique (code)', 'The code must be unique!')
    ]


class DocumentType(models.Model):
    _name = 'document.type'
    _description = 'Document Type'
    _order = 'sequence, name'

    def _get_default_name(self):
        return _('New')
    
    name = fields.Char(
        string='Name',
        required=True,
        readonly=True,
        copy=False,
        default=_get_default_name
    )
    law_type_id = fields.Many2one('law.type', string='Law Type', required=True)
    category_id = fields.Many2one('document.category', string='Category', required=True)
    sequence = fields.Integer(string='Sequence', default=10)
    active = fields.Boolean(default=True)
    code = fields.Char(string='Code', compute='_compute_code', store=True)

    @api.model
    def _ensure_sequence(self):
        """Ensure the document.type sequence exists."""
        sequence = self.env['ir.sequence'].search([('code', '=', 'document.type')], limit=1)
        if not sequence:
            sequence = self.env['ir.sequence'].create({
                'name': 'Document Type Sequence',
                'code': 'document.type',
                'prefix': 'DOC/%(year)s/',
                'padding': 4,
                'number_next': 1,
                'number_increment': 1,
                'implementation': 'standard',
            })
        return sequence

    @api.model_create_multi
    def create(self, vals_list):
        # Ensure sequence exists before using it
        self._ensure_sequence()

        for vals in vals_list:
            if vals.get('name', _('New')) == _('New'):
                vals['name'] = self.env['ir.sequence'].next_by_code('document.type') or _('New')
        return super(DocumentType, self).create(vals_list)

    def write(self, vals):
        # Store the law type and category in the name for display purposes
        result = super(DocumentType, self).write(vals)
        # The display_name will be automatically recomputed when law_type_id or category_id changes
        # because of the @api.depends decorator on _compute_display_name
        return result

    # Add a computed field for display purposes
    display_name = fields.Char(compute='_compute_display_name', store=True)

    @api.depends('name', 'law_type_id', 'category_id')
    def _compute_display_name(self):
        for record in self:
            if record.law_type_id and record.category_id:
                record.display_name = f"{record.name} - {record.law_type_id.name} - {record.category_id.name}"
            else:
                record.display_name = record.name

    @api.depends('law_type_id', 'category_id')
    def _compute_code(self):
        for record in self:
            if record.law_type_id and record.category_id:
                record.code = f"{record.law_type_id.code}_{record.category_id.code}"
            else:
                record.code = False

    _sql_constraints = [
        ('law_type_category_uniq', 'unique (law_type_id, category_id)', 'The combination of law type and category must be unique!')
    ]

    # No need for a manual initialization method as Odoo handles sequences automatically
