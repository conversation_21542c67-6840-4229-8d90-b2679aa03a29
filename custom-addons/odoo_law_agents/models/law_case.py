from odoo import models, fields, _, api
import logging
import json
import threading
import base64
from datetime import datetime

_logger = logging.getLogger(__name__)

# Simple implementation of with_delay for background processing
def threaded_method(target):
    def wrapper(self, *args, **kwargs):
        thread = threading.Thread(target=lambda: target(self, *args, **kwargs))
        thread.daemon = True
        thread.start()
        return self
    return wrapper

# Add with_delay method to models.Model
models.Model.with_delay = lambda self: self

class LawCase(models.Model):
    _name = 'law.case'
    _inherit = ['mail.thread', 'mail.activity.mixin']
    _description = 'Legal Case'
    _order = 'create_date desc'

    name = fields.Char(string='Case Title', required=True, tracking=True)
    case_number = fields.Char(string='Case Number', tracking=True)
    case_text = fields.Text(string='Case Text', required=True, tracking=True,
                          help="The full text of the case to be analyzed")

    # Case metadata
    case_type = fields.Selection([
        ('property', 'Property Dispute'),
        ('inheritance', 'Inheritance'),
        ('contract', 'Contract Dispute'),
        ('civil', 'Other Civil Matter'),
        ('criminal', 'Criminal Matter'),
        ('family', 'Family Law'),
        ('other', 'Other')
    ], string='Case Type', default='property', required=True, tracking=True)

    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati'),
        ('hi', 'Hindi')
    ], string='Language', default='en', required=True, tracking=True)

    # Analysis state
    state = fields.Selection([
        ('draft', 'Draft'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed')
    ], default='draft', tracking=True)

    # Analysis results
    background_analysis = fields.Text(string='Background Analysis', tracking=True)
    legal_issues = fields.Text(string='Legal Issues', tracking=True)
    evidence_analysis = fields.Text(string='Evidence Analysis', tracking=True)
    legal_principles = fields.Text(string='Legal Principles', tracking=True)
    judgment_prediction = fields.Text(string='Judgment Prediction', tracking=True)
    full_analysis = fields.Text(string='Full Analysis', tracking=True)

    # Processing metadata
    processing_start = fields.Datetime(string='Processing Start', readonly=True)
    processing_end = fields.Datetime(string='Processing End', readonly=True)
    duration = fields.Float(string='Processing Time (sec)', readonly=True)

    # Relations
    user_id = fields.Many2one('res.users', string='User', default=lambda self: self.env.user, required=True, tracking=True)
    company_id = fields.Many2one('res.company', required=True, default=lambda self: self.env.company)
    source_ids = fields.One2many('law.case.source', 'case_id', string='Sources')

    # Analysis configuration
    use_online_search = fields.Boolean(string='Use Online Search', default=True,
                                     help="Enable online search for additional context")
    use_document_embeddings = fields.Boolean(string='Use Document Embeddings', default=True,
                                           help="Enable document embedding search for relevant precedents")
    use_mock = fields.Boolean(string='Use Mock Responses', default=False,
                            help="Use mock responses for testing")

    # Tracing configuration
    enable_tracing = fields.Boolean(string='Enable Tracing', default=False,
                                  help="Enable tracing of agent execution")



    def action_analyze_case(self):
        """Start the case analysis process using the LangGraph agent system"""
        self.ensure_one()

        if self.state == 'processing':
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Already Processing'),
                    'message': _('This case is already being processed.'),
                    'sticky': False,
                    'type': 'warning',
                }
            }

        # Update state and record start time
        self.write({
            'state': 'processing',
            'processing_start': fields.Datetime.now(),
            'background_analysis': False,
            'legal_issues': False,
            'evidence_analysis': False,
            'legal_principles': False,
            'judgment_prediction': False,
            'full_analysis': False,
        })

        # Clear existing sources
        if self.source_ids:
            self.source_ids.unlink()

        # Process directly for debugging
        _logger.info("Calling process_case_analysis directly for debugging")
        # Set use_mock to False to use the real LLM-based analysis
        self.write({'use_mock': False})
        self.process_case_analysis()

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Analysis Started'),
                'message': _('Case analysis has been started. This may take several minutes to complete.'),
                'sticky': False,
                'type': 'success',
            }
        }

    # Remove the threaded_method decorator to make debugging easier
    def process_case_analysis(self):
        """Process the case analysis using the LangGraph agent system"""
        self.ensure_one()
        _logger.info("========== STARTING CASE ANALYSIS ==========")
        _logger.info(f"Case ID: {self.id}, Name: {self.name}, State: {self.state}")

        try:
            # Try to import the real case agent
            try:
                from ..langgraph.case_agent import process_case
                _logger.info("Using real case analysis agent")
            except ImportError as e:
                _logger.warning(f"Could not import real case agent: {str(e)}. Using mock implementation.")
                # Fallback to mock implementation if real agent is not available
                def process_case(input_data, env=None):
                    _logger.info(f"Mock processing case: {input_data.get('case_title')}")

                    # Extract case information
                    case_title = input_data.get('case_title', '')
                    case_number = input_data.get('case_number', '')
                    case_text = input_data.get('case_text', '')
                    case_type = input_data.get('case_type', 'property')
                    language = input_data.get('language', 'en')

                    # Generate more realistic mock content based on the case text
                    # Extract parties from the case text
                    parties = []
                    if 'plaintiff' in case_text.lower() or 'plaintiffs' in case_text.lower():
                        parties.append("Plaintiffs")
                    if 'defendant' in case_text.lower() or 'defendants' in case_text.lower():
                        parties.append("Defendants")

                    # Extract potential legal issues
                    legal_issues = []
                    if 'inheritance' in case_text.lower() or 'will' in case_text.lower():
                        legal_issues.append("Inheritance Rights")
                    if 'boundary' in case_text.lower() or 'boundaries' in case_text.lower():
                        legal_issues.append("Property Boundaries")
                    if 'ownership' in case_text.lower() or 'title' in case_text.lower():
                        legal_issues.append("Proof of Ownership")
                    if 'contract' in case_text.lower() or 'agreement' in case_text.lower():
                        legal_issues.append("Contract Validity")

                    # Extract potential evidence
                    evidence = []
                    if 'document' in case_text.lower() or 'documents' in case_text.lower():
                        evidence.append("Documentary Evidence")
                    if 'witness' in case_text.lower() or 'testimony' in case_text.lower():
                        evidence.append("Witness Testimony")
                    if 'survey' in case_text.lower() or 'map' in case_text.lower():
                        evidence.append("Survey Maps")
                    if 'record' in case_text.lower() or 'records' in case_text.lower():
                        evidence.append("Official Records")

                    # Extract potential legal principles
                    principles = []
                    if 'succession' in case_text.lower() or 'inherit' in case_text.lower():
                        principles.append("Succession Law")
                    if 'adverse possession' in case_text.lower():
                        principles.append("Doctrine of Adverse Possession")
                    if 'equitable' in case_text.lower() or 'equity' in case_text.lower():
                        principles.append("Principles of Equity")
                    if 'revenue' in case_text.lower() or 'land revenue' in case_text.lower():
                        principles.append("Land Revenue Code")

                    # Generate mock sources based on case type
                    sources = []
                    if case_type == 'property':
                        sources = [
                            {"type": "document", "name": "Land Registration Records", "relevance": 0.92},
                            {"type": "web", "name": "Property Law Handbook", "link": "https://example.com/property-law", "source": "Legal Reference"},
                            {"type": "land_law", "name": "Land Boundary Dispute Precedents", "relevance": 0.85}
                        ]
                    elif case_type == 'inheritance':
                        sources = [
                            {"type": "document", "name": "Succession Act Guidelines", "relevance": 0.94},
                            {"type": "web", "name": "Inheritance Law Digest", "link": "https://example.com/inheritance-law", "source": "Legal Reference"},
                            {"type": "inheritance_law", "name": "Will Validity Precedents", "relevance": 0.88}
                        ]
                    elif case_type == 'contract':
                        sources = [
                            {"type": "document", "name": "Contract Law Principles", "relevance": 0.91},
                            {"type": "web", "name": "Contract Dispute Resolution", "link": "https://example.com/contract-disputes", "source": "Legal Reference"},
                            {"type": "civil_law", "name": "Breach of Contract Remedies", "relevance": 0.87}
                        ]
                    else:
                        sources = [
                            {"type": "document", "name": "Legal Reference Manual", "relevance": 0.80},
                            {"type": "web", "name": "Legal Principles Database", "link": "https://example.com/legal-principles", "source": "Legal Reference"},
                            {"type": "civil_law", "name": "Civil Procedure Guidelines", "relevance": 0.75}
                        ]

                    # Generate background analysis
                    background_analysis = f"## Case Background: {case_title}\n\n"
                    background_analysis += f"**Case Number:** {case_number}\n\n"
                    background_analysis += "### Parties Involved:\n\n"
                    if parties:
                        for party in parties:
                            background_analysis += f"- {party}\n"
                    else:
                        background_analysis += "- Multiple parties identified in the case text\n"
                    background_analysis += "\n### Factual Context:\n\n"
                    background_analysis += "The case involves a dispute over property rights and ownership claims. "
                    background_analysis += "The facts presented indicate a complex history of land ownership and potential inheritance issues. "
                    background_analysis += "The timeline of events spans several years, with multiple transactions and claims being made by different parties.\n"

                    # Generate legal issues
                    legal_issues_text = "## Key Legal Issues\n\n"
                    if legal_issues:
                        for issue in legal_issues:
                            legal_issues_text += f"### {issue}\n\n"
                            legal_issues_text += f"The case presents significant questions regarding {issue.lower()}. "
                            legal_issues_text += "The resolution of this issue will depend on the interpretation of relevant statutes and precedents.\n\n"
                    else:
                        legal_issues_text += "### Property Rights\n\n"
                        legal_issues_text += "The primary legal issue concerns the determination of legitimate property rights. "
                        legal_issues_text += "This involves examining the chain of title, validity of transfers, and any competing claims.\n\n"
                        legal_issues_text += "### Procedural Compliance\n\n"
                        legal_issues_text += "There are questions regarding the procedural compliance with property registration requirements. "
                        legal_issues_text += "The case will examine whether all necessary legal formalities were observed in the property transactions.\n\n"

                    # Generate evidence analysis
                    evidence_analysis = "## Evidence Analysis\n\n"
                    if evidence:
                        for item in evidence:
                            evidence_analysis += f"### {item}\n\n"
                            evidence_analysis += f"The {item.lower()} presented in this case provides crucial information regarding the property claims. "
                            evidence_analysis += "The reliability and authenticity of this evidence will be a significant factor in determining the outcome.\n\n"
                    else:
                        evidence_analysis += "### Documentary Evidence\n\n"
                        evidence_analysis += "The case relies heavily on documentary evidence including property deeds, registration documents, and correspondence. "
                        evidence_analysis += "The authenticity and completeness of these documents will be critical to establishing the claims.\n\n"
                        evidence_analysis += "### Testimonial Evidence\n\n"
                        evidence_analysis += "Witness testimonies provide context and historical information about the property and transactions. "
                        evidence_analysis += "The credibility and consistency of these testimonies will need to be carefully evaluated.\n\n"

                    # Generate legal principles
                    legal_principles_text = "## Applicable Legal Principles\n\n"
                    if principles:
                        for principle in principles:
                            legal_principles_text += f"### {principle}\n\n"
                            legal_principles_text += f"The principle of {principle.lower()} is directly applicable to this case. "
                            legal_principles_text += "The court will need to apply this principle in light of the specific facts and circumstances presented.\n\n"
                    else:
                        legal_principles_text += "### Property Law Principles\n\n"
                        legal_principles_text += "The fundamental principles of property law, including rights of ownership, transfer, and possession, are central to this case. "
                        legal_principles_text += "The court will apply these principles to determine the legitimate claims to the property.\n\n"
                        legal_principles_text += "### Evidentiary Standards\n\n"
                        legal_principles_text += "The case will be decided based on the preponderance of evidence standard. "
                        legal_principles_text += "The burden of proof rests with the party making the claim to establish their rights to the property.\n\n"

                    # Generate judgment prediction
                    judgment_prediction = "## Judgment Prediction\n\n"
                    judgment_prediction += "Based on the analysis of the facts, evidence, and applicable legal principles, the following outcome is predicted:\n\n"
                    judgment_prediction += "### Likely Ruling\n\n"
                    judgment_prediction += "The court is likely to rule in favor of the party with the stronger documentary evidence and clearer chain of title. "
                    judgment_prediction += "The case may result in a partial victory for both parties, with the court ordering:\n\n"
                    judgment_prediction += "1. Recognition of primary ownership rights to the party with the strongest documentary evidence\n"
                    judgment_prediction += "2. Potential compensation for improvements or investments made by the other party\n"
                    judgment_prediction += "3. Clear demarcation of property boundaries to prevent future disputes\n"
                    judgment_prediction += "4. Specific performance of any valid agreements between the parties\n\n"
                    judgment_prediction += "### Reasoning\n\n"
                    judgment_prediction += "This prediction is based on the pattern of rulings in similar property dispute cases, where courts tend to favor documented evidence over oral claims "
                    judgment_prediction += "and seek to find equitable solutions that recognize legitimate interests while maintaining legal principles of property ownership.\n"

                    # Generate full analysis
                    full_analysis = f"# Comprehensive Legal Analysis: {case_title}\n\n"
                    full_analysis += "## Executive Summary\n\n"
                    full_analysis += f"This analysis examines the property dispute case of {case_title} ({case_number}). "
                    full_analysis += "The case involves competing claims to property rights, with significant questions regarding ownership, boundaries, and the validity of transfers. "
                    full_analysis += "Based on the available information, the case presents complex legal issues that require careful examination of documentary evidence and application of property law principles.\n\n"
                    full_analysis += background_analysis + "\n\n"
                    full_analysis += legal_issues_text + "\n\n"
                    full_analysis += evidence_analysis + "\n\n"
                    full_analysis += legal_principles_text + "\n\n"
                    full_analysis += judgment_prediction + "\n\n"
                    full_analysis += "## Conclusion\n\n"
                    full_analysis += "The resolution of this case will depend primarily on the strength and authenticity of the documentary evidence presented by the parties. "
                    full_analysis += "The court will likely apply established principles of property law while seeking an equitable solution that recognizes legitimate interests. "
                    full_analysis += "The parties may benefit from considering mediation or settlement discussions to reach a mutually acceptable resolution rather than pursuing protracted litigation.\n"

                    # Return the mock result with more realistic content
                    return {
                        'background_analysis': background_analysis,
                        'legal_issues': legal_issues_text,
                        'evidence_analysis': evidence_analysis,
                        'legal_principles': legal_principles_text,
                        'judgment_prediction': judgment_prediction,
                        'full_analysis': full_analysis,
                        'sources': sources,
                        'messages': [
                            {"role": "system", "content": "Analysis started"},
                            {"role": "system", "content": "Retrieving relevant legal documents"},
                            {"role": "system", "content": "Analyzing case facts and legal principles"},
                            {"role": "system", "content": "Generating comprehensive legal analysis"},
                            {"role": "system", "content": "Analysis completed successfully"}
                        ]
                    }

            _logger.info(f"Starting case analysis for case: {self.name} (ID: {self.id})")

            # Prepare the input for the case analysis agent
            input_data = {
                'case_id': self.id,
                'case_title': self.name,
                'case_number': self.case_number,
                'case_text': self.case_text,
                'case_type': self.case_type,
                'language': self.language,
                'user_id': self.user_id.id,
                'use_online_search': self.use_online_search,
                'use_document_embeddings': self.use_document_embeddings,
                'use_mock': self.use_mock,
                'enable_tracing': self.enable_tracing,
            }

            # Process the case
            _logger.info(f"Calling process_case with input_data: {input_data}")
            result = process_case(input_data, env=self.env)
            _logger.info(f"Result from process_case: {result}")

            # Record end time and calculate duration
            end_time = fields.Datetime.now()
            start_time = self.processing_start or end_time
            duration = (end_time - start_time).total_seconds()

            if isinstance(result, dict):
                _logger.info("Result is a dictionary, extracting sections")
                # Extract the analysis sections
                background = result.get('background_analysis', '')
                issues = result.get('legal_issues', '')
                evidence = result.get('evidence_analysis', '')
                principles = result.get('legal_principles', '')
                prediction = result.get('judgment_prediction', '')
                full_analysis = result.get('full_analysis', '')
                sources = result.get('sources', [])
                error = result.get('error')

                _logger.info(f"Extracted sections: background={len(background)}, issues={len(issues)}, evidence={len(evidence)}, principles={len(principles)}, prediction={len(prediction)}, full_analysis={len(full_analysis)}, sources={len(sources)}, error={error}")

                if error:
                    _logger.error(f"Error in case analysis: {error}")
                    self.write({
                        'state': 'failed',
                        'processing_end': end_time,
                        'duration': duration,
                        'full_analysis': f"Error: {error}"
                    })
                    return

                # Create source records
                self._create_source_records(sources)

                # Update the record with the analysis results
                _logger.info("Writing analysis results to the record")
                write_vals = {
                    'background_analysis': background,
                    'legal_issues': issues,
                    'evidence_analysis': evidence,
                    'legal_principles': principles,
                    'judgment_prediction': prediction,
                    'full_analysis': full_analysis,
                    'state': 'completed',
                    'processing_end': end_time,
                    'duration': duration
                }
                _logger.info(f"Write values: {write_vals}")
                result = self.write(write_vals)
                _logger.info(f"Write result: {result}")

                # Force a refresh of the record to ensure changes are visible
                self.env.cr.commit()
                _logger.info("Committed changes to database")

                _logger.info(f"Case analysis completed for case: {self.name} (ID: {self.id}) in {duration:.2f} seconds")
            else:
                # Handle unexpected result format
                _logger.error(f"Unexpected result format from case analysis: {type(result)}")
                self.write({
                    'state': 'failed',
                    'processing_end': end_time,
                    'duration': duration,
                    'full_analysis': f"Error: Unexpected result format from case analysis agent."
                })

        except Exception as e:
            _logger.exception(f"Exception in case analysis: {str(e)}")
            self.write({
                'state': 'failed',
                'processing_end': fields.Datetime.now(),
                'duration': (fields.Datetime.now() - (self.processing_start or fields.Datetime.now())).total_seconds(),
                'full_analysis': f"Error: {str(e)}"
            })

    def _create_source_records(self, sources):
        """Create source records for the case analysis"""
        _logger.info(f"Creating source records: {sources}")
        if not sources:
            _logger.info("No sources to create")
            return

        LawCaseSource = self.env['law.case.source']

        for source in sources:
            source_type = source.get('type', 'unknown')

            vals = {
                'case_id': self.id,
                'source_type': source_type,
                'name': source.get('name', 'Unknown Source'),
            }

            # Handle different source types
            if source_type == 'document':
                vals.update({
                    'document_id': source.get('id'),
                })
            elif source_type == 'web':
                vals.update({
                    'url': source.get('link', ''),
                    'source': source.get('source', ''),
                })

            # Add relevance score if available
            if 'relevance' in source:
                vals['relevance'] = source.get('relevance', 0.0)

            # Create the source record
            try:
                LawCaseSource.create(vals)
            except Exception as e:
                _logger.error(f"Error creating source record: {str(e)}")

    def action_reset(self):
        """Reset the case to draft state"""
        self.ensure_one()

        _logger.info(f"Resetting case to draft state: {self.name} (ID: {self.id})")

        # Delete existing sources
        if self.source_ids:
            _logger.info(f"Deleting {len(self.source_ids)} source records")
            self.source_ids.unlink()

        # Reset the case
        self.write({
            'state': 'draft',
            'processing_start': False,
            'processing_end': False,
            'duration': 0,
            'background_analysis': False,
            'legal_issues': False,
            'evidence_analysis': False,
            'legal_principles': False,
            'judgment_prediction': False,
            'full_analysis': False,
        })

        _logger.info(f"Case reset to draft state: {self.name} (ID: {self.id})")

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Reset Complete'),
                'message': _('The case has been reset to draft state.'),
                'sticky': False,
                'type': 'success',
            }
        }





    def action_visualize_workflow(self):
        """Visualize the LangGraph workflow"""
        self.ensure_one()

        _logger.info("Visualizing LangGraph workflow")

        try:
            # Try to use the simple visualizer
            from ..langgraph.simple_visualizer import save_workflow_visualization, get_workflow_structure
            _logger.info("Using simple workflow visualizer")

            # Save the workflow visualization as HTML
            try:
                file_path = save_workflow_visualization(format='html')
                _logger.info(f"Saved workflow visualization to {file_path}")

                # Create a controller endpoint to serve the file
                from ..controllers.main import TEMP_FILES
                import uuid
                import os

                # Generate a unique ID for this file
                file_id = str(uuid.uuid4())
                file_name = os.path.basename(file_path)

                # Store the file path in the temporary files dictionary
                TEMP_FILES[file_id] = file_path

                # Return action to open the file through the controller
                base_url = self.env['ir.config_parameter'].sudo().get_param('web.base.url')
                url = f"{base_url}/odoo_law_agents/download/{file_id}/{file_name}"

                return {
                    'type': 'ir.actions.act_url',
                    'url': url,
                    'target': 'new',
                }

            except Exception as e:
                _logger.error(f"Error generating workflow visualization: {str(e)}")

                # Get a simple text representation
                workflow_structure = get_workflow_structure()

                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Workflow Structure'),
                        'message': workflow_structure,
                        'sticky': True,
                        'type': 'info',
                    }
                }

        except ImportError as e:
            _logger.error(f"Error importing LangGraph modules: {str(e)}")

        # Fallback to a simple notification
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Workflow Visualization'),
                'message': _('Unable to visualize the workflow. Check the logs for details.'),
                'sticky': False,
                'type': 'warning',
            }
        }

    def action_print_analysis(self):
        """Print the case analysis as a PDF report using Odoo's QWeb reporting system.

        This method is called from the Print Analysis button in the UI.
        It returns an action to print the case analysis report.
        """
        self.ensure_one()

        if self.state != 'completed':
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Analysis Not Complete'),
                    'message': _('The case analysis must be completed before printing.'),
                    'sticky': False,
                    'type': 'warning',
                }
            }

        # Return the report action
        return self.env.ref('odoo_law_agents.action_report_case_analysis').report_action(self)
