from odoo import models, fields

class LawQuerySource(models.Model):
    _name = 'law.query.source'
    _description = 'Law Query Source'

    name = fields.Char(string='Name', required=True)
    query_id = fields.Many2one('law.query', string='Query', required=True, ondelete='cascade')
    source_type = fields.Selection([
        ('document', 'Document'),
        ('web', 'Web Source'),
        ('agent', 'Agent Knowledge'),
        ('civil_law', 'Civil Law'),
        ('land_law', 'Land Law'),
        ('gujarat_law', 'Gujarat Law'),
        ('mock', 'Mock Source'),
        ('unknown', 'Unknown')
    ], string='Source Type', required=True, default='unknown')
    document_id = fields.Many2one('law.document', string='Document')
    url = fields.Char(string='URL')
    source = fields.Char(string='Source')
    relevance = fields.Float(string='Relevance Score', default=0.0)

    def action_open_source(self):
        """Open the source based on its type"""
        self.ensure_one()

        if self.source_type == 'document' and self.document_id:
            # Return action to open the document
            return {
                'type': 'ir.actions.act_window',
                'res_model': 'law.document',
                'res_id': self.document_id.id,
                'view_mode': 'form',
                'target': 'current',
            }
        elif self.source_type == 'web' and self.url:
            # Return action to open the URL
            return {
                'type': 'ir.actions.act_url',
                'url': self.url,
                'target': 'new',
            }

        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': 'No Source',
                'message': 'No source available to open.',
                'sticky': False,
                'type': 'warning',
            }
        }
