from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64
import json
import logging
import numpy as np

_logger = logging.getLogger(__name__)

class LawEmbedding(models.Model):
    _name = 'law.embedding'
    _description = 'Law Document Embedding'
    _order = 'document_id, chunk_index'

    name = fields.Char(compute='_compute_name', store=True)
    document_id = fields.Many2one('law.document', string='Document', required=True, ondelete='cascade')
    chunk_index = fields.Integer(string='Chunk Index', required=True)
    chunk_text = fields.Text(string='Chunk Text', required=True)
    embedding = fields.Binary(string='Embedding Vector', attachment=False)
    embedding_model = fields.Char(string='Embedding Model', default='all-MiniLM-L6-v2')
    company_id = fields.Many2one('res.company', related='document_id.company_id', store=True)

    _sql_constraints = [
        ('unique_document_chunk', 'UNIQUE(document_id, chunk_index)', 'Document chunk must be unique!')
    ]

    @api.depends('document_id', 'chunk_index')
    def _compute_name(self):
        for record in self:
            if record.document_id and record.chunk_index >= 0:
                record.name = f"{record.document_id.name} - Chunk {record.chunk_index}"
            else:
                record.name = "New Embedding"

    def store_embedding(self, vector):
        """Store embedding vector as binary"""
        self.ensure_one()
        try:
            if vector is None:
                _logger.warning("Attempted to store None vector")
                return False

            # Convert numpy array to list if needed
            if isinstance(vector, np.ndarray):
                vector = vector.tolist()

            vector_json = json.dumps(vector)
            self.embedding = base64.b64encode(vector_json.encode('utf-8'))
            return True
        except Exception as e:
            _logger.error(f"Error storing embedding: {str(e)}")
            return False

    def get_embedding(self):
        """Retrieve embedding vector from binary storage"""
        self.ensure_one()
        try:
            if not self.embedding:
                return None

            vector_json = base64.b64decode(self.embedding).decode('utf-8')
            vector = json.loads(vector_json)
            return vector
        except Exception as e:
            _logger.error(f"Error retrieving embedding: {str(e)}")
            return None

    def compute_similarity(self, other_embedding):
        """Compute cosine similarity with another embedding.

        Args:
            other_embedding: Another embedding vector or LawEmbedding record.

        Returns:
            The cosine similarity score (0-1).
        """
        try:
            # Get this embedding vector
            vector1 = self.get_embedding()
            if not vector1:
                return 0.0

            # Get the other embedding vector
            if isinstance(other_embedding, LawEmbedding):
                vector2 = other_embedding.get_embedding()
            else:
                vector2 = other_embedding

            if not vector2:
                return 0.0

            # Convert to numpy arrays
            v1 = np.array(vector1)
            v2 = np.array(vector2)

            # Compute cosine similarity
            similarity = np.dot(v1, v2) / (np.linalg.norm(v1) * np.linalg.norm(v2))
            return float(similarity)
        except Exception as e:
            _logger.error(f"Error computing similarity: {str(e)}")
            return 0.0
