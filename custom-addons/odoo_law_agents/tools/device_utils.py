"""Utility functions for device management."""
import os
import torch
import logging

_logger = logging.getLogger(__name__)

def get_device():
    """Get the device to use for computations (forcing CPU)."""
    return torch.device('cpu')

def setup_device():
    """Configure the environment for CPU-only operation."""
    try:
        # Force CPU usage
        os.environ['CUDA_VISIBLE_DEVICES'] = ''
        
        # Set reasonable thread limits for CPU operations
        torch.set_num_threads(4)
        
        # Disable gradient computation by default since we're mainly doing inference
        torch.set_grad_enabled(False)
        
        _logger.info("Device setup completed: using CPU")
    except Exception as e:
        _logger.error(f"Error during device setup: {str(e)}")
        raise
