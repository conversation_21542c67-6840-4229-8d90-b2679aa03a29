"""
DuckDuckGo Search integration for Odoo Law Agents.
This module provides a wrapper around the duckduckgo-search library.
"""

import logging
from typing import List, Dict, Any, Optional

from duckduckgo_search import DDGS

_logger = logging.getLogger(__name__)

class DuckDuckGoSearch:
    """DuckDuckGo Search API wrapper for legal research."""

    def __init__(self, max_results: int = 5, timeout: int = 10):
        """Initialize the DuckDuckGo search client.

        Args:
            max_results: Maximum number of results to return.
            timeout: Timeout in seconds for the search request.
        """
        self.max_results = max_results
        self.timeout = timeout
        self.client = DDGS()

    def search(self, query: str, region: str = "in-en", safesearch: str = "moderate", legal_focus: bool = True, content_type: str = "all") -> List[Dict[str, Any]]:
        """Search the web using DuckDuckGo, focused on legal content.

        Args:
            query: The search query.
            region: The region to search in (default: "in-en" for India, English).
            safesearch: The safe search level (default: "moderate").
            legal_focus: Whether to focus on legal content (default: True).
            content_type: Type of legal content to focus on ("civil", "land", "all").

        Returns:
            A list of search results relevant to legal domain.
        """
        try:
            # Modify the query based on legal focus and content type
            modified_query = query

            if legal_focus:
                # Add legal-specific terms based on content type
                if content_type == "civil":
                    modified_query = f"{query} civil law legal judgment court"
                elif content_type == "land":
                    modified_query = f"{query} land law property rights legal"
                else:  # "all"
                    modified_query = f"{query} law legal judgment court case"

                # Log the modified query
                _logger.info(f"Modified search query for legal focus: '{modified_query}'")

            results = list(self.client.text(
                modified_query,
                region=region,
                safesearch=safesearch,
                max_results=self.max_results,
                timelimit=self.timeout
            ))

            # Format results to match the expected structure
            formatted_results = []
            for result in results:
                # Extract the content and check if it's relevant to legal domain
                title = result.get("title", "")
                body = result.get("body", "")

                # Skip results that don't seem to be related to legal content if legal_focus is enabled
                if legal_focus:
                    combined_text = (title + " " + body).lower()

                    # Define legal terms based on content type
                    legal_terms = ["law", "legal", "court", "judgment", "rights", "case", "act", "section", "tribunal",
                                  "tenant", "agricultural", "farm", "regulation", "statute", "legislation", "code"]

                    # For specific content types, add more specific terms
                    if content_type == "civil":
                        legal_terms.extend(["civil", "procedure", "contract", "liability", "damages"])
                    elif content_type == "land":
                        legal_terms.extend(["land", "property", "ownership", "title", "deed", "real estate"])
                    elif content_type == "agricultural":
                        legal_terms.extend(["agricultural", "farm", "tenant", "crop", "cultivation", "farmer"])

                    # Log the combined text for debugging
                    _logger.debug(f"Checking content: {combined_text[:100]}...")

                    # Check if any legal term is present in the content
                    matching_terms = [term for term in legal_terms if term in combined_text]
                    is_legal_content = len(matching_terms) > 0

                    # Log the matching terms for debugging
                    if is_legal_content:
                        _logger.debug(f"Found legal terms: {matching_terms}")
                    else:
                        _logger.debug(f"No legal terms found in result: {title}")

                    # Only filter if we have enough results, otherwise be more lenient
                    if not is_legal_content and len(results) > 3:
                        continue

                formatted_results.append({
                    "title": title,
                    "link": result.get("href", ""),
                    "snippet": body,
                    "source": "DuckDuckGo",
                    "content_type": content_type if legal_focus else "general"
                })

            return formatted_results
        except Exception as e:
            _logger.error(f"DuckDuckGo search error: {str(e)}")
            return []

    def news_search(self, query: str, region: str = "in-en", max_age: Optional[int] = None, legal_focus: bool = True, content_type: str = "all") -> List[Dict[str, Any]]:
        """Search for news using DuckDuckGo, focused on legal content.

        Args:
            query: The search query.
            region: The region to search in (default: "in-en" for India, English).
            max_age: Maximum age of news in days (default: None).
            legal_focus: Whether to focus on legal content (default: True).
            content_type: Type of legal content to focus on ("civil", "land", "all").

        Returns:
            A list of news search results relevant to legal domain.
        """
        try:
            # Modify the query based on legal focus and content type
            modified_query = query

            if legal_focus:
                # Add legal-specific terms based on content type
                if content_type == "civil":
                    modified_query = f"{query} civil law legal judgment court"
                elif content_type == "land":
                    modified_query = f"{query} land law property rights legal"
                else:  # "all"
                    modified_query = f"{query} law legal judgment court case"

                # Log the modified query
                _logger.info(f"Modified query for legal focus: '{modified_query}'")

            # Use only the supported parameters for DDGS.news()
            # The 'time' parameter is not supported in some versions of duckduckgo_search
            kwargs = {
                "keywords": modified_query,
                "region": region,
                "max_results": self.max_results
            }

            # We'll try to use the time parameter only if it's supported
            # Different versions of duckduckgo_search have different parameter names
            # Some use 'time', others use 'timelimit' or don't support time filtering at all
            # We'll handle this gracefully

            # Safely check the available parameters without using the 'time' parameter
            # This avoids the "got an unexpected keyword argument 'time'" error
            results = []
            try:
                results = list(self.client.news(**kwargs))
            except TypeError as e:
                if "unexpected keyword argument" in str(e):
                    # Log the error and try to identify which parameter is causing the issue
                    _logger.warning(f"DuckDuckGo news search parameter error: {str(e)}")
                    # Try to remove problematic parameters one by one
                    for param in list(kwargs.keys()):
                        try_kwargs = {k: v for k, v in kwargs.items() if k != param}
                        try:
                            results = list(self.client.news(**try_kwargs))
                            _logger.info(f"DuckDuckGo news search succeeded after removing '{param}' parameter")
                            break
                        except Exception:
                            continue
                else:
                    raise

            # Format results to match the expected structure
            formatted_results = []
            for result in results:
                # Extract the content and check if it's relevant to legal domain
                title = result.get("title", "")
                body = result.get("body", "")

                # Skip results that don't seem to be related to legal content if legal_focus is enabled
                if legal_focus:
                    combined_text = (title + " " + body).lower()

                    # Define legal terms based on content type
                    legal_terms = ["law", "legal", "court", "judgment", "rights", "case", "act", "section", "tribunal",
                                  "tenant", "agricultural", "farm", "regulation", "statute", "legislation", "code"]

                    # For specific content types, add more specific terms
                    if content_type == "civil":
                        legal_terms.extend(["civil", "procedure", "contract", "liability", "damages"])
                    elif content_type == "land":
                        legal_terms.extend(["land", "property", "ownership", "title", "deed", "real estate"])
                    elif content_type == "agricultural":
                        legal_terms.extend(["agricultural", "farm", "tenant", "crop", "cultivation", "farmer"])

                    # Log the combined text for debugging
                    _logger.debug(f"Checking news content: {combined_text[:100]}...")

                    # Check if any legal term is present in the content
                    matching_terms = [term for term in legal_terms if term in combined_text]
                    is_legal_content = len(matching_terms) > 0

                    # Log the matching terms for debugging
                    if is_legal_content:
                        _logger.debug(f"Found legal terms in news: {matching_terms}")
                    else:
                        _logger.debug(f"No legal terms found in news result: {title}")

                    # For news, be even more lenient - only filter if we have many results
                    if not is_legal_content and len(results) > 5:
                        continue

                formatted_results.append({
                    "title": title,
                    "link": result.get("url", ""),
                    "snippet": body,
                    "source": result.get("source", "DuckDuckGo News"),
                    "published": result.get("date", ""),
                    "content_type": content_type if legal_focus else "general"
                })

            return formatted_results
        except Exception as e:
            _logger.error(f"DuckDuckGo news search error: {str(e)}")
            return []

    def legal_search(self, query: str, law_type: str = "all", region: str = "in-en") -> List[Dict[str, Any]]:
        """Search specifically for legal documents and information.

        This is a convenience method that wraps the search method with legal-specific parameters.

        Args:
            query: The search query.
            law_type: The type of law to focus on ("civil", "land", "agricultural", "all").
            region: The region to search in (default: "in-en" for India, English).

        Returns:
            A list of search results specifically focused on legal content.
        """
        # Map law_type to content_type
        content_type = "all"
        if law_type in ["civil", "land", "agricultural"]:
            content_type = law_type

        # Add specific legal terms based on law type
        legal_terms = ""
        if law_type == "civil":
            legal_terms = "civil code procedure judgment"
        elif law_type == "land":
            legal_terms = "land property ownership rights"
        elif law_type == "agricultural":
            legal_terms = "agricultural farm tenant section"
        else:
            # For general queries, don't add too many terms to avoid over-filtering
            legal_terms = "law legal"

        # Check if the query already contains legal terms
        query_lower = query.lower()
        if any(term in query_lower for term in ["section", "act", "law", "legal", "court", "tenant", "rights"]):
            # If query already has legal terms, don't add more to avoid over-filtering
            enhanced_query = query
            _logger.info(f"Query already contains legal terms, using as is: '{query}'")
        else:
            # Enhance the query with legal terms
            enhanced_query = f"{query} {legal_terms}".strip()

        # Log the enhanced query
        _logger.info(f"Legal search query: '{enhanced_query}' (law type: {law_type})")

        # Use the regular search method with legal focus enabled
        results = self.search(
            query=enhanced_query,
            region=region,
            legal_focus=True,
            content_type=content_type
        )

        # Log the number of results
        _logger.info(f"Legal search returned {len(results)} results for query: '{enhanced_query}'")

        return results

    def legal_news_search(self, query: str, law_type: str = "all", region: str = "in-en", max_age: Optional[int] = 30) -> List[Dict[str, Any]]:
        """Search specifically for legal news.

        This is a convenience method that wraps the news_search method with legal-specific parameters.

        Args:
            query: The search query.
            law_type: The type of law to focus on ("civil", "land", "agricultural", "all").
            region: The region to search in (default: "in-en" for India, English).
            max_age: Maximum age of news in days (default: 30 days).

        Returns:
            A list of news search results specifically focused on legal content.
        """
        # Map law_type to content_type
        content_type = "all"
        if law_type in ["civil", "land", "agricultural"]:
            content_type = law_type

        # Add specific legal terms based on law type
        legal_terms = ""
        if law_type == "civil":
            legal_terms = "civil code procedure judgment"
        elif law_type == "land":
            legal_terms = "land property ownership rights"
        elif law_type == "agricultural":
            legal_terms = "agricultural farm tenant section"
        else:
            # For general queries, use minimal terms
            legal_terms = "law"

        # Check if the query already contains legal terms
        query_lower = query.lower()
        if any(term in query_lower for term in ["section", "act", "law", "legal", "court", "tenant", "rights"]):
            # If query already has legal terms, don't add more to avoid over-filtering
            enhanced_query = query
            _logger.info(f"Query already contains legal terms, using as is: '{query}'")
        else:
            # Enhance the query with legal terms
            enhanced_query = f"{query} {legal_terms}".strip()

        # Log the enhanced query
        _logger.info(f"Legal news search query: '{enhanced_query}' (law type: {law_type})")

        # Use the news_search method with legal focus enabled
        # Note: We're not passing max_age directly since it might not be supported
        # The news_search method will handle time filtering gracefully
        results = self.news_search(
            query=enhanced_query,
            region=region,
            legal_focus=True,
            content_type=content_type
        )

        # Log if we're not using max_age
        if max_age is not None:
            _logger.info(f"Note: max_age parameter ({max_age} days) might not be applied due to library limitations")

        # Log the number of results
        _logger.info(f"Legal news search returned {len(results)} results for query: '{enhanced_query}'")

        return results
