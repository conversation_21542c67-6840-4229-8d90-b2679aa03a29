"""
Simple tracing utilities for LangGraph agents using Opik.
"""

import logging
import os
from typing import Optional, Dict, Any

_logger = logging.getLogger(__name__)


class MockTracer:
    """A mock tracer implementation for use when Opik is not available.

    This class implements the minimal interface needed for langgraph tracing.
    """
    def __init__(self, graph: Any = None, project_name: str = None):
        self.graph = graph
        self.project_name = project_name
        _logger.debug(f"Created MockTracer with project: {self.project_name}")

    def __call__(self, *args, **kwargs):
        """Called when the tracer is used as a callback."""
        return args[0] if args else None

    def ignore_chain(self, *args, **kwargs):
        """Mock implementation of ignore_chain method required by langgraph."""
        _logger.debug("MockTracer: ignore_chain called")
        return None

    def raise_error(self, *args, **kwargs):
        """Mock implementation of raise_error method required by langgraph."""
        _logger.debug("MockTracer: raise_error called")
        return None

def get_tracing_config(agent: Any, agent_model: Optional[Any] = None) -> Dict[str, Any]:
    """Get tracing configuration for the agent.

    Args:
        agent: The LangGraph agent
        agent_model: Optional agent model with configuration

    Returns:
        Dictionary with tracing configuration
    """
    # The following code is commented out until Opik integration is stable
    try:
        # Get tracing configuration from agent model
        api_key = getattr(agent_model, 'opik_api_key', None)
        workspace = getattr(agent_model, 'opik_workspace', None)

        if api_key and workspace and getattr(agent_model, 'enable_tracing', False):
            # Set up environment
            os.environ["OPIK_API_KEY"] = api_key
            os.environ["OPIK_WORKSPACE"] = workspace

            try:
                from opik.integrations.langgraph import OpikTracer
                tracer = OpikTracer(graph=agent)
                return {"callbacks": [tracer]}
            except ImportError:
                _logger.warning("Could not import OpikTracer, falling back to MockTracer")
                return {"callbacks": [MockTracer(graph=agent, project_name="odoo_law_agents")]}

        else:
            _logger.info("Tracing not enabled or missing configuration")
            return {"callbacks": [MockTracer(graph=agent, project_name="odoo_law_agents")]}

    except Exception as e:
        _logger.error(f"Error configuring tracing: {str(e)}")
        return {"callbacks": [MockTracer(graph=agent, project_name="odoo_law_agents")]}

