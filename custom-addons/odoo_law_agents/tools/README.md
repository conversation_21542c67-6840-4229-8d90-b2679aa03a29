# Odoo Law Agents Tools

This directory contains utility tools for the Odoo Law Agents module.

## Tracing Tools

The tracing tools provide integration with Opik for monitoring and debugging LangGraph agents.

### simple_tracing.py

This file provides a simplified approach to Opik tracing based on the example code. It includes:

1. **setup_opik_tracing**: Sets up Opik tracing by setting environment variables
2. **get_opik_tracer**: Gets an Opik tracer for a LangGraph graph
3. **get_tracing_config**: Gets the tracing configuration for a LangGraph graph
4. **MockTracer**: A simple mock tracer for testing without Opik

The implementation is Python version-aware and automatically uses the appropriate tracing method:
- For Python 3.11+: Uses the real Opik tracer
- For Python 3.10 or lower: Uses a MockTracer for compatibility

### tracing.py (Legacy)

This file contains the original implementation of Opik tracing. It's more complex and has been replaced by the simplified approach in `simple_tracing.py`.

## Usage

To use the tracing tools in your code:

```python
from ..tools.simple_tracing import get_tracing_config, setup_opik_tracing, MockTracer

# Set up Opik tracing
setup_opik_tracing(api_key, workspace)

# Get tracing configuration
config = get_tracing_config(graph, agent)

# Use the configuration with LangGraph
result = graph.invoke(inputs, config=config)
```

## Example

You can find a complete example in the `examples/opik_tracing_example.py` file.

## Compatibility

The tracing tools are designed to work with different Python versions:

- Python 3.11+: Uses the real Opik tracer with full functionality
- Python 3.10 or lower: Uses a MockTracer that implements the same interface but doesn't actually send data to Opik

This ensures that the code works correctly regardless of the Python version being used.
