"""
Retrieval Augmented Generation (RAG) utilities for Odoo Law Agents.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import os
import multiprocessing

from . import device_utils
device_utils.setup_device()  # Configure for CPU-only operation

from sentence_transformers import SentenceTransformer

_logger = logging.getLogger(__name__)

# Default model for embeddings
DEFAULT_EMBEDDING_MODEL = "all-MiniLM-L6-v2"

def initialize_model(model_name):
    """Initialize the sentence transformer model."""
    device = device_utils.get_device()
    return SentenceTransformer(model_name, device=device)

class RAGProcessor:
    """RAG processor for enhancing LLM responses with retrieved context."""

    def __init__(self, embedding_model: str = DEFAULT_EMBEDDING_MODEL):
        """Initialize the RAG processor.

        Args:
            embedding_model: The name of the sentence-transformers model to use.
        """
        self.embedding_model_name = embedding_model
        self._model = None

        # Initialize the thread pool for sentence-transformers
        self._initialize_thread_pool()

    def _initialize_thread_pool(self):
        """Initialize the thread pool for sentence-transformers.

        This is needed to prevent the 'The global thread pool has not been initialized' error.
        """
        try:
            # Set the number of threads for PyTorch
            os.environ["OMP_NUM_THREADS"] = "1"
            os.environ["MKL_NUM_THREADS"] = "1"

            # Get the number of CPUs, but limit to a reasonable number to avoid resource exhaustion
            num_cpus = min(multiprocessing.cpu_count(), 4)

            # Set environment variables for thread pool
            os.environ["TOKENIZERS_PARALLELISM"] = "false"

            _logger.info(f"Thread pool initialized with {num_cpus} threads")
        except Exception as e:
            _logger.error(f"Error initializing thread pool: {str(e)}")

    @property
    def model(self):
        """Lazy-load the embedding model."""
        if self._model is None:
            try:
                # Initialize the thread pool again just to be safe
                self._initialize_thread_pool()

                # Load the model with explicit device setting
                self._model = initialize_model(self.embedding_model_name)
                _logger.info(f"Loaded embedding model: {self.embedding_model_name}")
            except Exception as e:
                _logger.error(f"Error loading embedding model: {str(e)}")
                raise
        return self._model

    def get_embedding(self, text: str) -> np.ndarray:
        """Get the embedding for a text.

        Args:
            text: The text to embed.

        Returns:
            The embedding vector.
        """
        try:
            return self.model.encode(text)
        except Exception as e:
            _logger.error(f"Error generating embedding: {str(e)}")
            raise

    def compute_similarity(self, query_embedding: np.ndarray, doc_embedding: np.ndarray) -> float:
        """Compute the cosine similarity between two embeddings.

        Args:
            query_embedding: The query embedding.
            doc_embedding: The document embedding.

        Returns:
            The cosine similarity score.
        """
        return np.dot(query_embedding, doc_embedding) / (
            np.linalg.norm(query_embedding) * np.linalg.norm(doc_embedding)
        )

    def rank_documents(self, query: str, documents: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Rank documents by relevance to the query.

        Args:
            query: The query text.
            documents: A list of document dictionaries.

        Returns:
            The documents sorted by relevance.
        """
        try:
            # Get the query embedding
            query_embedding = self.get_embedding(query)

            # Compute similarity scores for each document
            for doc in documents:
                # Use the content field for embedding
                content = doc.get("content", "")
                if content:
                    doc_embedding = self.get_embedding(content)
                    doc["relevance"] = float(self.compute_similarity(query_embedding, doc_embedding))
                else:
                    doc["relevance"] = 0.0

            # Sort by relevance
            return sorted(documents, key=lambda x: x.get("relevance", 0.0), reverse=True)
        except Exception as e:
            _logger.error(f"Error ranking documents: {str(e)}")
            return documents

    def enhance_search_results(self, query: str, search_results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Enhance search results with relevance scores.

        Args:
            query: The query text.
            search_results: A list of search result dictionaries.

        Returns:
            The search results with relevance scores.
        """
        try:
            # Get the query embedding
            query_embedding = self.get_embedding(query)

            # Compute similarity scores for each search result
            for result in search_results:
                # Use the snippet field for embedding
                snippet = result.get("snippet", "")
                if snippet:
                    result_embedding = self.get_embedding(snippet)
                    result["relevance"] = float(self.compute_similarity(query_embedding, result_embedding))
                else:
                    result["relevance"] = 0.0

            # Sort by relevance
            return sorted(search_results, key=lambda x: x.get("relevance", 0.0), reverse=True)
        except Exception as e:
            _logger.error(f"Error enhancing search results: {str(e)}")
            return search_results

    def prepare_context(self, query: str, documents: List[Dict[str, Any]], search_results: List[Dict[str, Any]],
                       max_docs: int = 3, max_search: int = 2) -> str:
        """Prepare context for the LLM from documents and search results.

        Args:
            query: The query text.
            documents: A list of document dictionaries.
            search_results: A list of search result dictionaries.
            max_docs: Maximum number of documents to include.
            max_search: Maximum number of search results to include.

        Returns:
            The prepared context string.
        """
        context = ""

        # Add documents to context
        if documents:
            ranked_docs = self.rank_documents(query, documents)
            context += "## Relevant Documents\n\n"
            for i, doc in enumerate(ranked_docs[:max_docs], 1):
                relevance = doc.get("relevance", 0.0)
                context += f"{i}. {doc.get('name', 'Unnamed')} (Relevance: {relevance:.2f})\n"
                context += f"   Type: {doc.get('document_type', 'Unknown')}, Language: {doc.get('language', 'Unknown')}\n"
                context += f"   Content: {doc.get('content', '')[:500]}...\n\n"

        # Add search results to context
        if search_results:
            enhanced_results = self.enhance_search_results(query, search_results)
            context += "## Web Search Results\n\n"
            for i, result in enumerate(enhanced_results[:max_search], 1):
                relevance = result.get("relevance", 0.0)
                context += f"{i}. {result.get('title', 'Untitled')} (Relevance: {relevance:.2f})\n"
                context += f"   Source: {result.get('source', 'Unknown')}\n"
                context += f"   Snippet: {result.get('snippet', '')}...\n"
                context += f"   Link: {result.get('link', '')}\n\n"

        return context
