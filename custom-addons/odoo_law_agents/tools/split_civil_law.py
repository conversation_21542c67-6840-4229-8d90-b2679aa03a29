#!/usr/bin/env python3
"""
Script to split the large civil_law.txt file into multiple smaller files
based on logical sections of the Code of Civil Procedure.
"""

import os
import re
import logging

# Set up logging
logging.basicConfig(level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Define paths
INPUT_FILE = '../data/english/civil_law/civil_law.txt'
OUTPUT_DIR = '../data/english/civil_law/split'

# Create output directory if it doesn't exist
os.makedirs(OUTPUT_DIR, exist_ok=True)

# Define patterns for splitting
PART_PATTERN = r'Part [ivxlcdm]+\s+([^\.]+)'
ORDER_PATTERN = r'order [ivxlcdm]+\s+([^\.]+)'

def clean_text(text):
    """Clean and format the text with proper line breaks."""
    # Replace multiple spaces with a single space
    text = re.sub(r'\s+', ' ', text)

    # Add line breaks after periods followed by a number or capital letter
    text = re.sub(r'(\.\s+)(\d+\.|\d+[a-z]\.|\d+|[A-Z])', r'\1\n\2', text)

    # Add line breaks after section headers
    text = re.sub(r'(s\s*ections?)', r'\n\1', text, flags=re.IGNORECASE)
    text = re.sub(r'(r\s*ules?)', r'\n\1', text, flags=re.IGNORECASE)

    # Add line breaks before "Part" and "Order"
    text = re.sub(r'(Part\s+[IVXLCDM]+)', r'\n\n\1', text, flags=re.IGNORECASE)
    text = re.sub(r'(Order\s+[IVXLCDM]+)', r'\n\n\1', text, flags=re.IGNORECASE)

    return text.strip()

def sanitize_filename(name, max_length=50):
    """Convert a string to a valid filename with a maximum length."""
    # Replace spaces and special characters
    name = re.sub(r'[^\w\s-]', '', name.lower())
    name = re.sub(r'[-\s]+', '_', name)
    name = name.strip('_')

    # Truncate if too long
    if len(name) > max_length:
        name = name[:max_length]
        # Ensure we don't end with a partial word
        if '_' in name:
            name = name.rsplit('_', 1)[0]

    return name

def main():
    logger.info(f"Reading input file: {INPUT_FILE}")

    try:
        with open(INPUT_FILE, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        logger.error(f"Error reading input file: {e}")
        return

    logger.info(f"File size: {len(content)} bytes")

    # Create an index file
    index_file_path = os.path.join(OUTPUT_DIR, '00_index.txt')
    index_content = "# Index of Civil Law Files\n\n"

    # First, split by preliminary section
    prelim_match = re.search(r'(.*?)(Part\s+i\s+)', content, re.IGNORECASE | re.DOTALL)

    if prelim_match:
        prelim_text = prelim_match.group(1)
        rest_content = content[len(prelim_text):]

        # Save preliminary section
        prelim_file = os.path.join(OUTPUT_DIR, '01_preliminary.txt')
        with open(prelim_file, 'w', encoding='utf-8') as f:
            f.write(clean_text(prelim_text))

        logger.info(f"Created file: {prelim_file}")
        index_content += f"1. [Preliminary Sections](01_preliminary.txt)\n"
    else:
        logger.warning("Could not find preliminary section")
        rest_content = content

    # Split by Parts
    part_matches = list(re.finditer(r'(Part\s+[IVXLCDM]+\s+[^\.]+)', rest_content, re.IGNORECASE))

    if not part_matches:
        logger.warning("No parts found in the content")
        return

    # Process each part
    for i, match in enumerate(part_matches):
        start_pos = match.start()

        # Determine end position (start of next part or end of content)
        if i < len(part_matches) - 1:
            end_pos = part_matches[i + 1].start()
        else:
            end_pos = len(rest_content)

        part_content = rest_content[start_pos:end_pos]
        part_title = match.group(1)

        # Extract part number and name
        part_num_match = re.search(r'Part\s+([IVXLCDM]+)', part_title, re.IGNORECASE)
        part_name_match = re.search(r'Part\s+[IVXLCDM]+\s+(.*)', part_title, re.IGNORECASE)

        if part_num_match and part_name_match:
            part_num = part_num_match.group(1).upper()
            part_name = part_name_match.group(1).strip()
            file_name = f"{i+2:02d}_part_{part_num.lower()}_{sanitize_filename(part_name)}.txt"

            # Save part to file
            part_file = os.path.join(OUTPUT_DIR, file_name)
            with open(part_file, 'w', encoding='utf-8') as f:
                f.write(clean_text(part_content))

            logger.info(f"Created file: {part_file}")
            index_content += f"{i+2}. [Part {part_num} - {part_name}]({file_name})\n"

    # Now split the Orders (which are in the appendix)
    order_content = ""
    appendix_match = re.search(r'(the first schedule.*)', content, re.IGNORECASE | re.DOTALL)

    if appendix_match:
        order_content = appendix_match.group(1)

        # Find all orders
        order_matches = list(re.finditer(r'(order\s+[IVXLCDM]+\s+[^\.]+)', order_content, re.IGNORECASE))

        if order_matches:
            # Create a file for the appendix introduction
            intro_end = order_matches[0].start()
            intro_content = order_content[:intro_end]

            intro_file = os.path.join(OUTPUT_DIR, f"{len(part_matches)+2:02d}_appendix_intro.txt")
            with open(intro_file, 'w', encoding='utf-8') as f:
                f.write(clean_text(intro_content))

            logger.info(f"Created file: {intro_file}")
            index_content += f"{len(part_matches)+2}. [Appendix Introduction]({os.path.basename(intro_file)})\n"

            # Process each order
            for i, match in enumerate(order_matches):
                start_pos = match.start()

                # Determine end position
                if i < len(order_matches) - 1:
                    end_pos = order_matches[i + 1].start()
                else:
                    end_pos = len(order_content)

                order_text = order_content[start_pos:end_pos]
                order_title = match.group(1)

                # Extract order number and name
                order_num_match = re.search(r'order\s+([IVXLCDM]+)', order_title, re.IGNORECASE)
                order_name_match = re.search(r'order\s+[IVXLCDM]+\s+(.*)', order_title, re.IGNORECASE)

                if order_num_match and order_name_match:
                    order_num = order_num_match.group(1).upper()
                    order_name = order_name_match.group(1).strip()
                    file_name = f"{len(part_matches)+3+i:02d}_order_{order_num.lower()}_{sanitize_filename(order_name)}.txt"

                    # Save order to file
                    order_file = os.path.join(OUTPUT_DIR, file_name)
                    with open(order_file, 'w', encoding='utf-8') as f:
                        f.write(clean_text(order_text))

                    logger.info(f"Created file: {order_file}")
                    index_content += f"{len(part_matches)+3+i}. [Order {order_num} - {order_name}]({file_name})\n"

    # Write the index file
    with open(index_file_path, 'w', encoding='utf-8') as f:
        f.write(index_content)

    logger.info(f"Created index file: {index_file_path}")
    logger.info("Splitting complete!")

if __name__ == "__main__":
    main()
