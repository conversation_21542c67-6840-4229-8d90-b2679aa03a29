"""
Utilities for visualizing LangGraph agents using Mermaid.

This module provides functions for rendering and saving LangGraph visualizations
using the Mermaid.ink API, which doesn't require additional packages.
"""

import logging
import os
import tempfile
import base64
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple
import requests
import json

_logger = logging.getLogger(__name__)

def get_mermaid_definition(graph, curve_style="basis") -> str:
    """Generate a Mermaid graph definition from a LangGraph graph.

    Args:
        graph: The compiled LangGraph graph.
        curve_style: The curve style to use for edges (default: "basis").

    Returns:
        A Mermaid graph definition string.
    """
    try:
        # Try to get the nodes and edges from the graph
        nodes = []
        edges = []

        # Check Python version for compatibility
        import sys
        python_version = sys.version_info
        _logger.info(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")

        # Try to get the graph structure
        try:
            # First try to use the get_graph method with xray=True
            try:
                internal_graph = graph.get_graph(xray=True)
                _logger.info("Successfully got internal graph representation.")

                # Get nodes and edges from the internal graph
                nodes = list(internal_graph.nodes())
                edges = list(internal_graph.edges())
            except Exception as e:
                _logger.warning(f"Could not get internal graph with xray=True: {str(e)}")

                # Try to access the graph directly
                try:
                    nodes = list(graph._graph.nodes())
                    edges = list(graph._graph.edges())
                except Exception as e:
                    _logger.warning(f"Could not access graph directly: {str(e)}")

                    # Use a predefined structure based on the agent type
                    _logger.info("Using predefined graph structure as fallback")

                    # Check if we can determine the agent type from the graph
                    agent_type = "unknown"
                    if hasattr(graph, "agent_type"):
                        agent_type = graph.agent_type
                    elif hasattr(graph, "_agent_type"):
                        agent_type = graph._agent_type

                    # Define detailed nodes and edges based on agent type
                    if "gujarat" in str(agent_type).lower():
                        # Gujarat Law Agent has a more complex workflow
                        nodes = [
                            "Gujarat Law Agent",
                            "Retrival tools",
                            "Web Search",
                            "Get Suitable PDF Results",
                            "Civil Law Agent",
                            "Land Law Agent",
                            "Generate Response",
                            "END"
                        ]

                        # Define the edges with conditional routing
                        edges = [
                            # Conditional edges from Gujarat Law Agent
                            ("Gujarat Law Agent", "Civil Law Agent"),
                            ("Gujarat Law Agent", "Land Law Agent"),
                            ("Gujarat Law Agent", "Retrival tools"),

                            # Conditional edges from Retrival tools
                            ("Retrival tools", "Web Search"),
                            ("Retrival tools", "Get Suitable PDF Results"),

                            # Edges to Generate Response
                            ("Web Search", "Generate Response"),
                            ("Get Suitable PDF Results", "Generate Response"),
                            ("Civil Law Agent", "Generate Response"),
                            ("Land Law Agent", "Generate Response"),

                            # Final edge
                            ("Generate Response", "END")
                        ]
                    elif "land" in str(agent_type).lower():
                        # Land Law Agent has a simpler workflow
                        nodes = ["Land Law Agent", "Generate Response", "END"]
                        edges = [("Land Law Agent", "Generate Response"),
                                ("Generate Response", "END")]
                    elif "civil" in str(agent_type).lower():
                        # Civil Law Agent has a simpler workflow
                        nodes = ["Civil Law Agent", "Generate Response", "END"]
                        edges = [("Civil Law Agent", "Generate Response"),
                                ("Generate Response", "END")]
                    else:
                        # Default structure as fallback
                        nodes = ["Start", "Process", "End"]
                        edges = [("Start", "Process"), ("Process", "End")]
        except Exception as e:
            _logger.error(f"Failed to get graph structure: {str(e)}")
            # Use a default structure
            nodes = ["Start", "Process", "End"]
            edges = [("Start", "Process"), ("Process", "End")]

        # Generate the Mermaid definition as a single string
        # Using the most basic format for maximum compatibility
        mermaid_def = "flowchart TD\n"

        # Add nodes
        for node in nodes:
            node_id = str(node).replace(" ", "_")
            node_label = str(node)
            mermaid_def += f"    {node_id}[\"{ node_label }\"]\n"

        # Add edges
        for edge in edges:
            source = str(edge[0]).replace(" ", "_")
            target = str(edge[1]).replace(" ", "_")
            mermaid_def += f"    {source} --> {target}\n"

        # Return the complete definition
        return mermaid_def
    except Exception as e:
        _logger.error(f"Failed to generate Mermaid definition: {str(e)}")
        return "flowchart TD\\n    A[\"Error\"] \\n    B[\"Failed to generate graph\"] \\n    C[\"Check logs for details\"] \\n    A --> B \\n    B --> C"

def render_mermaid_png(mermaid_def: str) -> Optional[bytes]:
    """Render a Mermaid definition to a PNG image using the Mermaid.ink API.

    Args:
        mermaid_def: The Mermaid graph definition.

    Returns:
        The PNG image data as bytes, or None if rendering failed.
    """
    try:
        # Encode the Mermaid definition for the URL
        encoded_def = base64.urlsafe_b64encode(mermaid_def.encode()).decode()

        # Create the URL for the Mermaid.ink API
        url = f"https://mermaid.ink/img/{encoded_def}"

        # Log the URL for debugging
        _logger.info(f"Mermaid.ink URL: {url}")

        # Make the request to the Mermaid.ink API
        response = requests.get(url)

        # Check if the request was successful
        if response.status_code == 200:
            _logger.info("Successfully rendered Mermaid graph.")
            return response.content
        else:
            _logger.error(f"Failed to render Mermaid graph: {response.status_code} {response.text}")
            return None
    except Exception as e:
        _logger.error(f"Failed to render Mermaid graph: {str(e)}")
        return None

def save_mermaid_png(mermaid_def: str, filepath: str) -> Optional[str]:
    """Save a Mermaid definition to a PNG file using the Mermaid.ink API.

    Args:
        mermaid_def: The Mermaid graph definition.
        filepath: The path to save the PNG file.

    Returns:
        The path to the saved file, or None if saving failed.
    """
    try:
        # Render the Mermaid definition to a PNG image
        png_data = render_mermaid_png(mermaid_def)

        if png_data:
            # Save the PNG image to a file
            with open(filepath, "wb") as f:
                f.write(png_data)

            _logger.info(f"Saved Mermaid graph to {filepath}")
            return filepath
        else:
            _logger.error("Failed to render Mermaid graph.")
            return None
    except Exception as e:
        _logger.error(f"Failed to save Mermaid graph: {str(e)}")
        return None

def save_graph_to_odoo_filestore(env, graph, filename: str = "langgraph") -> Optional[Dict[str, Any]]:
    """Save a LangGraph graph to the Odoo filestore using Mermaid.

    Args:
        env: The Odoo environment.
        graph: The compiled LangGraph graph.
        filename: The name of the output file (without extension).

    Returns:
        A dictionary with information about the saved file, or None if saving failed.
    """
    try:
        # Generate the Mermaid definition
        mermaid_def = get_mermaid_definition(graph)

        # Create a temporary file for the PNG image
        with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp_file:
            temp_path = temp_file.name

        # Save the Mermaid graph to the temporary file
        output_path = save_mermaid_png(mermaid_def, temp_path)

        if not output_path:
            _logger.error("Failed to save Mermaid graph.")
            return None

        # Read the rendered file
        with open(output_path, 'rb') as f:
            file_data = f.read()

        # Get the filename from the path
        file_name = f"{filename}.png"

        # Create an attachment in Odoo
        attachment = env['ir.attachment'].create({
            'name': file_name,
            'datas': base64.b64encode(file_data),
            'res_model': 'law.agent',
            'res_id': 0,  # Generic attachment not linked to a specific record
            'type': 'binary',
            'mimetype': 'image/png',
        })

        _logger.info(f"Graph saved to Odoo filestore with attachment ID: {attachment.id}")

        # Clean up the temporary file
        os.unlink(output_path)

        # Return information about the saved file
        return {
            'attachment_id': attachment.id,
            'name': file_name,
            'url': f'/web/content/{attachment.id}?download=true',
        }
    except Exception as e:
        _logger.error(f"Failed to save graph to Odoo filestore: {str(e)}")
        return None

def save_graph_to_folder(graph, folder_path: str, filename: str = "langgraph") -> Optional[str]:
    """Save a LangGraph graph to a folder using Mermaid.

    Args:
        graph: The compiled LangGraph graph.
        folder_path: The path to the folder to save the graph in.
        filename: The name of the output file (without extension).

    Returns:
        The path to the saved file, or None if saving failed.
    """
    try:
        # Ensure the folder exists
        os.makedirs(folder_path, exist_ok=True)

        # Generate the Mermaid definition
        mermaid_def = get_mermaid_definition(graph)

        # Create the output path
        output_path = os.path.join(folder_path, f"{filename}.png")

        # Save the Mermaid graph to the output path
        result_path = save_mermaid_png(mermaid_def, output_path)

        if not result_path:
            _logger.error("Failed to save Mermaid graph.")
            return None

        _logger.info(f"Graph saved to: {result_path}")

        return result_path
    except Exception as e:
        _logger.error(f"Failed to save graph to folder: {str(e)}")
        return None
