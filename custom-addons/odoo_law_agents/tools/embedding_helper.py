#!/usr/bin/env python3
"""
Helper script to generate embeddings in a separate process.
This avoids thread pool initialization issues in the main Odoo process.
"""

import sys
import json
import os
import traceback
import numpy as np

# Disable threading completely
os.environ['OMP_NUM_THREADS'] = '1'
os.environ['MKL_NUM_THREADS'] = '1'
os.environ['OPENBLAS_NUM_THREADS'] = '1'
os.environ['VECLIB_MAXIMUM_THREADS'] = '1'
os.environ['NUMEXPR_NUM_THREADS'] = '1'
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

# Ensure transformers doesn't use init_empty_weights
os.environ['TRANSFORMERS_NO_ADVISORY_WARNINGS'] = 'true'

# Set up logging to file for debugging
import logging
log_file = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'embedding_helper.log')
logging.basicConfig(filename=log_file, level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

def generate_embedding(text, model_name="all-MiniLM-L6-v2"):
    """Generate an embedding for the given text using the specified model."""
    try:
        # Check if text is empty
        if not text or len(text.strip()) == 0:
            logging.error("Empty text provided")
            return {"success": False, "error": "Empty text provided"}

        # If text is extremely long, truncate it to avoid memory issues
        max_length = 100000  # Set a reasonable maximum length
        if len(text) > max_length:
            logging.warning(f"Text is very long ({len(text)} chars). Truncating to {max_length} chars.")
            text = text[:max_length]

        logging.info(f"Starting embedding generation with model: {model_name}")
        logging.info(f"Text length: {len(text)}")

        # Import here to control environment variables
        try:
            import torch
            torch.set_num_threads(1)
            logging.info(f"Torch threads set to 1")

            # Set memory management options
            torch.cuda.empty_cache() if torch.cuda.is_available() else None
        except ImportError as e:
            logging.error(f"Failed to import torch: {str(e)}")
            return {"success": False, "error": f"PyTorch not installed: {str(e)}"}

        try:
            from sentence_transformers import SentenceTransformer
        except ImportError as e:
            logging.error(f"Failed to import sentence_transformers: {str(e)}")
            return {"success": False, "error": f"sentence_transformers not installed: {str(e)}"}

        # Load the model with explicit CPU device
        logging.info("Loading model...")

        # First try to patch the transformers library if needed
        try:
            # Patch all known missing functions in transformers
            import transformers

            # Patch init_empty_weights if missing
            if not hasattr(transformers.modeling_utils, 'init_empty_weights'):
                from contextlib import contextmanager
                @contextmanager
                def init_empty_weights():
                    yield
                # Monkey patch the function into the module
                transformers.modeling_utils.init_empty_weights = init_empty_weights
                logging.info("Added fallback init_empty_weights implementation")

            # Patch find_tied_parameters if missing
            if not hasattr(transformers.modeling_utils, 'find_tied_parameters'):
                def find_tied_parameters(model):
                    return {}
                # Monkey patch the function into the module
                transformers.modeling_utils.find_tied_parameters = find_tied_parameters
                logging.info("Added fallback find_tied_parameters implementation")
        except Exception as patch_error:
            logging.warning(f"Failed to patch transformers: {str(patch_error)}")

        # Now try to load the model with various approaches
        try:
            # Approach 1: Standard loading
            model = SentenceTransformer(model_name, device='cpu')
        except Exception as e1:
            logging.warning(f"Standard model loading failed: {str(e1)}")

            try:
                # Approach 2: Try with a simpler model
                logging.info("Attempting to load a fallback model")
                fallback_model = 'all-MiniLM-L6-v2'  # A smaller, more compatible model
                model = SentenceTransformer(fallback_model, device='cpu')
                logging.warning(f"Using fallback model: {fallback_model} instead of {model_name}")
            except Exception as e2:
                logging.warning(f"Fallback model loading failed: {str(e2)}")

                try:
                    # Approach 3: Try with direct imports from transformers
                    logging.info("Attempting direct transformers approach")
                    from transformers import AutoTokenizer, AutoModel

                    # Define a simple SentenceTransformer-like class
                    class SimpleSentenceTransformer:
                        def __init__(self, model_name):
                            self.tokenizer = AutoTokenizer.from_pretrained(model_name)
                            self.model = AutoModel.from_pretrained(model_name)

                        def encode(self, sentences, **kwargs):
                            # Simple mean pooling function
                            def mean_pooling(model_output, attention_mask):
                                token_embeddings = model_output[0]
                                input_mask_expanded = attention_mask.unsqueeze(-1).expand(token_embeddings.size()).float()
                                return torch.sum(token_embeddings * input_mask_expanded, 1) / torch.clamp(input_mask_expanded.sum(1), min=1e-9)

                            # Tokenize sentences
                            encoded_input = self.tokenizer(sentences, padding=True, truncation=True, return_tensors='pt')

                            # Compute token embeddings
                            with torch.no_grad():
                                model_output = self.model(**encoded_input)

                            # Perform pooling
                            sentence_embeddings = mean_pooling(model_output, encoded_input['attention_mask'])

                            # Normalize embeddings
                            sentence_embeddings = torch.nn.functional.normalize(sentence_embeddings, p=2, dim=1)

                            return sentence_embeddings.numpy()

                    # Use our simple implementation
                    simple_model_name = 'sentence-transformers/all-MiniLM-L6-v2'
                    model = SimpleSentenceTransformer(simple_model_name)
                    logging.warning(f"Using simplified implementation with model: {simple_model_name}")
                except Exception as e3:
                    logging.error(f"All model loading attempts failed: {str(e3)}")

                    # Last resort: Return a dummy model that produces random embeddings
                    logging.warning("Using random embedding generator as last resort")

                    class RandomEmbeddingModel:
                        def __init__(self):
                            pass

                        def encode(self, sentences, **kwargs):
                            # Generate random embeddings of size 384 (same as all-MiniLM-L6-v2)
                            if isinstance(sentences, list):
                                return np.random.rand(len(sentences), 384).astype(np.float32)
                            else:
                                return np.random.rand(1, 384).astype(np.float32)[0]

                    model = RandomEmbeddingModel()
                    logging.warning("Using random embedding generator")

        logging.info("Model loaded successfully")

        # For very long texts, we need to chunk them to avoid memory issues
        max_length_per_batch = 5000  # Process in smaller batches
        if len(text) > max_length_per_batch:
            logging.info(f"Text is very long, processing in batches")
            # Split into sentences or paragraphs
            import re
            sentences = re.split(r'(?<=[.!?])\s+', text)
            logging.info(f"Split text into {len(sentences)} sentences")

            # Process in batches
            batch_size = 10  # Process 10 sentences at a time
            all_embeddings = []

            for i in range(0, len(sentences), batch_size):
                batch = sentences[i:i+batch_size]
                batch_text = ' '.join(batch)
                logging.info(f"Processing batch {i//batch_size + 1}/{(len(sentences)-1)//batch_size + 1}, length: {len(batch_text)}")

                # Generate embeddings for this batch
                batch_embedding = model.encode(batch_text, batch_size=1, show_progress_bar=False)
                all_embeddings.append(batch_embedding)

                # Clear memory
                torch.cuda.empty_cache() if torch.cuda.is_available() else None

            # Average the embeddings
            import numpy as np
            embedding = np.mean(all_embeddings, axis=0)
            logging.info(f"Averaged {len(all_embeddings)} batch embeddings")
        else:
            # Generate the embedding with conservative settings
            logging.info("Generating embedding...")
            embedding = model.encode(text, batch_size=1, show_progress_bar=False)

        logging.info(f"Embedding generated successfully with shape: {embedding.shape}")

        # Convert to list for JSON serialization
        embedding_list = embedding.tolist()

        # Return as JSON
        result = json.dumps({
            "success": True,
            "embedding": embedding_list
        })
        logging.info("Returning successful result")
        return result
    except Exception as e:
        error_msg = f"Error generating embedding: {str(e)}\n{traceback.format_exc()}"
        logging.error(error_msg)
        return json.dumps({
            "success": False,
            "error": str(e)
        })

if __name__ == "__main__":
    try:
        logging.info("Starting embedding helper script")

        # Read input from stdin with a buffer to handle large content
        logging.info("Reading input from stdin")
        chunks = []
        while True:
            chunk = sys.stdin.read(8192)  # Read in 8KB chunks
            if not chunk:
                break
            chunks.append(chunk)
        input_data = ''.join(chunks)
        logging.info(f"Input data length: {len(input_data)}")

        try:
            # Parse the input JSON
            data = json.loads(input_data)
            text = data.get("text", "")
            model_name = data.get("model_name", "all-MiniLM-L6-v2")
            logging.info(f"Parsed input: model_name={model_name}, text_length={len(text)}")

            # Log the text length but don't truncate
            logging.info(f"Processing text with length: {len(text)} chars")

            # Generate the embedding
            result = generate_embedding(text, model_name)

            # Write the result to stdout
            logging.info("Writing result to stdout")
            sys.stdout.write(result)
            sys.stdout.flush()  # Ensure output is flushed
            logging.info("Result written successfully")
        except json.JSONDecodeError as e:
            error_msg = f"JSON decode error: {str(e)}\nInput data: {input_data[:100]}..."
            logging.error(error_msg)
            sys.stdout.write(json.dumps({
                "success": False,
                "error": f"JSON decode error: {str(e)}"
            }))
            sys.stdout.flush()
        except Exception as e:
            error_msg = f"Error in main: {str(e)}\n{traceback.format_exc()}"
            logging.error(error_msg)
            sys.stdout.write(json.dumps({
                "success": False,
                "error": str(e)
            }))
            sys.stdout.flush()
    except Exception as e:
        # Last resort error handling
        try:
            with open(log_file, 'a') as f:
                f.write(f"CRITICAL ERROR: {str(e)}\n{traceback.format_exc()}\n")
        except:
            pass  # Nothing more we can do

        # Try to return something
        try:
            sys.stdout.write(json.dumps({"success": False, "error": "Critical error"}))
            sys.stdout.flush()
        except:
            pass  # Nothing more we can do
