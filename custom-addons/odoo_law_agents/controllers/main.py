import logging
import os
import mimetypes
from odoo import http, _
from odoo.http import request, Response
from odoo.exceptions import UserError, AccessError
from odoo.addons.iap.tools import iap_tools

_logger = logging.getLogger(__name__)

# Default Odoo LLM Gateway endpoint
DEFAULT_OLG_ENDPOINT = 'https://olg.api.odoo.com'

# Dictionary to store temporary files for download
TEMP_FILES = {}

class LawAgentController(http.Controller):
    @http.route('/law/agent/query', type='json', auth='user')
    def process_query(self, **kwargs):
        """Process a query using a specific law agent.

        Args:
            query: The query text
            agent_id: The ID of the agent to use
            language: Optional language code (default: 'en')

        Returns:
            A JSON response with the agent's response
        """
        query = kwargs.get('query')
        agent_id = kwargs.get('agent_id')
        language = kwargs.get('language', 'en')

        if not query or not agent_id:
            return {'error': 'Missing required parameters', 'status': 'error'}

        agent = request.env['law.agent'].browse(int(agent_id))
        if not agent.exists():
            return {'error': 'Agent not found', 'status': 'error'}

        # Process query using LangGraph agent
        try:
            result = agent._process_query(query, language)
            return {'result': result, 'status': 'success'}
        except Exception as e:
            _logger.error(f"Error processing query: {str(e)}")
            return {'error': str(e), 'status': 'error'}

    @http.route('/odoo_law_agents/langgraph', type='json', auth='user', csrf=False)
    def langgraph_query(self, **kwargs):
        """Process a query using the langgraph agent.

        This endpoint provides direct access to the langgraph agent without going through
        a specific law.agent record. It's useful for testing and development.

        Args:
            query: The query text
            language: Optional language code (default: 'en')
            use_mock: Optional flag to use mock responses (default: False)

        Returns:
            A JSON response with the agent's response and metadata
        """
        query = kwargs.get('query')
        language = kwargs.get('language', 'en')
        use_mock = kwargs.get('use_mock', False)

        if not query:
            return {'error': 'Missing query parameter', 'status': 'error'}

        try:
            # Import the langgraph agent
            from ..langgraph.agent import process_query

            # Process the query directly using the langgraph agent
            result = process_query(
                query=query,
                language=language,
                user_id=request.env.user.id,
                agent_id=None,  # No specific agent
                env=request.env,  # Pass the environment
                use_mock=use_mock
            )

            # Return the result with additional metadata
            return {
                'result': result.get('response'),
                'sources': result.get('sources', []),
                'query_type': result.get('query_type'),
                'duration': result.get('duration', 0),
                'status': 'success' if not result.get('error') else 'error',
                'error': result.get('error'),
                'mock': use_mock
            }
        except ImportError as e:
            _logger.error(f"Error importing langgraph agent: {str(e)}")
            return {'error': f"Langgraph agent not available: {str(e)}", 'status': 'error'}
        except Exception as e:
            _logger.error(f"Error processing langgraph query: {str(e)}")
            return {'error': str(e), 'status': 'error'}


    @http.route(
        "/odoo_law_agents/response", type="json", auth="user", csrf=False
    )
    def generate_response(self, prompt, conversation_history=None, agent_id=None):
        """Generate a response using the Odoo LLM API.

        Args:
            prompt: The prompt to send to the LLM
            conversation_history: Optional conversation history for context
            agent_id: Optional agent ID to use a specific agent configuration

        Returns:
            A JSON response with the LLM response
        """
        if not prompt or not prompt.strip():
            return {"error": "Empty prompt", "status": "error"}

        # Initialize conversation history if not provided
        if conversation_history is None:
            conversation_history = []

        try:
            # If agent_id is provided, use the agent's configuration
            if agent_id:
                try:
                    agent = request.env['law.agent'].browse(int(agent_id))
                    if not agent.exists():
                        return {'error': 'Agent not found', 'status': 'error'}

                    # Check if we should use mock mode
                    if agent.use_mock:
                        _logger.info(f"Using mock mode for agent {agent.name}")
                        # Create a mock response based on the prompt content
                        if "land" in prompt.lower() or "property" in prompt.lower() or "registration" in prompt.lower():
                            mock_content = f"This is a mock response about land law for: {prompt[:50]}..."
                        elif "civil" in prompt.lower():
                            mock_content = f"This is a mock response about civil law for: {prompt[:50]}..."
                        elif "gujarat" in prompt.lower():
                            mock_content = f"This is a mock response about Gujarat law for: {prompt[:50]}..."
                        else:
                            mock_content = f"This is a mock response for: {prompt[:50]}..."

                        return {
                            "content": mock_content,
                            "status": "success",
                            "mock": True
                        }
                except Exception as e:
                    _logger.error(f"Error processing agent configuration: {str(e)}")
                    # Continue with default processing

            # Get the LLM API endpoint from system parameters
            IrConfigParameter = request.env["ir.config_parameter"].sudo()
            olg_api_endpoint = IrConfigParameter.get_param(
                "web_editor.olg_api_endpoint", DEFAULT_OLG_ENDPOINT
            )
            database_id = IrConfigParameter.get_param("database.uuid")

            # Make the API call
            response = iap_tools.iap_jsonrpc(
                olg_api_endpoint + "/api/olg/1/chat",
                params={
                    "prompt": prompt,
                    "conversation_history": conversation_history,
                    "database_id": database_id,
                },
                timeout=30,
            )

            # Handle the response based on status
            if response["status"] == "success":
                return {
                    "content": response["content"],
                    "status": "success"
                }
            elif response["status"] == "error_prompt_too_long":
                return {
                    "error": "Sorry, your prompt is too long. Try to say it in fewer words.",
                    "status": "error_prompt_too_long"
                }
            elif response["status"] == "limit_call_reached":
                return {
                    "error": "You have reached the maximum number of requests for this service. Try again later.",
                    "status": "limit_call_reached"
                }
            else:
                return {
                    "error": "Sorry, we could not generate a response. Please try again later.",
                    "status": "error"
                }
        except AccessError:
            return {
                "error": "Oops, it looks like our AI is unreachable!",
                "status": "error"
            }
        except Exception as e:
            _logger.error(f"Error generating response: {str(e)}")
            return {
                "error": str(e),
                "status": "error"
            }

    @http.route('/odoo_law_agents/download/<string:file_id>/<string:file_name>', type='http', auth='user')
    def download_file(self, file_id, file_name, **kwargs):
        """Download a file by ID.

        Args:
            file_id: The unique ID of the file
            file_name: The name of the file

        Returns:
            The file content as a response
        """
        _logger.info(f"Download request for file: {file_id}/{file_name}")

        # Check if the file exists in the temporary files dictionary
        if file_id not in TEMP_FILES:
            _logger.error(f"File not found in TEMP_FILES: {file_id}/{file_name}")

            # Try to regenerate the file by finding the case
            try:
                # Parse the file name to find the case
                # Expected format: CaseName_YYYYMMDD_HHMMSS.ext
                # or: CaseName_timestamp.ext
                import re

                # Remove the extension
                base_name = os.path.splitext(file_name)[0]  # Remove extension

                # Try different approaches to extract the case name
                # First, try to remove any timestamp pattern (YYYYMMDD_HHMMSS)
                timestamp_pattern = re.compile(r'_\d{8}_\d{6}$')
                clean_name = timestamp_pattern.sub('', base_name)

                # If that didn't change anything, try removing the last part after underscore
                if clean_name == base_name and '_' in base_name:
                    clean_name = base_name.rsplit('_', 1)[0]

                # Replace underscores with spaces for case name matching
                search_name = clean_name.replace('_', ' ')

                # Also try a more aggressive search by taking just the first part
                # This helps with complex Gujarati names
                if ' ' in search_name:
                    first_word = search_name.split(' ')[0]
                    if len(first_word) > 3:  # Only use if it's a substantial word
                        alt_search_name = first_word
                    else:
                        alt_search_name = search_name
                else:
                    alt_search_name = search_name

                _logger.info(f"Trying to find case with name like: {search_name}")

                # Search for the case using multiple approaches
                LawCase = request.env['law.case'].sudo()

                # Try exact match first
                cases = LawCase.search([('name', '=', search_name)])

                # If no exact match, try a partial match
                if not cases:
                    cases = LawCase.search([('name', 'ilike', search_name)])

                # If still no match, try with the alternative search name
                if not cases and alt_search_name != search_name:
                    _logger.info(f"Trying alternative search with: {alt_search_name}")
                    cases = LawCase.search([('name', 'ilike', alt_search_name)])

                # If still no match, try searching by ID if the file_id looks like a number
                if not cases and file_id.isdigit():
                    _logger.info(f"Trying to find case by ID: {file_id}")
                    case = LawCase.browse(int(file_id))
                    if case.exists():
                        cases = case

                if cases:
                    case = cases[0]  # Take the first matching case
                    _logger.info(f"Found case: {case.name} (ID: {case.id})")

                    # Determine the format from the file extension
                    ext = os.path.splitext(file_name)[1].lower()
                    format_map = {
                        '.pdf': 'pdf',
                        '.docx': 'docx',
                        '.html': 'html',
                        '.txt': 'txt',
                        '.png': 'png'
                    }
                    format = format_map.get(ext, 'pdf')  # Default to PDF if unknown extension

                    # Regenerate the file
                    if hasattr(case, '_generate_analysis_document') and format != 'png':
                        _logger.info(f"Regenerating document in {format} format")
                        file_path, new_file_name = case._generate_analysis_document(format=format)

                        # Store the regenerated file
                        TEMP_FILES[file_id] = file_path
                        _logger.info(f"Regenerated file: {file_path}")
                    elif (format == 'png' or format == 'html') and hasattr(case, 'action_visualize_workflow'):
                        # For workflow visualization
                        from ..langgraph.simple_visualizer import save_workflow_visualization
                        file_path = save_workflow_visualization(format='html')
                        TEMP_FILES[file_id] = file_path
                        _logger.info(f"Regenerated workflow visualization: {file_path}")
                    else:
                        _logger.error("Cannot regenerate file: unsupported format or method not found")
                        return Response("Cannot regenerate file: unsupported format", status=404)
                else:
                    _logger.error(f"No matching case found for: {search_name}")
                    return Response(f"File not found and no matching case found for: {search_name}", status=404)
            except Exception as e:
                _logger.error(f"Error trying to regenerate file: {str(e)}")
                return Response(f"File not found and error regenerating: {str(e)}", status=404)

        # Get the file path
        file_path = TEMP_FILES[file_id]
        _logger.info(f"File path: {file_path}")

        # Check if the file exists
        if not os.path.exists(file_path):
            _logger.error(f"File does not exist at path: {file_path}")
            return Response(f"File not found at path: {file_path}", status=404)

        # Determine the content type based on the file extension
        content_type, _ = mimetypes.guess_type(file_path)
        if not content_type:
            if file_name.endswith('.png'):
                content_type = 'image/png'
            elif file_name.endswith('.html'):
                content_type = 'text/html'
            elif file_name.endswith('.pdf'):
                content_type = 'application/pdf'
            elif file_name.endswith('.docx'):
                content_type = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
            else:
                content_type = 'application/octet-stream'

        # Read the file content
        try:
            with open(file_path, 'rb') as f:
                file_content = f.read()

            # Return the file content as a response
            _logger.info(f"Returning file: {file_name} ({content_type})")
            return Response(
                file_content,
                headers=[
                    ('Content-Type', content_type),
                    ('Content-Disposition', f'attachment; filename="{file_name}"'),
                    ('Content-Length', str(len(file_content)))
                ]
            )
        except Exception as e:
            _logger.error(f"Error reading file: {str(e)}")
            return Response(f"Error reading file: {str(e)}", status=500)
