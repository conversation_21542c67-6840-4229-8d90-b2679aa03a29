"""
Example of using Opik tracing with LangGraph.

This example demonstrates how to use Opik tracing with LangGraph in a simple way,
similar to the example provided by the user.
"""

import os
from typing import Optional, TypedDict
from langgraph.graph import END, StateGraph

# Set up Opik environment variables
os.environ["OPIK_API_KEY"] = "RFTelG5OlZZgpawlu11IDacaC" 
os.environ["OPIK_WORKSPACE"] = "vinusoft85-gmail-com"

# Import Opik tracer
try:
    from opik.integrations.langgraph import OpikTracer
    print("Using langgraph integration for Opik tracing")
except ImportError:
    try:
        from opik.integrations.langchain import OpikTracer
        print("Using langchain integration for Opik tracing")
    except ImportError:
        print("Opik not installed. Install with: pip install opik")
        # Define a mock tracer for testing
        class OpikTracer:
            def __init__(self, graph=None, project_name=None):
                self.graph = graph
                self.project_name = project_name
                print(f"Using mock tracer for project: {project_name}")
            
            def __call__(self, *args, **kwargs):
                print("Mock tracer called")
                return None
            
            def raise_error(self, *args, **kwargs):
                print("Mock tracer: raise_error called")
                return None
            
            def ignore_chain(self, *args, **kwargs):
                print("Mock tracer: ignore_chain called")
                return None


# Define a simple classification function
def classify(question: str) -> str:
    return "greeting" if question.startswith("Hello") else "search"


# Define node functions
def classify_input_node(state):
    question = state.get("question", "").strip()
    classification = classify(question)
    return {"classification": classification}


def handle_greeting_node(state):
    return {"response": "Hello! How can I help you today?"}


def handle_search_node(state):
    question = state.get("question", "").strip()
    search_result = f"Search result for '{question}'"
    return {"response": search_result}


# Define the graph state type
class GraphState(TypedDict):
    question: Optional[str] = None
    classification: Optional[str] = None
    response: Optional[str] = None


# Create the graph
workflow = StateGraph(GraphState)
workflow.add_node("classify_input", classify_input_node)
workflow.add_node("handle_greeting", handle_greeting_node)
workflow.add_node("handle_search", handle_search_node)


# Define the decision function for conditional edges
def decide_next_node(state):
    return (
        "handle_greeting"
        if state.get("classification") == "greeting"
        else "handle_search"
    )


# Add edges to the graph
workflow.add_conditional_edges(
    "classify_input",
    decide_next_node,
    {"handle_greeting": "handle_greeting", "handle_search": "handle_search"},
)
workflow.set_entry_point("classify_input")
workflow.add_edge("handle_greeting", END)
workflow.add_edge("handle_search", END)

# Compile the graph
app = workflow.compile()

# Create the tracer
tracer = OpikTracer(graph=app.get_graph(xray=True))

# Define the input
inputs = {"question": "Hello, how are you?"}

# Invoke the graph with tracing
result = app.invoke(inputs, config={"callbacks": [tracer]})

# Print the result
print(result)


def main():
    """Run the example."""
    print("Running Opik tracing example...")
    
    # Define the input
    inputs = {"question": "Hello, how are you?"}
    
    # Invoke the graph with tracing
    result = app.invoke(inputs, config={"callbacks": [tracer]})
    
    # Print the result
    print(result)
    
    # Try a different input
    inputs = {"question": "What is the weather today?"}
    
    # Invoke the graph with tracing
    result = app.invoke(inputs, config={"callbacks": [tracer]})
    
    # Print the result
    print(result)


if __name__ == "__main__":
    main()
