# Odoo Law Agents Examples

This directory contains examples of how to use the Odoo Law Agents module.

## Opik Tracing Example

The `opik_tracing_example.py` file demonstrates how to use Opik tracing with LangGraph in a simple way. This example is based on the example provided by the user and shows how to:

1. Set up Opik environment variables
2. Create a simple LangGraph agent
3. Add Opik tracing to the agent
4. Invoke the agent with tracing

### Python Version Compatibility

The example includes Python version detection and compatibility handling:

```python
# Check Python version for compatibility
import sys
python_version = sys.version_info
print(f"Python version: {python_version.major}.{python_version.minor}.{python_version.micro}")

# For Python < 3.11, we need to handle the NotRequired type qualifier issue
if python_version.major == 3 and python_version.minor < 11:
    print("Python version < 3.11, using MockTracer for compatibility")
    return MockTracer(graph=graph, project_name=project_name)
```

This ensures that the code works correctly on different Python versions.

### Running the Example

To run the example, make sure you have the required dependencies installed:

```bash
pip install langgraph opik
```

Then run the example:

```bash
python opik_tracing_example.py
```

### Using in Odoo

To use Opik tracing in Odoo, you can:

1. Configure the agent in the Odoo interface:
   - Go to Law Agents > Configuration > Agents
   - Enable "Enable Opik Tracing"
   - Enter your Opik API Key
   - Enter your Project Name
   - Enter your Workspace

2. Process a query:
   - Go to Law Agents > Queries > Create
   - Enter your query
   - Select the agent with tracing enabled
   - Click "Process Query"

The query will be processed with Opik tracing enabled, and you can view the traces in your Comet.ml dashboard.

## Implementation Details

The Opik tracing integration is implemented in the `simple_tracing.py` file in the `tools` directory. This file provides:

1. `setup_opik_tracing`: Sets up Opik tracing by setting environment variables
2. `get_opik_tracer`: Gets an Opik tracer for a LangGraph graph
3. `get_tracing_config`: Gets the tracing configuration for a LangGraph graph
4. `MockTracer`: A simple mock tracer for testing without Opik

These functions are used in the `agent.py` file to add tracing to the LangGraph agent.

### MockTracer

The `MockTracer` class is a drop-in replacement for the Opik tracer that implements the same interface but doesn't actually send data to Opik. It's used as a fallback when:

1. The Python version is < 3.11 (to avoid the `NotRequired` type qualifier issue)
2. The Opik package is not installed
3. There's an error creating the real Opik tracer

This ensures that the agent can continue running even if Opik tracing is not available.

```python
class MockTracer:
    """A simple mock tracer for testing without Opik.

    This class implements the minimal interface needed for langgraph tracing.
    """

    def __init__(self, graph=None, project_name=None):
        """Initialize the mock tracer."""
        self.graph = graph
        self.project_name = project_name
        _logger.info(f"Using MockTracer for project: {project_name}")

    def __call__(self, *args, **kwargs):
        """Called when the tracer is used as a callback."""
        _logger.debug("MockTracer: __call__ called")
        return None

    # ... other methods ...
```
