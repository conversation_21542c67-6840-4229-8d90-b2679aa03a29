from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
import os

_logger = logging.getLogger(__name__)

class LoadDocumentsWizard(models.TransientModel):
    _name = 'load.documents.wizard'
    _description = 'Load Documents Wizard'

    document_type = fields.Selection([
        ('land_law', 'Land Law'),
        ('civil_law', 'Civil Law'),
        ('all', 'All Types')
    ], string='Document Type', required=True, default='all')

    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati')
    ], string='Language', required=True, default='en')

    folder_path = fields.Char(string='Custom Folder Path',
                             help='Leave empty to use the default data folder in the module')

    # Store the last language to prevent infinite onchange loops
    _last_language = None

    @api.onchange('language')
    def _onchange_language(self):
        """Update the placeholder when language changes."""
        # Skip if the language hasn't actually changed or is not set
        if not self.language or self._last_language == self.language:
            return

        # Update the last language
        self.__class__._last_language = self.language

        # Don't show a warning every time, just update the placeholder
        # This reduces UI noise and prevents potential infinite loops
        return

    @api.model
    def default_get(self, fields_list):
        """Override default_get to set initial values."""
        res = super(LoadDocumentsWizard, self).default_get(fields_list)

        # Set the initial language value to avoid onchange loops
        if 'language' in fields_list and not res.get('language'):
            res['language'] = 'en'
            self.__class__._last_language = 'en'

        # Set the initial document type
        if 'document_type' in fields_list and not res.get('document_type'):
            res['document_type'] = 'all'

        # Clear the folder path to ensure we use the default
        if 'folder_path' in fields_list:
            res['folder_path'] = False

        return res

    def get_default_folder_path(self):
        """Get the default folder path without triggering onchange."""
        LawDocument = self.env['law.document']
        return LawDocument.get_default_data_folder(self.language)

    def action_load_documents(self):
        """Load documents from the specified folder."""
        self.ensure_one()

        document_type = None if self.document_type == 'all' else self.document_type

        try:
            LawDocument = self.env['law.document']

            # Check if we should process PDF files
            pdf_folder = None
            if self.language == 'en':
                pdf_folder = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pdf_english')
            else:
                pdf_folder = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'pdf_gujarati')

            document_ids = []

            # First, load text documents from the specified folder
            text_document_ids = LawDocument.load_documents_from_folder(
                folder_path=self.folder_path or None,
                document_type=document_type,
                language=self.language
            )
            document_ids.extend(text_document_ids)

            # Then, process PDF files if the folder exists
            if os.path.exists(pdf_folder) and os.path.isdir(pdf_folder):
                from ..services.document_processing_service import DocumentProcessingService
                doc_service = DocumentProcessingService(self.env)

                if document_type:
                    # Process PDFs for the specific document type
                    pdf_document_ids = doc_service.process_pdf_folder(
                        folder_path=pdf_folder,
                        document_type=document_type,
                        language=self.language
                    )
                    document_ids.extend(pdf_document_ids)
                else:
                    # Process all PDFs
                    pdf_document_ids = doc_service.process_pdf_folder(
                        folder_path=pdf_folder,
                        document_type='land_law',  # Default to land_law for now
                        language=self.language
                    )
                    document_ids.extend(pdf_document_ids)

            if document_ids:
                # Count documents with embeddings
                LawDocument = self.env['law.document']
                docs_with_embeddings = LawDocument.search_count([('id', 'in', document_ids), ('has_embeddings', '=', True)])

                # Show success notification with embedding information
                message = _('{} documents loaded successfully. {} documents have embeddings.').format(
                    len(document_ids), docs_with_embeddings)

                # Show a notification first
                self.env['bus.bus']._sendone(self.env.user.partner_id, 'simple_notification', {
                    'title': _('Documents Loaded'),
                    'message': message,
                    'sticky': True,
                    'warning': False,
                })

                # Then redirect to the document list
                return {
                    'type': 'ir.actions.act_window',
                    'name': _('Loaded Documents'),
                    'res_model': 'law.document',
                    'view_mode': 'tree,form',
                    'domain': [('id', 'in', document_ids)],
                    'context': {'default_document_type': self.document_type if self.document_type != 'all' else False},
                    'target': 'current',
                    'flags': {'initial_mode': 'tree'},
                    'views': [(False, 'tree'), (False, 'form')],
                    'help': f"<p class='o_view_nocontent_smiling_face'>{message}</p>"
                }
            else:
                # Show warning and stay on the wizard
                self.env['bus.bus']._sendone(self.env.user.partner_id, 'simple_notification', {
                    'title': _('No Documents'),
                    'message': _('No documents were found or loaded. Check the logs for errors.'),
                    'sticky': False,
                    'warning': True,
                })
                # Return a client action to display a notification
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('No Documents'),
                        'message': _('No documents were found or loaded. Check the logs for errors.'),
                        'sticky': False,
                        'type': 'warning',
                    }
                }
        except Exception as e:
            _logger.error(f"Error loading documents: {str(e)}")
            raise UserError(_('Error loading documents: %s') % str(e))
