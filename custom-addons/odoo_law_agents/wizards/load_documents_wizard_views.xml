<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_load_documents_wizard_form" model="ir.ui.view">
        <field name="name">load.documents.wizard.form</field>
        <field name="model">load.documents.wizard</field>
        <field name="arch" type="xml">
            <form string="Load Documents">
                <sheet>
                    <group>
                        <field name="document_type"/>
                        <field name="language"/>
                        <field name="folder_path" placeholder="/path/to/documents"/>
                    </group>
                    <div class="alert alert-info" role="alert">
                        <p><strong>Note:</strong> If no folder path is specified, the system will automatically use the default data folder.</p>
                        <p>The default data folder is located in the <code>data/[language]</code> directory of the odoo_law_agents module.</p>
                        <p>The folder should contain subfolders for each document type (e.g., land_law, civil_law).</p>
                    </div>
                </sheet>
                <footer>
                    <button name="action_load_documents" string="Load Documents" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_load_documents_wizard" model="ir.actions.act_window">
        <field name="name">Load Documents</field>
        <field name="res_model">load.documents.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'create': True, 'default_language': 'en', 'default_document_type': 'all'}</field>
        <field name="binding_model_id" eval="False"/>
        <field name="binding_type">action</field>
        <field name="binding_view_types">form,tree</field>
    </record>


</odoo>
