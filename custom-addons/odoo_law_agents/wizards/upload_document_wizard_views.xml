<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_upload_document_wizard_form" model="ir.ui.view">
        <field name="name">upload.document.wizard.form</field>
        <field name="model">upload.document.wizard</field>
        <field name="arch" type="xml">
            <form string="Upload Document">
                <sheet>
                    <group>
                        <field name="name" placeholder="Document Name"/>
                        <field name="document_type" invisible="1"/>
                        <field name="law_type_id"/>
                        <field name="category_id"/>
                        <field name="document_type_ids" widget="many2many_tags" readonly="1"/>
                        <field name="language"/>
                        <field name="file" filename="filename" widget="binary"/>
                        <field name="filename" invisible="1"/>
                        <field name="file_type" readonly="1"/>
                        <field name="generate_embeddings"/>
                    </group>
                    <div class="alert alert-info" role="alert">
                        <p><strong>Note:</strong> Supported file formats are PDF, DOCX, and TXT.</p>
                        <p>For PDF files, text will be extracted automatically. If the PDF is scanned, OCR processing may be required.</p>
                    </div>
                </sheet>
                <footer>
                    <button name="action_upload_document" string="Upload" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_upload_document_wizard" model="ir.actions.act_window">
        <field name="name">Upload Document</field>
        <field name="res_model">upload.document.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="context">{'create': True, 'default_language': 'en'}</field>
        <field name="binding_model_id" eval="False"/>
        <field name="binding_type">action</field>
        <field name="binding_view_types">form,tree</field>
    </record>


</odoo>
