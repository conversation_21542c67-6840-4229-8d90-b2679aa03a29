from odoo import models, fields, api, _
from odoo.exceptions import UserError
import base64
import logging
import os
import tempfile

_logger = logging.getLogger(__name__)

class UploadDocumentWizard(models.TransientModel):
    _name = 'upload.document.wizard'
    _description = 'Upload Document Wizard'

    name = fields.Char(string='Document Name', required=True)

    # Legacy field for backward compatibility
    document_type = fields.Selection([
        ('act', 'Act'),
        ('judgment', 'Judgment'),
        ('regulation', 'Regulation'),
        ('land_law', 'Land Law'),
        ('civil_law', 'Civil Law')
    ], string='Document Type (Legacy)')

    # New document type fields
    law_type_id = fields.Many2one('law.type', string='Law Type', required=True)
    category_id = fields.Many2one('document.category', string='Document Category', required=True)
    document_type_ids = fields.Many2many('document.type', string='Document Types', compute='_compute_document_type_ids', store=True)

    language = fields.Selection([
        ('en', 'English'),
        ('gu', 'Gujarati')
    ], string='Language', required=True, default='en')

    file = fields.Binary(string='File', required=True, attachment=False)
    filename = fields.Char(string='Filename')
    file_type = fields.Selection([
        ('pdf', 'PDF'),
        ('docx', 'DOCX'),
        ('txt', 'TXT'),
        ('other', 'Other')
    ], string='File Type', compute='_compute_file_type', store=True)

    generate_embeddings = fields.Boolean(string='Generate Embeddings', default=True,
                                        help='Generate embeddings for the document after upload')

    @api.depends('filename')
    def _compute_file_type(self):
        for record in self:
            if not record.filename:
                record.file_type = 'other'
                continue

            filename_lower = record.filename.lower()
            if filename_lower.endswith('.pdf'):
                record.file_type = 'pdf'
            elif filename_lower.endswith('.docx'):
                record.file_type = 'docx'
            elif filename_lower.endswith('.txt'):
                record.file_type = 'txt'
            else:
                record.file_type = 'other'

    @api.depends('law_type_id', 'category_id')
    def _compute_document_type_ids(self):
        """Compute document_type_ids based on law_type_id and category_id."""
        for record in self:
            if record.law_type_id and record.category_id:
                # Find the document type that matches the selected law type and category
                document_type = self.env['document.type'].search([
                    ('law_type_id', '=', record.law_type_id.id),
                    ('category_id', '=', record.category_id.id),
                    ('active', '=', True)
                ], limit=1)

                if document_type:
                    record.document_type_ids = [(6, 0, [document_type.id])]
                else:
                    record.document_type_ids = [(5, 0, 0)]
            else:
                record.document_type_ids = [(5, 0, 0)]

    @api.onchange('law_type_id', 'category_id')
    def _onchange_law_type_category(self):
        """Update legacy document_type field for backward compatibility."""
        if self.law_type_id and self.category_id:
            # Map to legacy document_type
            if self.law_type_id.code == 'land_law':
                self.document_type = 'land_law'
            elif self.law_type_id.code == 'civil_law':
                self.document_type = 'civil_law'
            elif self.category_id.code == 'act':
                self.document_type = 'act'
            elif self.category_id.code == 'judgment':
                self.document_type = 'judgment'
            elif self.category_id.code == 'regulation':
                self.document_type = 'regulation'

    def action_upload_document(self):
        """Upload the document and create a law.document record."""
        self.ensure_one()

        if not self.file:
            raise UserError(_('Please select a file to upload.'))

        # Get file content
        file_content = base64.b64decode(self.file)

        # Process the file based on its type
        try:
            if self.file_type == 'pdf':
                content = self._extract_text_from_pdf(file_content)
            elif self.file_type == 'docx':
                content = self._extract_text_from_docx(file_content)
            elif self.file_type == 'txt':
                content = file_content.decode('utf-8', errors='replace')
            else:
                raise UserError(_('Unsupported file type. Please upload a PDF, DOCX, or TXT file.'))
        except Exception as e:
            _logger.error(f"Error extracting text from file: {str(e)}")
            raise UserError(_('Error extracting text from file: %s') % str(e))

        # Create the document
        LawDocument = self.env['law.document']
        document = LawDocument.create({
            'name': self.name,
            'document_type': self.document_type,  # Legacy field
            'law_type_id': self.law_type_id.id,
            'category_id': self.category_id.id,
            'document_type_ids': [(6, 0, self.document_type_ids.ids)] if self.document_type_ids else False,
            'content': content,
            'language': self.language,
            'original_file': self.file,
            'original_filename': self.filename,
            'file_type': self.file_type,
        })

        # Generate embeddings if requested
        if self.generate_embeddings:
            document.generate_embeddings()

        # Show success notification
        self.env['bus.bus']._sendone(self.env.user.partner_id, 'simple_notification', {
            'title': _('Document Uploaded'),
            'message': _('Document "%s" has been uploaded successfully.') % self.name,
            'sticky': False,
            'warning': False,
        })

        # Redirect to the document form
        return {
            'type': 'ir.actions.act_window',
            'name': _('Document'),
            'res_model': 'law.document',
            'view_mode': 'form',
            'res_id': document.id,
            'target': 'current',
        }

    def _extract_text_from_pdf(self, file_content):
        """Extract text from a PDF file using Odoo's built-in PDF tools."""
        try:
            # Use Odoo's built-in PDF tools (PyPDF2 based)
            from odoo.tools.pdf import OdooPdfFileReader
            import io

            # Create a temporary file to store the PDF
            with io.BytesIO(file_content) as pdf_stream:
                reader = OdooPdfFileReader(pdf_stream, strict=False)

                # Extract text from each page
                text = ""
                for page_num in range(reader.getNumPages()):
                    page = reader.getPage(page_num)
                    if '/Contents' in page:
                        try:
                            page_text = page.extractText()
                            text += page_text + "\n\n"
                        except Exception as e:
                            _logger.warning(f"Error extracting text from page {page_num}: {str(e)}")

                if not text.strip():
                    # If no text was extracted, the PDF might be scanned
                    _logger.warning("No text extracted from PDF. It might be a scanned document.")
                    text = f"[No extractable text found in PDF: {self.filename}]"

                return text
        except ImportError:
            _logger.error("Failed to import PDF tools. Make sure the required libraries are installed.")
            raise UserError(_("PDF processing tools are not available. Please contact your system administrator."))
        except Exception as e:
            _logger.error(f"Error extracting text from PDF: {str(e)}")
            raise UserError(_("Error extracting text from PDF: %s") % str(e))

    def _extract_text_from_docx(self, file_content):
        """Extract text from a DOCX file."""
        try:
            import docx
            import io

            # Create a temporary file to store the DOCX
            with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # Open the document and extract text
                doc = docx.Document(temp_file_path)
                text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
                return text
            finally:
                # Clean up the temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
        except ImportError:
            _logger.error("Failed to import python-docx. Make sure the library is installed.")
            raise UserError(_("DOCX processing tools are not available. Please contact your system administrator."))
        except Exception as e:
            _logger.error(f"Error extracting text from DOCX: {str(e)}")
            raise UserError(_("Error extracting text from DOCX: %s") % str(e))
