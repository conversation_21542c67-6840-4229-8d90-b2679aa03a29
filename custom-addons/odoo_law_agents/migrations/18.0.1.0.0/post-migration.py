# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

def migrate(cr, version):
    """
    This post-migration script handles the migration from Odoo 17.0 to Odoo 18.0.
    It performs necessary database changes after the module is updated.

    :param cr: database cursor
    :param version: module version from which we're upgrading
    """
    if not version:
        return

    # Log the migration start
    cr.execute("INSERT INTO ir_logging(create_date, create_uid, name, level, dbname, message) VALUES(now(), 1, 'odoo_law_agents', 'info', current_database(), 'Starting post-migration from 17.0 to 18.0')")

    # Update module description in ir_module_module to reflect Odoo 18 compatibility
    cr.execute("""
        UPDATE ir_module_module
        SET summary = 'AI-powered legal assistance using LangGraph and Odoo LLM',
            description = 'This module provides an intelligent legal assistant system for Odoo 18 that combines:\n- LangGraph-based agent architecture with visualization\n- Odoo\'s built-in LLM capabilities\n- Document processing and embedding\n- Multilingual support (English, Gujarati, and Hindi)\n- Legal document search and analysis\n- Agent workflow visualization and tracing'
        WHERE name = 'odoo_law_agents'
    """)

    # Check if law_agent table exists and has records
    cr.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'law_agent')")
    if cr.fetchone()[0]:
        # Check if there are any agents with state = 'draft'
        cr.execute("SELECT COUNT(*) FROM law_agent WHERE state = 'draft'")
        draft_count = cr.fetchone()[0]

        if draft_count > 0:
            # Log the number of draft agents
            cr.execute("INSERT INTO ir_logging(create_date, create_uid, name, level, dbname, message) VALUES(now(), 1, 'odoo_law_agents', 'info', current_database(), 'Found %s draft agents')" % draft_count)

    # Update any view records that might need changes for Odoo 18 compatibility
    # No specific view updates needed beyond the XML file changes

    # Log the migration completion
    cr.execute("INSERT INTO ir_logging(create_date, create_uid, name, level, dbname, message) VALUES(now(), 1, 'odoo_law_agents', 'info', current_database(), 'Post-migration from 17.0 to 18.0 completed')")
