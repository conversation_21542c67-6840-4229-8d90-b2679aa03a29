# Migration from Odoo 17.0 to Odoo 18.0

This directory contains migration scripts for upgrading the odoo_law_agents module from Odoo 17.0 to Odoo 18.0.

## Changes

1. Updated module version from ********.0 to ********.0
2. Updated module description to mention Odoo 18 compatibility
3. Updated Python dependencies for Python 3.12 compatibility
4. Updated view attributes for Odoo 18 compatibility:
   - Replaced deprecated `statusbar_visible` with `options="{'clickable': '1', 'visible_states': [...]}"` in statusbar widgets
5. Created migration scripts for database updates

## Migration Process

The migration process is handled by two scripts:

1. `pre-migration.py`: Runs before the module is updated
2. `post-migration.py`: Runs after the module is updated

These scripts handle any necessary database changes to ensure a smooth migration from Odoo 17.0 to Odoo 18.0.
