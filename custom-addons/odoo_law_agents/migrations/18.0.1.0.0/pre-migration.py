# -*- coding: utf-8 -*-
# Part of Odoo. See LICENSE file for full copyright and licensing details.

def migrate(cr, version):
    """
    This pre-migration script handles the migration from Odoo 17.0 to Odoo 18.0.
    It performs necessary database changes before the module is updated.

    :param cr: database cursor
    :param version: module version from which we're upgrading
    """
    if not version:
        return

    # Log the migration start
    cr.execute("INSERT INTO ir_logging(create_date, create_uid, name, level, dbname, message) VALUES(now(), 1, 'odoo_law_agents', 'info', current_database(), 'Starting migration from 17.0 to 18.0')")

    # Check if the law_agent table exists
    cr.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'law_agent')")
    table_exists = cr.fetchone()[0]

    if table_exists:
        # Check if we need to update any fields for Odoo 18 compatibility
        # First, check if the columns exist before trying to modify them
        cr.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'law_agent'")
        columns = [row[0] for row in cr.fetchall()]

        # Update any field attributes if needed for Odoo 18 compatibility
        # No specific field changes needed for this module in Odoo 18

        # Log the table check completion
        cr.execute("INSERT INTO ir_logging(create_date, create_uid, name, level, dbname, message) VALUES(now(), 1, 'odoo_law_agents', 'info', current_database(), 'Checked law_agent table structure')")

    # Check if the law_document table exists
    cr.execute("SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'law_document')")
    doc_table_exists = cr.fetchone()[0]

    if doc_table_exists:
        # Check if we need to update any fields for Odoo 18 compatibility
        cr.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'law_document'")
        doc_columns = [row[0] for row in cr.fetchall()]

        # No specific field changes needed for this module in Odoo 18

        # Log the document table check completion
        cr.execute("INSERT INTO ir_logging(create_date, create_uid, name, level, dbname, message) VALUES(now(), 1, 'odoo_law_agents', 'info', current_database(), 'Checked law_document table structure')")

    # Log the migration completion
    cr.execute("INSERT INTO ir_logging(create_date, create_uid, name, level, dbname, message) VALUES(now(), 1, 'odoo_law_agents', 'info', current_database(), 'Pre-migration from 17.0 to 18.0 completed')")
