# Migration Scripts

This directory contains migration scripts for the Odoo Law Agents module.

## Version ********.0

### Pre-migration

- Changes the `name` field in `document_type` to use ir.sequence instead of compute
- Creates a new ir.sequence for document.type if it doesn't exist
- Updates existing document.type records with sequence-based names

## Version ********.0

### Pre-migration

- Adds the `graph_attachment_id` field to the `law_agent` table
- Adds a foreign key constraint to the `ir_attachment` table

### Post-migration

- Updates the README.rst file in the database

## Version ********.0

### Pre-migration

- Adds the `use_mock` field to the `law_agent` table
- Adds the `debug_mode` field to the `law_agent` table

### Post-migration

- Updates the README.rst file in the database

## Version ********.0

### Pre-migration

- Adds the `enable_tracing` field to the `law_agent` table
- Adds the `opik_api_key` field to the `law_agent` table
- Adds the `opik_project_name` field to the `law_agent` table
- Adds the `opik_workspace` field to the `law_agent` table

### Post-migration

- Updates the README.rst file in the database
