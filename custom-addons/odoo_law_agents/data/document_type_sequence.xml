<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Sequence for Document Type -->
        <record id="seq_document_type" model="ir.sequence">
            <field name="name">Document Type Sequence</field>
            <field name="code">document.type</field>
            <field name="prefix">DOC/%(year)s/</field>
            <field name="padding">4</field>
            <field name="number_next">1</field>
            <field name="number_increment">1</field>
            <field name="implementation">standard</field>
            <field name="company_id" eval="False"/>
        </record>
    </data>
</odoo>
