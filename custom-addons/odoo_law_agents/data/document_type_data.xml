<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Law Types -->
        <record id="law_type_land_law" model="law.type">
            <field name="name">Land Law</field>
            <field name="code">land_law</field>
            <field name="sequence">10</field>
        </record>
        
        <record id="law_type_civil_law" model="law.type">
            <field name="name">Civil Law</field>
            <field name="code">civil_law</field>
            <field name="sequence">20</field>
        </record>
        
        <!-- Document Categories -->
        <record id="document_category_act" model="document.category">
            <field name="name">Act</field>
            <field name="code">act</field>
            <field name="sequence">10</field>
        </record>
        
        <record id="document_category_judgment" model="document.category">
            <field name="name">Judgment</field>
            <field name="code">judgment</field>
            <field name="sequence">20</field>
        </record>
        
        <record id="document_category_regulation" model="document.category">
            <field name="name">Regulation</field>
            <field name="code">regulation</field>
            <field name="sequence">30</field>
        </record>
        
        <!-- Document Types (Combinations) -->
        <record id="document_type_land_law_act" model="document.type">
            <field name="name">Land Law Act Category Type</field>
            <field name="law_type_id" ref="odoo_law_agents.law_type_land_law"/>
            <field name="category_id" ref="odoo_law_agents.document_category_act"/>
            <field name="sequence">10</field>
        </record>
        
        <record id="document_type_land_law_judgment" model="document.type">
            <field name="name">Land Law Judgment Category Type</field>
            <field name="law_type_id" ref="odoo_law_agents.law_type_land_law"/>
            <field name="category_id" ref="odoo_law_agents.document_category_judgment"/>
            <field name="sequence">20</field>
        </record>
        
        <record id="document_type_land_law_regulation" model="document.type">
            <field name="name">Land Law regulation Category Type</field>
            <field name="law_type_id" ref="odoo_law_agents.law_type_land_law"/>
            <field name="category_id" ref="odoo_law_agents.document_category_regulation"/>
            <field name="sequence">30</field>
        </record>
        
        <record id="document_type_civil_law_act" model="document.type">
            <field name="name">Civil Law Act Category Type</field>
            <field name="law_type_id" ref="odoo_law_agents.law_type_civil_law"/>
            <field name="category_id" ref="odoo_law_agents.document_category_act"/>
            <field name="sequence">40</field>
        </record>
        
        <record id="document_type_civil_law_judgment" model="document.type">
            <field name="name">Civil Law Judgment Category Type</field>
            <field name="law_type_id" ref="odoo_law_agents.law_type_civil_law"/>
            <field name="category_id" ref="odoo_law_agents.document_category_judgment"/>
            <field name="sequence">50</field>
        </record>
        
        <record id="document_type_civil_law_regulation" model="document.type">
            <field name="name">Civil Law Regulation Category Type</field>
            <field name="law_type_id" ref="odoo_law_agents.law_type_civil_law"/>
            <field name="category_id" ref="odoo_law_agents.document_category_regulation"/>
            <field name="sequence">60</field>
        </record>
    </data>
</odoo>
