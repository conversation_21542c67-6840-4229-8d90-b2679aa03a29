# Odoo Law Agents Module - Project Scope

## Overview
This module integrates LangGraph-based law agents into Odoo 18, providing a structured framework for legal document processing and analysis. The system implements custom tools including DuckDuckGo Search API integration and a specialized RAG (Retrieval-Augmented Generation) system for handling multilingual legal documents (English, Gujarati, and Hindi), while leveraging Odoo's standard LLM capabilities. The system includes a comprehensive case analysis agent that can process complete legal cases and generate detailed analysis reports.

## Core Components

### 1. LangGraph Agent Architecture
- Integration of LangGraph-generated agents (from build.langchain.com)
- State management for agent conversations
- Custom agent implementations for legal document processing
- Multilingual support for English, Gujarati, and Hindi content
- Tracing and monitoring using Opik for debugging and performance analysis
- Hierarchical agent structure with main agent and specialized sub-agents
- Task delegation between agents for complex legal analysis

### 2. Law Agent Types
- Gujarat Law Agent: Primary entry point for legal queries
- Civil Law Agent: Specialized for civil law matters
- Land Law Agent: Focused on land-related legal matters
- Retrieval Tools: Document search and analysis capabilities
- Case Analysis Agent: Comprehensive legal case analysis and reporting

### 3. Case Analysis System ✅
- Complete legal case processing in multiple languages (English, Gujarati, Hindi) ✅
- Hierarchical agent structure with main agent and specialized sub-agents ✅
- Task delegation for different aspects of case analysis ✅
- Integration with document embedding system for relevant precedent retrieval ✅
- Online search integration for legal research ✅
- Structured case analysis with sections for: ✅
  - Case background and parties involved ✅
  - Factual context and timeline ✅
  - Key legal issues identification ✅
  - Evidence analysis ✅
  - Applicable legal principles ✅
  - Precedent case comparison ✅
  - Legal reasoning and analysis ✅
  - Potential judgment prediction ✅
- Support for property disputes, inheritance cases, and other civil matters ✅
- Multilingual case report generation ✅
- PDF report generation using Odoo's QWeb reporting system ✅
- Fallback to sophisticated mock implementation when LangGraph is unavailable ✅
- Reset functionality to rerun analysis as needed ✅
- LangGraph workflow visualization with proper node naming ✅
- Optimized node structure to avoid state key conflicts ✅

### 4. Odoo LLM Integration
- Integration with Odoo's standard LLM provider
- Endpoint configuration: DEFAULT_OLG_ENDPOINT = "https://olg.api.odoo.com"
- Implementation details:
  ```python
  from odoo.addons.web_editor.tools import iap_tools

  # LLM request configuration
  IrConfigParameter = self.env["ir.config_parameter"].sudo()
  olg_api_endpoint = IrConfigParameter.get_param(
      "web_editor.olg_api_endpoint",
      DEFAULT_OLG_ENDPOINT
  )
  database_uuid = IrConfigParameter.get_param("database.uuid")

  # LLM API call
  response = iap_tools.iap_jsonrpc(
      olg_api_endpoint + "/api/olg/1/chat",
      params={
          "prompt": prompt,
          "conversation_history": [],
          "database_id": database_uuid,
      },
      timeout=30,
  )
  ```

### 5. Document Processing & Embedding
- Sentence Transformer implementation using all-MiniLM-L6-v2
- Bilingual embedding support for English and Gujarati
- Vector similarity search for relevant legal documents
- Document chunking and preprocessing
- Automatic embedding generation during document loading
- User-friendly document loading with progress feedback
- Visual indicators for documents with/without embeddings
- Subprocess-based embedding generation to avoid thread pool issues
- Robust fallback mechanisms for embedding generation failures
- Detailed logging and error handling for embedding processes
- Helper script architecture for isolated embedding generation
- Thread-safe embedding generation with resource constraints
- Integration with LangGraph agent workflow
- Context-aware document retrieval based on query type
- Combination of document embeddings with online search results
- Enhanced response generation with multiple context sources
- PDF, DOCX, and TXT file upload and processing
- Original document storage with binary fields
- Document upload wizard for individual file processing
- Automatic text extraction from various file formats
- Integration with Odoo's built-in PDF tools
- Folder-based PDF processing for batch operations
- Implementation details:
  ```python
  # Direct approach (fallback)
  from sentence_transformers import SentenceTransformer
  model = SentenceTransformer("all-MiniLM-L6-v2")
  embedding = model.encode("Your text here")

  # Subprocess approach (primary)
  import subprocess, json
  process = subprocess.Popen(
      [sys.executable, "embedding_helper.py"],
      stdin=subprocess.PIPE,
      stdout=subprocess.PIPE,
      text=True
  )
  stdout, _ = process.communicate(json.dumps({"text": "Your text here"}))
  result = json.loads(stdout)
  embedding = np.array(result["embedding"])

  # PDF processing using Odoo's built-in tools
  from odoo.tools.pdf import OdooPdfFileReader
  import io

  with io.BytesIO(pdf_content) as pdf_stream:
      reader = OdooPdfFileReader(pdf_stream, strict=False)
      text = ""
      for page_num in range(reader.getNumPages()):
          page = reader.getPage(page_num)
          if '/Contents' in page:
              text += page.extractText() + "\n\n"
  ```

### 6. Search and Retrieval Tools
- DuckDuckGo Search API integration for web-based legal research
- Custom RAG implementation for document retrieval
- Hybrid search combining embedding similarity and keyword matching
- Bilingual search capabilities
- Integration with LangGraph agent workflow
- Context-aware search based on query type
- Combination of document embeddings with web search results
- Enhanced response generation with multiple context sources
- Implementation details:
  ```python
  # Search for similar documents
  similar_docs = embedding_service.search_similar(query, limit=5, min_score=0.3)

  # Perform web search
  search_results = ddg.search(legal_query)
  news_results = ddg.news_search(legal_query, max_age=30)

  # Combine results for context generation
  context = rag_processor.prepare_context(query, documents, search_results)
  ```

### 7. LangGraph Agent Implementation
- Multi-agent workflow with specialized agents
- Gujarat Law Agent for initial query processing
- Civil Law Agent for civil law queries
- Land Law Agent for land law queries
- Retrieval tools for document and web search
- Environment handling for database access
- State management for agent communication
- Error handling and fallback mechanisms
- Response generation with context from multiple sources
- Implementation details:
  ```python
  # Define the agent workflow
  graph = StateGraph(LawAgentState)

  # Add nodes
  graph.add_node("Gujarat Law Agent", Gujarat_Law_Agent)
  graph.add_node("Civil Law Agent", Civil_Law_Agent)
  graph.add_node("Land Law Agent", Land_Law_Agent)
  graph.add_node("Retrival tools", Retrival_tools)
  graph.add_node("Web Search", Web_Search)
  graph.add_node("Get Suitable PDF Results", Get_Suitable_PDF_Results)
  graph.add_node("Generate Response", Generate_Response)

  # Add conditional edges
  graph.add_conditional_edges(
      "Gujarat Law Agent",
      Check_for_Civil_Land,
      {
          "Civil Law Agent": "Civil Law Agent",
          "Land Law Agent": "Land Law Agent",
          "Retrival tools": "Retrival tools",
      },
  )
  ```

### 8. Integration Points
- Connection with Odoo's document management
- Custom embedding and similarity search system
- Workflow automation for legal processes
- Integration with Odoo's IAP (In-App Purchase) for LLM credits

## Technical Requirements

### UI Guidelines
- Use modern Odoo view attributes instead of deprecated `attrs` and `states`
- Use direct attribute modifiers (`required`, `readonly`, `invisible`) with domain expressions
- For statusbar, use `options="{'clickable': '1', 'visible_states': ['state1', 'state2']}"` instead of `statusbar_visible`
- For buttons, use `invisible="state != 'draft'"` instead of `states="draft"`
- Follow Odoo 18 UI design patterns and guidelines
- Ensure responsive design for all views
- Implement proper field validation and error handling
- Use tree view decorations for visual status indicators (e.g., `decoration-success="has_embeddings"`)
- Provide clear user feedback for long-running operations
- Use sticky notifications for important messages

### Dependencies
- Python packages:
  - langgraph
  - pydantic
  - typing-extensions
  - sentence-transformers
  - duckduckgo-search (API client)
  - opik (for LangGraph tracing and monitoring)
  - python-docx (optional, for DOCX file processing)
- Odoo modules:
  - web_editor (for LLM integration and PDF tools)
  - mail (for chatter integration)
  - document_management (optional)

### Implementation Standards
- Follow Odoo 18 coding guidelines
- Implement proper security measures
- Use type hints and documentation
- Follow PEP 8 style guide
- Use Odoo's built-in tools when available (e.g., PDF tools)
- Use ir.sequence for generating sequential identifiers instead of computed fields
- Implement proper error handling and user feedback
- Use binary fields with attachment=True for file storage
- Provide clear progress indicators for long-running operations

### Testing Framework
- Use Odoo's standard test framework
- Implement TransactionCase tests for database operations
- Use test tags for proper test execution timing
- Create comprehensive test coverage for all components
- Implement test fixtures and data generation
- Test normal usage, edge cases, and error conditions
- Follow Odoo's testing guidelines and best practices

#### Test Structure
- **Embedding Tests**: Test embedding generation, similar search, subprocess approach
- **Document Loading Tests**: Test document loading, specific document types, automatic embedding
- **File Processing Tests**: Test PDF, DOCX, and TXT file processing, text extraction
- **RAG System Tests**: Test context retrieval, document search, bilingual support
- **Test Tags**: Use `post_install` and `-at_install` for proper execution timing
- **Test Data**: Create test data within tests for repeatability and reliability
- **Binary Field Tests**: Test file upload, storage, and retrieval

### Module Structure
- Maintain separation between generated code and Odoo integration
- Use proper inheritance patterns
- Implement clean interfaces between components

## Limitations and Constraints
- Handle rate limiting for DuckDuckGo Search API
- Manage embedding computation resources
- Consider memory usage for large document collections
- Handle bilingual text processing challenges
- Monitor LLM credit usage through Odoo IAP
- Handle large PDF files efficiently
- Consider OCR requirements for scanned documents
- Manage storage space for binary file attachments
- Handle different PDF formats and structures

## Security Considerations
- Access control for legal documents
- API key management for DuckDuckGo Search (if required)
- Data privacy compliance
- Audit logging for all operations
- Secure handling of IAP tokens

## Performance Considerations
- Efficient embedding computation and storage
- Optimized vector similarity search
- Caching strategies for frequently accessed documents
- Batch processing for large document sets
- Batch embedding generation for multiple documents at once
- Chunked processing for very large documents without character limits
- Memory-efficient embedding generation for large PDF files
- LLM response caching when appropriate
- Efficient PDF text extraction for large files
- Optimized binary field storage and retrieval
- Background processing for document uploads
- Progress tracking for long-running operations
