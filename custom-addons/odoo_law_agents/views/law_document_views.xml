<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- List View -->
    <record id="view_law_document_list" model="ir.ui.view">
        <field name="name">law.document.list</field>
        <field name="model">law.document</field>
        <field name="arch" type="xml">
            <list decoration-success="has_embeddings" decoration-danger="not has_embeddings">
                <field name="name"/>
                <field name="document_type" invisible="1"/>
                <field name="law_type_id"/>
                <field name="category_id"/>
                <field name="document_type_ids" widget="many2many_tags"/>
                <field name="language"/>
                <field name="file_type"/>
                <field name="embedding_count"/>
                <field name="has_embeddings"/>
                <button name="action_generate_embeddings" string="Generate" type="object" icon="fa-cogs" invisible="has_embeddings"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_law_document_form" model="ir.ui.view">
        <field name="name">law.document.form</field>
        <field name="model">law.document</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_generate_embeddings" string="Generate Embeddings" type="object" class="oe_highlight" invisible="has_embeddings"/>
                    <button name="action_generate_embeddings" string="Regenerate Embeddings" type="object" invisible="not has_embeddings"/>
                    <button name="action_view_embeddings" string="View Embeddings" type="object" invisible="embedding_count == 0"/>
                </header>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="action_view_embeddings" type="object" class="oe_stat_button" icon="fa-list-ol" invisible="embedding_count == 0">
                            <field name="embedding_count" widget="statinfo" string="Embeddings"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Document Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="document_type" invisible="1"/>
                            <field name="law_type_id"/>
                            <field name="category_id"/>
                            <field name="document_type_ids" widget="many2many_tags"/>
                            <field name="language"/>
                            <field name="has_embeddings" readonly="1"/>
                        </group>
                        <group>
                            <field name="file_type"/>
                            <field name="original_filename" readonly="1" invisible="original_filename == False"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Content">
                            <field name="content" widget="html"/>
                        </page>
                        <page string="Original File" invisible="original_file == False">
                            <group>
                                <field name="original_file" filename="original_filename" widget="binary" readonly="1"/>
                            </group>
                        </page>
                        <page string="Embeddings">
                            <field name="embedding_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="chunk_index"/>
                                    <field name="embedding_model"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_law_document_search" model="ir.ui.view">
        <field name="name">law.document.search</field>
        <field name="model">law.document</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="document_type" invisible="1"/>
                <field name="law_type_id"/>
                <field name="category_id"/>
                <field name="document_type_ids"/>
                <field name="language"/>
                <separator/>
                <filter string="Land Law" name="land_law" domain="[('law_type_id.code', '=', 'land_law')]"/>
                <filter string="Civil Law" name="civil_law" domain="[('law_type_id.code', '=', 'civil_law')]"/>
                <separator/>
                <filter string="Acts" name="acts" domain="[('category_id.code', '=', 'act')]"/>
                <filter string="Judgments" name="judgments" domain="[('category_id.code', '=', 'judgment')]"/>
                <filter string="Regulations" name="regulations" domain="[('category_id.code', '=', 'regulation')]"/>
                <separator/>
                <filter string="With Embeddings" name="has_embeddings" domain="[('has_embeddings', '=', True)]"/>
                <filter string="Without Embeddings" name="no_embeddings" domain="[('has_embeddings', '=', False)]"/>
                <separator/>
                <filter string="PDF Files" name="pdf_files" domain="[('file_type', '=', 'pdf')]"/>
                <filter string="DOCX Files" name="docx_files" domain="[('file_type', '=', 'docx')]"/>
                <filter string="TXT Files" name="txt_files" domain="[('file_type', '=', 'txt')]"/>
                <group expand="0" string="Group By">
                    <filter string="Law Type" name="group_by_law_type" context="{'group_by': 'law_type_id'}"/>
                    <filter string="Document Category" name="group_by_category" context="{'group_by': 'category_id'}"/>
                    <filter string="Language" name="group_by_language" context="{'group_by': 'language'}"/>
                    <filter string="File Type" name="group_by_file_type" context="{'group_by': 'file_type'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Server Action for Batch Embedding Generation -->
    <record id="action_generate_embeddings_batch" model="ir.actions.server">
        <field name="name">Generate Embeddings</field>
        <field name="model_id" ref="model_law_document"/>
        <field name="binding_model_id" ref="model_law_document"/>
        <field name="binding_view_types">list</field>
        <field name="state">code</field>
        <field name="code">action = records.action_generate_embeddings_batch()</field>
    </record>

    <!-- Action -->
    <record id="action_law_documents" model="ir.actions.act_window">
        <field name="name">Legal Documents</field>
        <field name="res_model">law.document</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_law_document_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first legal document!
            </p>
            <p>
                Legal documents can be processed and embedded for AI-powered search and analysis.
            </p>
        </field>
    </record>
</odoo>
