<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Law Case list View -->
    <record id="view_law_case_list" model="ir.ui.view">
        <field name="name">law.case.list</field>
        <field name="model">law.case</field>
        <field name="arch" type="xml">
            <list string="Legal Cases" decoration-info="state == 'draft'" decoration-success="state == 'completed'" decoration-danger="state == 'failed'" decoration-warning="state == 'processing'">
                <field name="name"/>
                <field name="case_number"/>
                <field name="case_type"/>
                <field name="language"/>
                <field name="state"/>
                <field name="create_date"/>
                <field name="user_id"/>
            </list>
        </field>
    </record>

    <!-- Law Case Form View -->
    <record id="view_law_case_form" model="ir.ui.view">
        <field name="name">law.case.form</field>
        <field name="model">law.case</field>
        <field name="arch" type="xml">
            <form string="Legal Case">
                <header>
                    <button name="action_analyze_case" string="Analyze Case" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                    <button name="action_reset" string="Reset to Draft" type="object" class="btn-secondary" invisible="state == 'draft'" help="Reset the case to draft state to run the analysis again"/>
                    <button name="action_print_analysis" string="Print Analysis" type="object" class="btn-info" invisible="state != 'completed'" help="Print the case analysis as a PDF report"/>
                    <button name="action_visualize_workflow" string="Visualize Workflow" type="object" class="btn-secondary" help="Visualize the LangGraph workflow"/>
                    <field name="state" widget="statusbar" options="{'clickable': '1', 'visible_states': ['draft', 'processing', 'completed']}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Case Title"/>
                        </h1>
                        <h2>
                            <field name="case_number" placeholder="Case Number"/>
                        </h2>
                    </div>
                    <group>
                        <group>
                            <field name="case_type"/>
                            <field name="language"/>
                            <field name="user_id"/>
                        </group>
                        <group>
                            <field name="processing_start" readonly="1"/>
                            <field name="processing_end" readonly="1"/>
                            <field name="duration" widget="float_time" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Case Text" name="case_text">
                            <field name="case_text" placeholder="Enter the full text of the case to be analyzed..." nolabel="1" widget="text_markdown"/>
                        </page>
                        <page string="Analysis" name="analysis" invisible="state != 'completed'">
                            <group>
                                <field name="full_analysis" nolabel="1" readonly="1" widget="html"/>
                            </group>
                        </page>
                        <page string="Analysis Sections" name="analysis_sections" invisible="state != 'completed'">
                            <group>
                                <separator string="Background Analysis"/>
                                <field name="background_analysis" nolabel="1" readonly="1" widget="html"/>
                                <separator string="Legal Issues"/>
                                <field name="legal_issues" nolabel="1" readonly="1" widget="html"/>
                                <separator string="Evidence Analysis"/>
                                <field name="evidence_analysis" nolabel="1" readonly="1" widget="html"/>
                                <separator string="Legal Principles"/>
                                <field name="legal_principles" nolabel="1" readonly="1" widget="html"/>
                                <separator string="Judgment Prediction"/>
                                <field name="judgment_prediction" nolabel="1" readonly="1" widget="html"/>
                            </group>
                        </page>
                        <page string="Sources" name="sources">
                            <field name="source_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="source_type"/>
                                    <field name="document_id"/>
                                    <field name="url"/>
                                    <field name="source"/>
                                    <field name="relevance"/>
                                    <button name="action_open_source" string="Open" type="object" icon="fa-external-link"/>
                                </list>
                                <form>
                                    <group>
                                        <group>
                                            <field name="name"/>
                                            <field name="source_type"/>
                                            <field name="document_id" invisible="source_type != 'document'"/>
                                            <field name="url" invisible="source_type != 'web'"/>
                                            <field name="source" invisible="source_type != 'web'"/>
                                            <field name="relevance"/>
                                        </group>
                                    </group>
                                    <notebook>
                                        <page string="Excerpt" name="excerpt">
                                            <field name="excerpt" nolabel="1" widget="html"/>
                                        </page>
                                    </notebook>
                                </form>
                            </field>
                        </page>
                        <page string="Configuration" name="configuration">
                            <group>
                                <group string="Analysis Options">
                                    <field name="use_online_search"/>
                                    <field name="use_document_embeddings"/>
                                    <field name="use_mock"/>
                                    <field name="enable_tracing"/>
                                </group>

                            </group>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Law Case Search View -->
    <record id="view_law_case_search" model="ir.ui.view">
        <field name="name">law.case.search</field>
        <field name="model">law.case</field>
        <field name="arch" type="xml">
            <search string="Search Legal Cases">
                <field name="name"/>
                <field name="case_number"/>
                <field name="case_text"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Processing" name="processing" domain="[('state', '=', 'processing')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                <separator/>
                <filter string="Property Disputes" name="property" domain="[('case_type', '=', 'property')]"/>
                <filter string="Inheritance Cases" name="inheritance" domain="[('case_type', '=', 'inheritance')]"/>
                <filter string="Contract Disputes" name="contract" domain="[('case_type', '=', 'contract')]"/>
                <filter string="Family Law" name="family" domain="[('case_type', '=', 'family')]"/>
                <separator/>
                <filter string="English" name="english" domain="[('language', '=', 'en')]"/>
                <filter string="Gujarati" name="gujarati" domain="[('language', '=', 'gu')]"/>
                <filter string="Hindi" name="hindi" domain="[('language', '=', 'hi')]"/>
                <group expand="0" string="Group By">
                    <filter string="State" name="group_by_state" domain="[]" context="{'group_by': 'state'}"/>
                    <filter string="Case Type" name="group_by_case_type" domain="[]" context="{'group_by': 'case_type'}"/>
                    <filter string="Language" name="group_by_language" domain="[]" context="{'group_by': 'language'}"/>
                    <filter string="User" name="group_by_user" domain="[]" context="{'group_by': 'user_id'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Law Case Action -->
    <record id="action_law_case" model="ir.actions.act_window">
        <field name="name">Legal Cases</field>
        <field name="res_model">law.case</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_law_case_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first legal case for analysis
            </p>
            <p>
                Enter the details of a legal case to analyze it using our AI-powered legal analysis system.
            </p>
        </field>
    </record>

    <!-- Law Case Source Form View -->
    <record id="view_law_case_source_form" model="ir.ui.view">
        <field name="name">law.case.source.form</field>
        <field name="model">law.case.source</field>
        <field name="arch" type="xml">
            <form string="Case Source">
                <sheet>
                    <group>
                        <group>
                            <field name="name"/>
                            <field name="case_id"/>
                            <field name="source_type"/>
                            <field name="document_id" invisible="source_type != 'document'"/>
                            <field name="url" invisible="source_type != 'web'"/>
                            <field name="source" invisible="source_type != 'web'"/>
                            <field name="relevance"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Excerpt" name="excerpt">
                            <field name="excerpt" nolabel="1" widget="html"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Law Case Source list View -->
    <record id="view_law_case_source_list" model="ir.ui.view">
        <field name="name">law.case.source.list</field>
        <field name="model">law.case.source</field>
        <field name="arch" type="xml">
            <list string="Case Sources">
                <field name="name"/>
                <field name="case_id"/>
                <field name="source_type"/>
                <field name="document_id"/>
                <field name="url"/>
                <field name="source"/>
                <field name="relevance"/>
                <button name="action_open_source" string="Open" type="object" icon="fa-external-link"/>
            </list>
        </field>
    </record>

    <!-- Law Case Source Action -->
    <record id="action_law_case_source" model="ir.actions.act_window">
        <field name="name">Case Sources</field>
        <field name="res_model">law.case.source</field>
        <field name="view_mode">list,form</field>
    </record>

</odoo>
