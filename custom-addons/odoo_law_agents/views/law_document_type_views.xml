<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Law Type Views -->
    <record id="view_law_type_list" model="ir.ui.view">
        <field name="name">law.type.list</field>
        <field name="model">law.type</field>
        <field name="arch" type="xml">
            <list>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <record id="view_law_type_form" model="ir.ui.view">
        <field name="name">law.type.form</field>
        <field name="model">law.type</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Law Type Name"/>
                        </h1>
                    </div>
                    <group>
                        <field name="code"/>
                        <field name="sequence"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_law_type" model="ir.actions.act_window">
        <field name="name">Law Types</field>
        <field name="res_model">law.type</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first law type!
            </p>
            <p>
                Law types define the main categories of legal documents.
            </p>
        </field>
    </record>

    <!-- Document Category Views -->
    <record id="view_document_category_list" model="ir.ui.view">
        <field name="name">document.category.list</field>
        <field name="model">document.category</field>
        <field name="arch" type="xml">
            <list>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="code"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <record id="view_document_category_form" model="ir.ui.view">
        <field name="name">document.category.form</field>
        <field name="model">document.category</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Document Category Name"/>
                        </h1>
                    </div>
                    <group>
                        <field name="code"/>
                        <field name="sequence"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_document_category" model="ir.actions.act_window">
        <field name="name">Document Categories</field>
        <field name="res_model">document.category</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first document category!
            </p>
            <p>
                Document categories define the types of legal documents.
            </p>
        </field>
    </record>

    <!-- Document Type Views -->
    <record id="view_document_type_list" model="ir.ui.view">
        <field name="name">document.type.list</field>
        <field name="model">document.type</field>
        <field name="arch" type="xml">
            <list>
                <field name="sequence" widget="handle"/>
                <field name="name"/>
                <field name="display_name"/>
                <field name="law_type_id"/>
                <field name="category_id"/>
                <field name="code"/>
                <field name="active" invisible="1"/>
            </list>
        </field>
    </record>

    <record id="view_document_type_form" model="ir.ui.view">
        <field name="name">document.type.form</field>
        <field name="model">document.type</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_button_box" name="button_box">
                        <button name="toggle_active" type="object" class="oe_stat_button" icon="fa-archive">
                            <field name="active" widget="boolean_button" options="{'terminology': 'archive'}"/>
                        </button>
                    </div>
                    <div class="oe_title">
                        <h1>
                            <field name="name"/>
                        </h1>
                    </div>
                    <group>
                        <field name="law_type_id"/>
                        <field name="category_id"/>
                        <field name="code"/>
                        <field name="sequence"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <record id="action_document_type" model="ir.actions.act_window">
        <field name="name">Document Types</field>
        <field name="res_model">document.type</field>
        <field name="view_mode">list,form</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first document type!
            </p>
            <p>
                Document types combine law types and document categories.
            </p>
        </field>
    </record>

 </odoo>
