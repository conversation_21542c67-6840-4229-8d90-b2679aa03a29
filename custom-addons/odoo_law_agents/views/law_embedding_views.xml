<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- list View -->
    <record id="view_law_embedding_list" model="ir.ui.view">
        <field name="name">law.embedding.list</field>
        <field name="model">law.embedding</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="document_id"/>
                <field name="chunk_index"/>
                <field name="embedding_model"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_law_embedding_form" model="ir.ui.view">
        <field name="name">law.embedding.form</field>
        <field name="model">law.embedding</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="document_id"/>
                            <field name="chunk_index"/>
                            <field name="embedding_model"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company" readonly="1"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Chunk Text">
                            <field name="chunk_text" widget="html"/>
                        </page>
                    </notebook>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_law_embedding_search" model="ir.ui.view">
        <field name="name">law.embedding.search</field>
        <field name="model">law.embedding</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="document_id"/>
                <field name="embedding_model"/>
                <group expand="0" string="Group By">
                    <filter string="Document" name="group_by_document" context="{'group_by': 'document_id'}"/>
                    <filter string="Embedding Model" name="group_by_model" context="{'group_by': 'embedding_model'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_law_embeddings" model="ir.actions.act_window">
        <field name="name">Document Embeddings</field>
        <field name="res_model">law.embedding</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_law_embedding_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No document embeddings found
            </p>
            <p>
                Document embeddings are automatically generated when documents are processed.
            </p>
        </field>
    </record>
</odoo>
