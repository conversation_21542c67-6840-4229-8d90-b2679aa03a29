<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_law_agent_list" model="ir.ui.view">
        <field name="name">law.agent.list</field>
        <field name="model">law.agent</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="agent_type"/>
                <field name="state"/>
                <field name="use_mock" groups="base.group_system"/>
                <field name="debug_mode" groups="base.group_system"/>
            </list>
        </field>
    </record>

    <record id="view_law_agent_form" model="ir.ui.view">
        <field name="name">law.agent.form</field>
        <field name="model">law.agent</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <field name="state" widget="statusbar" options="{'clickable': '1', 'visible_states': ['draft', 'active', 'inactive']}"/>
                    <button name="generate_graph_visualization" string="Generate Graph Visualization" type="object" class="oe_highlight" groups="base.group_system"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Agent Name"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="agent_type"/>
                            <field name="active"/>
                        </group>
                        <group>
                            <field name="company_id" groups="base.group_multi_company"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Development Options" groups="base.group_system">
                            <group string="Testing and Debugging">
                                <field name="use_mock"/>
                                <field name="debug_mode"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <p><strong>Note:</strong> Mock mode returns predefined responses without calling the LLM API. This is useful for testing and development.</p>
                                <p>Debug mode enables additional logging and debugging information.</p>
                            </div>
                        </page>
                        <page string="Tracing Configuration" groups="base.group_system">
                            <group>
                                <field name="enable_tracing"/>
                                <field name="opik_api_key" password="True" required="enable_tracing"/>
                                <field name="opik_project_name" required="enable_tracing"/>
                                <field name="opik_workspace"/>
                            </group>
                            <div class="alert alert-info" role="alert">
                                <p><strong>Note:</strong> Opik tracing allows you to monitor and debug the LangGraph agent execution.</p>
                                <p>You need an Opik API key from <a href="https://www.comet.com/" target="_blank">Comet.com</a> to use this feature.</p>
                                <p>If no API key is provided here, the system will try to use the OPIK_API_KEY environment variable.</p>
                                <p>The workspace field should match your Comet.ml workspace name (e.g., 'username-gmail-com'). If not provided, the system will try to use the OPIK_WORKSPACE environment variable.</p>
                            </div>
                        </page>
                        <page string="Graph Visualization" groups="base.group_system">
                            <group>
                                <field name="graph_attachment_id" invisible="1"/>
                                <field name="graph_download_url" widget="url" text="Download Graph Visualization" invisible="graph_attachment_id == False"/>
                            </group>
                            <div class="alert alert-info" role="alert" invisible="graph_attachment_id != False">
                                <p><strong>Note:</strong> Click the "Generate Graph Visualization" button in the header to create a visual representation of the agent's workflow graph.</p>
                                <p>This will create a PNG image that you can download and use for documentation or reference.</p>
                            </div>
                            <div invisible="graph_attachment_id == False">
                                <field name="graph_attachment_id"/>
                            </div>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_law_agent_search" model="ir.ui.view">
        <field name="name">law.agent.search</field>
        <field name="model">law.agent</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="agent_type"/>
                <filter string="Gujarat Law" name="gujarat" domain="[('agent_type', '=', 'gujarat')]"/>
                <filter string="Civil Law" name="civil" domain="[('agent_type', '=', 'civil')]"/>
                <filter string="Land Law" name="land" domain="[('agent_type', '=', 'land')]"/>
                <filter string="Active" name="active" domain="[('active', '=', True)]"/>
                <filter string="Archived" name="inactive" domain="[('active', '=', False)]"/>
                <filter string="Mock Mode" name="mock" domain="[('use_mock', '=', True)]" groups="base.group_system"/>
                <filter string="Debug Mode" name="debug" domain="[('debug_mode', '=', True)]" groups="base.group_system"/>
                <group expand="0" string="Group By">
                    <filter string="Agent Type" name="group_by_type" context="{'group_by': 'agent_type'}"/>
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_law_agents" model="ir.actions.act_window">
        <field name="name">Law Agents</field>
        <field name="res_model">law.agent</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_law_agent_search"/>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first law agent!
            </p>
            <p>
                Law agents help process and analyze legal documents using AI.
            </p>
        </field>
    </record>
</odoo>
