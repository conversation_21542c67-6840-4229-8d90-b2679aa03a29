<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Chat Form View -->
    <record id="view_law_query_chat_form" model="ir.ui.view">
        <field name="name">law.query.chat.form</field>
        <field name="model">law.query</field>
        <field name="arch" type="xml">
            <form string="Legal Chat" create="true" edit="false" delete="false">
                <header>
                    <button name="process_query" string="Send" type="object" class="oe_highlight" invisible="state != 'draft'"/>
                    <button name="action_reset" string="New Query" type="object" invisible="state not in ('completed', 'failed')"/>
                    <field name="state" widget="statusbar" options="{'clickable': '1', 'visible_states': ['draft', 'processing', 'completed']}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Ask a legal question..." readonly="state != 'draft'"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="agent_id" readonly="state != 'draft'"/>
                            <field name="language" readonly="state != 'draft'"/>
                        </group>
                        <group>
                            <field name="user_id" readonly="1"/>
                            <field name="duration" widget="float_time" readonly="1" invisible="duration == 0"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Response" name="response">
                            <div class="alert alert-info" role="alert" invisible="state != 'processing'">
                                <p><i class="fa fa-spinner fa-spin"></i> Processing your query... This may take a moment.</p>
                            </div>
                            <div class="alert alert-danger" role="alert" invisible="state != 'failed'">
                                <p><i class="fa fa-exclamation-triangle"></i> Failed to process query.</p>
                            </div>
                            <field name="response" widget="html" readonly="1" invisible="state == 'draft'"/>
                        </page>
                        <page string="Sources" name="sources" invisible="not source_ids">
                            <field name="source_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="source_type"/>
                                    <field name="document_id"/>
                                    <field name="url"/>
                                    <field name="source"/>
                                    <field name="relevance" widget="percentage"/>
                                    <button name="action_open_source" string="Open" type="object" icon="fa-external-link"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Chat Action -->
    <record id="action_law_query_chat" model="ir.actions.act_window">
        <field name="name">Legal Chat</field>
        <field name="res_model">law.query</field>
        <field name="view_mode">form</field>
        <field name="view_id" ref="view_law_query_chat_form"/>
        <field name="target">current</field>
        <field name="context">{'default_state': 'draft'}</field>
    </record>


</odoo>
