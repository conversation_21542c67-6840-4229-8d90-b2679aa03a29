<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_law_query_list" model="ir.ui.view">
        <field name="name">law.query.list</field>
        <field name="model">law.query</field>
        <field name="arch" type="xml">
            <list decoration-info="state == 'draft'" decoration-warning="state == 'processing'" decoration-success="state == 'completed'" decoration-danger="state == 'failed'">
                <field name="name"/>
                <field name="user_id"/>
                <field name="agent_id"/>
                <field name="create_date"/>
                <field name="duration" widget="float_time"/>
                <field name="state"/>
            </list>
        </field>
    </record>

    <!-- Form View -->
    <record id="view_law_query_form" model="ir.ui.view">
        <field name="name">law.query.form</field>
        <field name="model">law.query</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="process_query" string="Process Query" type="object" class="oe_highlight" invisible="state not in ['draft', 'failed']"/>
                    <button name="action_send_acknowledgment" string="Send Acknowledgment" type="object" class="btn btn-primary"
                            invisible="state != 'completed' or acknowledgment_sent"/>
                    <button name="action_reset" string="Reset to Draft" type="object"
                            invisible="state == 'draft'"/>
                    <field name="state" widget="statusbar" options="{'clickable': '1', 'visible_states': ['draft', 'processing', 'completed', 'failed']}"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" placeholder="Enter your legal query here..."/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="user_id"/>
                            <field name="agent_id"/>
                        </group>
                        <group>
                            <field name="create_date" readonly="1"/>
                            <field name="duration" widget="float_time" readonly="1"/>
                            <field name="company_id" groups="base.group_multi_company"/>
                            <field name="language"/>
                            <field name="acknowledgment_sent" readonly="1" invisible="state != 'completed'"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Response">
                            <field name="response" widget="html" readonly="1"/>
                        </page>
                        <page string="Sources" invisible="not source_ids">
                            <field name="source_ids" readonly="1">
                                <list>
                                    <field name="name"/>
                                    <field name="source_type"/>
                                    <field name="url" widget="url" invisible="not url"/>
                                    <field name="source" invisible="not source"/>
                                    <field name="relevance" widget="percentage" invisible="relevance == 0"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_law_query_search" model="ir.ui.view">
        <field name="name">law.query.search</field>
        <field name="model">law.query</field>
        <field name="arch" type="xml">
            <search>
                <field name="name"/>
                <field name="user_id"/>
                <field name="agent_id"/>
                <filter string="My Queries" name="my_queries" domain="[('user_id', '=', uid)]"/>
                <filter string="Draft" name="draft" domain="[('state', '=', 'draft')]"/>
                <filter string="Processing" name="processing" domain="[('state', '=', 'processing')]"/>
                <filter string="Completed" name="completed" domain="[('state', '=', 'completed')]"/>
                <filter string="Failed" name="failed" domain="[('state', '=', 'failed')]"/>
                <group expand="0" string="Group By">
                    <filter string="Agent" name="group_by_agent" context="{'group_by': 'agent_id'}"/>
                    <filter string="User" name="group_by_user" context="{'group_by': 'user_id'}"/>
                    <filter string="Status" name="group_by_state" context="{'group_by': 'state'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_law_queries" model="ir.actions.act_window">
        <field name="name">Legal Queries</field>
        <field name="res_model">law.query</field>
        <field name="view_mode">list,form</field>
        <field name="search_view_id" ref="view_law_query_search"/>
        <field name="context">{'search_default_my_queries': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                Create your first legal query!
            </p>
            <p>
                Ask questions about legal documents and get AI-powered responses.
            </p>
        </field>
    </record>
</odoo>
