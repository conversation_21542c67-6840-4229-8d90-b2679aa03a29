# This YAML was auto-generated based on an architecture 
# designed in LangGraph Builder (https://build.langchain.com).
#
# The YAML was used by langgraph-gen (https://github.com/langchain-ai/langgraph-gen-py) 
# to generate a code stub for a LangGraph application that follows the architecture.
#
# langgraph-gen is an open source CLI tool that converts YAML specifications into LangGraph code stubs.
#
# The code stub generated from this YAML can be found in stub.py.
#
# A placeholder implementation for the generated stub can be found in implementation.py.

name: CustomAgent
nodes:
  - name: Gujarat Law Agent
  - name: Retrival tools
  - name: Get Suitable PDF Results
  - name: Civil Law Agent
  - name: Land Law Agent
  - name: Generate Response
edges:
  - from: __start__
    to: Gujarat Law Agent
  - from: Generate Response
    to: __end__
  - from: Civil Law Agent
    to: Generate Response
  - from: Land Law Agent
    to: Generate Response
  - from: Retrival tools
    condition: Query Retrival
    paths: [Get Suitable PDF Results]
  - from: Gujarat Law Agent
    condition: Check for Civil/Land
    paths: [Civil Law Agent, Land Law Agent, Retrival tools]