"""
Document processing service for Odoo Law Agents.
"""

import base64
import io
import logging
import os
import tempfile
from typing import Dict, Any, Optional, List, Tuple

_logger = logging.getLogger(__name__)

class DocumentProcessingService:
    """Service for processing various document types."""

    def __init__(self, env):
        """Initialize the document processing service.

        Args:
            env: The Odoo environment.
        """
        self.env = env

    def process_document(self, file_content: bytes, file_type: str, filename: str = None) -> str:
        """Process a document and extract its text content.

        Args:
            file_content: The binary content of the file.
            file_type: The type of the file ('pdf', 'docx', 'txt', etc.).
            filename: The name of the file (optional).

        Returns:
            The extracted text content.
        """
        try:
            if file_type == 'pdf':
                return self.extract_text_from_pdf(file_content)
            elif file_type == 'docx':
                return self.extract_text_from_docx(file_content)
            elif file_type == 'txt':
                return file_content.decode('utf-8', errors='replace')
            else:
                _logger.warning(f"Unsupported file type: {file_type}")
                return f"[Unsupported file type: {file_type}]"
        except Exception as e:
            _logger.error(f"Error processing document: {str(e)}")
            return f"[Error processing document: {str(e)}]"

    def extract_text_from_pdf(self, file_content: bytes) -> str:
        """Extract text from a PDF file using Odoo's built-in PDF tools (PyPDF2).

        Args:
            file_content: The binary content of the PDF file.

        Returns:
            The extracted text content.
        """
        try:
            # Use Odoo's built-in PDF tools (PyPDF2 based)
            _logger.info("Using PyPDF2 for text extraction.")
            from odoo.tools.pdf import OdooPdfFileReader

            with io.BytesIO(file_content) as pdf_stream:
                reader = OdooPdfFileReader(pdf_stream, strict=False)

                # Extract text from each page
                text = ""
                for page_num in range(reader.getNumPages()):
                    page = reader.getPage(page_num)
                    if '/Contents' in page:
                        try:
                            page_text = page.extractText()
                            text += page_text + "\n\n"
                        except Exception as e:
                            _logger.warning(f"Error extracting text from page {page_num}: {str(e)}")

                if not text.strip():
                    # If no text was extracted, the PDF might be scanned
                    _logger.warning("No text extracted from PDF. It might be a scanned document.")
                    text = "[No extractable text found in PDF. It might be a scanned document.]"
                else:
                    _logger.info("Successfully extracted text using PyPDF2.")

                return text
        except ImportError:
            _logger.error("Failed to import PDF tools. Make sure the required libraries are installed.")
            return "[PDF processing tools are not available.]"
        except Exception as e:
            _logger.error(f"Error extracting text from PDF: {str(e)}")
            return f"[Error extracting text from PDF: {str(e)}]"

    def extract_text_from_docx(self, file_content: bytes) -> str:
        """Extract text from a DOCX file.

        Args:
            file_content: The binary content of the DOCX file.

        Returns:
            The extracted text content.
        """
        try:
            import docx

            # Create a temporary file to store the DOCX
            with tempfile.NamedTemporaryFile(delete=False, suffix='.docx') as temp_file:
                temp_file.write(file_content)
                temp_file_path = temp_file.name

            try:
                # Open the document and extract text
                doc = docx.Document(temp_file_path)
                text = "\n".join([paragraph.text for paragraph in doc.paragraphs])
                return text
            finally:
                # Clean up the temporary file
                if os.path.exists(temp_file_path):
                    os.unlink(temp_file_path)
        except ImportError:
            _logger.error("Failed to import python-docx. Make sure the library is installed.")
            return "[DOCX processing tools are not available.]"
        except Exception as e:
            _logger.error(f"Error extracting text from DOCX: {str(e)}")
            return f"[Error extracting text from DOCX: {str(e)}]"

    def process_pdf_folder(self, folder_path: str, document_type: str, language: str) -> List[int]:
        """Process all PDF files in a folder and create law.document records.

        Args:
            folder_path: The path to the folder containing PDF files.
            document_type: The type of documents to create.
            language: The language of the documents.

        Returns:
            A list of created document IDs.
        """
        if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
            _logger.error(f"Folder not found: {folder_path}")
            return []

        document_ids = []
        LawDocument = self.env['law.document']

        # Get all PDF files in the folder
        pdf_files = [f for f in os.listdir(folder_path) if f.lower().endswith('.pdf')]

        if not pdf_files:
            _logger.warning(f"No PDF files found in folder: {folder_path}")
            return []

        for pdf_file in pdf_files:
            try:
                file_path = os.path.join(folder_path, pdf_file)

                # Read the file content
                with open(file_path, 'rb') as f:
                    file_content = f.read()

                # Extract text from the PDF
                text_content = self.extract_text_from_pdf(file_content)

                # Create a document name from the file name
                name = pdf_file.replace('.pdf', '').replace('_', ' ').title()

                # Check if document already exists
                existing = LawDocument.search([
                    ('name', '=', name),
                    ('document_type', '=', document_type),
                    ('language', '=', language)
                ])

                if existing:
                    # Update the existing document
                    existing.write({
                        'content': text_content,
                        'original_file': base64.b64encode(file_content),
                        'original_filename': pdf_file,
                        'file_type': 'pdf'
                    })
                    document = existing
                else:
                    # Create a new document
                    document = LawDocument.create({
                        'name': name,
                        'document_type': document_type,
                        'content': text_content,
                        'language': language,
                        'original_file': base64.b64encode(file_content),
                        'original_filename': pdf_file,
                        'file_type': 'pdf'
                    })

                document_ids.append(document.id)

                # Generate embeddings for the document
                try:
                    _logger.info(f"Generating embeddings for document: {document.name}")
                    document.generate_embeddings()
                except Exception as e:
                    _logger.error(f"Error generating embeddings for document {document.name}: {str(e)}")

            except Exception as e:
                _logger.error(f"Error processing PDF file {pdf_file}: {str(e)}")

        return document_ids
