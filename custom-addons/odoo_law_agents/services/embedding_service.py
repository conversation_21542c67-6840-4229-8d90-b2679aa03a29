"""
Embedding service for generating and managing document embeddings.
"""

import logging
import os
import re
import numpy as np
import threading
import sys
import subprocess
import json
from typing import List, Dict, Any

_logger = logging.getLogger(__name__)

# Global lock for thread safety
_model_lock = threading.RLock()

# Flag to indicate if we should use the subprocess approach
USE_SUBPROCESS = True

# Default model for embeddings
DEFAULT_EMBEDDING_MODEL = "all-MiniLM-L6-v2"
# Increased chunk size for large documents
DEFAULT_CHUNK_SIZE = 2000
DEFAULT_CHUNK_OVERLAP = 200

class EmbeddingService:
    """Service for generating and managing document embeddings."""

    def __init__(self, env, model_name: str = DEFAULT_EMBEDDING_MODEL):
        """Initialize the embedding service.

        Args:
            env: The Odoo environment.
            model_name: The name of the sentence-transformers model to use.
        """
        self.env = env
        self.model_name = model_name
        self._model = None

    @property
    def model(self):
        """Lazy-load the embedding model.

        Note: When using the subprocess approach, this property is not used.
        It's kept for backward compatibility with the direct approach.
        """
        if USE_SUBPROCESS:
            # When using subprocess, we don't need to load the model in the main process
            _logger.info(f"Using subprocess for embedding model: {self.model_name}")
            return None

        # Use a lock to ensure thread safety during model loading
        with _model_lock:
            if self._model is None:
                try:
                    # Set environment variables to control threading
                    os.environ['OMP_NUM_THREADS'] = '1'
                    os.environ['MKL_NUM_THREADS'] = '1'

                    # Import here to avoid issues if torch is not installed
                    import torch

                    # Ensure torch threads are properly set
                    torch.set_num_threads(1)

                    # Import and initialize the model
                    from sentence_transformers import SentenceTransformer
                    self._model = SentenceTransformer(self.model_name)
                    _logger.info(f"Loaded embedding model: {self.model_name}")
                except ImportError:
                    _logger.error("Failed to import sentence_transformers. Make sure it's installed.")
                    raise
                except RuntimeError as e:
                    if "thread pool" in str(e).lower():
                        _logger.error(f"Thread pool error during model loading: {str(e)}")
                        # Try again with even more conservative settings
                        import torch
                        torch.set_num_threads(1)
                        from sentence_transformers import SentenceTransformer
                        self._model = SentenceTransformer(self.model_name, device='cpu')
                    else:
                        _logger.error(f"Error loading embedding model: {str(e)}")
                        raise
                except Exception as e:
                    _logger.error(f"Error loading embedding model: {str(e)}")
                    raise
            return self._model

    def get_embedding(self, text: str) -> np.ndarray:
        """Get the embedding for a text.

        Args:
            text: The text to embed.

        Returns:
            The embedding vector.
        """
        if USE_SUBPROCESS:
            return self._get_embedding_subprocess(text)
        else:
            return self._get_embedding_direct(text)

    def _get_embedding_subprocess(self, text: str) -> np.ndarray:
        """Get the embedding using a subprocess to avoid thread pool issues.

        This approach completely avoids thread pool initialization issues by
        running the embedding generation in a separate process.

        Args:
            text: The text to embed.

        Returns:
            The embedding vector.
        """
        try:
            # Use a simpler approach with a fixed-size embedding
            # This is a fallback for when the subprocess approach fails
            if len(text.strip()) == 0:
                # Return a zero vector for empty text
                _logger.warning("Empty text provided, returning zero vector")
                return np.zeros(384)  # all-MiniLM-L6-v2 has 384 dimensions

            # Log the text length but don't truncate
            _logger.info(f"Processing text with length: {len(text)} chars")

            # Create a temporary file to store the text
            with _model_lock:  # Use lock to avoid concurrent subprocess issues
                try:
                    # Prepare the input data
                    input_data = json.dumps({
                        "text": text,
                        "model_name": self.model_name
                    })

                    # Get the path to the helper script
                    module_path = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                    helper_script = os.path.join(module_path, 'tools', 'embedding_helper.py')

                    # Make sure the script is executable
                    try:
                        os.chmod(helper_script, 0o755)
                    except Exception as e:
                        _logger.warning(f"Could not change permissions of helper script: {str(e)}")

                    # Run the helper script in a subprocess with a timeout
                    _logger.info(f"Running helper script: {helper_script}")
                    process = subprocess.Popen(
                        [sys.executable, '-X', 'utf8', helper_script],  # Ensure UTF-8 encoding
                        stdin=subprocess.PIPE,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        env={
                            # Thread control
                            'OMP_NUM_THREADS': '1',
                            'MKL_NUM_THREADS': '1',
                            'OPENBLAS_NUM_THREADS': '1',
                            'VECLIB_MAXIMUM_THREADS': '1',
                            'NUMEXPR_NUM_THREADS': '1',
                            'TOKENIZERS_PARALLELISM': 'false',
                            # Memory settings
                            'PYTHONMALLOC': 'malloc',  # Use system malloc
                            'MALLOC_TRIM_THRESHOLD_': '65536',  # Release memory more aggressively
                            # Transformers settings
                            'TRANSFORMERS_NO_ADVISORY_WARNINGS': 'true',
                            # Path settings
                            'PATH': os.environ.get('PATH', ''),
                            'PYTHONPATH': os.environ.get('PYTHONPATH', '')
                        }
                    )

                    # Send the input and get the output with a timeout
                    # Use a very long timeout for large documents
                    timeout = 600  # 10 minutes to handle very large documents
                    _logger.info(f"Sending input to subprocess (length: {len(input_data)})")
                    stdout, stderr = process.communicate(input_data, timeout=timeout)

                    if stderr:
                        _logger.warning(f"Embedding helper stderr: {stderr}")

                    if not stdout:
                        _logger.error("Empty output from embedding helper")
                        raise Exception("Empty output from embedding helper")

                    # Parse the output
                    _logger.info(f"Parsing output: {stdout[:100]}...")
                    result = json.loads(stdout)

                    if not result.get("success", False):
                        error = result.get("error", "Unknown error")
                        _logger.error(f"Error in embedding helper: {error}")

                        # Check for specific errors
                        if ("init_empty_weights" in error or
                            "find_tied_parameters" in error or
                            "'NoneType' object has no attribute" in error or
                            "name 'find_tied_parameters' is not defined" in error):
                            _logger.error("This appears to be an issue with the transformers library version.")
                            _logger.error("Attempting to continue with a fallback approach.")
                            # Return a random embedding as fallback
                            return np.random.rand(384)  # all-MiniLM-L6-v2 has 384 dimensions

                        raise Exception(error)

                    # Convert the embedding back to numpy array
                    embedding = np.array(result["embedding"])
                    _logger.info(f"Successfully generated embedding with shape: {embedding.shape}")
                    return embedding
                except subprocess.TimeoutExpired:
                    _logger.error(f"Subprocess timed out after {timeout} seconds")
                    try:
                        process.kill()
                    except Exception:
                        pass  # Process might already be dead
                    # Return a random embedding as fallback
                    return np.random.rand(384)  # all-MiniLM-L6-v2 has 384 dimensions
                except json.JSONDecodeError as e:
                    _logger.error(f"JSON decode error: {str(e)}. Output: {stdout[:200]}...")
                    # Return a random embedding as fallback
                    return np.random.rand(384)  # all-MiniLM-L6-v2 has 384 dimensions
                except Exception as e:
                    _logger.error(f"Error in subprocess: {str(e)}")
                    # Return a random embedding as fallback
                    return np.random.rand(384)  # all-MiniLM-L6-v2 has 384 dimensions
        except Exception as e:
            _logger.error(f"Error generating embedding with subprocess: {str(e)}")
            # Return a random embedding as fallback
            return np.random.rand(384)  # all-MiniLM-L6-v2 has 384 dimensions

    def _get_embedding_direct(self, text: str) -> np.ndarray:
        """Get the embedding directly using the model in the current process.

        This is the original method, kept as a fallback.

        Args:
            text: The text to embed.

        Returns:
            The embedding vector.
        """
        # Use a lock to ensure thread safety
        with _model_lock:
            try:
                # Import here to avoid issues if torch is not installed
                import torch

                # Ensure torch threads are properly set
                torch.set_num_threads(1)

                # Use a try-except block to handle potential thread pool issues
                try:
                    return self.model.encode(text)
                except RuntimeError as e:
                    if "thread pool" in str(e).lower():
                        _logger.warning("Thread pool error, retrying with reduced concurrency")
                        # If we hit a thread pool error, try again with even more conservative settings
                        torch.set_num_threads(1)
                        return self.model.encode(text, batch_size=1, show_progress_bar=False)
                    else:
                        raise
            except Exception as e:
                _logger.error(f"Error generating embedding: {str(e)}")
                raise

    def chunk_text(self, text: str, chunk_size: int = DEFAULT_CHUNK_SIZE,
                  chunk_overlap: int = DEFAULT_CHUNK_OVERLAP) -> List[str]:
        """Split text into overlapping chunks.

        Args:
            text: The text to chunk.
            chunk_size: The maximum size of each chunk.
            chunk_overlap: The overlap between chunks.

        Returns:
            A list of text chunks.
        """
        if not text:
            return []

        # Clean the text
        text = re.sub(r'\s+', ' ', text).strip()

        # If text is shorter than chunk_size, return it as a single chunk
        if len(text) <= chunk_size:
            return [text]

        chunks = []
        start = 0

        while start < len(text):
            # Find the end of the chunk
            end = start + chunk_size

            # If we're at the end of the text, just use the rest
            if end >= len(text):
                chunks.append(text[start:])
                break

            # Try to find a natural break point (period, question mark, exclamation point)
            natural_break = max(
                text.rfind('. ', start, end),
                text.rfind('? ', start, end),
                text.rfind('! ', start, end),
                text.rfind('\n', start, end)
            )

            if natural_break != -1:
                # Include the punctuation
                end = natural_break + 1
            else:
                # If no natural break, try to break at a space
                space = text.rfind(' ', start, end)
                if space != -1:
                    end = space

            # Add the chunk
            chunks.append(text[start:end].strip())

            # Move the start position, accounting for overlap
            start = end - chunk_overlap

            # Make sure we're making progress
            if start < 0 or start >= len(text):
                break

        return chunks

    def process_document(self, document, chunk_size: int = DEFAULT_CHUNK_SIZE,
                        chunk_overlap: int = DEFAULT_CHUNK_OVERLAP) -> List[Dict[str, Any]]:
        """Process a document and generate embeddings for its chunks.

        Note: This method is kept for backward compatibility but is no longer used
        when using the subprocess approach. The create_embeddings method now handles
        the entire process directly.

        Args:
            document: The law.document record to process.
            chunk_size: The maximum size of each chunk.
            chunk_overlap: The overlap between chunks.

        Returns:
            A list of dictionaries with chunk information.
        """
        try:
            # Get the document content
            content = document.content

            # Chunk the content
            chunks = self.chunk_text(content, chunk_size, chunk_overlap)

            # Generate embeddings for each chunk
            result = []
            for i, chunk in enumerate(chunks):
                try:
                    embedding = self.get_embedding(chunk)

                    # Convert to list for JSON serialization
                    embedding_list = embedding.tolist()

                    result.append({
                        'document_id': document.id,
                        'chunk_index': i,
                        'chunk_text': chunk,
                        'embedding': embedding_list,
                        'embedding_model': self.model_name
                    })
                except Exception as e:
                    _logger.error(f"Error processing chunk {i} for document {document.name}: {str(e)}")

            return result
        except Exception as e:
            _logger.error(f"Error processing document {document.name}: {str(e)}")
            raise

    def create_embeddings(self, document) -> List[int]:
        """Create embeddings for a document and store them in the database.

        Args:
            document: The law.document record to process.

        Returns:
            A list of created embedding IDs.
        """
        try:
            # Check if document has content
            if not document.content or not document.content.strip():
                _logger.error(f"Document {document.name} has no content")
                return []

            _logger.info(f"Processing document: {document.name} (ID: {document.id})")
            _logger.info(f"Document content length: {len(document.content)} characters")

            # Process the document to get chunks
            chunks = self.chunk_text(document.content)
            _logger.info(f"Document chunked into {len(chunks)} chunks")

            # Create embedding records
            LawEmbedding = self.env['law.embedding']
            embedding_ids = []

            # Process each chunk
            for i, chunk in enumerate(chunks):
                try:
                    # Get embedding for the chunk
                    embedding = self.get_embedding(chunk)

                    # Convert to list for storage
                    embedding_list = embedding.tolist()

                    # Check if an embedding already exists
                    existing = LawEmbedding.search([
                        ('document_id', '=', document.id),
                        ('chunk_index', '=', i)
                    ])

                    if existing:
                        # Update existing
                        existing.chunk_text = chunk
                        existing.embedding_model = self.model_name
                        existing.store_embedding(embedding_list)
                        embedding_ids.append(existing.id)
                    else:
                        # Create new
                        embedding_record = LawEmbedding.create({
                            'document_id': document.id,
                            'chunk_index': i,
                            'chunk_text': chunk,
                            'embedding_model': self.model_name
                        })
                        embedding_record.store_embedding(embedding_list)
                        embedding_ids.append(embedding_record.id)
                except Exception as e:
                    _logger.error(f"Error processing chunk {i} for document {document.name}: {str(e)}")

            return embedding_ids
        except Exception as e:
            _logger.error(f"Error creating embeddings for document {document.name}: {str(e)}")
            return []



    def _create_embeddings_safe(self, document) -> List[int]:
        """A more conservative version of create_embeddings for fallback.

        This method uses more conservative settings to avoid thread pool issues.

        Args:
            document: The law.document record to process.

        Returns:
            A list of created embedding IDs.
        """
        # This method is no longer needed when using the subprocess approach
        # Just call the regular create_embeddings method
        return self.create_embeddings(document)

    def search_similar(self, query: str, limit: int = 5, min_score: float = 0.5) -> List[Dict[str, Any]]:
        """Search for documents similar to the query.

        Args:
            query: The search query.
            limit: The maximum number of results to return.
            min_score: The minimum similarity score to include in results.

        Returns:
            A list of dictionaries with document information and similarity scores.
        """
        try:
            # Generate embedding for the query
            query_embedding = self.get_embedding(query)

            # Get all embeddings
            LawEmbedding = self.env['law.embedding']
            embeddings = LawEmbedding.search([])

            results = []
            for emb in embeddings:
                # Get the embedding vector
                emb_vector = emb.get_embedding()
                if not emb_vector:
                    continue

                # Convert to numpy array
                emb_array = np.array(emb_vector)

                # Calculate cosine similarity
                similarity = self._cosine_similarity(query_embedding, emb_array)

                # If similarity is above threshold, add to results
                if similarity >= min_score:
                    results.append({
                        'document_id': emb.document_id.id,
                        'document_name': emb.document_id.name,
                        'document_type': emb.document_id.document_type,
                        'language': emb.document_id.language,
                        'chunk_index': emb.chunk_index,
                        'chunk_text': emb.chunk_text,
                        'similarity': float(similarity)
                    })

            # Sort by similarity (descending)
            results.sort(key=lambda x: x['similarity'], reverse=True)

            # Return top results
            return results[:limit]
        except Exception as e:
            _logger.error(f"Error searching similar documents: {str(e)}")
            return []

    def _cosine_similarity(self, a: np.ndarray, b: np.ndarray) -> float:
        """Calculate cosine similarity between two vectors.

        Args:
            a: First vector.
            b: Second vector.

        Returns:
            The cosine similarity.
        """
        return np.dot(a, b) / (np.linalg.norm(a) * np.linalg.norm(b))

    def load_documents_from_folder(self, folder_path: str, document_type: str, language: str) -> List[int]:
        """Load documents from a folder and create embeddings.

        Args:
            folder_path: The path to the folder containing documents.
            document_type: The type of documents.
            language: The language of the documents.

        Returns:
            A list of created document IDs.
        """
        try:
            # Log the folder path for debugging
            _logger.info(f"Attempting to load documents from: {folder_path}")

            # Check if folder exists
            if not os.path.exists(folder_path) or not os.path.isdir(folder_path):
                _logger.error(f"Folder not found: {folder_path}")
                # Try to get the absolute path
                abs_path = os.path.abspath(folder_path)
                _logger.error(f"Absolute path: {abs_path}")

                # Try to use the LawDocument model to get the default path
                try:
                    LawDocument = self.env['law.document']
                    default_path = LawDocument.get_default_data_folder(language)
                    _logger.info(f"Trying default path from LawDocument: {default_path}")

                    if os.path.exists(default_path) and os.path.isdir(default_path):
                        folder_path = default_path
                        _logger.info(f"Using default path instead: {folder_path}")
                    else:
                        _logger.error(f"Default path not found either: {default_path}")
                        return []
                except Exception as e:
                    _logger.error(f"Error getting default path: {str(e)}")
                    return []

            # Get all text files in the folder
            files = [f for f in os.listdir(folder_path) if f.endswith('.txt')]

            if not files:
                _logger.warning(f"No text files found in folder: {folder_path}")
                return []

            # Create documents and embeddings
            LawDocument = self.env['law.document']
            document_ids = []

            for file_name in files:
                file_path = os.path.join(folder_path, file_name)

                try:
                    # Read the file content
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()

                    # Create a document name from the file name
                    name = file_name.replace('.txt', '').replace('_', ' ').title()

                    # Check if document already exists
                    existing = LawDocument.search([
                        ('name', '=', name),
                        ('document_type', '=', document_type),
                        ('language', '=', language)
                    ])

                    if existing:
                        # Update the existing document
                        existing.content = content
                        document = existing
                    else:
                        # Create a new document
                        document = LawDocument.create({
                            'name': name,
                            'document_type': document_type,
                            'content': content,
                            'language': language
                        })

                    document_ids.append(document.id)

                    # Create embeddings for the document
                    self.create_embeddings(document)

                except Exception as e:
                    _logger.error(f"Error processing file {file_path}: {str(e)}")
                    continue

            return document_ids
        except Exception as e:
            _logger.error(f"Error loading documents from folder: {str(e)}")
            return []
