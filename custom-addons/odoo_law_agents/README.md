# Odoo Law Agents

## Overview
This module provides an AI-powered legal assistance system for Odoo 18, combining LangGraph-based agents with Odoo's LLM capabilities to process and analyze legal documents and cases in multiple languages (English, Gujarati, and Hindi). The system includes document embedding, similarity search, online research, and comprehensive case analysis features.

## Features
- Multi-agent system for legal document processing
- Integration with Odoo's built-in LLM
- Document embedding and similarity search
- Multilingual support (English, Gujarati, and Hindi)
- Legal document analysis and retrieval
- Law-specific DuckDuckGo Search integration for targeted legal research
- PDF, DOCX, and TXT file upload and processing
- Original document storage and management
- Automatic text extraction from documents
- Seamless embedding generation for uploaded files
- Automatic sequence-based naming for document types using ir.sequence
- Support for processing very large documents without character limits
- Batch embedding generation for multiple documents at once
- Memory-efficient processing for large PDF files
- Full support for Gujarati language content in embeddings and search
- PDF text extraction using Odoo's built-in tools
- Minimal dependencies for easy deployment
- Comprehensive case analysis system with LangGraph integration
- Workflow visualization for agent processes
- Fallback mock implementation for testing without LLM calls

## Installation

### Prerequisites
- Odoo 18.0
- Python 3.12+
- Required Python packages:
  ```bash
  pip install sentence-transformers duckduckgo-search pydantic typing-extensions opik>=0.6.0
  ```
- Optional packages for enhanced functionality:
  ```bash
  pip install langgraph  # For real LangGraph-based case analysis
  ```

### Steps
1. Clone this repository into your Odoo addons directory
2. Update your Odoo configuration to include this directory in the addons path
3. Install the module through Odoo's Apps menu
4. Configure the LLM endpoint in the settings

## Configuration
1. Go to Settings > Law Agents
2. Configure the following:
   - LLM API settings
   - Document storage location
   - Search API credentials
   - Agent behavior parameters

## Usage
1. Navigate to Legal > Law Agents
2. Create or select an agent
3. Upload legal documents using one of these methods:
   - Use the "Upload Document" wizard to upload individual PDF, DOCX, or TXT files
   - Use the "Load Documents" wizard to process multiple documents from folders
   - Place PDF files in the pdf_english or pdf_gujarati folders and use the Load Documents wizard
4. View uploaded documents and their extracted text content
5. Generate embeddings for the documents (automatic or manual)
6. Start querying the system with legal questions

### Multilingual Support
1. The system supports both English and Gujarati content
2. When creating a document, select the appropriate language (English or Gujarati)
3. Upload PDF files or enter text directly in Gujarati
4. The embedding system will process Gujarati text correctly
5. Search queries can be performed in either language
6. For testing Gujarati content, sample files are provided in the data/gujarati directory

### Law-Specific Search
1. The system uses specialized search methods for legal content
2. Searches are automatically focused on the relevant law type:
   - Civil law focused search for civil law queries
   - Land law focused search for land law queries
   - General legal search for other queries
3. Search results are filtered for legal relevance
4. Legal news is automatically included when relevant
5. Results include information about the content type and law type

### Case Analysis System

The module includes a comprehensive case analysis system that can process legal cases and generate detailed analysis reports:

1. Navigate to Legal > Case Analysis
2. Create a new case with the following information:
   - Case Title
   - Case Number
   - Case Text (the full text of the case to be analyzed)
   - Case Type (Property Dispute, Inheritance, Contract Dispute, etc.)
   - Language (English, Gujarati, Hindi)
3. Configure analysis options:
   - Use Online Search (enable/disable web search)
   - Use Document Embeddings (enable/disable document retrieval)
   - Use Mock Responses (for testing without LLM calls)
4. Click "Analyze Case" to start the analysis process
5. The system will generate a comprehensive analysis with sections for:
   - Background Analysis
   - Legal Issues
   - Evidence Analysis
   - Legal Principles
   - Judgment Prediction
   - Full Analysis
6. Use the "Print Analysis" button to generate a PDF report of the analysis
7. Use the "Reset to Draft" button to rerun the analysis if needed
8. Use the "Visualize Workflow" button to see the LangGraph workflow structure

### LangGraph Integration

The system can work with or without the LangGraph library:

1. For full functionality, install LangGraph:
   ```bash
   pip install langgraph
   ```
2. If LangGraph is not available, the system will automatically use a sophisticated mock implementation
3. The mock implementation generates realistic content based on the case text
4. You can toggle between real and mock implementations using the "Use Mock Responses" option

## Security
- Role-based access control
- Document-level security
- API key management
- Audit logging

## Support
For issues and feature requests, please use the GitHub issue tracker.

## License
This module is licensed under LGPL-3.