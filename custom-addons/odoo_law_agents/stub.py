"""This is an automatically generated file. Do not modify it.

This file was generated using `langgraph-gen` version 0.0.3.
To regenerate this file, run `langgraph-gen` with the source `yaml` file as an argument.

Usage:

1. Add the generated file to your project.
2. Create a new agent using the stub.

Below is a sample implementation of the generated stub:

```python
from typing_extensions import TypedDict

from stub import CustomAgent

class SomeState(TypedDict):
    # define your attributes here
    foo: str

# Define stand-alone functions
def Gujarat_Law_Agent(state: SomeState) -> dict:
    print("In node: Gujarat Law Agent")
    return {
        # Add your state update logic here
    }


def Retrival_tools(state: SomeState) -> dict:
    print("In node: Retrival tools")
    return {
        # Add your state update logic here
    }


def Get_Suitable_PDF_Results(state: SomeState) -> dict:
    print("In node: Get Suitable PDF Results")
    return {
        # Add your state update logic here
    }


def Civil_Law_Agent(state: SomeState) -> dict:
    print("In node: Civil Law Agent")
    return {
        # Add your state update logic here
    }


def Land_Law_Agent(state: SomeState) -> dict:
    print("In node: Land Law Agent")
    return {
        # Add your state update logic here
    }


def Generate_Response(state: SomeState) -> dict:
    print("In node: Generate Response")
    return {
        # Add your state update logic here
    }


def Query Retrival(state: SomeState) -> str:
    print("In condition: Query Retrival")
    raise NotImplementedError("Implement me.")


def Check for Civil/Land(state: SomeState) -> str:
    print("In condition: Check for Civil/Land")
    raise NotImplementedError("Implement me.")


agent = CustomAgent(
    state_schema=SomeState,
    impl=[
        ("Gujarat Law Agent", Gujarat_Law_Agent),
        ("Retrival tools", Retrival_tools),
        ("Get Suitable PDF Results", Get_Suitable_PDF_Results),
        ("Civil Law Agent", Civil_Law_Agent),
        ("Land Law Agent", Land_Law_Agent),
        ("Generate Response", Generate_Response),
        ("Query Retrival", Query Retrival),
        ("Check for Civil/Land", Check for Civil/Land),
    ]
)

compiled_agent = agent.compile()

print(compiled_agent.invoke({"foo": "bar"}))
"""

from typing import Callable, Any, Optional, Type

from langgraph.constants import START, END
from langgraph.graph import StateGraph


def CustomAgent(
    *,
    state_schema: Optional[Type[Any]] = None,
    config_schema: Optional[Type[Any]] = None,
    input: Optional[Type[Any]] = None,
    output: Optional[Type[Any]] = None,
    impl: list[tuple[str, Callable]],
) -> StateGraph:
    """Create the state graph for CustomAgent."""
    # Declare the state graph
    builder = StateGraph(
        state_schema, config_schema=config_schema, input=input, output=output
    )

    nodes_by_name = {name: imp for name, imp in impl}

    all_names = set(nodes_by_name)

    expected_implementations = {
        "Gujarat_Law_Agent",
        "Retrival_tools",
        "Get_Suitable_PDF_Results",
        "Civil_Law_Agent",
        "Land_Law_Agent",
        "Generate_Response",
        "Query Retrival",
        "Check for Civil/Land",
    }

    missing_nodes = expected_implementations - all_names
    if missing_nodes:
        raise ValueError(f"Missing implementations for: {missing_nodes}")

    extra_nodes = all_names - expected_implementations

    if extra_nodes:
        raise ValueError(
            f"Extra implementations for: {extra_nodes}. Please regenerate the stub."
        )

    # Add nodes
    builder.add_node("Gujarat Law Agent", nodes_by_name["Gujarat_Law_Agent"])
    builder.add_node("Retrival tools", nodes_by_name["Retrival_tools"])
    builder.add_node("Get Suitable PDF Results", nodes_by_name["Get_Suitable_PDF_Results"])
    builder.add_node("Civil Law Agent", nodes_by_name["Civil_Law_Agent"])
    builder.add_node("Land Law Agent", nodes_by_name["Land_Law_Agent"])
    builder.add_node("Generate Response", nodes_by_name["Generate_Response"])

    # Add edges
    builder.add_edge(START, "Gujarat Law Agent")
    builder.add_edge("Generate Response", END)
    builder.add_edge("Civil Law Agent", "Generate Response")
    builder.add_edge("Land Law Agent", "Generate Response")
    builder.add_conditional_edges(
        "Retrival tools",
        nodes_by_name["Query Retrival"],
        [
            "Get Suitable PDF Results",
        ],
    )
    builder.add_conditional_edges(
        "Gujarat Law Agent",
        nodes_by_name["Check for Civil/Land"],
        [
            "Civil Law Agent",
            "Land Law Agent",
            "Retrival tools",
        ],
    )
    return builder
