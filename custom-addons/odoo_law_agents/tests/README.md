# Odoo Law Agents Tests

This directory contains tests for the Odoo Law Agents module. The tests are written using Odoo's standard testing framework.

## Test Structure

The tests are organized into three main files:

1. `test_embedding.py` - Tests for the embedding functionality
2. `test_document_loading.py` - Tests for document loading
3. `test_rag_system.py` - Tests for the RAG (Retrieval-Augmented Generation) system

## Running the Tests

To run the tests, use the Odoo test command:

```bash
python3 odoo-bin -d your_database --test-enable --test-tags odoo_law_agents
```

To run specific test files:

```bash
python3 odoo-bin -d your_database --test-enable --test-tags odoo_law_agents.test_embedding
```

## Test Tags

The tests are tagged with:

- `post_install` - Run after module installation
- `-at_install` - Do not run during module installation

## Test Coverage

The tests cover the following functionality:

### Embedding Tests

- Embedding generation
- Similar document search
- Subprocess-based embedding generation
- Large document processing
- Batch embedding generation
- Memory-efficient processing
- Empty text handling
- Error handling

### Document Loading Tests

- Loading documents from a folder
- Loading specific document types
- Loading with automatic embedding generation
- Reloading documents
- Testing ir.sequence generation for document types
- Error handling

### RAG System Tests

- Context retrieval
- Document search
- Response generation (if LLM API is configured)
- Bilingual support
- Error handling

## Adding New Tests

To add new tests:

1. Create a new test file in this directory
2. Add the file to the `__init__.py` file
3. Use the `TransactionCase` class for tests that need database access
4. Use the `tagged` decorator to specify when the tests should run

## Test Data

The tests create their own test data, so they don't depend on existing data in the database. This ensures that the tests are repeatable and reliable.

## Troubleshooting

If the tests fail:

1. Check the logs for error messages
2. Verify that the module is installed correctly
3. Make sure the database is accessible
4. Check that the required dependencies are installed
