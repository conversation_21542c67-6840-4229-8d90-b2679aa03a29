from odoo.tests import TransactionCase
from odoo.exceptions import ValidationError

class TestDocumentTypeSequence(TransactionCase):
    """Test the ir.sequence implementation for document.type model."""

    def setUp(self):
        super(TestDocumentTypeSequence, self).setUp()
        self.LawType = self.env['law.type']
        self.DocumentCategory = self.env['document.category']
        self.DocumentType = self.env['document.type']
        
        # Create test data
        self.law_type = self.LawType.create({
            'name': 'Test Law Type',
            'code': 'test_law',
        })
        
        self.category = self.DocumentCategory.create({
            'name': 'Test Category',
            'code': 'test_cat',
        })

    def test_01_sequence_generation(self):
        """Test that a sequence is generated when creating a document type."""
        doc_type = self.DocumentType.create({
            'law_type_id': self.law_type.id,
            'category_id': self.category.id,
        })
        
        # Check that the name is not 'New'
        self.assertNotEqual(doc_type.name, 'New', 
                           "Document type name should be generated from sequence")
        
        # Check that the name starts with the sequence prefix
        self.assertTrue(doc_type.name.startswith('DOC/'), 
                       f"Document type name should start with 'DOC/', got {doc_type.name}")
        
        # Check that the display_name is correctly computed
        expected_display_name = f"{doc_type.name} - {self.law_type.name} - {self.category.name}"
        self.assertEqual(doc_type.display_name, expected_display_name,
                        f"Display name should be '{expected_display_name}', got '{doc_type.display_name}'")

    def test_02_multiple_document_types(self):
        """Test that multiple document types get unique sequence numbers."""
        doc_type1 = self.DocumentType.create({
            'law_type_id': self.law_type.id,
            'category_id': self.category.id,
        })
        
        doc_type2 = self.DocumentType.create({
            'law_type_id': self.law_type.id,
            'category_id': self.category.id,
        })
        
        # This should fail due to unique constraint on law_type_id and category_id
        with self.assertRaises(ValidationError):
            self.DocumentType.create({
                'law_type_id': self.law_type.id,
                'category_id': self.category.id,
            })
        
        # Create a new category to test another document type
        new_category = self.DocumentCategory.create({
            'name': 'Another Category',
            'code': 'another_cat',
        })
        
        doc_type3 = self.DocumentType.create({
            'law_type_id': self.law_type.id,
            'category_id': new_category.id,
        })
        
        # Check that all names are different
        self.assertNotEqual(doc_type1.name, doc_type2.name, 
                           "Document type names should be unique")
        self.assertNotEqual(doc_type1.name, doc_type3.name, 
                           "Document type names should be unique")
        self.assertNotEqual(doc_type2.name, doc_type3.name, 
                           "Document type names should be unique")
        
        # Check that all names follow the sequence pattern
        for doc_type in [doc_type1, doc_type2, doc_type3]:
            self.assertTrue(doc_type.name.startswith('DOC/'), 
                           f"Document type name should start with 'DOC/', got {doc_type.name}")

    def test_03_update_display_name(self):
        """Test that display_name is updated when law_type_id or category_id changes."""
        doc_type = self.DocumentType.create({
            'law_type_id': self.law_type.id,
            'category_id': self.category.id,
        })
        
        initial_name = doc_type.name
        initial_display_name = doc_type.display_name
        
        # Create a new law type
        new_law_type = self.LawType.create({
            'name': 'New Law Type',
            'code': 'new_law',
        })
        
        # Update the document type
        doc_type.write({
            'law_type_id': new_law_type.id,
        })
        
        # Check that the name hasn't changed
        self.assertEqual(doc_type.name, initial_name,
                        "Document type name should not change when law_type_id changes")
        
        # Check that the display_name has been updated
        expected_display_name = f"{initial_name} - {new_law_type.name} - {self.category.name}"
        self.assertEqual(doc_type.display_name, expected_display_name,
                        f"Display name should be '{expected_display_name}', got '{doc_type.display_name}'")
        
        # Check that the display_name is different from the initial one
        self.assertNotEqual(doc_type.display_name, initial_display_name,
                           "Display name should change when law_type_id changes")
