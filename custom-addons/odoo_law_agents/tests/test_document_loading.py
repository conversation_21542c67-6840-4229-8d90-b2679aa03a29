from odoo.tests.common import TransactionCase, tagged
import os
import tempfile
import shutil
import logging

_logger = logging.getLogger(__name__)

@tagged('post_install', '-at_install')
class TestDocumentLoading(TransactionCase):
    """Test the document loading functionality."""

    def setUp(self):
        super(TestDocumentLoading, self).setUp()
        # Create a temporary directory for test documents
        self.temp_dir = tempfile.mkdtemp()
        self.land_law_dir = os.path.join(self.temp_dir, 'land_law')
        self.civil_law_dir = os.path.join(self.temp_dir, 'civil_law')
        
        # Create the subdirectories
        os.makedirs(self.land_law_dir, exist_ok=True)
        os.makedirs(self.civil_law_dir, exist_ok=True)
        
        # Create test document files
        self.create_test_documents()
        
        # Get the document model
        self.LawDocument = self.env['law.document']

    def tearDown(self):
        # Clean up the temporary directory
        shutil.rmtree(self.temp_dir)
        super(TestDocumentLoading, self).tearDown()

    def create_test_documents(self):
        """Create test document files."""
        # Create a land law document
        with open(os.path.join(self.land_law_dir, 'property_rights.txt'), 'w') as f:
            f.write("""
            Property Rights
            
            Property rights are legal rights or interests in land and fixtures or immovable property.
            Property rights include ownership, possession, use, and disposal of a particular item or piece of property.
            Property rights can be viewed as extensions of the human right to property.
            
            In common law, property rights are protected under Article 17 of the Universal Declaration of Human Rights,
            which recognizes the right to own property and states that no one may be arbitrarily deprived of property.
            """)
        
        # Create a civil law document
        with open(os.path.join(self.civil_law_dir, 'contract_law.txt'), 'w') as f:
            f.write("""
            Contract Law
            
            Contract law is the body of law that relates to making and enforcing agreements.
            A contract is an agreement that a party can turn to a court to enforce.
            Contract law is the area of law that governs making contracts, carrying them out and fashioning a fair remedy when there's a breach.
            
            Anyone who conducts business uses contract law. Both companies and consumers use contracts when they buy and sell goods,
            when they license products or activities, for employment agreements, for insurance agreements and more.
            """)

    def test_01_load_documents(self):
        """Test loading documents from a folder."""
        # Load documents from the temporary directory
        document_ids = self.LawDocument.load_documents_from_folder(
            folder_path=self.temp_dir,
            document_type=None,  # Load all document types
            language='en'
        )
        
        # Check that documents were loaded
        self.assertEqual(len(document_ids), 2, "Two documents should be loaded")
        
        # Check that the documents have the correct properties
        documents = self.LawDocument.browse(document_ids)
        
        # Check document types
        land_law_docs = documents.filtered(lambda d: d.document_type == 'land_law')
        civil_law_docs = documents.filtered(lambda d: d.document_type == 'civil_law')
        
        self.assertEqual(len(land_law_docs), 1, "One land law document should be loaded")
        self.assertEqual(len(civil_law_docs), 1, "One civil law document should be loaded")
        
        # Check document names
        property_rights_docs = documents.filtered(lambda d: 'property' in d.name.lower())
        contract_law_docs = documents.filtered(lambda d: 'contract' in d.name.lower())
        
        self.assertEqual(len(property_rights_docs), 1, "One property rights document should be loaded")
        self.assertEqual(len(contract_law_docs), 1, "One contract law document should be loaded")
        
        # Check that the documents have content
        for doc in documents:
            self.assertTrue(doc.content, "Document should have content")
            self.assertGreater(len(doc.content), 100, "Document content should be substantial")

    def test_02_load_specific_document_type(self):
        """Test loading documents of a specific type."""
        # Load only land law documents
        document_ids = self.LawDocument.load_documents_from_folder(
            folder_path=self.temp_dir,
            document_type='land_law',
            language='en'
        )
        
        # Check that only land law documents were loaded
        self.assertEqual(len(document_ids), 1, "One land law document should be loaded")
        
        # Check the document type
        document = self.LawDocument.browse(document_ids[0])
        self.assertEqual(document.document_type, 'land_law', "Document should be of type land_law")
        
        # Load only civil law documents
        document_ids = self.LawDocument.load_documents_from_folder(
            folder_path=self.temp_dir,
            document_type='civil_law',
            language='en'
        )
        
        # Check that only civil law documents were loaded
        self.assertEqual(len(document_ids), 1, "One civil law document should be loaded")
        
        # Check the document type
        document = self.LawDocument.browse(document_ids[0])
        self.assertEqual(document.document_type, 'civil_law', "Document should be of type civil_law")

    def test_03_load_with_embeddings(self):
        """Test that embeddings are generated when loading documents."""
        # Load documents from the temporary directory
        document_ids = self.LawDocument.load_documents_from_folder(
            folder_path=self.temp_dir,
            document_type=None,  # Load all document types
            language='en'
        )
        
        # Check that documents were loaded
        self.assertTrue(document_ids, "Documents should be loaded")
        
        # Check that embeddings were generated
        documents = self.LawDocument.browse(document_ids)
        
        # Refresh the records to get the latest values
        documents.invalidate_cache()
        
        # Check that at least some documents have embeddings
        docs_with_embeddings = documents.filtered(lambda d: d.has_embeddings)
        self.assertTrue(docs_with_embeddings, "At least some documents should have embeddings")
        
        # Check that the documents have embedding records
        for doc in docs_with_embeddings:
            self.assertGreater(doc.embedding_count, 0, "Document should have at least one embedding")
            
            # Get the embeddings
            embeddings = self.env['law.embedding'].search([('document_id', '=', doc.id)])
            self.assertGreater(len(embeddings), 0, "Document should have at least one embedding record")

    def test_04_reload_documents(self):
        """Test reloading documents."""
        # Load documents from the temporary directory
        document_ids_1 = self.LawDocument.load_documents_from_folder(
            folder_path=self.temp_dir,
            document_type=None,
            language='en'
        )
        
        # Load documents again
        document_ids_2 = self.LawDocument.load_documents_from_folder(
            folder_path=self.temp_dir,
            document_type=None,
            language='en'
        )
        
        # Check that the same number of documents were loaded
        self.assertEqual(len(document_ids_1), len(document_ids_2), 
                         "Same number of documents should be loaded on reload")
        
        # Check that the document IDs are the same (documents were updated, not recreated)
        self.assertEqual(set(document_ids_1), set(document_ids_2), 
                         "Same documents should be loaded on reload")

    def test_05_error_handling(self):
        """Test error handling when loading documents."""
        # Try to load from a non-existent folder
        document_ids = self.LawDocument.load_documents_from_folder(
            folder_path='/non/existent/folder',
            document_type=None,
            language='en'
        )
        
        # Check that no documents were loaded
        self.assertEqual(document_ids, [], "No documents should be loaded from non-existent folder")
        
        # Try to load with an invalid document type
        document_ids = self.LawDocument.load_documents_from_folder(
            folder_path=self.temp_dir,
            document_type='invalid_type',
            language='en'
        )
        
        # Check that no documents were loaded
        self.assertEqual(document_ids, [], "No documents should be loaded with invalid document type")
