from odoo.tests.common import TransactionCase, tagged
import numpy as np
import os
import logging

_logger = logging.getLogger(__name__)

@tagged('post_install', '-at_install')
class TestEmbedding(TransactionCase):
    """Test the embedding functionality."""

    def setUp(self):
        super(TestEmbedding, self).setUp()
        # Create a test document
        self.test_document = self.env['law.document'].create({
            'name': 'Test Property Rights',
            'document_type': 'land_law',
            'language': 'en',
            'content': """
            Property rights are legal rights or interests in land and fixtures or immovable property.
            Property rights include ownership, possession, use, and disposal of a particular item or piece of property.
            Property rights can be viewed as extensions of the human right to property.
            
            In common law, property rights are protected under Article 17 of the Universal Declaration of Human Rights,
            which recognizes the right to own property and states that no one may be arbitrarily deprived of property.
            
            Property rights are important because they provide a legal framework for allocating scarce resources.
            They define who owns what, who can do what with what they own, and who can prevent others from using their property.
            
            There are several types of property rights:
            1. Ownership rights: The right to use, possess, and dispose of property.
            2. Usufruct rights: The right to use and derive profit from property belonging to another person.
            3. Easement rights: The right to use another person's property for a specific purpose.
            4. Mineral rights: The right to extract minerals from land.
            5. Water rights: The right to use water from a particular source.
            
            Property rights can be transferred through sale, gift, or inheritance.
            They can also be limited by government regulations, such as zoning laws and environmental regulations.
            """
        })
        
        # Get the embedding service
        from odoo.addons.odoo_law_agents.services.embedding_service import EmbeddingService
        self.embedding_service = EmbeddingService(self.env)

    def test_01_embedding_generation(self):
        """Test that embeddings can be generated for a document."""
        # Generate embeddings for the test document
        embedding_ids = self.embedding_service.create_embeddings(self.test_document)
        
        # Check that embeddings were created
        self.assertTrue(embedding_ids, "No embeddings were created")
        
        # Check that the document has embeddings
        self.test_document.invalidate_cache()  # Refresh the record
        self.assertTrue(self.test_document.has_embeddings, "Document should have embeddings")
        
        # Check the embedding count
        self.assertGreater(self.test_document.embedding_count, 0, "Document should have at least one embedding")
        
        # Get the embeddings
        embeddings = self.env['law.embedding'].search([('document_id', '=', self.test_document.id)])
        self.assertEqual(len(embeddings), len(embedding_ids), "Number of embeddings should match")
        
        # Check that each embedding has a vector
        for embedding in embeddings:
            self.assertTrue(embedding.embedding, "Embedding should have a vector")
            
            # Check that the embedding vector can be loaded
            vector = embedding.get_embedding()
            self.assertIsInstance(vector, np.ndarray, "Embedding vector should be a numpy array")
            self.assertGreater(len(vector), 0, "Embedding vector should not be empty")

    def test_02_similar_search(self):
        """Test that similar documents can be found using embeddings."""
        # Generate embeddings for the test document if they don't exist
        if not self.test_document.has_embeddings:
            self.embedding_service.create_embeddings(self.test_document)
        
        # Search for similar documents
        query = "What are property rights?"
        results = self.embedding_service.search_similar(query, limit=5)
        
        # Check that results were found
        self.assertTrue(results, "No similar documents were found")
        
        # Check that the test document is in the results
        found = False
        for result in results:
            if result['document_id'] == self.test_document.id:
                found = True
                break
        self.assertTrue(found, "Test document should be found in the results")
        
        # Check that the results have the expected fields
        for result in results:
            self.assertIn('document_id', result, "Result should have document_id")
            self.assertIn('score', result, "Result should have score")
            self.assertIn('chunk_text', result, "Result should have chunk_text")
            self.assertIn('chunk_index', result, "Result should have chunk_index")
            
            # Check that the score is between 0 and 1
            self.assertGreaterEqual(result['score'], 0, "Score should be >= 0")
            self.assertLessEqual(result['score'], 1, "Score should be <= 1")

    def test_03_subprocess_embedding(self):
        """Test that embeddings can be generated using the subprocess approach."""
        # Get a sample text
        sample_text = "This is a test text for embedding generation."
        
        # Generate an embedding using the subprocess approach
        embedding = self.embedding_service._get_embedding_subprocess(sample_text)
        
        # Check that the embedding is a numpy array
        self.assertIsInstance(embedding, np.ndarray, "Embedding should be a numpy array")
        
        # Check that the embedding has the expected shape
        self.assertEqual(embedding.shape[0], 384, "Embedding should have 384 dimensions")
        
        # Check that the embedding is not all zeros or all ones
        self.assertFalse(np.all(embedding == 0), "Embedding should not be all zeros")
        self.assertFalse(np.all(embedding == 1), "Embedding should not be all ones")
        
        # Check that the embedding has a reasonable norm
        norm = np.linalg.norm(embedding)
        self.assertGreater(norm, 0.1, "Embedding norm should be > 0.1")
        self.assertLess(norm, 10, "Embedding norm should be < 10")

    def test_04_empty_text_handling(self):
        """Test that empty text is handled properly."""
        # Generate an embedding for empty text
        empty_text = ""
        embedding = self.embedding_service.get_embedding(empty_text)
        
        # Check that the embedding is a numpy array
        self.assertIsInstance(embedding, np.ndarray, "Embedding should be a numpy array")
        
        # Check that the embedding has the expected shape
        self.assertEqual(embedding.shape[0], 384, "Embedding should have 384 dimensions")

    def test_05_error_handling(self):
        """Test that errors are handled properly."""
        # Try to generate embeddings for a non-existent document
        non_existent_doc = self.env['law.document'].browse([9999])  # ID that doesn't exist
        embedding_ids = self.embedding_service.create_embeddings(non_existent_doc)
        
        # Check that no embeddings were created
        self.assertEqual(embedding_ids, [], "No embeddings should be created for non-existent document")
        
        # Try to search for similar documents with an invalid query
        results = self.embedding_service.search_similar(None, limit=5)
        self.assertEqual(results, [], "No results should be found for None query")
        
        # Try with an extremely long query
        long_query = "a" * 10000  # Very long query
        results = self.embedding_service.search_similar(long_query, limit=5)
        # This should not raise an exception, but might return empty results
        self.assertIsInstance(results, list, "Results should be a list even for long query")
