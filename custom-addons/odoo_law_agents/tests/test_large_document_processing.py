from odoo.tests import TransactionCase, tagged
import base64
import os
import logging

_logger = logging.getLogger(__name__)

@tagged('post_install', '-at_install')
class TestLargeDocumentProcessing(TransactionCase):
    """Test the processing of large documents."""

    def setUp(self):
        super(TestLargeDocumentProcessing, self).setUp()
        self.LawDocument = self.env['law.document']
        self.LawEmbedding = self.env['law.embedding']
        
        # Create test data
        self.law_type = self.env['law.type'].create({
            'name': 'Test Law Type',
            'code': 'test_law',
        })
        
        self.category = self.env['document.category'].create({
            'name': 'Test Category',
            'code': 'test_cat',
        })
        
        # Create a document type
        self.doc_type = self.env['document.type'].create({
            'law_type_id': self.law_type.id,
            'category_id': self.category.id,
        })

    def _create_large_text(self, size=100000):
        """Create a large text for testing."""
        # Create a text with 'size' characters
        return "This is a test document. " * (size // 24)

    def test_01_large_document_embedding(self):
        """Test embedding generation for a large document."""
        # Create a large document
        large_text = self._create_large_text()
        
        document = self.LawDocument.create({
            'name': 'Large Test Document',
            'content': large_text,
            'law_type_id': self.law_type.id,
            'category_id': self.category.id,
            'document_type_ids': [(6, 0, [self.doc_type.id])],
            'language': 'en',
        })
        
        # Generate embeddings
        embedding_ids = document.generate_embeddings()
        
        # Check that embeddings were created
        self.assertTrue(embedding_ids, "No embeddings were created for the large document")
        
        # Check that the document has embeddings
        self.assertTrue(document.has_embeddings, "Document should have embeddings")
        
        # Check the number of embeddings (should be multiple chunks)
        self.assertGreater(len(embedding_ids), 1, "Large document should be split into multiple chunks")
        
        _logger.info(f"Created {len(embedding_ids)} embeddings for document with {len(large_text)} characters")

    def test_02_very_large_document_embedding(self):
        """Test embedding generation for a very large document."""
        # Create a very large document (500K characters)
        very_large_text = self._create_large_text(500000)
        
        document = self.LawDocument.create({
            'name': 'Very Large Test Document',
            'content': very_large_text,
            'law_type_id': self.law_type.id,
            'category_id': self.category.id,
            'document_type_ids': [(6, 0, [self.doc_type.id])],
            'language': 'en',
        })
        
        # Generate embeddings
        embedding_ids = document.generate_embeddings()
        
        # Check that embeddings were created
        self.assertTrue(embedding_ids, "No embeddings were created for the very large document")
        
        # Check that the document has embeddings
        self.assertTrue(document.has_embeddings, "Document should have embeddings")
        
        # Check the number of embeddings (should be many chunks)
        self.assertGreater(len(embedding_ids), 10, "Very large document should be split into many chunks")
        
        _logger.info(f"Created {len(embedding_ids)} embeddings for document with {len(very_large_text)} characters")

    def test_03_batch_embedding_generation(self):
        """Test batch embedding generation for multiple documents."""
        # Create multiple documents
        documents = []
        for i in range(5):
            # Create documents of varying sizes
            text_size = 10000 * (i + 1)
            document = self.LawDocument.create({
                'name': f'Batch Test Document {i+1}',
                'content': self._create_large_text(text_size),
                'law_type_id': self.law_type.id,
                'category_id': self.category.id,
                'document_type_ids': [(6, 0, [self.doc_type.id])],
                'language': 'en',
            })
            documents.append(document)
        
        # Get the document recordset
        document_recordset = self.LawDocument.browse([doc.id for doc in documents])
        
        # Generate embeddings in batch
        result = document_recordset.action_generate_embeddings_batch()
        
        # Check that all documents have embeddings
        for document in documents:
            document.invalidate_recordset()  # Refresh from database
            self.assertTrue(document.has_embeddings, f"Document {document.name} should have embeddings")
            self.assertGreater(document.embedding_count, 0, f"Document {document.name} should have embeddings")
        
        # Check the result message
        self.assertEqual(result['params']['title'], 'Embedding Generation Complete', 
                        "Batch embedding generation should complete successfully")
        
        # Log the result
        _logger.info(f"Batch embedding generation result: {result['params']['message']}")
