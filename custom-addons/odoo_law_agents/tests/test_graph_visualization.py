"""
Tests for the graph visualization functionality.
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import patch

from odoo.tests.common import TransactionCase


class TestGraphVisualization(TransactionCase):
    """Test the graph visualization functionality."""

    def setUp(self):
        super().setUp()
        # Create a test agent
        self.agent = self.env['law.agent'].create({
            'name': 'Test Agent',
            'agent_type': 'Gujarat Law',
            'state': 'active',
        })

    @patch('odoo.addons.odoo_law_agents.tools.graph_visualization.HAS_GRAPHVIZ', True)
    @patch('odoo.addons.odoo_law_agents.tools.graph_visualization.render_graph')
    def test_generate_graph_visualization(self, mock_render_graph):
        """Test generating a graph visualization."""
        # Mock the render_graph function to return a path
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_file.close()
        mock_render_graph.return_value = temp_file.name

        # Call the generate_graph_visualization method
        result = self.agent.generate_graph_visualization()

        # Check that the method was called with the correct parameters
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['tag'], 'display_notification')
        self.assertEqual(result['params']['title'], 'Success')
        self.assertIn('Graph visualization generated successfully', result['params']['message'])

        # Check that the attachment was created
        self.assertTrue(self.agent.graph_attachment_id)
        self.assertTrue(self.agent.graph_download_url)

        # Clean up
        os.unlink(temp_file.name)

    @patch('odoo.addons.odoo_law_agents.tools.graph_visualization.HAS_GRAPHVIZ', True)
    @patch('odoo.addons.odoo_law_agents.tools.graph_visualization.save_graph_to_folder')
    def test_save_graph_to_folder(self, mock_save_graph_to_folder):
        """Test saving a graph to a folder."""
        # Mock the save_graph_to_folder function to return a path
        temp_dir = tempfile.mkdtemp()
        temp_file = os.path.join(temp_dir, 'test_graph.png')
        mock_save_graph_to_folder.return_value = temp_file

        # Call the generate_graph_visualization method with save_to_folder=True
        result = self.agent.generate_graph_visualization(save_to_folder=True, folder_path=temp_dir)

        # Check that the method was called with the correct parameters
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['tag'], 'display_notification')
        self.assertEqual(result['params']['title'], 'Success')
        self.assertIn('Graph saved to', result['params']['message'])

        # Clean up
        os.rmdir(temp_dir)

    @patch('odoo.addons.odoo_law_agents.tools.graph_visualization.HAS_GRAPHVIZ', False)
    def test_graphviz_not_installed(self):
        """Test behavior when graphviz is not installed."""
        # Call the generate_graph_visualization method
        result = self.agent.generate_graph_visualization()

        # Check that the method returns an error notification
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['tag'], 'display_notification')
        self.assertEqual(result['params']['title'], 'Error')
        self.assertIn('Graph visualization utilities not available', result['params']['message'])

    @patch('odoo.addons.odoo_law_agents.langgraph.agent.create_agent')
    def test_agent_creation_error(self, mock_create_agent):
        """Test behavior when agent creation fails."""
        # Mock the create_agent function to return None
        mock_create_agent.return_value = None

        # Call the generate_graph_visualization method
        result = self.agent.generate_graph_visualization()

        # Check that the method returns an error notification
        self.assertEqual(result['type'], 'ir.actions.client')
        self.assertEqual(result['tag'], 'display_notification')
        self.assertEqual(result['params']['title'], 'Error')
        self.assertIn('Failed to create agent graph', result['params']['message'])
