from odoo.tests.common import TransactionCase, tagged
import logging

_logger = logging.getLogger(__name__)

@tagged('post_install', '-at_install')
class TestRAGSystem(TransactionCase):
    """Test the RAG (Retrieval-Augmented Generation) system."""

    def setUp(self):
        super(TestRAGSystem, self).setUp()
        # Create test documents
        self.land_law_doc = self.env['law.document'].create({
            'name': 'Property Rights',
            'document_type': 'land_law',
            'language': 'en',
            'content': """
            Property rights are legal rights or interests in land and fixtures or immovable property.
            Property rights include ownership, possession, use, and disposal of a particular item or piece of property.
            Property rights can be viewed as extensions of the human right to property.
            
            In common law, property rights are protected under Article 17 of the Universal Declaration of Human Rights,
            which recognizes the right to own property and states that no one may be arbitrarily deprived of property.
            
            Property rights are important because they provide a legal framework for allocating scarce resources.
            They define who owns what, who can do what with what they own, and who can prevent others from using their property.
            """
        })
        
        self.civil_law_doc = self.env['law.document'].create({
            'name': 'Contract Law',
            'document_type': 'civil_law',
            'language': 'en',
            'content': """
            Contract law is the body of law that relates to making and enforcing agreements.
            A contract is an agreement that a party can turn to a court to enforce.
            Contract law is the area of law that governs making contracts, carrying them out and fashioning a fair remedy when there's a breach.
            
            Anyone who conducts business uses contract law. Both companies and consumers use contracts when they buy and sell goods,
            when they license products or activities, for employment agreements, for insurance agreements and more.
            
            Contract law regulates the obligations established in a contract, a legally binding agreement between two or more parties.
            """
        })
        
        # Generate embeddings for the test documents
        from odoo.addons.odoo_law_agents.services.embedding_service import EmbeddingService
        self.embedding_service = EmbeddingService(self.env)
        
        self.embedding_service.create_embeddings(self.land_law_doc)
        self.embedding_service.create_embeddings(self.civil_law_doc)
        
        # Get the RAG service
        from odoo.addons.odoo_law_agents.services.rag_service import RAGService
        self.rag_service = RAGService(self.env)

    def test_01_retrieve_context(self):
        """Test retrieving context for a query."""
        # Retrieve context for a property rights query
        property_query = "What are property rights?"
        property_context = self.rag_service.retrieve_context(property_query)
        
        # Check that context was retrieved
        self.assertTrue(property_context, "Context should be retrieved for property rights query")
        
        # Check that the context contains relevant information
        self.assertIn("property", property_context.lower(), "Context should contain 'property'")
        self.assertIn("rights", property_context.lower(), "Context should contain 'rights'")
        
        # Retrieve context for a contract law query
        contract_query = "What is contract law?"
        contract_context = self.rag_service.retrieve_context(contract_query)
        
        # Check that context was retrieved
        self.assertTrue(contract_context, "Context should be retrieved for contract law query")
        
        # Check that the context contains relevant information
        self.assertIn("contract", contract_context.lower(), "Context should contain 'contract'")
        self.assertIn("law", contract_context.lower(), "Context should contain 'law'")
        
        # Check that the contexts are different
        self.assertNotEqual(property_context, contract_context, "Contexts should be different for different queries")

    def test_02_search_documents(self):
        """Test searching for documents."""
        # Search for property rights documents
        property_query = "What are property rights?"
        property_results = self.rag_service.search_documents(property_query)
        
        # Check that results were found
        self.assertTrue(property_results, "Results should be found for property rights query")
        
        # Check that the property rights document is in the results
        found = False
        for result in property_results:
            if result['document_id'] == self.land_law_doc.id:
                found = True
                break
        self.assertTrue(found, "Property rights document should be found in the results")
        
        # Search for contract law documents
        contract_query = "What is contract law?"
        contract_results = self.rag_service.search_documents(contract_query)
        
        # Check that results were found
        self.assertTrue(contract_results, "Results should be found for contract law query")
        
        # Check that the contract law document is in the results
        found = False
        for result in contract_results:
            if result['document_id'] == self.civil_law_doc.id:
                found = True
                break
        self.assertTrue(found, "Contract law document should be found in the results")

    def test_03_generate_response(self):
        """Test generating a response."""
        # This test is optional as it may require an LLM API
        # Skip if the LLM API is not configured
        if not self.env['ir.config_parameter'].sudo().get_param('odoo_law_agents.enable_llm', False):
            self.skipTest("LLM API is not configured")
        
        # Generate a response for a property rights query
        property_query = "What are property rights?"
        property_response = self.rag_service.generate_response(property_query)
        
        # Check that a response was generated
        self.assertTrue(property_response, "Response should be generated for property rights query")
        
        # Check that the response contains relevant information
        self.assertIn("property", property_response.lower(), "Response should contain 'property'")
        self.assertIn("rights", property_response.lower(), "Response should contain 'rights'")
        
        # Generate a response for a contract law query
        contract_query = "What is contract law?"
        contract_response = self.rag_service.generate_response(contract_query)
        
        # Check that a response was generated
        self.assertTrue(contract_response, "Response should be generated for contract law query")
        
        # Check that the response contains relevant information
        self.assertIn("contract", contract_response.lower(), "Response should contain 'contract'")
        self.assertIn("law", contract_response.lower(), "Response should contain 'law'")
        
        # Check that the responses are different
        self.assertNotEqual(property_response, contract_response, "Responses should be different for different queries")

    def test_04_bilingual_support(self):
        """Test bilingual support."""
        # Create a Gujarati document
        gujarati_doc = self.env['law.document'].create({
            'name': 'મિલકત અધિકારો',  # Property Rights in Gujarati
            'document_type': 'land_law',
            'language': 'gu',
            'content': """
            મિલકત અધિકારો એ જમીન અને ફિક્સચર્સ અથવા અચલ મિલકતમાં કાનૂની અધિકારો અથવા હિતો છે.
            મિલકત અધિકારોમાં કોઈ ચોક્કસ વસ્તુ અથવા મિલકતના ભાગની માલિકી, કબજો, ઉપયોગ અને નિકાલનો સમાવેશ થાય છે.
            મિલકત અધિકારોને માનવ અધિકારના વિસ્તરણ તરીકે જોઈ શકાય છે.
            """
        })
        
        # Generate embeddings for the Gujarati document
        self.embedding_service.create_embeddings(gujarati_doc)
        
        # Test with a Gujarati query
        gujarati_query = "મિલકત અધિકારો શું છે?"  # What are property rights? in Gujarati
        gujarati_results = self.rag_service.search_documents(gujarati_query)
        
        # Check that results were found
        self.assertTrue(gujarati_results, "Results should be found for Gujarati query")
        
        # Check that the Gujarati document is in the results
        found = False
        for result in gujarati_results:
            if result['document_id'] == gujarati_doc.id:
                found = True
                break
        self.assertTrue(found, "Gujarati document should be found in the results")
        
        # Test cross-lingual search (English query, Gujarati document)
        english_query = "What are property rights?"
        cross_results = self.rag_service.search_documents(english_query)
        
        # Check if the Gujarati document is in the results
        # This is optional as cross-lingual search may not be fully supported
        found = False
        for result in cross_results:
            if result['document_id'] == gujarati_doc.id:
                found = True
                break
        # We don't assert this as it depends on the embedding model's cross-lingual capabilities
        if found:
            _logger.info("Cross-lingual search works: English query found Gujarati document")
        else:
            _logger.info("Cross-lingual search limitation: English query did not find Gujarati document")

    def test_05_error_handling(self):
        """Test error handling."""
        # Test with an empty query
        empty_results = self.rag_service.search_documents("")
        self.assertEqual(empty_results, [], "No results should be found for empty query")
        
        # Test with a very short query
        short_query = "a"
        short_results = self.rag_service.search_documents(short_query)
        # This should not raise an exception, but might return empty results
        self.assertIsInstance(short_results, list, "Results should be a list even for short query")
        
        # Test with a very long query
        long_query = "a" * 10000  # Very long query
        long_results = self.rag_service.search_documents(long_query)
        # This should not raise an exception, but might return empty results
        self.assertIsInstance(long_results, list, "Results should be a list even for long query")
