<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Law Agents Security Category -->
        <record id="module_category_law_agents" model="ir.module.category">
            <field name="name">Law Agents</field>
            <field name="description">Manage law agents and documents</field>
            <field name="sequence">20</field>
        </record>

        <!-- Law Agents User Group -->
        <record id="group_law_agents_user" model="res.groups">
            <field name="name">User</field>
            <field name="category_id" ref="module_category_law_agents"/>
            <field name="implied_ids" eval="[(4, ref('base.group_user'))]"/>
        </record>

        <!-- Law Agents Manager Group -->
        <record id="group_law_agents_manager" model="res.groups">
            <field name="name">Manager</field>
            <field name="category_id" ref="module_category_law_agents"/>
            <field name="implied_ids" eval="[(4, ref('group_law_agents_user'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
</odoo>
