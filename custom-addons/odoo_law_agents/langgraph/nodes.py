"""Node implementations for LangGraph agents."""

import logging
import time
from typing import Dict, Any

from odoo.addons.iap.tools import iap_tools
from .state import LawAgentState, CaseState
from ..tools.simple_tracing import MockTracer

_logger = logging.getLogger(__name__)

# Default Odoo LLM Gateway endpoint
DEFAULT_OLG_ENDPOINT = "https://olg.api.odoo.com"

def get_env(self=None):
    """Get an Odoo environment dynamically.

    This function creates a new Odoo environment that can be used for API calls
    when no environment is passed from the agent model. It's particularly useful
    for LLM API calls that require an environment.

    Args:
        self: Optional self reference from an Odoo model. If provided, we'll use its environment.

    Returns:
        An Odoo environment with SUPERUSER_ID, or None if it fails.
    """
    # If self is provided and has an environment, use it directly
    if self is not None and hasattr(self, 'env'):
        _logger.info("Using environment from provided self reference")
        return self.env

    try:
        import odoo
        from odoo import api, SUPERUSER_ID
        import os
        db_name = None

        # Alternative methods to get the database name if needed in other environments
        if not db_name:
            # Try to get the database name from the current thread
            try:
                from threading import current_thread
                if hasattr(current_thread(), 'dbname'):
                    db_name = current_thread().dbname
                    _logger.info(f"Got database name from current thread: {db_name}")
            except Exception as e:
                _logger.warning(f"Could not get database name from current thread: {str(e)}")

            # Try to get it from environment variables
            if not db_name:
                db_name = os.environ.get('PGDATABASE')
                if db_name:
                    _logger.info(f"Got database name from environment variable: {db_name}")

            # Try to get it from the Odoo configuration
            if not db_name:
                try:
                    from odoo.tools import config
                    db_name = config.get('db_name')
                    if db_name:
                        _logger.info(f"Got database name from Odoo config: {db_name}")
                except Exception as e:
                    _logger.warning(f"Error getting database name from config: {str(e)}")

            # Try to get it from the registry
            if not db_name:
                try:
                    db_name = odoo.modules.registry.Registry._current_db
                    if db_name:
                        _logger.info(f"Got database name from registry._current_db: {db_name}")
                    else:
                        # Get the first available database
                        db_list = odoo.service.db.list_dbs(force=True)
                        if db_list:
                            db_name = db_list[0]
                            _logger.info(f"Got database name from list_dbs (first available): {db_name}")
                except Exception as e:
                    _logger.warning(f"Error getting database from registry: {str(e)}")

        if not db_name:
            _logger.error("Could not determine database name")
            return None

        _logger.info(f"Using database: {db_name}")

        # Get the registry for the database
        registry = odoo.modules.registry.Registry(db_name)
        registry.check_signaling()

        # Create a new environment
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            return env
    except ImportError as e:
        _logger.error(f"Error importing Odoo modules: {str(e)}")
        return None
    except Exception as e:
        _logger.error(f"Error creating Odoo environment: {str(e)}")
        return None

def call_llm(prompt: str, conversation_history=None, env=None, use_mock=False, agent_id=None, state=None):
    """Call the Odoo LLM service using the controller endpoint.

    Args:
        prompt: The prompt to send to the LLM.
        conversation_history: Optional conversation history for context.
        env: Optional Odoo environment for database access.
        use_mock: If True, return a mock response for testing (default: False).
        agent_id: Optional agent ID to use a specific agent configuration.
        state: Optional state dictionary from the langgraph agent.

    Returns:
        A standardized response dictionary with the following keys:
        - status: 'success' or 'error'
        - content: The LLM response content or error message
    """
    # Default fallback responses for different scenarios
    fallback_responses = {
        "general": {
            "status": "error",
            "content": "I apologize, but I'm currently unable to process your request due to a technical issue. "
                       "Please try again later or contact support if the problem persists."
        },
        "too_long": {
            "status": "error",
            "content": "I apologize, but your prompt is too long. Please try to express your question more concisely."
        },
        "limit_reached": {
            "status": "error",
            "content": "You have reached the maximum number of requests for this service. Please try again later."
        },
        "mock": {
            "status": "success",
            "content": "This is a mock response for testing purposes. Your prompt was: " + prompt[:50] + "..."
        }
    }

    # If mock mode is enabled, return a mock response
    if use_mock:
        _logger.info(f"Using mock LLM response for prompt: {prompt[:50]}...")

        # Create a more specific mock response based on the prompt content
        if "gujarat" in prompt.lower():
            return {"status": "success", "content": "gujarat"}
        elif "civil" in prompt.lower():
            return {"status": "success", "content": "civil"}
        elif "land" in prompt.lower() or "property" in prompt.lower() or "registration" in prompt.lower():
            return {"status": "success", "content": "land"}
        else:
            return {"status": "success", "content": "retrieval"}

    # Try to use the controller endpoint if we have an environment
    try:
        # If no environment is provided, try to get one dynamically
        if env is None:
            _logger.info("No environment provided, trying to get one dynamically")

            # Try to get self reference from the state
            self_ref = state.get("self_ref") if state else None
            if self_ref is not None:
                _logger.info("Using self reference from state for environment access")

            # Get environment using self reference if available
            env = get_env(self_ref)

            if env is not None:
                _logger.info("Successfully obtained environment dynamically")
                # Get the database name for logging
                try:
                    db_name = env.cr.dbname
                    _logger.info(f"Using database: {db_name}")
                except Exception:
                    pass
            else:
                _logger.warning("Failed to get environment dynamically")

        # First, try to use the controller endpoint
        if env is not None:
            try:
                _logger.info("Using controller endpoint for LLM call")

                # Call the controller endpoint directly
                try:
                    # Import the controller
                    from ..controllers.main import LawAgentController

                    # Create an instance of the controller
                    controller = LawAgentController()

                    # Call the controller method directly
                    response = controller.generate_response(
                        prompt=prompt,
                        conversation_history=conversation_history,
                        agent_id=agent_id
                    )

                    # Handle the response
                    if response.get('status') == 'success':
                        return {"status": "success", "content": response.get('content', '')}
                    elif response.get('status') == 'error_prompt_too_long':
                        _logger.warning("Prompt too long error from controller")
                        return fallback_responses["too_long"]
                    elif response.get('status') == 'limit_call_reached':
                        _logger.warning("Limit reached error from controller")
                        return fallback_responses["limit_reached"]
                    else:
                        _logger.error(f"Error from controller: {response.get('error', 'Unknown error')}")
                        return {"status": "error", "content": response.get('error', 'Unknown error')}
                except ImportError as e:
                    _logger.error(f"Error importing controller: {str(e)}")
                    _logger.info("Falling back to direct API call")
                except Exception as e:
                    _logger.error(f"Error calling controller: {str(e)}")
                    _logger.info("Falling back to direct API call")
            except Exception as e:
                _logger.error(f"Error setting up controller call: {str(e)}")
                _logger.info("Falling back to direct API call")

        # If controller call failed or we still don't have an environment, use a direct API call
        if env is None:
            _logger.warning("No environment available, using direct API call with default endpoint")
            # Use the default endpoint
            olg_api_endpoint = DEFAULT_OLG_ENDPOINT
            database_id = None

            # Make the API call with standard parameters
            try:
                # Use history parameter for direct calls (for backward compatibility)
                params = {
                    'prompt': prompt,
                    'history': conversation_history or [],
                }

                # Add database_id if available
                if database_id:
                    params['database_id'] = database_id

                response = iap_tools.iap_jsonrpc(
                    olg_api_endpoint + "/api/olg/1/chat",
                    params=params,
                    timeout=30
                )

                # Handle the response based on status
                if response['status'] == 'success':
                    return {"status": "success", "content": response['content']}
                elif response['status'] == 'error_prompt_too_long':
                    _logger.warning("Prompt too long error from LLM API")
                    return fallback_responses["too_long"]
                elif response['status'] == 'limit_call_reached':
                    _logger.warning("Limit reached error from LLM API")
                    return fallback_responses["limit_reached"]
                else:
                    _logger.error(f"Unknown error from LLM API: {response.get('status', 'unknown')}")
                    return fallback_responses["general"]
            except Exception as e:
                _logger.error(f"Error calling LLM API directly: {str(e)}")
                return {"status": "error", "content": f"Failed to call LLM API: {str(e)}"}
        else:
            # If we have an environment and controller call failed, use the environment with direct API call
            # Get the configuration parameters from the environment
            try:
                IrConfigParameter = env["ir.config_parameter"].sudo()
                olg_api_endpoint = IrConfigParameter.get_param("web_editor.olg_api_endpoint", DEFAULT_OLG_ENDPOINT)
                database_id = IrConfigParameter.get_param("database.uuid")
                _logger.info(f"Using environment-based API endpoint: {olg_api_endpoint}")
            except Exception as e:
                _logger.error(f"Error getting API endpoint from environment: {str(e)}")
                olg_api_endpoint = DEFAULT_OLG_ENDPOINT
                database_id = None
                _logger.warning(f"Falling back to default API endpoint: {olg_api_endpoint}")

            # Make the API call with standard parameters
            try:
                # Use conversation_history parameter for environment-based calls
                params = {
                    'prompt': prompt,
                    'conversation_history': conversation_history or [],
                }

                # Add database_id if available
                if database_id:
                    params['database_id'] = database_id

                response = iap_tools.iap_jsonrpc(
                    olg_api_endpoint + "/api/olg/1/chat",
                    params=params,
                    timeout=30
                )

                # Handle the response based on status
                if response['status'] == 'success':
                    return {"status": "success", "content": response['content']}
                elif response['status'] == 'error_prompt_too_long':
                    _logger.warning("Prompt too long error from LLM API")
                    return fallback_responses["too_long"]
                elif response['status'] == 'limit_call_reached':
                    _logger.warning("Limit reached error from LLM API")
                    return fallback_responses["limit_reached"]
                else:
                    _logger.error(f"Unknown error from LLM API: {response.get('status', 'unknown')}")
                    return fallback_responses["general"]
            except iap_tools.InsufficientCreditError:
                error_msg = "Insufficient IAP credits to use the LLM service"
                _logger.error(error_msg)
                return {"status": "error", "content": error_msg}
            except Exception as e:
                _logger.error(f"Error calling LLM with environment: {str(e)}")
                return fallback_responses["general"]
    except Exception as e:
        _logger.error(f"Unexpected error in call_llm: {str(e)}")
        return fallback_responses["general"]

def create_environment():
    """Create an Odoo environment."""
    _logger.info("Attempting to create an Odoo environment")

    try:
        import odoo
        from odoo import api, SUPERUSER_ID
        import os
        db_name = None

        # Try to get the database name from the environment
        if 'PGDATABASE' in os.environ:
            db_name = os.environ['PGDATABASE']
            _logger.info(f"Got database name from environment: {db_name}")

        # If not found, try to get it from the Odoo registry
        if not db_name:
            try:
                db_name = odoo.service.db.list_dbs(True)[0]
                _logger.info(f"Got database name from registry: {db_name}")
            except Exception as e:
                _logger.warning(f"Failed to get database name from registry: {str(e)}")

        # If still not found, try to get it from the Odoo configuration
        if not db_name:
            try:
                from odoo.tools import config
                db_name = config.get('db_name')
                if db_name:
                    _logger.info(f"Got database name from Odoo config: {db_name}")
            except Exception as e:
                _logger.warning(f"Failed to get database name from config: {str(e)}")

        # If we have a database name, create an environment
        if db_name:
            registry = odoo.modules.registry.Registry(db_name)
            with registry.cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                _logger.info("Successfully created an Odoo environment")
                return env

        _logger.error("Failed to get database name, cannot create environment")
        return None

    except Exception as e:
        _logger.exception(f"Exception creating environment: {str(e)}")
        return None

def Web_Search(state: LawAgentState) -> Dict[str, Any]:
    """Web Search node."""
    _logger.info("In node: Web Search")

    # Skip if web search is disabled
    if not state.get("use_online_search", True):
        _logger.info("Web search disabled, skipping")
        return state

    # Get the query from the state
    query = state["query"]

    # Determine the query type
    query_type = state.get("query_type", "general")

    # Prepare the search query based on the query type
    search_query = query
    if query_type == "gujarat":
        search_query = f"Gujarat law {query}"
    elif query_type == "land":
        search_query = f"land law India {query}"
    elif query_type == "civil":
        search_query = f"civil law India {query}"

    try:
        # Import the DuckDuckGo search tool
        from ..tools.duckduckgo_search import DuckDuckGoSearch

        # Create a DuckDuckGo search client
        ddg = DuckDuckGoSearch(max_results=5, timeout=10)

        # Perform the search
        _logger.info(f"Searching for: {search_query}")
        search_results = ddg.search(search_query, region="in-en")

        # Check if we got any results
        if not search_results:
            _logger.warning(f"No search results found for '{search_query}', trying a more general search")
            search_results = ddg.search(query, region="in-en")

        # Update the state with the search results
        state["search_results"] = search_results

        # Log the number of results
        _logger.info(f"Found {len(search_results)} search results")

        return state

    except Exception as e:
        _logger.exception(f"Error in Web Search: {str(e)}")
        state["error"] = f"Error in web search: {str(e)}"
        return state

def RAG(state: LawAgentState) -> Dict[str, Any]:
    """RAG node."""
    _logger.info("In node: RAG")

    # Skip if document embeddings are disabled
    if not state.get("use_document_embeddings", True):
        _logger.info("Document embeddings disabled, skipping")
        return state

    # Get the query from the state
    query = state["query"]

    # Determine the query type
    query_type = state.get("query_type", "general")

    # Get the environment from the state
    env = state.get("env")

    # If we don't have an environment, try to create one
    if env is None:
        _logger.warning("No environment provided for RAG, attempting to create one")
        env = create_environment()

    # If we still don't have an environment, return an error
    if env is None:
        _logger.error("Failed to get or create an environment for RAG")
        state["error"] = "Failed to get or create an environment for RAG"
        return state

    try:
        # Import the embedding service
        from ..services.embedding_service import EmbeddingService

        # Create an embedding service
        service = EmbeddingService(env)

        # Search for similar documents
        _logger.info(f"Searching for documents similar to: {query}")
        similar_docs = service.search_similar(query, limit=5, min_score=0.3)

        # Update the state with the documents
        state["documents"] = similar_docs

        # Log the number of documents
        _logger.info(f"Found {len(similar_docs)} similar documents")

        # Combine the documents and search results into a context
        context = ""

        # Add documents to context
        documents = state.get("documents", [])
        if documents:
            context += "Relevant documents:\n\n"
            for i, doc in enumerate(documents, 1):
                context += f"{i}. {doc['name']}: {doc['content'][:500]}...\n\n"

        # Add search results to context
        search_results = state.get("search_results", [])
        if search_results:
            context += "Web search results:\n\n"
            for i, result in enumerate(search_results, 1):
                context += f"{i}. {result['title']}\n"
                context += f"   {result['snippet']}\n"
                context += f"   Source: {result['source']}\n\n"

        # Update the state with the context
        state["context"] = context

        return state

    except Exception as e:
        _logger.exception(f"Error in RAG: {str(e)}")
        state["error"] = f"Error in RAG: {str(e)}"
        return state

def Query_Classification(state: LawAgentState) -> Dict[str, Any]:
    """Query Classification node."""
    _logger.info("In node: Query Classification")

    # Get the query from the state
    query = state["query"]

    # If mock mode is enabled, return a mock classification
    if state.get("use_mock", False):
        _logger.info("Using mock mode for Query Classification")

        # Determine a mock classification based on keywords in the query
        if "gujarat" in query.lower() or "ahmedabad" in query.lower() or "surat" in query.lower():
            query_type = "gujarat"
        elif "land" in query.lower() or "property" in query.lower() or "real estate" in query.lower():
            query_type = "land"
        elif "civil" in query.lower() or "contract" in query.lower() or "liability" in query.lower():
            query_type = "civil"
        else:
            query_type = "general"

        _logger.info(f"Mock classification: {query_type}")
        state["query_type"] = query_type
        return state

    # Prepare the prompt for classification
    prompt = f"""
    Classify the following legal query into one of these categories:
    - "gujarat": Queries specifically about Gujarat state laws or legal matters in Gujarat
    - "land": Queries about land law, property rights, real estate, etc.
    - "civil": Queries about civil law, contracts, torts, etc.
    - "general": General legal queries that don't fit the above categories

    Query: {query}

    Respond with ONLY the category name, nothing else.
    """

    # Get the environment from the state
    env = state.get("env")

    # Call the LLM
    response = call_llm(
        prompt=prompt,
        conversation_history=[],
        env=env,
        use_mock=state.get("use_mock", False),
        state=state
    )

    if response["status"] != "success":
        _logger.error(f"Error in Query Classification: {response.get('content', 'Unknown error')}")
        state["error"] = f"Error in query classification: {response.get('content', 'Unknown error')}"
        return state

    # Extract the query type from the response
    query_type = response["content"].strip().lower()

    # Validate the query type
    valid_types = ["gujarat", "land", "civil", "general"]
    if query_type not in valid_types:
        _logger.warning(f"Invalid query type: {query_type}, defaulting to 'general'")
        query_type = "general"

    # Update the state with the query type
    state["query_type"] = query_type
    _logger.info(f"Query classified as: {query_type}")

    return state

def Gujarat_Law_Agent(state: LawAgentState) -> Dict[str, Any]:
    """Gujarat Law Agent node."""
    _logger.info("In node: Gujarat Law Agent")

    # Get the query from the state
    query = state["query"]

    # If mock mode is enabled, return a mock response
    if state.get("use_mock", False):
        _logger.info("Using mock mode for Gujarat Law Agent")
        mock_response = f"This is a mock response for a Gujarat-specific law query about '{query}':\n\n"
        mock_response += "In Gujarat, land registration follows the Gujarat Land Revenue Code. "
        mock_response += "The process is administered by the Department of Land Records and involves registration at the local Sub-Registrar's office. "
        mock_response += "Gujarat has implemented e-Dhara for computerized land records management."

        # Create mock sources
        mock_sources = [
            {"type": "mock", "name": "Gujarat Land Revenue Code", "description": "Generated for testing purposes"},
            {"type": "mock", "name": "e-Dhara Portal Documentation", "description": "No actual lookup performed"}
        ]

        # Update the state
        state["response"] = mock_response
        state["sources"] = mock_sources
        return state

    # Prepare the prompt for the Gujarat Law Agent
    prompt = f"""
    You are a legal expert specializing in Gujarat state laws. Answer the following query based on your knowledge of Gujarat's legal system:

    Query: {query}

    Context: {state.get('context', '')}

    Provide a comprehensive and accurate response focusing specifically on Gujarat's legal framework.
    """

    # Get the environment from the state
    env = state.get("env")

    # Call the LLM
    response = call_llm(
        prompt=prompt,
        conversation_history=[],
        env=env,
        use_mock=state.get("use_mock", False),
        state=state
    )

    if response["status"] != "success":
        _logger.error(f"Error in Gujarat Law Agent: {response.get('content', 'Unknown error')}")
        state["error"] = f"Error in Gujarat Law Agent: {response.get('content', 'Unknown error')}"
        return state

    # Update the state with the response
    state["response"] = response["content"]

    # Prepare sources
    sources = []

    # Add document sources
    documents = state.get("documents", [])
    for doc in documents:
        sources.append({"type": "document", "id": doc.get("id"), "name": doc.get("name", "Unnamed")})

    # Add web search sources
    search_results = state.get("search_results", [])
    for result in search_results:
        sources.append({"type": "web", "name": result.get("title", "Unknown"),
                       "link": result.get("link", ""), "source": result.get("source", "Unknown")})

    # Add a Gujarat Law Agent source
    sources.append({"type": "agent", "name": "Gujarat Law Agent", "description": "Specialized in Gujarat state laws"})

    # Update the state with the sources
    state["sources"] = sources

    return state


def Retrival_tools(state: LawAgentState) -> Dict[str, Any]:
    """Retrieval Tools node."""
    _logger.info("In node: Retrieval Tools")

    # Skip if document embeddings are disabled
    if not state.get("use_document_embeddings", True):
        _logger.info("Document embeddings disabled, skipping")
        return state

    # Get the query from the state
    query = state["query"]

    # Get the environment from the state
    env = state.get("env")

    # If we don't have an environment, try to create one
    if env is None:
        _logger.warning("No environment provided for Retrieval Tools, attempting to create one")
        env = create_environment()

    # If we still don't have an environment, return an error
    if env is None:
        _logger.error("Failed to get or create an environment for Retrieval Tools")
        state["error"] = "Failed to get or create an environment for Retrieval Tools"
        return state

    try:
        # Import the embedding service
        from ..services.embedding_service import EmbeddingService

        # Create an embedding service
        service = EmbeddingService(env)

        # Search for similar documents
        _logger.info(f"Searching for documents similar to: {query}")
        similar_docs = service.search_similar(query, limit=5, min_score=0.3)

        # Update the state with the documents
        state["documents"] = similar_docs

        # Log the number of documents
        _logger.info(f"Found {len(similar_docs)} similar documents")

        return state

    except Exception as e:
        _logger.exception(f"Error in Retrieval Tools: {str(e)}")
        state["error"] = f"Error in Retrieval Tools: {str(e)}"
        return state


def Get_Suitable_PDF_Results(state: LawAgentState) -> Dict[str, Any]:
    """Get Suitable PDF Results node."""
    _logger.info("In node: Get Suitable PDF Results")

    # Combine the documents and search results into a context
    context = ""

    # Add documents to context
    documents = state.get("documents", [])
    if documents:
        context += "Relevant documents:\n\n"
        for i, doc in enumerate(documents, 1):
            context += f"{i}. {doc['name']}: {doc['content'][:500]}...\n\n"

    # Add search results to context
    search_results = state.get("search_results", [])
    if search_results:
        context += "Web search results:\n\n"
        for i, result in enumerate(search_results, 1):
            context += f"{i}. {result['title']}\n"
            context += f"   {result['snippet']}\n"
            context += f"   Source: {result['source']}\n\n"

    # Update the state with the context
    state["context"] = context

    return state


def Civil_Law_Agent(state: LawAgentState) -> Dict[str, Any]:
    """Civil Law Agent node."""
    _logger.info("In node: Civil Law Agent")

    # Get the query from the state
    query = state["query"]

    # If mock mode is enabled, return a mock response
    if state.get("use_mock", False):
        _logger.info("Using mock mode for Civil Law Agent")
        mock_response = f"This is a mock response for a civil law query about '{query}':\n\n"
        mock_response += "Civil law governs relationships between individuals and organizations. "
        mock_response += "It covers areas such as contracts, property rights, family matters, and personal injury claims. "
        mock_response += "Unlike criminal law, civil law typically results in monetary compensation rather than imprisonment."

        # Create mock sources
        mock_sources = [
            {"type": "mock", "name": "Indian Civil Procedure Code", "description": "Generated for testing purposes"},
            {"type": "mock", "name": "Contract Law Handbook", "description": "No actual lookup performed"}
        ]

        # Update the state
        state["response"] = mock_response
        state["sources"] = mock_sources
        return state

    # Prepare the prompt for the Civil Law Agent
    prompt = f"""
    You are a legal expert specializing in civil law. Answer the following query based on your knowledge of civil law principles and the provided context:

    Query: {query}

    Context: {state.get('context', '')}

    Provide a comprehensive and accurate response focusing specifically on civil law principles and applications.
    """

    # Get the environment from the state
    env = state.get("env")

    # Call the LLM
    response = call_llm(
        prompt=prompt,
        conversation_history=[],
        env=env,
        use_mock=state.get("use_mock", False),
        state=state
    )

    if response["status"] != "success":
        _logger.error(f"Error in Civil Law Agent: {response.get('content', 'Unknown error')}")
        state["error"] = f"Error in Civil Law Agent: {response.get('content', 'Unknown error')}"
        return state

    # Update the state with the response
    state["response"] = response["content"]

    # Prepare sources
    sources = []

    # Add document sources
    documents = state.get("documents", [])
    for doc in documents:
        sources.append({"type": "document", "id": doc.get("id"), "name": doc.get("name", "Unnamed")})

    # Add web search sources
    search_results = state.get("search_results", [])
    for result in search_results:
        sources.append({"type": "web", "name": result.get("title", "Unknown"),
                       "link": result.get("link", ""), "source": result.get("source", "Unknown")})

    # Add a Civil Law Agent source
    sources.append({"type": "agent", "name": "Civil Law Agent", "description": "Specialized in civil law principles"})

    # Update the state with the sources
    state["sources"] = sources

    return state


def Land_Law_Agent(state: LawAgentState) -> Dict[str, Any]:
    """Land Law Agent node."""
    _logger.info("In node: Land Law Agent")

    # Get the query from the state
    query = state["query"]

    # If mock mode is enabled, return a mock response
    if state.get("use_mock", False):
        _logger.info("Using mock mode for Land Law Agent")
        mock_response = f"This is a mock response for a land law query about '{query}':\n\n"
        mock_response += "Land registration is the process of recording and maintaining information about the ownership, "
        mock_response += "boundaries, and rights related to land and property. The process typically involves:\n\n"
        mock_response += "1. **Initial Application**: Submit documents proving ownership\n"
        mock_response += "2. **Verification**: Officials verify the authenticity of documents\n"
        mock_response += "3. **Survey**: Property boundaries are surveyed and mapped\n"
        mock_response += "4. **Public Notice**: Notification to allow for objections\n"
        mock_response += "5. **Registration**: Final recording in the land registry\n\n"
        mock_response += "The registration provides legal protection against disputes and facilitates property transactions."

        # Create mock sources
        mock_sources = [
            {"type": "mock", "name": "Land Registration Act", "description": "Generated for testing purposes"},
            {"type": "mock", "name": "Property Law Handbook", "description": "No actual lookup performed"}
        ]

        # Update the state
        state["response"] = mock_response
        state["sources"] = mock_sources
        return state

    # Prepare the prompt for the Land Law Agent
    prompt = f"""
    You are a legal expert specializing in land law. Answer the following query based on your knowledge of land law principles and the provided context:

    Query: {query}

    Context: {state.get('context', '')}

    Provide a comprehensive and accurate response focusing specifically on land law principles, property rights, and land registration processes.
    """

    # Get the environment from the state
    env = state.get("env")

    # Call the LLM
    response = call_llm(
        prompt=prompt,
        conversation_history=[],
        env=env,
        use_mock=state.get("use_mock", False),
        state=state
    )

    if response["status"] != "success":
        _logger.error(f"Error in Land Law Agent: {response.get('content', 'Unknown error')}")
        state["error"] = f"Error in Land Law Agent: {response.get('content', 'Unknown error')}"
        return state

    # Update the state with the response
    state["response"] = response["content"]

    # Prepare sources
    sources = []

    # Add document sources
    documents = state.get("documents", [])
    for doc in documents:
        sources.append({"type": "document", "id": doc.get("id"), "name": doc.get("name", "Unnamed")})

    # Add web search sources
    search_results = state.get("search_results", [])
    for result in search_results:
        sources.append({"type": "web", "name": result.get("title", "Unknown"),
                       "link": result.get("link", ""), "source": result.get("source", "Unknown")})

    # Add a Land Law Agent source
    sources.append({"type": "agent", "name": "Land Law Agent", "description": "Specialized in land law principles"})

    # Update the state with the sources
    state["sources"] = sources

    return state


def Generate_Response(state: LawAgentState) -> Dict[str, Any]:
    """Generate Response node."""
    _logger.info("In node: Generate Response")

    # Record end time
    state["end_time"] = time.time()
    state["duration"] = state["end_time"] - state["start_time"]

    # If there's already a response, just return it
    if "response" in state:
        # Make sure we return the response and sources
        return {
            "response": state["response"],
            "sources": state.get("sources", [])
        }


class LLMNode:
    """Base class for LLM-based nodes in the case analysis graph."""

    def __init__(self, env=None):
        self.env = env
        self.name = "LLMNode"
        self.description = "Base LLM Node"
        self.system_prompt = "You are a helpful assistant."

    def _call_llm(self, prompt, state):
        """Call the LLM with the given prompt and state."""
        _logger.info(f"Calling LLM from {self.name}")

        # Use the call_llm function from this module
        # The call_llm function is defined at the top of this file

        # Use the call_llm function from the module
        response = call_llm(
            prompt=prompt,
            conversation_history=state.messages if hasattr(state, 'messages') else [],
            env=self.env,
            use_mock=state.use_mock if hasattr(state, 'use_mock') else False,
            state={"self_ref": self}
        )

        if response["status"] != "success":
            _logger.error(f"Error in LLM call from {self.name}: {response.get('content', 'Unknown error')}")
            return f"Error: {response.get('content', 'Unknown error')}"

        return response["content"]

    def run(self, state):
        """Run the node with the given state."""
        raise NotImplementedError("Subclasses must implement run()")


class RAGNode:
    """Base class for RAG-based nodes in the case analysis graph."""

    def __init__(self, env=None):
        self.env = env
        self.name = "RAGNode"
        self.description = "Base RAG Node"

    def _search_documents(self, query, state):
        """Search for relevant documents."""
        _logger.info(f"Searching documents from {self.name} for query: {query}")

        # Skip if no environment is available
        if not self.env:
            _logger.warning(f"No environment available for {self.name}, skipping document search")
            return []

        try:
            # Import the embedding service
            from ..services.embedding_service import EmbeddingService

            # Create an embedding service
            service = EmbeddingService(self.env)

            # Search for similar documents
            similar_docs = service.search_similar(query, limit=5, min_score=0.3)
            _logger.info(f"Found {len(similar_docs)} similar documents")

            return similar_docs
        except Exception as e:
            _logger.error(f"Error searching documents: {str(e)}")
            return []

    def run(self, state):
        """Run the node with the given state."""
        raise NotImplementedError("Subclasses must implement run()")


class WebSearchNode:
    """Base class for web search nodes in the case analysis graph."""

    def __init__(self, env=None):
        self.env = env
        self.name = "WebSearchNode"
        self.description = "Base Web Search Node"

    def _search_web(self, query, state):
        """Search the web for relevant information."""
        _logger.info(f"Searching web from {self.name} for query: {query}")

        # Skip if web search is disabled
        if hasattr(state, 'use_online_search') and not state.use_online_search:
            _logger.info(f"Web search disabled for {self.name}, skipping")
            return []

        try:
            # Import the DuckDuckGo search tool
            from ..tools.duckduckgo_search import DuckDuckGoSearch

            # Create a DuckDuckGo search client
            ddg = DuckDuckGoSearch(max_results=5, timeout=10)

            # Determine the language for region setting
            language = state.language if hasattr(state, 'language') else 'en'
            region = "in-en"  # Default to India-English

            if language == 'gu':
                # For Gujarati, still use India region but note it's Gujarati
                _logger.info("Using Gujarati language for search")
            elif language == 'hi':
                # For Hindi, still use India region but note it's Hindi
                _logger.info("Using Hindi language for search")

            # Perform the search
            search_results = ddg.search(query, region=region)
            _logger.info(f"Web search returned {len(search_results)} results")

            return search_results
        except Exception as e:
            _logger.error(f"Error searching web: {str(e)}")
            return []

    def run(self, state):
        """Run the node with the given state."""
        raise NotImplementedError("Subclasses must implement run()")
