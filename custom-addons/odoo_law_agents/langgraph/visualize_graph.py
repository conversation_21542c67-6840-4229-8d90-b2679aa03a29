#!/usr/bin/env python3
"""
<PERSON>ript to visualize the LangGraph workflow for the case analysis agent.
"""

import os
import logging
from case_agent import create_case_analysis_graph

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def visualize_graph():
    """Visualize the case analysis graph."""
    logger.info("Creating case analysis graph for visualization")
    
    # Create the graph
    app = create_case_analysis_graph()
    
    # Get the graph for visualization
    graph = app.get_graph(xray=True)
    
    # Generate Mermaid diagram
    try:
        mermaid_code = graph.to_mermaid()
        logger.info("Generated Mermaid diagram code")
        
        # Save to file
        output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "visualizations")
        os.makedirs(output_dir, exist_ok=True)
        
        output_file = os.path.join(output_dir, "case_analysis_graph.md")
        with open(output_file, "w") as f:
            f.write("# Case Analysis Graph\n\n")
            f.write("```mermaid\n")
            f.write(mermaid_code)
            f.write("\n```\n")
        
        logger.info(f"Saved Mermaid diagram to {output_file}")
        
        # Print the Mermaid code for easy copying
        print("\nMermaid Diagram Code:")
        print("```mermaid")
        print(mermaid_code)
        print("```")
        
        # Generate URL for Mermaid Live Editor
        mermaid_live_url = f"https://mermaid.live/edit#pako:eNp{mermaid_code}"
        logger.info(f"Mermaid Live Editor URL: {mermaid_live_url}")
        
    except Exception as e:
        logger.error(f"Error generating Mermaid diagram: {str(e)}")
        
        # Try to get a simple representation
        try:
            nodes = list(graph.nodes.keys())
            edges = []
            for node, targets in graph.edges.items():
                for target in targets:
                    edges.append(f"{node} -> {target}")
            
            logger.info(f"Graph nodes: {nodes}")
            logger.info(f"Graph edges: {edges}")
            
            # Create a simple text representation
            output_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), "visualizations")
            os.makedirs(output_dir, exist_ok=True)
            
            output_file = os.path.join(output_dir, "case_analysis_graph.txt")
            with open(output_file, "w") as f:
                f.write("Case Analysis Graph\n\n")
                f.write("Nodes:\n")
                for node in nodes:
                    f.write(f"- {node}\n")
                f.write("\nEdges:\n")
                for edge in edges:
                    f.write(f"- {edge}\n")
            
            logger.info(f"Saved simple graph representation to {output_file}")
            
        except Exception as inner_e:
            logger.error(f"Error creating simple graph representation: {str(inner_e)}")

if __name__ == "__main__":
    visualize_graph()
