"""
Simple visualization for the case analysis workflow.
"""

import logging
import os
import tempfile
import urllib.parse
import urllib.request
import base64

_logger = logging.getLogger(__name__)

def generate_mermaid_diagram():
    """Generate a Mermaid diagram of the case analysis workflow."""

    # Define the nodes and edges of the workflow
    nodes = [
        "research",
        "online_research",
        "background",
        "issues",
        "evidence",
        "principles",
        "judgment",
        "full_analysis",
        "END"
    ]

    edges = [
        ("research", "online_research"),
        ("online_research", "background"),
        ("background", "issues"),
        ("issues", "evidence"),
        ("evidence", "principles"),
        ("principles", "judgment"),
        ("judgment", "full_analysis"),
        ("full_analysis", "END")
    ]

    # Generate the Mermaid code
    mermaid_code = "graph TD;\n"

    # Add nodes
    for node in nodes:
        mermaid_code += f"    {node}[{node}];\n"

    # Add edges
    for source, target in edges:
        mermaid_code += f"    {source} --> {target};\n"

    return mermaid_code

def save_workflow_visualization(format='html'):
    """Save the workflow visualization to a file.

    Args:
        format (str): The format to save the visualization in ('png' or 'html').
                      Note: 'png' is no longer supported directly due to API restrictions.
                      All requests will use HTML format.

    Returns:
        str: The path to the saved file.
    """

    # Generate the Mermaid diagram
    mermaid_code = generate_mermaid_diagram()

    # Create a temporary directory if it doesn't exist
    temp_dir = tempfile.gettempdir()

    # Always use HTML format due to restrictions with Mermaid.ink API
    # Create a simple HTML to display the diagram
    html_content = f"""
    <html>
    <head>
        <title>Case Analysis Workflow</title>
        <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
        <script>
            mermaid.initialize({{ startOnLoad: true }});
        </script>
        <style>
            body {{
                font-family: Arial, sans-serif;
                margin: 20px;
            }}
            h1 {{
                color: #333;
            }}
            .mermaid {{
                margin-top: 20px;
            }}
        </style>
    </head>
    <body>
        <h1>Case Analysis Workflow</h1>
        <div class="mermaid">
        {mermaid_code}
        </div>
    </body>
    </html>
    """

    # Save to a temporary file
    file_path = os.path.join(temp_dir, "case_analysis_workflow.html")

    with open(file_path, "w") as f:
        f.write(html_content)

    _logger.info(f"Saved workflow visualization to HTML: {file_path}")

    return file_path

def get_workflow_structure():
    """Get a simple text representation of the workflow structure."""

    # Define the nodes and edges of the workflow
    nodes = [
        "research",
        "online_research",
        "background",
        "issues",
        "evidence",
        "principles",
        "judgment",
        "full_analysis",
        "END"
    ]

    edges = [
        ("research", "online_research"),
        ("online_research", "background"),
        ("background", "issues"),
        ("issues", "evidence"),
        ("evidence", "principles"),
        ("principles", "judgment"),
        ("judgment", "full_analysis"),
        ("full_analysis", "END")
    ]

    # Create a simple text representation
    node_text = "\n".join([f"- {node}" for node in nodes])
    edge_text = "\n".join([f"- {source} -> {target}" for source, target in edges])

    return f"Workflow Structure:\n\nNodes:\n{node_text}\n\nEdges:\n{edge_text}"
