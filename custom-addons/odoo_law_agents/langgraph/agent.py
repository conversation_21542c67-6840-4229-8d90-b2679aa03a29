"""
LangGraph agent implementation for Odoo Law Agents.
"""

import logging
import time
from typing import Dict, Any, Optional

from langgraph.graph import StateGraph

from ..tools.simple_tracing import get_tracing_config, MockTracer
from ..tools.graph_visualization import render_graph, save_graph_to_folder

from .state import LawAgentState
from .nodes import (
    Gujarat_Law_Agent,
    Retrival_tools,
    Web_Search,
    Get_Suitable_PDF_Results,
    Civil_Law_Agent,
    Land_Law_Agent,
    Generate_Response,
)
from .conditions import Query_Retrival, Check_for_Civil_Land

_logger = logging.getLogger(__name__)


def create_law_agent() -> StateGraph:
    """Create a LangGraph agent for law queries."""
    # Create the graph
    graph = StateGraph(LawAgentState)

    # Add nodes
    graph.add_node("Gujarat Law Agent", Gujarat_Law_Agent)
    graph.add_node("Retrival tools", Retrival_tools)
    graph.add_node("Web Search", Web_Search)
    graph.add_node("Get Suitable PDF Results", Get_Suitable_PDF_Results)
    graph.add_node("Civil Law Agent", Civil_Law_Agent)
    graph.add_node("Land Law Agent", Land_Law_Agent)
    graph.add_node("Generate Response", Generate_Response)

    # Add conditional edges from Gujarat Law Agent
    graph.add_conditional_edges(
        "Gujarat Law Agent",
        Check_for_Civil_Land,
        {
            "Civil Law Agent": "Civil Law Agent",
            "Land Law Agent": "Land Law Agent",
            "Retrival tools": "Retrival tools",
        },
    )

    # Add edge from Retrival tools to Web Search
    graph.add_edge("Retrival tools", "Web Search")

    graph.add_conditional_edges(
        "Web Search",
        Query_Retrival,
        {
            "Get Suitable PDF Results": "Get Suitable PDF Results",
        },
    )

    graph.add_edge("Get Suitable PDF Results", "Generate Response")
    graph.add_edge("Civil Law Agent", "Generate Response")
    graph.add_edge("Land Law Agent", "Generate Response")

    # Set the entry point
    graph.set_entry_point("Gujarat Law Agent")

    # Compile the graph
    return graph.compile()


def process_query(query: str, language: str = "en", user_id: int = 1, agent_id: int = 1,
               agent_model=None, env=None, use_mock: bool = False, self_ref=None) -> Dict[str, Any]:
    """Process a law query using the LangGraph agent.

    Args:
        query: The query text.
        language: The language of the query (default: "en").
        user_id: The ID of the user making the query.
        agent_id: The ID of the agent processing the query.
        agent_model: The agent model instance with configuration.
        env: The Odoo environment to use for database access.
        use_mock: If True, use mock responses instead of calling the LLM API (default: False).
        self_ref: Optional reference to the calling model instance for database access.

    Returns:
        A dictionary with the response and metadata.
    """
    try:
        # Create the agent
        agent = create_law_agent()

        # Create the initial state
        initial_state = {
            "query": query,
            "language": language,
            "user_id": user_id,
            "agent_id": agent_id,
            "start_time": time.time(),
            "use_mock": use_mock,
            "self_ref": self_ref,
        }

        # Add the environment if it's available
        if env is not None:
            initial_state["env"] = env
            _logger.info("Environment added to initial state")
        else:
            _logger.warning("No environment available for agent - LLM API calls may fail")

        if use_mock:
            _logger.info("Using mock mode for agent responses - no LLM API calls will be made")

        # Configure tracing
        config = {}
        if agent_model and hasattr(agent_model, 'enable_tracing') and agent_model.enable_tracing:
            try:
                _logger.info(f"Setting up tracing for agent: {agent_model.name}")
                # Get tracing configuration with the agent model
                config = get_tracing_config(agent, agent_model)
                
                if config.get('callbacks'):
                    _logger.info("Tracing configured successfully")
                else:
                    _logger.warning("Tracing configuration returned no callbacks")
            except Exception as e:
                _logger.error(f"Failed to initialize tracing: {str(e)}")
                # Fallback to MockTracer
                config = {"callbacks": [MockTracer(graph=agent, project_name="odoo_law_agents")]}
        else:
            _logger.info("Tracing not enabled for this agent")
            config = {"callbacks": [MockTracer(graph=agent, project_name="odoo_law_agents")]}

        # Invoke the agent with tracing configuration
        result = agent.invoke(initial_state, config=config)

        # Process and return results
        response_data = {
            "response": result.get("response", "No response generated"),
            "sources": result.get("sources", []),
            "error": result.get("error"),
            "duration": result.get("duration", 0),
            "query_type": result.get("query_type")
        }

        if "warning" in result:
            response_data["warning"] = result["warning"]

        return response_data

    except Exception as e:
        _logger.error(f"Error processing query: {str(e)}")
        return {
            "response": "I apologize, but I encountered an error while processing your query. Please try again later.",
            "sources": [],
            "error": f"Error processing query: {str(e)}",
            "duration": 0,
        }


def create_agent(agent_type: str) -> Any:
    """Create a LangGraph agent of the specified type.

    Args:
        agent_type: The type of agent to create.

    Returns:
        A compiled LangGraph agent, or None if creation fails.
    """
    try:
        _logger.info(f"Creating agent of type: {agent_type}")

        # Import the END constant here to avoid circular imports
        from langgraph.graph import END

        # Normalize the agent type for case-insensitive comparison
        agent_type_lower = agent_type.lower()

        # Create the state graph
        workflow = StateGraph(LawAgentState)

        # Add nodes based on agent type (with flexible matching)
        if 'gujarat' in agent_type_lower:
            _logger.info(f"Creating Gujarat Law agent workflow")
            workflow.add_node("Gujarat Law Agent", Gujarat_Law_Agent)
            workflow.add_node("Land Law Agent", Land_Law_Agent)
            workflow.add_node("Generate Response", Generate_Response)

            # Add edges
            workflow.add_edge("Gujarat Law Agent", "Land Law Agent")
            workflow.add_edge("Land Law Agent", "Generate Response")
            workflow.add_edge("Generate Response", END)

            # Set entry point
            workflow.set_entry_point("Gujarat Law Agent")
        elif 'land' in agent_type_lower:
            _logger.info(f"Creating Land Law agent workflow")
            workflow.add_node("Land Law Agent", Land_Law_Agent)
            workflow.add_node("Generate Response", Generate_Response)

            # Add edges
            workflow.add_edge("Land Law Agent", "Generate Response")
            workflow.add_edge("Generate Response", END)

            # Set entry point
            workflow.set_entry_point("Land Law Agent")
        elif 'civil' in agent_type_lower:
            _logger.info(f"Creating Civil Law agent workflow")
            workflow.add_node("Civil Law Agent", Civil_Law_Agent)
            workflow.add_node("Generate Response", Generate_Response)

            # Add edges
            workflow.add_edge("Civil Law Agent", "Generate Response")
            workflow.add_edge("Generate Response", END)

            # Set entry point
            workflow.set_entry_point("Civil Law Agent")
        else:
            _logger.error(f"Unknown agent type: {agent_type}")
            _logger.info(f"Supported types: Gujarat Law, Land Law, Civil Law")
            return None

        # Compile the graph
        agent = workflow.compile()
        _logger.info(f"Successfully created agent of type: {agent_type}")

        # Set the agent_type as an attribute on the agent object for visualization
        agent.agent_type = agent_type

        return agent
    except Exception as e:
        _logger.error(f"Error creating agent: {str(e)}")
        return None
