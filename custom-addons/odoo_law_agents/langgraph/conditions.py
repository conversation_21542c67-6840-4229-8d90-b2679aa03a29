"""
Condition implementations for LangGraph agents.
"""

from typing import Literal

from .state import LawAgentState


def Query_Retrival(state: LawAgentState) -> Literal["Get Suitable PDF Results"]:
    """Condition for routing from Retrieval tools."""
    # This condition always routes to Get Suitable PDF Results
    return "Get Suitable PDF Results"


def Check_for_Civil_Land(state: LawAgentState) -> Literal["Civil Law Agent", "Land Law Agent", "Retrival tools"]:
    """Condition for routing from Gujarat Law Agent."""
    # Check the query type to determine the next node
    query_type = state.get("query_type", "retrieval")
    
    if query_type == "civil":
        return "Civil Law Agent"
    elif query_type == "land":
        return "Land Law Agent"
    else:  # "gujarat" or "retrieval"
        return "Retrival tools"
