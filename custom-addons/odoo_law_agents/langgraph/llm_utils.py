"""
Utility functions for LLM integration in Odoo Law Agents.
"""

import logging
import os
from typing import Dict, Any, Optional, List, Union

_logger = logging.getLogger(__name__)

# Default Odoo LLM Gateway endpoint
DEFAULT_OLG_ENDPOINT = "https://olg.api.odoo.com"

def get_env_from_state(state):
    """Get the Odoo environment from the state."""
    # If the state has an env attribute, use it
    if hasattr(state, "env"):
        return state.env
    
    # If the state is a dict and has an env key, use it
    if isinstance(state, dict) and "env" in state:
        return state["env"]
    
    # Otherwise, return None
    return None

def call_llm(prompt, conversation_history=None, env=None, use_mock=False, agent_id=None, state=None):
    """Call the LLM with the given prompt and conversation history."""
    _logger.info("Calling LLM")
    
    # If mock mode is enabled, return a mock response
    if use_mock:
        _logger.info("Using mock mode for LLM call")
        return {
            "status": "success",
            "content": "This is a mock response from the LLM. In a real scenario, this would be generated by an AI model."
        }
    
    # Get the Odoo environment
    if env is None:
        _logger.warning("No environment provided for LLM call, attempting to get from state")
        env = get_env_from_state(state)
    
    # If we still don't have an environment, try to create one
    if env is None:
        _logger.warning("No environment available, attempting to create one")
        env = create_environment()
    
    # If we still don't have an environment, return an error
    if env is None:
        _logger.error("Failed to get or create an environment for LLM call")
        return {
            "status": "error",
            "content": "Failed to get or create an environment for LLM call"
        }
    
    try:
        # Get the LLM endpoint from system parameters
        IrConfigParameter = env['ir.config_parameter'].sudo()
        endpoint = IrConfigParameter.get_param('web_editor.olg_api_endpoint', DEFAULT_OLG_ENDPOINT)
        
        # Prepare the request data
        data = {
            'prompt': prompt,
            'conversation_history': conversation_history or [],
        }
        
        # If an agent ID is provided, include it in the request
        if agent_id:
            data['agent_id'] = agent_id
        
        # Call the LLM API
        _logger.info(f"Calling LLM API at {endpoint}")
        
        # Import here to avoid circular imports
        from odoo.addons.iap.tools import iap_tools
        
        result = iap_tools.iap_jsonrpc(endpoint + '/api/olg/1/call', params=data)
        
        # Check the response status
        if result.get('status') == 'success':
            return {
                "status": "success",
                "content": result.get('content', '')
            }
        elif result.get('status') == 'error_prompt_too_long':
            _logger.error("Prompt too long for LLM call")
            return {
                "status": "error",
                "content": "The prompt is too long. Please try a shorter query."
            }
        elif result.get('status') == 'limit_call_reached':
            _logger.error("Limit reached for LLM calls")
            return {
                "status": "error",
                "content": "You have reached the limit of LLM calls. Please try again later."
            }
        else:
            _logger.error(f"Unknown error in LLM call: {result.get('status')}")
            return {
                "status": "error",
                "content": "An unknown error occurred while processing your request."
            }
    
    except Exception as e:
        _logger.exception(f"Exception in LLM call: {str(e)}")
        return {
            "status": "error",
            "content": f"An error occurred while processing your request: {str(e)}"
        }

def create_environment():
    """Create an Odoo environment."""
    _logger.info("Attempting to create an Odoo environment")
    
    try:
        import odoo
        from odoo import api, SUPERUSER_ID
        
        db_name = None
        
        # Try to get the database name from the environment
        if 'PGDATABASE' in os.environ:
            db_name = os.environ['PGDATABASE']
            _logger.info(f"Got database name from environment: {db_name}")
        
        # If not found, try to get it from the Odoo registry
        if not db_name:
            try:
                db_name = odoo.service.db.list_dbs(True)[0]
                _logger.info(f"Got database name from registry: {db_name}")
            except Exception as e:
                _logger.warning(f"Failed to get database name from registry: {str(e)}")
        
        # If still not found, try to get it from the Odoo configuration
        if not db_name:
            try:
                from odoo.tools import config
                db_name = config.get('db_name')
                if db_name:
                    _logger.info(f"Got database name from Odoo config: {db_name}")
            except Exception as e:
                _logger.warning(f"Failed to get database name from config: {str(e)}")
        
        # If we have a database name, create an environment
        if db_name:
            registry = odoo.modules.registry.Registry(db_name)
            with registry.cursor() as cr:
                env = api.Environment(cr, SUPERUSER_ID, {})
                _logger.info("Successfully created an Odoo environment")
                return env
        
        _logger.error("Failed to get database name, cannot create environment")
        return None
    
    except Exception as e:
        _logger.exception(f"Exception creating environment: {str(e)}")
        return None
