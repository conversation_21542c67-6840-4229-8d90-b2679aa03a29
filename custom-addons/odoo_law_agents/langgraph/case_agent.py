import logging
import json
import time
from typing import Dict, List, Any, <PERSON><PERSON>, <PERSON><PERSON>, TypedDict, Annotated
from datetime import datetime

# Try to import LangGraph, fall back to mock if not available
try:
    from langgraph.graph import StateGraph, END
    from langgraph.prebuilt import ToolNode
    import langgraph.checkpoint as checkpoint
    LANGGRAPH_AVAILABLE = True
except ImportError:
    _logger = logging.getLogger(__name__)
    _logger.warning("LangGraph not available, using mock implementation")
    # Mock implementation
    class StateGraph:
        def __init__(self, *args, **kwargs):
            self.nodes = {}
            self.edges = {}
            self.entry_point = None

        def add_node(self, name, func):
            self.nodes[name] = func
            return self

        def add_edge(self, start, end):
            if start not in self.edges:
                self.edges[start] = []
            self.edges[start].append(end)
            return self

        def set_entry_point(self, name):
            self.entry_point = name
            return self

        def compile(self):
            return self

        def get_graph(self, xray=False):
            return self

        def to_mermaid(self):
            mermaid = "graph TD;\n"
            for start, ends in self.edges.items():
                for end in ends:
                    mermaid += f"    {start}-->{end};\n"
            return mermaid

        def invoke(self, state):
            # Simple mock implementation that just returns the state
            return state

    class ToolNode:
        def __init__(self, *args, **kwargs):
            pass

    checkpoint = None
    END = "END"
    LANGGRAPH_AVAILABLE = False

from .state import CaseState
from .nodes import LLMNode, RAGNode, WebSearchNode
from .llm_utils import call_llm
from ..tools import duckduckgo_search, simple_tracing, graph_visualization

_logger = logging.getLogger(__name__)

# Define specialized nodes for case analysis
class BackgroundAnalysisNode(LLMNode):
    """Node for analyzing the background of a legal case"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "BackgroundAnalysis"
        self.description = "Analyzes the background of a legal case"
        self.system_prompt = """You are a legal expert specializing in case background analysis.
Your task is to analyze the background of the legal case provided and extract key information:

1. Identify all parties involved in the case
2. Determine the timeline of events
3. Identify the jurisdiction and applicable legal framework
4. Summarize the factual context of the case
5. Identify any relevant historical or contextual factors

Provide a comprehensive background analysis that will serve as the foundation for further legal analysis.
Focus on facts, not legal arguments or conclusions.
"""

    def run(self, state) -> Dict[str, Any]:
        """Run the background analysis node"""
        _logger.info("Running Background Analysis Node")

        # Get the case text from the state
        case_text = state.get('case_text', '')
        language = state.get('language', 'en')
        case_type = state.get('case_type', 'general')

        # Prepare the prompt
        prompt = f"""
# Case Background Analysis

## Case Information
- Case Type: {case_type}
- Language: {language}

## Case Text
{case_text}

## Task
Please analyze the background of this case and provide:
1. A summary of all parties involved
2. The timeline of key events
3. The jurisdiction and applicable legal framework
4. A comprehensive factual context summary
5. Any relevant historical or contextual factors

Provide your analysis in a clear, structured format.
"""

        # Get the response from the LLM
        response = self._call_llm(prompt, state)

        # Update the state with the background analysis
        state['background_analysis'] = response
        if 'messages' not in state:
            state['messages'] = []
        state['messages'].append({
            "role": "system",
            "content": f"Background analysis completed: {len(response)} characters"
        })

        return state


class LegalIssuesNode(LLMNode):
    """Node for identifying legal issues in a case"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "LegalIssues"
        self.description = "Identifies legal issues in a case"
        self.system_prompt = """You are a legal expert specializing in identifying legal issues.
Your task is to identify and analyze the key legal issues in the case provided:

1. Identify the primary legal questions that need to be resolved
2. Determine the legal principles that apply to each issue
3. Analyze any jurisdictional or procedural issues
4. Identify any conflicts of law or interpretation
5. Prioritize the issues by importance to the case outcome

Provide a comprehensive analysis of the legal issues that will guide further analysis.
Be specific and cite relevant legal frameworks where possible.
"""

    def run(self, state) -> CaseState:
        """Run the legal issues node"""
        _logger.info("Running Legal Issues Node")

        # Check if state is a dictionary or CaseState object
        if isinstance(state, dict):
            _logger.info("State is a dictionary, accessing attributes via dict keys")
            case_text = state.get('case_text', '')
            background = state.get('background_analysis', '')
            language = state.get('language', 'en')
            case_type = state.get('case_type', 'property')
        else:
            _logger.info("State is a CaseState object, accessing attributes directly")
            case_text = state.case_text
            background = state.background_analysis
            language = state.language
            case_type = state.case_type

        # Prepare the prompt
        prompt = f"""
# Legal Issues Analysis

## Case Information
- Case Type: {case_type}
- Language: {language}

## Background Analysis
{background}

## Case Text
{case_text}

## Task
Please identify and analyze the key legal issues in this case:
1. What are the primary legal questions that need to be resolved?
2. What legal principles apply to each issue?
3. Are there any jurisdictional or procedural issues?
4. Are there any conflicts of law or interpretation?
5. How would you prioritize these issues by importance to the case outcome?

Provide your analysis in a clear, structured format.
"""

        # Get the response from the LLM
        response = self._call_llm(prompt, state)

        # Update the state with the legal issues analysis
        if isinstance(state, dict):
            state['legal_issues'] = response
            if 'messages' not in state:
                state['messages'] = []
            state['messages'].append({
                "role": "system",
                "content": f"Legal issues analysis completed: {len(response)} characters"
            })
        else:
            state.legal_issues = response
            state.messages.append({
                "role": "system",
                "content": f"Legal issues analysis completed: {len(response)} characters"
            })

        return state


class EvidenceAnalysisNode(LLMNode):
    """Node for analyzing evidence in a case"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "EvidenceAnalysis"
        self.description = "Analyzes evidence in a case"
        self.system_prompt = """You are a legal expert specializing in evidence analysis.
Your task is to analyze the evidence presented in the case:

1. Identify all evidence mentioned in the case
2. Evaluate the strength and relevance of each piece of evidence
3. Identify any missing or potentially helpful evidence
4. Analyze credibility issues with witnesses or documents
5. Determine how the evidence supports or contradicts each party's claims

Provide a comprehensive analysis of the evidence that will inform legal reasoning.
Be objective and consider both supporting and contradicting evidence.
"""

    def run(self, state) -> CaseState:
        """Run the evidence analysis node"""
        _logger.info("Running Evidence Analysis Node")

        # Check if state is a dictionary or CaseState object
        if isinstance(state, dict):
            _logger.info("State is a dictionary, accessing attributes via dict keys")
            case_text = state.get('case_text', '')
            background = state.get('background_analysis', '')
            legal_issues = state.get('legal_issues', '')
            language = state.get('language', 'en')
            case_type = state.get('case_type', 'property')
        else:
            _logger.info("State is a CaseState object, accessing attributes directly")
            case_text = state.case_text
            background = state.background_analysis
            legal_issues = state.legal_issues
            language = state.language
            case_type = state.case_type

        # Prepare the prompt
        prompt = f"""
# Evidence Analysis

## Case Information
- Case Type: {case_type}
- Language: {language}

## Background Analysis
{background}

## Legal Issues
{legal_issues}

## Case Text
{case_text}

## Task
Please analyze the evidence presented in this case:
1. Identify all evidence mentioned in the case
2. Evaluate the strength and relevance of each piece of evidence
3. Identify any missing or potentially helpful evidence
4. Analyze credibility issues with witnesses or documents
5. Determine how the evidence supports or contradicts each party's claims

Provide your analysis in a clear, structured format.
"""

        # Get the response from the LLM
        response = self._call_llm(prompt, state)

        # Update the state with the evidence analysis
        if isinstance(state, dict):
            state['evidence_analysis'] = response
            if 'messages' not in state:
                state['messages'] = []
            state['messages'].append({
                "role": "system",
                "content": f"Evidence analysis completed: {len(response)} characters"
            })
        else:
            state.evidence_analysis = response
            state.messages.append({
                "role": "system",
                "content": f"Evidence analysis completed: {len(response)} characters"
            })

        return state


class LegalPrinciplesNode(LLMNode):
    """Node for applying legal principles to a case"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "LegalPrinciples"
        self.description = "Applies legal principles to a case"
        self.system_prompt = """You are a legal expert specializing in applying legal principles.
Your task is to identify and apply the relevant legal principles to the case:

1. Identify the key legal principles applicable to the case
2. Explain how each principle applies to the facts
3. Analyze any conflicts between different legal principles
4. Consider precedent cases and their relevance
5. Evaluate statutory interpretations and their application

Provide a comprehensive analysis of the legal principles that will guide the judgment prediction.
Be specific about how each principle applies to the particular facts of this case.
"""

    def run(self, state) -> CaseState:
        """Run the legal principles node"""
        _logger.info("Running Legal Principles Node")

        # Check if state is a dictionary or CaseState object
        if isinstance(state, dict):
            _logger.info("State is a dictionary, accessing attributes via dict keys")
            case_text = state.get('case_text', '')
            background = state.get('background_analysis', '')
            legal_issues = state.get('legal_issues', '')
            evidence = state.get('evidence_analysis', '')
            language = state.get('language', 'en')
            case_type = state.get('case_type', 'property')
        else:
            _logger.info("State is a CaseState object, accessing attributes directly")
            case_text = state.case_text
            background = state.background_analysis
            legal_issues = state.legal_issues
            evidence = state.evidence_analysis
            language = state.language
            case_type = state.case_type

        # Prepare the prompt
        prompt = f"""
# Legal Principles Analysis

## Case Information
- Case Type: {case_type}
- Language: {language}

## Background Analysis
{background}

## Legal Issues
{legal_issues}

## Evidence Analysis
{evidence}

## Case Text
{case_text}

## Task
Please identify and apply the relevant legal principles to this case:
1. What are the key legal principles applicable to the case?
2. How does each principle apply to the specific facts?
3. Are there any conflicts between different legal principles?
4. What precedent cases are relevant and how do they apply?
5. What statutory interpretations are relevant and how do they apply?

Provide your analysis in a clear, structured format.
"""

        # Get the response from the LLM
        response = self._call_llm(prompt, state)

        # Update the state with the legal principles analysis
        if isinstance(state, dict):
            state['legal_principles'] = response
            if 'messages' not in state:
                state['messages'] = []
            state['messages'].append({
                "role": "system",
                "content": f"Legal principles analysis completed: {len(response)} characters"
            })
        else:
            state.legal_principles = response
            state.messages.append({
                "role": "system",
                "content": f"Legal principles analysis completed: {len(response)} characters"
            })

        return state


class JudgmentPredictionNode(LLMNode):
    """Node for predicting the judgment in a case"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "JudgmentPrediction"
        self.description = "Predicts the judgment in a case"
        self.system_prompt = """You are a legal expert specializing in judgment prediction.
Your task is to predict the likely judgment in the case based on all previous analyses:

1. Evaluate the strength of each party's legal position
2. Consider how the court is likely to interpret and apply the law
3. Assess how the evidence supports each party's claims
4. Predict the likely outcome for each legal issue
5. Provide an overall judgment prediction with reasoning

Provide a comprehensive prediction that is well-reasoned and based on the facts and law.
Be balanced and consider alternative outcomes where appropriate.
"""

    def run(self, state) -> CaseState:
        """Run the judgment prediction node"""
        _logger.info("Running Judgment Prediction Node")

        # Check if state is a dictionary or CaseState object
        if isinstance(state, dict):
            _logger.info("State is a dictionary, accessing attributes via dict keys")
            case_text = state.get('case_text', '')
            background = state.get('background_analysis', '')
            legal_issues = state.get('legal_issues', '')
            evidence = state.get('evidence_analysis', '')
            principles = state.get('legal_principles', '')
            language = state.get('language', 'en')
            case_type = state.get('case_type', 'property')
        else:
            _logger.info("State is a CaseState object, accessing attributes directly")
            case_text = state.case_text
            background = state.background_analysis
            legal_issues = state.legal_issues
            evidence = state.evidence_analysis
            principles = state.legal_principles
            language = state.language
            case_type = state.case_type

        # Prepare the prompt
        prompt = f"""
# Judgment Prediction

## Case Information
- Case Type: {case_type}
- Language: {language}

## Background Analysis
{background}

## Legal Issues
{legal_issues}

## Evidence Analysis
{evidence}

## Legal Principles
{principles}

## Case Text
{case_text}

## Task
Please predict the likely judgment in this case:
1. Evaluate the strength of each party's legal position
2. Consider how the court is likely to interpret and apply the law
3. Assess how the evidence supports each party's claims
4. Predict the likely outcome for each legal issue
5. Provide an overall judgment prediction with reasoning

Provide your prediction in a clear, structured format.
"""

        # Get the response from the LLM
        response = self._call_llm(prompt, state)

        # Update the state with the judgment prediction
        if isinstance(state, dict):
            state['judgment_prediction'] = response
            if 'messages' not in state:
                state['messages'] = []
            state['messages'].append({
                "role": "system",
                "content": f"Judgment prediction completed: {len(response)} characters"
            })
        else:
            state.judgment_prediction = response
            state.messages.append({
                "role": "system",
                "content": f"Judgment prediction completed: {len(response)} characters"
            })

        return state


class FullAnalysisNode(LLMNode):
    """Node for compiling a full analysis of a case"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "FullAnalysis"
        self.description = "Compiles a full analysis of a case"
        self.system_prompt = """You are a legal expert specializing in comprehensive case analysis.
Your task is to compile a full analysis of the case based on all previous analyses:

1. Synthesize all previous analyses into a coherent whole
2. Ensure consistency across all sections
3. Highlight the most important aspects of the case
4. Provide a balanced and objective analysis
5. Format the analysis in a professional, readable manner

Provide a comprehensive analysis that could serve as a complete legal memorandum on the case.
Be thorough but concise, focusing on the most important aspects of the case.
"""

    def run(self, state) -> CaseState:
        """Run the full analysis node"""
        _logger.info("Running Full Analysis Node")

        # Check if state is a dictionary or CaseState object
        if isinstance(state, dict):
            _logger.info("State is a dictionary, accessing attributes via dict keys")
            case_text = state.get('case_text', '')
            background = state.get('background_analysis', '')
            legal_issues = state.get('legal_issues', '')
            evidence = state.get('evidence_analysis', '')
            principles = state.get('legal_principles', '')
            judgment = state.get('judgment_prediction', '')
            language = state.get('language', 'en')
            case_type = state.get('case_type', 'property')
        else:
            _logger.info("State is a CaseState object, accessing attributes directly")
            case_text = state.case_text
            background = state.background_analysis
            legal_issues = state.legal_issues
            evidence = state.evidence_analysis
            principles = state.legal_principles
            judgment = state.judgment_prediction
            language = state.language
            case_type = state.case_type

        # Prepare the prompt
        prompt = f"""
# Full Case Analysis

## Case Information
- Case Type: {case_type}
- Language: {language}

## Background Analysis
{background}

## Legal Issues
{legal_issues}

## Evidence Analysis
{evidence}

## Legal Principles
{principles}

## Judgment Prediction
{judgment}

## Task
Please compile a full analysis of this case by synthesizing all previous analyses:
1. Create a comprehensive legal memorandum
2. Ensure consistency across all sections
3. Highlight the most important aspects of the case
4. Provide a balanced and objective analysis
5. Format the analysis in a professional, readable manner

Structure your analysis with the following sections:
- Executive Summary
- Case Background
- Legal Issues
- Evidence Analysis
- Legal Principles and Application
- Judgment Prediction
- Conclusion

Provide your analysis in a clear, structured format.
"""

        # Get the response from the LLM
        response = self._call_llm(prompt, state)

        # Update the state with the full analysis
        if isinstance(state, dict):
            state['full_analysis'] = response
            if 'messages' not in state:
                state['messages'] = []
            state['messages'].append({
                "role": "system",
                "content": f"Full analysis completed: {len(response)} characters"
            })
        else:
            state.full_analysis = response
            state.messages.append({
                "role": "system",
                "content": f"Full analysis completed: {len(response)} characters"
            })

        return state


class CaseResearchNode(RAGNode):
    """Node for researching relevant legal documents for a case"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "CaseResearch"
        self.description = "Researches relevant legal documents for a case"

    def run(self, state) -> Dict[str, Any]:
        """Run the case research node"""
        _logger.info("Running Case Research Node")

        # Skip if document embeddings are disabled
        if not state.get('use_document_embeddings', True):
            if 'messages' not in state:
                state['messages'] = []
            state['messages'].append({
                "role": "system",
                "content": "Document embedding search disabled, skipping research."
            })
            return state

        # Get the case text and type
        case_text = state.get('case_text', '')
        case_type = state.get('case_type', 'general')
        language = state.get('language', 'en')

        # Prepare search queries based on case type
        queries = []

        if case_type == 'property':
            queries = [
                "property dispute legal principles",
                "land boundary dispute precedents",
                "property ownership rights",
                "adverse possession property law",
                "property inheritance rights"
            ]
        elif case_type == 'inheritance':
            queries = [
                "inheritance law principles",
                "succession rights legal precedents",
                "will testament validity",
                "intestate succession rules",
                "inheritance dispute resolution"
            ]
        elif case_type == 'contract':
            queries = [
                "contract law principles",
                "breach of contract remedies",
                "contract interpretation rules",
                "specific performance contract law",
                "contract termination conditions"
            ]
        elif case_type == 'family':
            queries = [
                "family law principles",
                "divorce settlement precedents",
                "child custody determination factors",
                "alimony calculation principles",
                "marital property division"
            ]
        else:
            # Generic civil law queries
            queries = [
                "civil law principles",
                "legal rights and obligations",
                "civil dispute resolution",
                "legal remedies civil law",
                "burden of proof civil cases"
            ]

        # Add language-specific queries
        if language == 'gu':
            # Add Gujarati-specific queries
            queries.extend([
                "ગુજરાત મિલકત કાયદો",
                "ગુજરાત વારસા કાયદો",
                "ગુજરાત કૌટુંબિક કાયદો"
            ])
        elif language == 'hi':
            # Add Hindi-specific queries
            queries.extend([
                "संपत्ति कानून भारत",
                "वारिस कानून भारत",
                "परिवार कानून भारत"
            ])

        # Extract key phrases from the case text for additional queries
        # This is a simplified approach - in a real implementation, you might use
        # NLP techniques to extract key phrases

        # Perform RAG search for each query
        all_results = []
        for query in queries:
            try:
                results = self._search_documents(query, state)
                if results:
                    all_results.extend(results)
            except Exception as e:
                _logger.error(f"Error searching documents for query '{query}': {str(e)}")

        # Deduplicate results
        seen_ids = set()
        unique_results = []
        for result in all_results:
            result_id = result.get('id')
            if result_id not in seen_ids:
                seen_ids.add(result_id)
                unique_results.append(result)

        # Sort by relevance
        unique_results.sort(key=lambda x: x.get('score', 0), reverse=True)

        # Take top results
        top_results = unique_results[:10]

        # Add to sources
        for result in top_results:
            source = {
                'type': 'document',
                'id': result.get('id'),
                'name': result.get('title', 'Unknown Document'),
                'relevance': result.get('score', 0),
                'excerpt': result.get('content', '')[:500] + '...' if len(result.get('content', '')) > 500 else result.get('content', '')
            }
            if 'sources' not in state:
                state['sources'] = []
            state['sources'].append(source)

        # Update state with research results
        if 'messages' not in state:
            state['messages'] = []
        state['messages'].append({
            "role": "system",
            "content": f"Document research completed: found {len(top_results)} relevant documents."
        })

        return state


class OnlineResearchNode(WebSearchNode):
    """Node for online legal research"""

    def __init__(self, env=None):
        super().__init__(env)
        self.name = "OnlineResearch"
        self.description = "Conducts online legal research"

    def run(self, state) -> Dict[str, Any]:
        """Run the online research node"""
        _logger.info("Running Online Research Node")

        # Skip if online search is disabled
        if not state.get('use_online_search', True):
            if 'messages' not in state:
                state['messages'] = []
            state['messages'].append({
                "role": "system",
                "content": "Online search disabled, skipping research."
            })
            return state

        # Get the case text and type
        case_text = state.get('case_text', '')
        case_type = state.get('case_type', 'general')
        language = state.get('language', 'en')

        # Prepare search queries based on case type and language
        queries = []

        # Base query components
        if case_type == 'property':
            base_queries = [
                "property dispute legal principles",
                "land boundary dispute precedents",
                "property ownership rights",
                "adverse possession property law",
                "property inheritance rights"
            ]
        elif case_type == 'inheritance':
            base_queries = [
                "inheritance law principles",
                "succession rights legal precedents",
                "will testament validity",
                "intestate succession rules",
                "inheritance dispute resolution"
            ]
        elif case_type == 'contract':
            base_queries = [
                "contract law principles",
                "breach of contract remedies",
                "contract interpretation rules",
                "specific performance contract law",
                "contract termination conditions"
            ]
        elif case_type == 'family':
            base_queries = [
                "family law principles",
                "divorce settlement precedents",
                "child custody determination factors",
                "alimony calculation principles",
                "marital property division"
            ]
        else:
            # Generic civil law queries
            base_queries = [
                "civil law principles",
                "legal rights and obligations",
                "civil dispute resolution",
                "legal remedies civil law",
                "burden of proof civil cases"
            ]

        # Add language and jurisdiction specifics
        if language == 'en':
            for q in base_queries:
                queries.append(f"{q} India legal system")
        elif language == 'gu':
            # Gujarati queries
            queries = [
                "ગુજરાત મિલકત કાયદો",
                "ગુજરાત વારસા કાયદો",
                "ગુજરાત કૌટુંબિક કાયદો",
                "ભારતીય કાયદા પ્રણાલી ગુજરાતી",
                "ગુજરાત હાઈકોર્ટ ચુકાદાઓ"
            ]
        elif language == 'hi':
            # Hindi queries
            queries = [
                "संपत्ति कानून भारत",
                "वारिस कानून भारत",
                "परिवार कानून भारत",
                "भારતીય કાયદા પ્રણાલી",
                "ભારતીય ઉચ્ચ ન્યાયાલય નિર્ણય"
            ]

        # Perform web search for each query
        all_results = []
        for query in queries:
            try:
                results = self._search_web(query, state)
                if results:
                    all_results.extend(results)
            except Exception as e:
                _logger.error(f"Error searching web for query '{query}': {str(e)}")

        # Deduplicate results
        seen_urls = set()
        unique_results = []
        for result in all_results:
            url = result.get('link')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)

        # Take top results
        top_results = unique_results[:10]

        # Add to sources
        for result in top_results:
            source = {
                'type': 'web',
                'name': result.get('title', 'Unknown Source'),
                'url': result.get('link', ''),
                'source': result.get('source', 'web search'),
                'excerpt': result.get('snippet', '')
            }
            if 'sources' not in state:
                state['sources'] = []
            state['sources'].append(source)

        # Update state with research results
        if 'messages' not in state:
            state['messages'] = []
        state['messages'].append({
            "role": "system",
            "content": f"Online research completed: found {len(top_results)} relevant sources."
        })

        return state


def create_case_analysis_graph(env=None):
    """Create the case analysis graph"""
    _logger.info("Creating case analysis graph")

    # Create the nodes
    background_node = BackgroundAnalysisNode(env)
    legal_issues_node = LegalIssuesNode(env)
    evidence_node = EvidenceAnalysisNode(env)
    legal_principles_node = LegalPrinciplesNode(env)
    judgment_node = JudgmentPredictionNode(env)
    full_analysis_node = FullAnalysisNode(env)
    case_research_node = CaseResearchNode(env)
    online_research_node = OnlineResearchNode(env)

    # Create the graph
    workflow = StateGraph(CaseState)

    # Add nodes with different names to avoid conflicts with state keys
    workflow.add_node("background_node", background_node.run)
    workflow.add_node("issues_node", legal_issues_node.run)
    workflow.add_node("evidence_node", evidence_node.run)
    workflow.add_node("principles_node", legal_principles_node.run)
    workflow.add_node("judgment_node", judgment_node.run)
    workflow.add_node("analysis_node", full_analysis_node.run)
    workflow.add_node("research_node", case_research_node.run)
    workflow.add_node("online_node", online_research_node.run)

    # Define the edges
    # Start with research nodes
    workflow.add_edge("research_node", "online_node")
    workflow.add_edge("online_node", "background_node")

    # Main analysis flow
    workflow.add_edge("background_node", "issues_node")
    workflow.add_edge("issues_node", "evidence_node")
    workflow.add_edge("evidence_node", "principles_node")
    workflow.add_edge("principles_node", "judgment_node")
    workflow.add_edge("judgment_node", "analysis_node")
    workflow.add_edge("analysis_node", END)

    # Set the entry point
    workflow.set_entry_point("research_node")

    # Compile the graph
    app = workflow.compile()

    return app


def process_case(input_data, env=None):
    """Process a case using the case analysis graph"""
    _logger.info(f"Processing case: {input_data.get('case_title')}")

    try:
        # Create the initial state
        state = CaseState(
            case_id=input_data.get('case_id'),
            case_title=input_data.get('case_title'),
            case_number=input_data.get('case_number'),
            case_text=input_data.get('case_text'),
            case_type=input_data.get('case_type'),
            language=input_data.get('language'),
            user_id=input_data.get('user_id'),
            use_online_search=input_data.get('use_online_search', True),
            use_document_embeddings=input_data.get('use_document_embeddings', True),
            use_mock=input_data.get('use_mock', False),
            enable_tracing=input_data.get('enable_tracing', False),
            env=env,  # Include the environment in the state
            messages=[],
            sources=[],
            background_analysis="",
            legal_issues="",
            evidence_analysis="",
            legal_principles="",
            judgment_prediction="",
            full_analysis=""
        )

        # Create the graph
        app = create_case_analysis_graph(env)

        # Initialize config as an empty dict
        config = {}

        from ..tools.simple_tracing import get_tracing_config, MockTracer
        # Configure tracing if enabled
        if input_data.get('enable_tracing'):
            try:
                agent_model = input_data.get('agent_model')
                config = get_tracing_config(app, agent_model)
                _logger.info("Tracing configured successfully")
            except Exception as e:
                _logger.error(f"Failed to initialize Opik tracing: {str(e)}")
                # Fallback to MockTracer
                config = {"callbacks": [MockTracer(graph=app, project_name="odoo_law_agents")]}

        # Process the case
        start_time = time.time()
        # We don't need to convert the state anymore since we're already creating it as a CaseState object
        # with the environment included. The previous error was because we were trying to add the env
        # attribute to a TypedDict object, which doesn't support adding attributes dynamically.

        # Log the state for debugging
        _logger.info(f"State type: {type(state)}")
        _logger.info(f"State has case_text: {'case_text' in state}")
        _logger.info(f"State has env: {'env' in state}")

        result = app.invoke(state, config=config)
        end_time = time.time()

        _logger.info(f"Case processing completed in {end_time - start_time:.2f} seconds")

        # Extract the final state
        final_state = result

        # Log the type of final_state for debugging
        _logger.info(f"Final state type: {type(final_state)}")
        _logger.info(f"Final state keys: {final_state.keys() if hasattr(final_state, 'keys') else 'No keys method'}")

        # Prepare the result - handle both dictionary and object access
        if isinstance(final_state, dict):
            _logger.info("Final state is a dictionary, accessing via keys")
            result = {
                'background_analysis': final_state.get('background_analysis', ''),
                'legal_issues': final_state.get('legal_issues', ''),
                'evidence_analysis': final_state.get('evidence_analysis', ''),
                'legal_principles': final_state.get('legal_principles', ''),
                'judgment_prediction': final_state.get('judgment_prediction', ''),
                'full_analysis': final_state.get('full_analysis', ''),
                'sources': final_state.get('sources', []),
                'messages': final_state.get('messages', [])
            }
        else:
            _logger.info("Final state is an object, accessing via attributes")
            try:
                result = {
                    'background_analysis': getattr(final_state, 'background_analysis', ''),
                    'legal_issues': getattr(final_state, 'legal_issues', ''),
                    'evidence_analysis': getattr(final_state, 'evidence_analysis', ''),
                    'legal_principles': getattr(final_state, 'legal_principles', ''),
                    'judgment_prediction': getattr(final_state, 'judgment_prediction', ''),
                    'full_analysis': getattr(final_state, 'full_analysis', ''),
                    'sources': getattr(final_state, 'sources', []),
                    'messages': getattr(final_state, 'messages', [])
                }
            except Exception as attr_error:
                _logger.error(f"Error accessing attributes: {str(attr_error)}")
                # Fallback to a simpler approach - convert to string representation
                result = {
                    'error': f"Could not extract state attributes: {str(attr_error)}",
                    'background_analysis': '',
                    'legal_issues': '',
                    'evidence_analysis': '',
                    'legal_principles': '',
                    'judgment_prediction': '',
                    'full_analysis': f"Error processing state: {str(final_state)}",
                    'sources': [],
                    'messages': [{'role': 'system', 'content': f"Error: {str(attr_error)}"}]
                }

        return result

    except Exception as e:
        _logger.exception(f"Error processing case: {str(e)}")
        return {
            'error': str(e),
            'background_analysis': "",
            'legal_issues': "",
            'evidence_analysis': "",
            'legal_principles': "",
            'judgment_prediction': "",
            'full_analysis': f"Error processing case: {str(e)}",
            'sources': [],
            'messages': [{"role": "system", "content": f"Error: {str(e)}"}]
        }
