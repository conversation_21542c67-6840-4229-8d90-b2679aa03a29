"""
State definitions for LangGraph agents.
"""

from typing import List, Dict, Any, Optional
from typing_extensions import TypedDict, NotRequired


class Document(TypedDict):
    """Document retrieved from the knowledge base."""
    id: int
    name: str
    content: str
    document_type: str
    language: str
    relevance: float


class SearchResult(TypedDict):
    """Search result from web search."""
    title: str
    link: str
    snippet: str
    source: str
    relevance: NotRequired[float]
    published: NotRequired[str]


class LawAgentState(TypedDict):
    """State for the Law Agent."""
    # Input
    query: str
    language: str
    user_id: int
    agent_id: int

    # Processing
    query_type: NotRequired[str]  # 'gujarat', 'civil', 'land', or 'general'
    requires_retrieval: NotRequired[bool]
    requires_web_search: NotRequired[bool]
    documents: NotRequired[List[Document]]
    search_results: NotRequired[List[SearchResult]]
    context: NotRequired[str]  # Combined context from documents and search results

    # Output
    response: NotRequired[str]
    sources: NotRequired[List[Dict[str, Any]]]
    error: NotRequired[str]

    # Metadata
    start_time: NotRequired[float]
    end_time: NotRequired[float]
    duration: NotRequired[float]


class CaseState(TypedDict):
    """State for the case analysis agent."""
    # Case information
    case_id: int
    case_title: str
    case_number: str
    case_text: str
    case_type: str
    language: str
    user_id: int

    # Configuration
    use_mock: NotRequired[bool]
    use_online_search: NotRequired[bool]
    use_document_embeddings: NotRequired[bool]
    enable_tracing: NotRequired[bool]

    # Odoo environment (for database access)
    env: NotRequired[Any]

    # Communication
    messages: NotRequired[List[Dict[str, Any]]]
    sources: NotRequired[List[Dict[str, Any]]]

    # Analysis sections
    background_analysis: NotRequired[str]
    legal_issues: NotRequired[str]
    evidence_analysis: NotRequired[str]
    legal_principles: NotRequired[str]
    judgment_prediction: NotRequired[str]
    full_analysis: NotRequired[str]
