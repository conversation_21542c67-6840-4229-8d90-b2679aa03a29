/** @odoo-module **/

import { registry } from '@web/core/registry';
import { standardFieldProps } from "@web/views/fields/standard_field_props";
import { Component } from "@odoo/owl";

export class LawAgentChat extends Component {
    setup() {
        // Chat widget implementation will go here
    }
}

LawAgentChat.template = 'odoo_law_agents.LawAgentChat';
LawAgentChat.props = {
    ...standardFieldProps
};

registry.category("fields").add("law_agent_chat", LawAgentChat);