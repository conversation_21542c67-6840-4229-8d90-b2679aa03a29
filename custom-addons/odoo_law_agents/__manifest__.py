{
    'name': 'Odoo Law Agents',
    'version': '********.0',
    'category': 'Legal',
    'summary': 'AI-powered legal assistance using LangGraph and Odoo LLM',
    'description': """
        This module provides an intelligent legal assistant system for Odoo 18 that combines:
        - LangGraph-based agent architecture with visualization
        - Odoo's built-in LLM capabilities
        - Document processing and embedding
        - Multilingual support (English, Gujarati, and Hindi)
        - Legal document search and analysis
        - Agent workflow visualization and tracing
    """,
    'author': 'VPerfectCS',
    'website': 'https://www.vperfectcs.com',
    'license': 'LGPL-3',
    'depends': [
        'base',
        'web_editor',
        'mail'
    ],
    'external_dependencies': {
        'python': [
            'sentence-transformers',
            'langgraph',
            'duckduckgo-search',  # Free alternative to brave-search
            'pydantic',
            'typing-extensions',
            'markdown',
            'requests',  # For Mermaid.ink API
            'python-docx',  # For DOCX file processing
            'opik>=0.6.0',  # For LangGraph tracing and monitoring (Python 3.12 compatible)
        ],
        # Optional dependencies
        'python_optional': [
            'graphviz',  # For graph visualization (alternative)
            'networkx',  # For graph manipulation (alternative)
        ],
    },
    'data': [
        'security/law_agents_security.xml',
        'security/ir.model.access.csv',
        'views/law_agent_views.xml',
        'views/law_document_type_views.xml',
        'views/law_document_views.xml',
        'views/law_query_views.xml',
        'views/law_embedding_views.xml',
        'views/law_case_views.xml',
        'views/law_query_chat_views.xml',
        'wizards/load_documents_wizard_views.xml',
        'wizards/upload_document_wizard_views.xml',
        'views/menu_views.xml',
        'data/document_type_sequence.xml',
        'data/document_type_data.xml',
        'data/law_case_data.xml',
        'reports/case_analysis_report.xml',
        'reports/case_analysis_report_action.xml',
    ],
    'demo': [
        'data/law_agents_demo.xml',
    ],
    'assets': {
        'web.assets_backend': [
            'odoo_law_agents/static/src/js/**/*',
            'odoo_law_agents/static/src/css/**/*',
        ],
    },
    'application': True,
    'installable': True,
    'auto_install': False,
    'test': [
        'tests/test_embedding.py',
        'tests/test_document_loading.py',
        'tests/test_rag_system.py',
        'tests/test_graph_visualization.py',
        'tests/test_document_type_sequence.py',
        'tests/test_large_document_processing.py',
    ],
}
