<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <data>
        <record model="ir.module.category" id="module_category_queue_job">
            <field name="name">Job Queue</field>
            <field name="sequence">20</field>
        </record>
        <record id="group_queue_job_manager" model="res.groups">
            <field name="name">Job Queue Manager</field>
            <field name="category_id" ref="module_category_queue_job" />
            <field
                name="users"
                eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"
            />
        </record>
    </data>
    <data noupdate="1">
        <record id="queue_job_comp_rule" model="ir.rule">
            <field name="name">Job Queue multi-company</field>
            <field name="model_id" ref="model_queue_job" />
            <field name="global" eval="True" />
            <field
                name="domain_force"
            >['|', ('company_id', '=', False), ('company_id', 'in', company_ids)]</field>
        </record>
    </data>
</odoo>
