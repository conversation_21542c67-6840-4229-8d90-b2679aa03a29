# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* queue_job
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 15.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2023-09-20 21:07+0000\n"
"Last-Translator: Ivorra78 <<EMAIL>>\n"
"Language-Team: none\n"
"Language: es\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: Weblate 4.17\n"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid ""
"<br/>\n"
"                            <span class=\"oe_grey oe_inline\"> If the max. "
"retries is 0, the number of retries is infinite.</span>"
msgstr ""
"<br/>\n"
"                             <span class=\"oe_grey oe_inline\"> Si el máx. "
"reintentos es 0, el número de reintentos es infinito.</span>"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/controllers/main.py:0
msgid "Access Denied"
msgstr "Acceso denegado"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_needaction
msgid "Action Needed"
msgstr "Acción requerida"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_ids
msgid "Activities"
msgstr "Actividades"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "Decoración de Actividad de Excepción"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_state
msgid "Activity State"
msgstr "Estado de la actividad"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_type_icon
msgid "Activity Type Icon"
msgstr "Icono de tipo de actividad"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__args
msgid "Args"
msgstr "Argumentos"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_attachment_count
msgid "Attachment Count"
msgstr "Nº de archivos adjuntos"

#. module: queue_job
#: model:ir.actions.server,name:queue_job.ir_cron_autovacuum_queue_jobs_ir_actions_server
msgid "AutoVacuum Job Queue"
msgstr "Vaciado automático de la cola de trabajos"

#. module: queue_job
#: model:ir.model,name:queue_job.model_base
msgid "Base"
msgstr "Base"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Cancel"
msgstr "Cancelar"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_jobs_to_cancelled
msgid "Cancel all selected jobs"
msgstr "Cancelar todos los trabajos seleccionados"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Cancel job"
msgstr "Cancelar trabajo"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_set_jobs_cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
msgid "Cancel jobs"
msgstr "Cancelar trabajos"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Cancelled"
msgstr "Cancelada"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Cancelled by {}"
msgstr ""

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
msgid "Cannot change the root channel"
msgstr "No se puede cambiar el canal raíz"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
msgid "Cannot remove the root channel"
msgstr "No se puede eliminar el canal raíz"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__channel
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__channel_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Channel"
msgstr "Canal"

#. module: queue_job
#: model:ir.model.constraint,message:queue_job.constraint_queue_job_channel_name_uniq
msgid "Channel complete name must be unique"
msgstr "El nombre completo del canal debe ser único"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job_channel
#: model:ir.ui.menu,name:queue_job.menu_queue_job_channel
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_channel_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_channel_search
msgid "Channels"
msgstr "Canales"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__company_id
msgid "Company"
msgstr "Empresa"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__channel_method_name
msgid "Complete Method Name"
msgstr "Nombre completo del método"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__complete_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__channel
msgid "Complete Name"
msgstr "Nombre completo"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_created
msgid "Created Date"
msgstr "Fecha de creación"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__create_uid
msgid "Created by"
msgstr "Creado por"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Created date"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__create_date
msgid "Created on"
msgstr "Creado el"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__retry
msgid "Current try"
msgstr "Intento actual"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Current try / max. retries"
msgstr "Intento actual / reintentos máx"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_cancelled
msgid "Date Cancelled"
msgstr "Fecha de cancelación"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_done
msgid "Date Done"
msgstr "Fecha de realización"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__dependencies
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Dependencies"
msgstr "Dependencias"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__dependency_graph
msgid "Dependency Graph"
msgstr "Gráfico de dependencias"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__name
msgid "Description"
msgstr "Descripción"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__display_name
msgid "Display Name"
msgstr "Nombre mostrado"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__done
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Done"
msgstr "Hecho"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_enqueued
msgid "Enqueue Time"
msgstr "Hora en que se puso en cola"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__enqueued
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Enqueued"
msgstr "En la cola"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_name
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Exception"
msgstr "Excepción"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_info
msgid "Exception Info"
msgstr "Información de la excepción"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Exception Information"
msgstr "Información de la excepción"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_message
msgid "Exception Message"
msgstr "Mensaje de la excepción"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Exception message"
msgstr "Mensaje de la excepción"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Exception:"
msgstr "Excepción:"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__eta
msgid "Execute only after"
msgstr "Ejecutar solo después de"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exec_time
msgid "Execution Time (avg)"
msgstr "Duración media de ejecución"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__failed
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Failed"
msgstr "Fallido"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_ir_model_fields__ttype
msgid "Field Type"
msgstr "Tipo de campo"

#. module: queue_job
#: model:ir.model,name:queue_job.model_ir_model_fields
msgid "Fields"
msgstr "Campos"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_follower_ids
msgid "Followers"
msgstr "Seguidores"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "Seguidores (Socios)"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Icono de Font Awesome ej. fa-tasks"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Graph"
msgstr "Gráfico"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Graph Jobs"
msgstr "Gráfico de trabajos"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__graph_jobs_count
msgid "Graph Jobs Count"
msgstr "Nº de gráfico de trabajos"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__graph_uuid
msgid "Graph UUID"
msgstr "UUID del Gráfico"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Group By"
msgstr "Agrupar por"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__has_message
msgid "Has Message"
msgstr "Tiene un mensaje"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__id
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__id
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__id
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__id
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__id
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__id
msgid "ID"
msgstr "ID"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_exception_icon
msgid "Icon"
msgstr "Icono"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "Icono para indicar una actividad de excepción."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__identity_key
msgid "Identity Key"
msgstr "Clave identificadora"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "If both parameters are 0, ALL jobs will be requeued!"
msgstr ""
"Si ambos parámetros son 0, ¡TODOS los trabajos se volverán a poner en la "
"cola!"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr ""
"Si se encuentra seleccionado, hay nuevos mensajes que requieren tu atención."

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_error
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "Si se encuentra seleccionado, algunos mensajes tienen error de envío."

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid "Invalid job function: {}"
msgstr "Función del trabajo no válida: {}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_is_follower
msgid "Is Follower"
msgstr "Es un seguidor"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_job_channel
msgid "Job Channels"
msgstr "Canales de trabajos"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__job_function_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Job Function"
msgstr "Función del trabajo"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job_function
#: model:ir.model,name:queue_job.model_queue_job_function
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__job_function_ids
#: model:ir.ui.menu,name:queue_job.menu_queue_job_function
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
msgid "Job Functions"
msgstr "Funciones de los trabajos"

#. module: queue_job
#: model:ir.module.category,name:queue_job.module_category_queue_job
#: model:ir.ui.menu,name:queue_job.menu_queue_job_root
msgid "Job Queue"
msgstr "Cola de trabajos"

#. module: queue_job
#: model:res.groups,name:queue_job.group_queue_job_manager
msgid "Job Queue Manager"
msgstr "Gestor de la cola de trabajos"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__ir_model_fields__ttype__job_serialized
msgid "Job Serialized"
msgstr "Trabajo en serie"

#. module: queue_job
#: model:mail.message.subtype,name:queue_job.mt_job_failed
msgid "Job failed"
msgstr "Trabajo fallido"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__job_ids
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__job_ids
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__job_ids
#: model:ir.ui.menu,name:queue_job.menu_queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_graph
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_pivot
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Jobs"
msgstr "Trabajos"

#. module: queue_job
#: model:ir.actions.server,name:queue_job.ir_cron_queue_job_garbage_collector_ir_actions_server
msgid "Jobs Garbage Collector"
msgstr "Recolector de basura de trabajos"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Jobs for graph %s"
msgstr "Trabajos para gráfico %s"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__kwargs
msgid "Kwargs"
msgstr "Kwargs"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Last 24 hours"
msgstr ""

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Last 30 days"
msgstr ""

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Last 7 days"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__write_uid
msgid "Last Updated by"
msgstr "Última actualización por"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__write_date
msgid "Last Updated on"
msgstr "Última actualización el"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Manually set to done by {}"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__max_retries
msgid "Max. retries"
msgstr "Reintentos máx"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_error
msgid "Message Delivery error"
msgstr "Error de Envío de Mensaje"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_ids
msgid "Messages"
msgstr "Mensajes"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__method
msgid "Method"
msgstr "Método"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__method_name
msgid "Method Name"
msgstr "Nombre del método"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__model_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__model_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Model"
msgstr "Modelo"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid "Model {} not found"
msgstr "No se ha encontrado el modelo {}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "Mi fecha límite de actividad"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__name
msgid "Name"
msgstr "Nombre"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "Fecha límite de siguiente actividad"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_summary
msgid "Next Activity Summary"
msgstr "Resumen de la siguiente actividad"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_type_id
msgid "Next Activity Type"
msgstr "Siguiente tipo de actividad"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "No action available for this job"
msgstr "No hay ninguna acción disponible para este trabajo"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Not allowed to change field(s): {}"
msgstr "No se permite cambiar los campos: {}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_needaction_counter
msgid "Number of Actions"
msgstr "Número de acciones"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_error_counter
msgid "Number of errors"
msgstr "Numero de errores"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "Número de mensajes que requieren una acción"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "Número de mensajes con error de envío"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__parent_id
msgid "Parent Channel"
msgstr "Canal padre"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
msgid "Parent channel required."
msgstr "Se requiere un canal padre."

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job_function__edit_retry_pattern
msgid ""
"Pattern expressing from the count of retries on retryable errors, the number "
"of of seconds to postpone the next execution. Setting the number of seconds "
"to a 2-element tuple or list will randomize the retry interval between the 2 "
"values.\n"
"Example: {1: 10, 5: 20, 10: 30, 15: 300}.\n"
"Example: {1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}.\n"
"See the module description for details."
msgstr ""
"Patrón que expresa cuántos segundos se pospondrá la próxima ejecución, "
"basado en el número de reintentos de los errores reintentables. Si se usa "
"una tupla o lista de 2 elementos para expresar el número de segundos, se "
"escogerá un número aleatorio entre ambos valores.\n"
"Ejemplo: {1: 10, 5: 20, 10: 30, 15: 300}.\n"
"Ejemplo: {1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}.\n"
"Vea la descripción del módulo para más detalles."

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__pending
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Pending"
msgstr "Pendiente"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__priority
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Priority"
msgstr "Prioridad"

#. module: queue_job
#: model:ir.ui.menu,name:queue_job.menu_queue
msgid "Queue"
msgstr "Cola"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_job
msgid "Queue Job"
msgstr "Cola de trabajos"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Queue jobs must be created by calling 'with_delay()'."
msgstr "Los trabajos en cola deben crearse llamando a 'with_delay()'."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__records
msgid "Record(s)"
msgstr "Registro(s)"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Related"
msgstr "Relacionado"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__edit_related_action
msgid "Related Action"
msgstr "Acción relacionada"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__related_action
msgid "Related Action (serialized)"
msgstr "Acción relacionada (en serie)"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Related Record"
msgstr "Registro relacionado"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Related Records"
msgstr "Registros relacionados"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_tree
msgid "Remaining days to execute"
msgstr "Días restantes para ejecutar"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__removal_interval
msgid "Removal Interval"
msgstr "Intervalo de eliminación"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "Requeue"
msgstr "Volver a poner en la cola"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Requeue Job"
msgstr "Volver a poner el trabajo en la cola"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_requeue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "Requeue Jobs"
msgstr "Volver a poner los trabajos en la cola"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_user_id
msgid "Responsible User"
msgstr "Usuario responsable"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__result
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Result"
msgstr "Resultado"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Results"
msgstr "Resultados"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__edit_retry_pattern
msgid "Retry Pattern"
msgstr "Patrón de reintentos"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__retry_pattern
msgid "Retry Pattern (serialized)"
msgstr "Patrón de reintentos (en serie)"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_sms_error
msgid "SMS Delivery error"
msgstr "Error de entrega del SMS"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_jobs_to_done
msgid "Set all selected jobs to done"
msgstr "Marcar como hechos todos los trabajos seleccionados"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Set jobs done"
msgstr "Marcar trabajos como hechos"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_set_jobs_done
msgid "Set jobs to done"
msgstr "Marcar trabajos como hechos"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Set to 'Done'"
msgstr "Marcar como 'Hecho'"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Set to done"
msgstr "Marcar como hecho"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__graph_uuid
msgid "Single shared identifier of a Graph. Empty for a single job."
msgstr ""
"Identificador único compartido de un gráfico. Vacío para un solo trabajo."

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid ""
"Something bad happened during the execution of the job. More details in the "
"'Exception Information' section."
msgstr ""
"Algo malo pasó durante la ejecución del trabajo. Más detalles en la sección "
"'Información de la excepción'."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_started
msgid "Start Date"
msgstr "Fecha de inicio"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__started
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Started"
msgstr "Iniciado"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__state
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "State"
msgstr "Estado"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"Estado basado en actividades\n"
"Vencida: la fecha tope ya ha pasado\n"
"Hoy: La fecha tope es hoy\n"
"Planificada: futuras actividades."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__func_string
msgid "Task"
msgstr "Tarea"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job_function__edit_related_action
msgid ""
"The action when the button *Related Action* is used on a job. The default "
"action is to open the view of the record related to the job. Configured as a "
"dictionary with optional keys: enable, func_name, kwargs.\n"
"See the module description for details."
msgstr ""
"Acción cuando se usa el botón *Acción relacionada* en un trabajo. La acción "
"por defecto es abrir la vista del registro relacionado con el trabajo. Se "
"configura como un diccionario con estas claves opcionales: enable, "
"func_name, kwargs.\n"
"Vea la descripción del módulo para más detalles."

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__max_retries
msgid ""
"The job will fail if the number of tries reach the max. retries.\n"
"Retries are infinite when empty."
msgstr ""
"El trabajo fallará si alcanza el máx. de re intentos.\n"
"Los reintentos son infinitos si se deja vacío."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
msgid "The selected jobs will be cancelled."
msgstr "Los trabajos seleccionados serán cancelados."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "The selected jobs will be requeued."
msgstr "Los trabajos seleccionados volverán a ponerse en la cola."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "The selected jobs will be set to done."
msgstr "Los trabajos seleccionados se marcarán como hechos."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Time (s)"
msgstr "Tiempo (s)"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__exec_time
msgid "Time required to execute this job in seconds. Average when grouped."
msgstr ""
"Tiempo requerido para ejecutar este trabajo en segundos. Promedio cuando se "
"agrupa."

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Tried many times"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "Tipo de actividad de excepción registrada."

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__uuid
msgid "UUID"
msgstr "UUID"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid ""
"Unexpected format of Related Action for {}.\n"
"Example of valid format:\n"
"{{\"enable\": True, \"func_name\": \"related_action_foo\", "
"\"kwargs\" {{\"limit\": 10}}}}"
msgstr ""
"Formato inesperado en la acción relacionada con {}.\n"
"Ejemplo de un formato válido:\n"
"{{\"enable\": True, \"func_name\": \"related_action_foo\", "
"\"kwargs\" {{\"limit\": 10}}}}"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid ""
"Unexpected format of Retry Pattern for {}.\n"
"Example of valid formats:\n"
"{{1: 300, 5: 600, 10: 1200, 15: 3000}}\n"
"{{1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}}"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__user_id
msgid "User ID"
msgstr "ID de usuario"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__wait_dependencies
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Wait Dependencies"
msgstr "Esperando dependencias"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_requeue_job
msgid "Wizard to requeue a selection of jobs"
msgstr "Asistente para volver a poner en cola una selección de trabajos"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__worker_pid
msgid "Worker Pid"
msgstr "Pid del trabajador"

#, python-format
#~ msgid "Cancelled by %s"
#~ msgstr "Cancelado por %s"

#, python-format
#~ msgid "Job interrupted and set to Done: nothing to do."
#~ msgstr "Trabajo interrumpido y marcado como hecho: nada que hacer."

#, python-format
#~ msgid "Manually set to done by %s"
#~ msgstr "Marcado como hecho a mano por %s"

#~ msgid "Record"
#~ msgstr "Registro"

#, python-format
#~ msgid ""
#~ "Unexpected format of Retry Pattern for {}.\n"
#~ "Example of valid format:\n"
#~ "{{1: 300, 5: 600, 10: 1200, 15: 3000}}"
#~ msgstr ""
#~ "Formato inesperado en el patrón de reintentos de {}.\n"
#~ "Ejemplo de un formato válido:\n"
#~ "{{1: 300, 5: 600, 10: 1200, 15: 3000}}"

#~ msgid "Last Modified on"
#~ msgstr "Última modificación el"

#~ msgid "Main Attachment"
#~ msgstr "Adjuntos principales"

#~ msgid "Number of messages which requires an action"
#~ msgstr "Número de mensajes que requieren una acción"

#~ msgid ""
#~ "<span class=\"oe_grey oe_inline\"> If the max. retries is 0, the number "
#~ "of retries is infinite.</span>"
#~ msgstr ""
#~ "<span class=\"oe_grey oe_inline\"> Si los reintentos máximos son 0, el "
#~ "número de reintentos es infinito.</span>"

#~ msgid "Override Channel"
#~ msgstr "Sobreescribir canal"

#~ msgid "Number of unread messages"
#~ msgstr "Número de mensajes no leidos"
