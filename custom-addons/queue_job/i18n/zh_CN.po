# Translation of Odoo Server.
# This file contains the translation of the following modules:
# 	* queue_job
#
msgid ""
msgstr ""
"Project-Id-Version: Odoo Server 12.0\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2024-07-02 09:47+0000\n"
"Last-Translator: x<PERSON><PERSON>ha <<EMAIL>>\n"
"Language-Team: none\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: \n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Generator: Weblate 4.17\n"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid ""
"<br/>\n"
"                            <span class=\"oe_grey oe_inline\"> If the max. "
"retries is 0, the number of retries is infinite.</span>"
msgstr ""
"<br/>\n"
"                            <span class=\"oe_grey oe_inline\"> 如果最大重试次"
"数设置为0，则表示重试次数是无限的。</span>"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/controllers/main.py:0
msgid "Access Denied"
msgstr "拒绝访问"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_needaction
msgid "Action Needed"
msgstr "前置操作"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_ids
msgid "Activities"
msgstr "活动"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_exception_decoration
msgid "Activity Exception Decoration"
msgstr "活动异常装饰"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_state
msgid "Activity State"
msgstr "活动状态"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_type_icon
msgid "Activity Type Icon"
msgstr "活动类型图标"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__args
msgid "Args"
msgstr "位置参数"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_attachment_count
msgid "Attachment Count"
msgstr "附件数量"

#. module: queue_job
#: model:ir.actions.server,name:queue_job.ir_cron_autovacuum_queue_jobs_ir_actions_server
msgid "AutoVacuum Job Queue"
msgstr "自动清空作业队列"

#. module: queue_job
#: model:ir.model,name:queue_job.model_base
msgid "Base"
msgstr "基础"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Cancel"
msgstr "取消"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_jobs_to_cancelled
msgid "Cancel all selected jobs"
msgstr "取消所有选中作业"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Cancel job"
msgstr "取消作业"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_set_jobs_cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
msgid "Cancel jobs"
msgstr "取消作业"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__cancelled
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Cancelled"
msgstr "已取消"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Cancelled by {}"
msgstr ""

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
msgid "Cannot change the root channel"
msgstr "无法更改root频道"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
msgid "Cannot remove the root channel"
msgstr "无法删除root频道"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__channel
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__channel_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Channel"
msgstr "频道"

#. module: queue_job
#: model:ir.model.constraint,message:queue_job.constraint_queue_job_channel_name_uniq
msgid "Channel complete name must be unique"
msgstr "频道完整名称必须是唯一的"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job_channel
#: model:ir.ui.menu,name:queue_job.menu_queue_job_channel
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_channel_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_channel_search
msgid "Channels"
msgstr "频道"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__company_id
msgid "Company"
msgstr "公司"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__channel_method_name
msgid "Complete Method Name"
msgstr "完整方法名"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__complete_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__channel
msgid "Complete Name"
msgstr "完整名称"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_created
msgid "Created Date"
msgstr "创建日期"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__create_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__create_uid
msgid "Created by"
msgstr "创建者"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Created date"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__create_date
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__create_date
msgid "Created on"
msgstr "创建时间"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__retry
msgid "Current try"
msgstr "当前尝试"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Current try / max. retries"
msgstr "当前尝试/最大重试次数"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_cancelled
msgid "Date Cancelled"
msgstr "取消日期"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_done
msgid "Date Done"
msgstr "完成日期"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__dependencies
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Dependencies"
msgstr "依赖项"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__dependency_graph
msgid "Dependency Graph"
msgstr "依赖关系图"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__name
msgid "Description"
msgstr "说明"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__display_name
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__display_name
msgid "Display Name"
msgstr "显示名称"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__done
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Done"
msgstr "完成"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_enqueued
msgid "Enqueue Time"
msgstr "排队时间"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__enqueued
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Enqueued"
msgstr "排队"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_name
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Exception"
msgstr "异常"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_info
msgid "Exception Info"
msgstr "异常信息"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Exception Information"
msgstr "异常信息"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exc_message
msgid "Exception Message"
msgstr "异常信息"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Exception message"
msgstr "异常信息"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Exception:"
msgstr "异常："

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__eta
msgid "Execute only after"
msgstr "仅在此之后执行"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__exec_time
msgid "Execution Time (avg)"
msgstr "平均执行时间"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__failed
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Failed"
msgstr "失败"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_ir_model_fields__ttype
msgid "Field Type"
msgstr "字段类型"

#. module: queue_job
#: model:ir.model,name:queue_job.model_ir_model_fields
msgid "Fields"
msgstr "字段"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_follower_ids
msgid "Followers"
msgstr "关注者"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_partner_ids
msgid "Followers (Partners)"
msgstr "关注者(业务伙伴)"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_type_icon
msgid "Font awesome icon e.g. fa-tasks"
msgstr "Font Awesome 图标，例如：fa-tasks"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Graph"
msgstr "图表"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Graph Jobs"
msgstr "图表作业"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__graph_jobs_count
msgid "Graph Jobs Count"
msgstr "图表作业数量"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__graph_uuid
msgid "Graph UUID"
msgstr "图表唯一标识符（UUID）"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Group By"
msgstr "分组"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__has_message
msgid "Has Message"
msgstr "有消息"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__id
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__id
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__id
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__id
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__id
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__id
msgid "ID"
msgstr "ID"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_exception_icon
msgid "Icon"
msgstr "图标"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_exception_icon
msgid "Icon to indicate an exception activity."
msgstr "指示异常活动的图标。"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__identity_key
msgid "Identity Key"
msgstr "身份密钥"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "If both parameters are 0, ALL jobs will be requeued!"
msgstr "如果两个参数都为0，所有任务都将被重新排队！"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_needaction
msgid "If checked, new messages require your attention."
msgstr "确认后, 出现提示消息。"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_error
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_sms_error
msgid "If checked, some messages have a delivery error."
msgstr "如果勾选此项， 某些消息将会产生传递错误。"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid "Invalid job function: {}"
msgstr "无效的作业函数：{}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_is_follower
msgid "Is Follower"
msgstr "关注者"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_job_channel
msgid "Job Channels"
msgstr "作业频道"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__job_function_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Job Function"
msgstr "作业函数"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job_function
#: model:ir.model,name:queue_job.model_queue_job_function
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__job_function_ids
#: model:ir.ui.menu,name:queue_job.menu_queue_job_function
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_function_search
msgid "Job Functions"
msgstr "作业函数"

#. module: queue_job
#: model:ir.module.category,name:queue_job.module_category_queue_job
#: model:ir.ui.menu,name:queue_job.menu_queue_job_root
msgid "Job Queue"
msgstr "作业队列"

#. module: queue_job
#: model:res.groups,name:queue_job.group_queue_job_manager
msgid "Job Queue Manager"
msgstr "作业队列管理员"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__ir_model_fields__ttype__job_serialized
msgid "Job Serialized"
msgstr "任务序列化"

#. module: queue_job
#: model:mail.message.subtype,name:queue_job.mt_job_failed
msgid "Job failed"
msgstr "作业失败"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__job_ids
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__job_ids
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__job_ids
#: model:ir.ui.menu,name:queue_job.menu_queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_graph
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_pivot
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Jobs"
msgstr "作业"

#. module: queue_job
#: model:ir.actions.server,name:queue_job.ir_cron_queue_job_garbage_collector_ir_actions_server
msgid "Jobs Garbage Collector"
msgstr "作业垃圾收集器"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Jobs for graph %s"
msgstr "图表 %s 的作业"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__kwargs
msgid "Kwargs"
msgstr "关键字参数"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Last 24 hours"
msgstr ""

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Last 30 days"
msgstr ""

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Last 7 days"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__write_uid
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__write_uid
msgid "Last Updated by"
msgstr "最后更新者"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_cancelled__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_jobs_to_done__write_date
#: model:ir.model.fields,field_description:queue_job.field_queue_requeue_job__write_date
msgid "Last Updated on"
msgstr "最后更新时间"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Manually set to done by {}"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__max_retries
msgid "Max. retries"
msgstr "最大重试次数"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_error
msgid "Message Delivery error"
msgstr "消息递送错误"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_ids
msgid "Messages"
msgstr "消息"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__method
msgid "Method"
msgstr "方法名称"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__method_name
msgid "Method Name"
msgstr "方法名称"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__model_name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__model_id
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Model"
msgstr "模型"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid "Model {} not found"
msgstr "模型 {} 未找到"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__my_activity_date_deadline
msgid "My Activity Deadline"
msgstr "我的活动截止日期"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__name
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__name
msgid "Name"
msgstr "名称"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_date_deadline
msgid "Next Activity Deadline"
msgstr "下一活动截止日期"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_summary
msgid "Next Activity Summary"
msgstr "下一活动摘要"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_type_id
msgid "Next Activity Type"
msgstr "下一活动类型"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "No action available for this job"
msgstr "此作业无法执行任何操作"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Not allowed to change field(s): {}"
msgstr "不允许更改字段：{}"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_needaction_counter
msgid "Number of Actions"
msgstr "操作次数"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_error_counter
msgid "Number of errors"
msgstr "错误数量"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_needaction_counter
msgid "Number of messages requiring action"
msgstr "需要处理的消息数量"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__message_has_error_counter
msgid "Number of messages with delivery error"
msgstr "递送错误消息数量"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__parent_id
msgid "Parent Channel"
msgstr "父频道"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_channel.py:0
msgid "Parent channel required."
msgstr "父频道必填。"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job_function__edit_retry_pattern
msgid ""
"Pattern expressing from the count of retries on retryable errors, the number "
"of of seconds to postpone the next execution. Setting the number of seconds "
"to a 2-element tuple or list will randomize the retry interval between the 2 "
"values.\n"
"Example: {1: 10, 5: 20, 10: 30, 15: 300}.\n"
"Example: {1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}.\n"
"See the module description for details."
msgstr ""
"表达式用于根据重试可重试错误的次数，指定下一次执行推迟的秒数。将秒数设置为一"
"个两个元素的元组或列表将会在两个值之间随机化重试间隔。 \n"
"示例：{1: 10, 5: 20, 10: 30, 15: 300}。 \n"
"示例：{1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}。 \n"
"详情请参阅模块描述。"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__pending
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Pending"
msgstr "等待"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__priority
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Priority"
msgstr "优先级"

#. module: queue_job
#: model:ir.ui.menu,name:queue_job.menu_queue
msgid "Queue"
msgstr "队列"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_job
msgid "Queue Job"
msgstr "队列作业"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Queue jobs must be created by calling 'with_delay()'."
msgstr "队列任务必须通过调用'with_delay()'方法来创建。"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__records
msgid "Record(s)"
msgstr "记录"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Related"
msgstr "相关的"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__edit_related_action
msgid "Related Action"
msgstr "相关动作"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__related_action
msgid "Related Action (serialized)"
msgstr "相关操作（已序列化）"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Related Record"
msgstr "相关记录"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid "Related Records"
msgstr "相关记录"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_tree
msgid "Remaining days to execute"
msgstr "剩余执行天数"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_channel__removal_interval
msgid "Removal Interval"
msgstr "清除间隔"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "Requeue"
msgstr "重新排队"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Requeue Job"
msgstr "重新排队作业"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_requeue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "Requeue Jobs"
msgstr "重新排队作业"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__activity_user_id
msgid "Responsible User"
msgstr "负责的用户"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__result
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Result"
msgstr "结果"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Results"
msgstr "结果"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__edit_retry_pattern
msgid "Retry Pattern"
msgstr "重试模式"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job_function__retry_pattern
msgid "Retry Pattern (serialized)"
msgstr "重试模式（已序列化）"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__message_has_sms_error
msgid "SMS Delivery error"
msgstr "短信传递错误"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_jobs_to_done
msgid "Set all selected jobs to done"
msgstr "将所有选定的作业设置为完成"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Set jobs done"
msgstr "设置作业完成"

#. module: queue_job
#: model:ir.actions.act_window,name:queue_job.action_set_jobs_done
msgid "Set jobs to done"
msgstr "将作业设置为完成"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Set to 'Done'"
msgstr "设置为“完成”"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "Set to done"
msgstr "设置为完成"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__graph_uuid
msgid "Single shared identifier of a Graph. Empty for a single job."
msgstr "图的唯一共享标识符。对于单个任务，则为空。"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job.py:0
msgid ""
"Something bad happened during the execution of the job. More details in the "
"'Exception Information' section."
msgstr ""
"在执行作业期间发生了一些不好的事情。有关详细信息，请参见“异常信息”部分。"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__date_started
msgid "Start Date"
msgstr "开始日期"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__started
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Started"
msgstr "开始"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__state
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "State"
msgstr "状态"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_state
msgid ""
"Status based on activities\n"
"Overdue: Due date is already passed\n"
"Today: Activity date is today\n"
"Planned: Future activities."
msgstr ""
"基于活动的状态\n"
"逾期：已经超过截止日期\n"
"现今：活动日期是当天\n"
"计划：未来的活动。"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__func_string
msgid "Task"
msgstr "任务"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job_function__edit_related_action
msgid ""
"The action when the button *Related Action* is used on a job. The default "
"action is to open the view of the record related to the job. Configured as a "
"dictionary with optional keys: enable, func_name, kwargs.\n"
"See the module description for details."
msgstr ""
"当在作业上使用“相关操作”按钮时所执行的动作。默认动作是打开与该作业相关的记录"
"视图。此配置作为一个字典，包含可选键：enable, func_name, kwargs。 详细配置请"
"参阅模块说明。"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__max_retries
msgid ""
"The job will fail if the number of tries reach the max. retries.\n"
"Retries are infinite when empty."
msgstr ""
"如果尝试次数达到最大重试次数，作业将失败。\n"
"空的时候重试是无限的。"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_cancelled
msgid "The selected jobs will be cancelled."
msgstr "被选中的任务将被取消。"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_requeue_job
msgid "The selected jobs will be requeued."
msgstr "所选作业将重新排队。"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_set_jobs_done
msgid "The selected jobs will be set to done."
msgstr "所选作业将设置为完成。"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_form
msgid "Time (s)"
msgstr "时间（秒）"

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__exec_time
msgid "Time required to execute this job in seconds. Average when grouped."
msgstr "以秒为单位执行此任务所需的时间。分组时为平均值。"

#. module: queue_job
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Tried many times"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,help:queue_job.field_queue_job__activity_exception_decoration
msgid "Type of the exception activity on record."
msgstr "记录的异常活动的类型。"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__uuid
msgid "UUID"
msgstr "UUID"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid ""
"Unexpected format of Related Action for {}.\n"
"Example of valid format:\n"
"{{\"enable\": True, \"func_name\": \"related_action_foo\", "
"\"kwargs\" {{\"limit\": 10}}}}"
msgstr ""
"对于 {}，关联操作的格式不符合预期。 \n"
"有效格式的示例：\n"
"{{\"enable\": True, \"func_name\": \"related_action_foo\", "
"\"kwargs\" {{\"limit\": 10}}}}"

#. module: queue_job
#. odoo-python
#: code:addons/queue_job/models/queue_job_function.py:0
msgid ""
"Unexpected format of Retry Pattern for {}.\n"
"Example of valid formats:\n"
"{{1: 300, 5: 600, 10: 1200, 15: 3000}}\n"
"{{1: (1, 10), 5: (11, 20), 10: (21, 30), 15: (100, 300)}}"
msgstr ""

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__user_id
msgid "User ID"
msgstr "用户"

#. module: queue_job
#: model:ir.model.fields.selection,name:queue_job.selection__queue_job__state__wait_dependencies
#: model_terms:ir.ui.view,arch_db:queue_job.view_queue_job_search
msgid "Wait Dependencies"
msgstr "等待依赖项"

#. module: queue_job
#: model:ir.model,name:queue_job.model_queue_requeue_job
msgid "Wizard to requeue a selection of jobs"
msgstr "重新排队向导所选的作业"

#. module: queue_job
#: model:ir.model.fields,field_description:queue_job.field_queue_job__worker_pid
msgid "Worker Pid"
msgstr "工作进程PID"

#, python-format
#~ msgid "Cancelled by %s"
#~ msgstr "被%s取消"

#, python-format
#~ msgid "Job interrupted and set to Done: nothing to do."
#~ msgstr "作业中断并设置为已完成：无需执行任何操作。"

#, python-format
#~ msgid "Manually set to done by %s"
#~ msgstr "由%s手动设置为完成"

#~ msgid "Record"
#~ msgstr "记录"

#, python-format
#~ msgid ""
#~ "Unexpected format of Retry Pattern for {}.\n"
#~ "Example of valid format:\n"
#~ "{{1: 300, 5: 600, 10: 1200, 15: 3000}}"
#~ msgstr ""
#~ "对于 {}，重试模式的格式不符合预期。 有效格式的示例：\n"
#~ "{{1: 300, 5: 600, 10: 1200, 15: 3000}}"

#~ msgid "Last Modified on"
#~ msgstr "最后修改日"

#~ msgid "Main Attachment"
#~ msgstr "附件"

#~ msgid "Number of messages which requires an action"
#~ msgstr "需要操作消息数量"

#~ msgid ""
#~ "<span class=\"oe_grey oe_inline\"> If the max. retries is 0, the number "
#~ "of retries is infinite.</span>"
#~ msgstr ""
#~ "<span class=\"oe_grey oe_inline\">如果最大重试次数是0，则重试次数是无限"
#~ "的。</span>"

#~ msgid "Override Channel"
#~ msgstr "覆盖频道"

#~ msgid "Number of unread messages"
#~ msgstr "未读消息数量"
