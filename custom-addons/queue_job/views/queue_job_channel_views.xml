<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <record id="view_queue_job_channel_form" model="ir.ui.view">
        <field name="name">queue.job.channel.form</field>
        <field name="model">queue.job.channel</field>
        <field name="arch" type="xml">
            <form string="Channels">
                <group>
                    <field
                        name="name"
                        required="name != 'root'"
                        readonly="name == 'root'"
                    />
                    <field
                        name="parent_id"
                        required="name != 'root'"
                        readonly="name == 'root'"
                    />
                    <field name="complete_name" />
                    <field name="removal_interval" />
                </group>
                <group>
                    <field name="job_function_ids" widget="many2many_tags" />
                </group>
            </form>
        </field>
    </record>

    <record id="view_queue_job_channel_tree" model="ir.ui.view">
        <field name="name">queue.job.channel.tree</field>
        <field name="model">queue.job.channel</field>
        <field name="arch" type="xml">
            <list>
                <field name="complete_name" />
            </list>
        </field>
    </record>

    <record id="view_queue_job_channel_search" model="ir.ui.view">
        <field name="name">queue.job.channel.search</field>
        <field name="model">queue.job.channel</field>
        <field name="arch" type="xml">
            <search string="Channels">
                <field name="name" />
                <field name="complete_name" />
                <field name="parent_id" />
            </search>
        </field>
    </record>

    <record id="action_queue_job_channel" model="ir.actions.act_window">
        <field name="name">Channels</field>
        <field name="res_model">queue.job.channel</field>
        <field name="view_mode">list,form</field>
        <field name="context">{}</field>
        <field name="view_id" ref="view_queue_job_channel_tree" />
    </record>
</odoo>
