<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Work Center Form View -->
    <record id="view_workcenter_toc_form" model="ir.ui.view">
        <field name="name">mrp.workcenter.form.toc</field>
        <field name="model">mrp.workcenter</field>
        <field name="inherit_id" ref="mrp.mrp_workcenter_view"/>
        <field name="arch" type="xml">
            <!-- Add TOC metrics to the form view -->
            <xpath expr="//page[@name='general_info']" position="after">
                <page name="toc_metrics" string="TOC Metrics">
                    <group string="Real-time Metrics">
                        <group string="Current Status">
                            <field name="x_utilization_percentage" widget="progressbar" class="oe_inline"/>
                            <field name="x_work_order_queue_time" class="oe_inline"/>
                            <field name="x_average_processing_time" class="oe_inline"/>
                            <field name="x_wip_value" class="oe_inline"/>
                            <field name="x_scrap_rate_percentage" widget="percentage" class="oe_inline"/>
                        </group>
                        <group string="Daily Performance">
                            <field name="x_actual_load_hours_daily" class="oe_inline"/>
                            <field name="resource_calendar_id" class="oe_inline"/>
                            <field name="department_id" class="oe_inline"/>
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
    
    <!-- Work Center Tree View -->
    <record id="view_workcenter_toc_tree" model="ir.ui.view">
        <field name="name">mrp.workcenter.tree.toc</field>
        <field name="model">mrp.workcenter</field>
        <field name="inherit_id" ref="mrp.mrp_workcenter_tree_view"/>
        <field name="arch" type="xml">
            <!-- Add TOC columns to the tree view -->
            <xpath expr="//field[@name='time_efficiency']" position="after">
                <field name="x_utilization_percentage" widget="progressbar" options="{'editable': False}" class="oe_inline"/>
                <field name="x_work_order_queue_time" class="oe_inline"/>
                <field name="x_wip_value" class="oe_inline"/>
            </xpath>
        </field>
    </record>
    
    <!-- Work Center Kanban View -->
    <record id="view_workcenter_toc_kanban" model="ir.ui.view">
        <field name="name">mrp.workcenter.kanban.toc</field>
        <field name="model">mrp.workcenter</field>
        <field name="inherit_id" ref="mrp.mrp_workcenter_kanban"/>
        <field name="arch" type="xml">
            <!-- Add TOC metrics to the kanban card after the graph -->
            <xpath expr="//field[@name='kanban_dashboard_graph']/.." position="after">
                <div class="row mt-2">
                    <div class="col-12">
                        <div class="row">
                            <div class="col-6">
                                <div>Utilization: <field name="x_utilization_percentage" widget="progressbar" options="{'editable': False}" class="oe_inline"/>%</div>
                                <div>Queue: <field name="x_work_order_queue_time" class="oe_inline"/>h</div>
                            </div>
                            <div class="col-6">
                                <div>WIP Value: <field name="x_wip_value" class="oe_inline"/></div>
                                <div>Scrap Rate: <field name="x_scrap_rate_percentage" class="oe_inline"/>%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </xpath>
        </field>
    </record>
</odoo>
