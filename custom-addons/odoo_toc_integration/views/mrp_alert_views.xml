<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- MRP Alert List View -->
    <record id="view_mrp_alert_list" model="ir.ui.view">
        <field name="name">mrp.alert.list</field>
        <field name="model">mrp.alert</field>
        <field name="arch" type="xml">
            <list>
                <field name="name"/>
                <field name="workcenter_id"/>
                <field name="state"/>
                <field name="create_date"/>
            </list>
        </field>
    </record>

    <!-- MRP Alert Form View -->
    <record id="view_mrp_alert_form" model="ir.ui.view">
        <field name="name">mrp.alert.form</field>
        <field name="model">mrp.alert</field>
        <field name="arch" type="xml">
            <form>
                <header>
                    <button name="action_acknowledge" string="Acknowledge" type="object" class="btn-primary" invisible="state != 'open'"/>
                    <button name="action_resolve" string="Resolve" type="object" class="btn-primary" invisible="state != 'acknowledged'"/>
                    <field name="state" widget="statusbar" statusbar_visible="open,acknowledged,resolved"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="name" readonly="1"/>
                        </h1>
                    </div>
                    <group>
                        <group>
                            <field name="workcenter_id"/>
                        </group>
                        <group>
                            <field name="create_date" string="Creation Date"/>
                        </group>
                    </group>
                    <group string="Message">
                        <field name="message" nolabel="1"/>
                    </group>
                </sheet>
                <chatter/>
            </form>
        </field>
    </record>

    <!-- MRP Alert Action -->
    <record id="action_mrp_alert" model="ir.actions.act_window">
        <field name="name">MRP Alerts</field>
        <field name="res_model">mrp.alert</field>
        <field name="view_mode">list,form</field>
    </record>

    <!-- MRP Alert Menu -->
    <menuitem 
        id="menu_mrp_alert" 
        name="Alerts" 
        parent="mrp.menu_mrp_root" 
        action="action_mrp_alert" 
        sequence="2"/>
</odoo>