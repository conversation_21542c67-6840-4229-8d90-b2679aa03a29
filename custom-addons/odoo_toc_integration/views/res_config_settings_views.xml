<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Configuration Settings View -->
    <record id="res_config_settings_view_form_toc" model="ir.ui.view">
        <field name="name">res.config.settings.view.form.toc</field>
        <field name="model">res.config.settings</field>
        <field name="priority" eval="35"/>
        <field name="inherit_id" ref="base.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="TOC Integration" string="TOC Integration" name="odoo_toc_integration" groups="base.group_system">
                    <block title="TOC Settings">
                        <setting string="Default Shift Hours" help="Default number of hours for a shift">
                            <field name="x_toc_default_shift_hours"/>
                        </setting>
                        <setting string="High Utilization Threshold" help="Threshold for high utilization percentage">
                            <field name="x_toc_high_utilization_threshold"/>
                        </setting>
                        <setting string="Medium Utilization Threshold" help="Threshold for medium utilization percentage">
                            <field name="x_toc_medium_utilization_threshold"/>
                        </setting>
                        <setting string="High Wait Time Multiplier" help="Multiplier for high wait time threshold">
                            <field name="x_toc_high_wait_time_multiplier"/>
                        </setting>
                        <setting string="Medium Wait Time Multiplier" help="Multiplier for medium wait time threshold">
                            <field name="x_toc_medium_wait_time_multiplier"/>
                        </setting>
                    </block>
                </app>
            </xpath>
        </field>
    </record>
</odoo>
