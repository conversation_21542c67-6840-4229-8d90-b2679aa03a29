<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Work Order Form View -->
    <record id="view_workorder_toc_form" model="ir.ui.view">
        <field name="name">mrp.workorder.form.toc</field>
        <field name="model">mrp.workorder</field>
        <field name="inherit_id" ref="mrp.mrp_production_workorder_form_view_inherit" />
        <field name="arch" type="xml">
            <!-- Add TOC metrics to the form view -->
            <xpath expr="//group/group/field[@name='production_id']" position="after">
                <field name="x_start_time_at_current_wc" readonly="1" />
                <field name="x_end_time_at_previous_wc" readonly="1" />
                <field name="x_waiting_duration_minutes" readonly="1" />
            </xpath>
            
            <!-- Add a notebook page for TOC metrics -->
            <xpath expr="//page[@name='components']" position="after">
                <page name="toc_metrics" string="TOC Metrics">
                    <group>
                        <group string="Timing">
                            <field name="x_start_time_at_current_wc" readonly="1" />
                            <field name="x_end_time_at_previous_wc" readonly="1" />
                            <field name="x_waiting_duration_minutes" readonly="1" />
                            <field name="duration" readonly="1" />
                        </group>
                        <group string="Bottleneck Analysis">
                            <field name="workcenter_id" no_create="1" no_open="1" readonly="1" />
                            <field name="x_workcenter_utilization" widget="percentage" string="WC Utilization %" />
                            <field name="x_workcenter_queue_time" string="WC Queue Time (hours)" />
                        </group>
                    </group>
                </page>
            </xpath>
        </field>
    </record>
    
    <!-- Work Order Tree View -->
    <record id="view_workorder_toc_tree" model="ir.ui.view">
        <field name="name">mrp.workorder.tree.toc</field>
        <field name="model">mrp.workorder</field>
        <field name="inherit_id" ref="mrp.mrp_production_workorder_tree_view" />
        <field name="priority" eval="15"/>
        <field name="arch" type="xml">
            <!-- Add TOC columns after the state field -->
            <xpath expr="//field[@name='state']" position="after">
                <field name="x_waiting_duration_minutes" string="Wait Time (min)" />
                <field name="duration" string="Process Time (hrs)" />
                <field name="x_workcenter_utilization" string="WC Util %" widget="percentage" />
            </xpath>
        </field>
    </record>
    
    <!-- Work Order Search View -->
    <record id="view_workorder_toc_search" model="ir.ui.view">
        <field name="name">mrp.workorder.search.toc</field>
        <field name="model">mrp.workorder</field>
        <field name="inherit_id" ref="mrp.view_mrp_production_work_order_search" />
        <field name="arch" type="xml">
            <!-- Add TOC filters and group bys to the search view -->
            <xpath expr="//filter[@name='late']" position="after">
                <filter string="High Wait Time" name="high_wait_time" domain="[('x_waiting_duration_minutes', '>', 60)]"/>
                <separator/>
                
                <filter string="By Work Center Utilization" name="group_by_wc_util" context="{'group_by': 'x_workcenter_utilization'}"/>
                <filter string="By Wait Time" name="group_by_wait_time" context="{'group_by': 'x_waiting_duration_minutes'}"/>
            </xpath>
        </field>
    </record>
</odoo>
