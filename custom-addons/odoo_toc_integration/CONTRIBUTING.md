# Contributing to Odoo TOC Integration

Thank you for your interest in contributing to the Odoo TOC Integration module! We welcome contributions from the community to help improve this project.

## Getting Started

1. **Fork the repository** on GitHub
2. **Clone your fork** locally
3. **Create a new branch** for your changes
4. **Make your changes** following the coding standards
5. **Test your changes** thoroughly
6. **Submit a pull request** with a clear description of your changes

## Development Setup

### Prerequisites

- Python 3.8+
- Odoo 18.0 Community or Enterprise Edition
- Node.js 16+ (for SCSS compilation)
- PostgreSQL 12+

### Installation

1. Set up a Python virtual environment:
   ```bash
   python3 -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

2. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

3. Set up your Odoo development environment following the [official documentation](https://www.odoo.com/documentation/18.0/developer/howtos/rdtraining/01_odoo_rdtraining_install.html).

## Coding Standards

### Python

- Follow [PEP 8](https://www.python.org/dev/peps/pep-0008/) style guide
- Use [PEP 484](https://www.python.org/dev/peps/pep-0484/) type hints
- Keep methods short and focused on a single responsibility
- Write docstrings for all public methods and classes following the Google style:
  ```python
  def example_method(param1, param2):
      """Short description of the method.

      Args:
          param1 (type): Description of param1
          param2 (type): Description of param2

      Returns:
          type: Description of the return value
      """
  ```

### JavaScript

- Follow [Odoo JavaScript Guidelines](https://www.odoo.com/documentation/18.0/developer/reference/frontend/javascript.html)
- Use ES6+ syntax
- Use OWL (Odoo Web Library) components
- Add JSDoc comments for all functions and classes

### SCSS/CSS

- Follow [Odoo SCSS Guidelines](https://www.odoo.com/documentation/18.0/developer/reference/frontend/css.html)
- Use Bootstrap 5 utility classes when possible
- Keep selectors specific and avoid !important
- Use variables for colors, spacing, and other reusable values

## Testing

### Running Tests

1. Start your Odoo instance with test parameters:
   ```bash
   ./odoo-bin -d test_db --test-enable --stop-after-init -i odoo_toc_integration
   ```

### Writing Tests

- Write tests for all new functionality
- Follow the Odoo testing framework guidelines
- Test both success and error cases
- Use the `savepoint_case` fixture for database tests

## Pull Request Process

1. Ensure your code passes all tests
2. Update the documentation if needed
3. Add or update tests for your changes
4. Ensure your commit messages follow the [Conventional Commits](https://www.conventionalcommits.org/) specification
5. Submit your pull request with a clear description of the changes

## Code Review

- All pull requests require at least one approval from a maintainer
- Address all review comments before merging
- Keep pull requests focused and small when possible

## Reporting Issues

When reporting bugs, please include:

- Odoo version
- Module version
- Steps to reproduce the issue
- Expected vs. actual behavior
- Any error messages or logs

## License

By contributing to this project, you agree that your contributions will be licensed under the [LGPL-3.0 license](LICENSE).
