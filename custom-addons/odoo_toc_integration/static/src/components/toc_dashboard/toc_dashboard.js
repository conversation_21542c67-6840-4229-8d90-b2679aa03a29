/** @odoo-module **/

import { Component, onWillStart, onMounted, useState, useRef } from "@odoo/owl";
import { registry } from "@web/core/registry";
import { useService } from "@web/core/utils/hooks";
import { Layout } from "@web/search/layout";
import { standardActionServiceProps } from "@web/webclient/actions/action_service";

export class TocDashboard extends Component {
    static components = {
        Layout,
    };
    static props = { ...standardActionServiceProps };

    static template = "odoo_toc_integration.TocDashboard";

    setup() {
        this.actionService = useService("action");
        this.ormService = useService("orm");
        this.root = useRef("root");
        this.state = useState({
            data: {
                graph_data: { nodes: [], edges: [] },
                summary: {},
                workcenter_overview: [],
            },
            isSidePanelOpen: false,
            selectedNode: null,
        });

        onWillStart(async () => {
            await this.getTocData();
        });

        onMounted(() => {
            this.renderGraph();
        });
    }

    async getTocData() {
        const reportValues = await this.ormService.call(
            "report.toc.dashboard",
            "get_report_values",
            [],
            { context: this.props.action.context }
        );
        this.state.data = reportValues;
        this.renderGraph();
    }

    renderGraph() {
        const { nodes, edges } = this.state.data.graph_data;
        if (!this.root.el || !nodes || !edges) return;

        const svg = this.root.el.querySelector("svg");
        const width = svg.clientWidth;
        const height = svg.clientHeight;
        const g = svg.querySelector("g.graph-content");
        g.innerHTML = ""; // Clear previous content

        // Hierarchical layout
        const levels = {};
        nodes.forEach(node => {
            if (!levels[node.level]) {
                levels[node.level] = [];
            }
            levels[node.level].push(node);
        });

        const levelKeys = Object.keys(levels).sort((a, b) => a - b);
        const columnWidth = width / (levelKeys.length + 1);

        levelKeys.forEach((level, i) => {
            const columnNodes = levels[level];
            const rowHeight = height / (columnNodes.length + 1);
            columnNodes.forEach((node, j) => {
                node.x = columnWidth * (i + 1);
                node.y = rowHeight * (j + 1);
            });
        });

        // Render edges first
        const edgeGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
        edges.forEach(edge => {
            const sourceNode = nodes.find(n => n.id === edge.source);
            const targetNode = nodes.find(n => n.id === edge.target);
            if (sourceNode && targetNode) {
                const line = this.createEdgeElement(sourceNode, targetNode);
                edgeGroup.appendChild(line);
            }
        });
        g.appendChild(edgeGroup);

        // Render nodes on top of edges
        const nodeGroup = document.createElementNS("http://www.w3.org/2000/svg", "g");
        nodes.forEach(node => {
            const nodeEl = this.createNodeElement(node);
            nodeGroup.appendChild(nodeEl);
        });
        g.appendChild(nodeGroup);

        this.setupPanAndZoom(svg, g);
    }

    setupPanAndZoom(svg, g) {
        let isPanning = false;
        let startPoint = { x: 0, y: 0 };
        let endPoint = { x: 0, y: 0 };
        let viewBox = { x: 0, y: 0, w: svg.clientWidth, h: svg.clientHeight };

        svg.addEventListener('mousedown', (e) => {
            isPanning = true;
            startPoint = { x: e.clientX, y: e.clientY };
        });

        svg.addEventListener('mousemove', (e) => {
            if (!isPanning) return;
            endPoint = { x: e.clientX, y: e.clientY };
            var dx = (startPoint.x - endPoint.x) / (viewBox.w / width);
            var dy = (startPoint.y - endPoint.y) / (viewBox.h / height);
            var movedViewBox = { x: viewBox.x + dx, y: viewBox.y + dy, w: viewBox.w, h: viewBox.h };
            svg.setAttribute('viewBox', `${movedViewBox.x} ${movedViewBox.y} ${movedViewBox.w} ${movedViewBox.h}`);
        });

        svg.addEventListener('mouseup', () => {
            isPanning = false;
        });

        svg.addEventListener('mouseleave', () => {
            isPanning = false;
        });

        svg.addEventListener('wheel', (e) => {
            e.preventDefault();
            var w = viewBox.w;
            var h = viewBox.h;
            var mx = e.offsetX;
            var my = e.offsetY;
            var dw = w * Math.sign(e.deltaY) * 0.1;
            var dh = h * Math.sign(e.deltaY) * 0.1;
            var dx = dw * mx / w;
            var dy = dh * my / h;
            viewBox = { x: viewBox.x + dx, y: viewBox.y + dy, w: viewBox.w - dw, h: viewBox.h - dh };
            svg.setAttribute('viewBox', `${viewBox.x} ${viewBox.y} ${viewBox.w} ${viewBox.h}`);
        });
    }

    createNodeElement(node) {
        const g = document.createElementNS("http://www.w3.org/2000/svg", "g");
        g.setAttribute("transform", `translate(${node.x - 60}, ${node.y - 30})`);
        g.setAttribute("class", "graph-node");
        g.dataset.nodeId = node.id;

        const rect = document.createElementNS("http://www.w3.org/2000/svg", "rect");
        rect.setAttribute("width", 120);
        rect.setAttribute("height", 60);
        rect.setAttribute("rx", 5);
        rect.setAttribute("ry", 5);
        
        let fill = '#e9ecef'; // Default color
        if (node.type === 'wc') {
            fill = node.data.color === 'danger' ? '#f8d7da' : (node.data.color === 'warning' ? '#fff3cd' : '#d1e7dd');
        } else if (node.type === 'mo') {
            fill = '#cfe2ff';
        } else if (node.type === 'so') {
            fill = '#dff0d8';
        }
        rect.setAttribute("fill", fill);
        rect.setAttribute("stroke", "#adb5bd");
        rect.setAttribute("stroke-width", 1);

        const title = document.createElementNS("http://www.w3.org/2000/svg", "text");
        title.setAttribute("x", 60);
        title.setAttribute("y", 20);
        title.setAttribute("text-anchor", "middle");
        title.setAttribute("font-weight", "bold");
        title.textContent = node.label;

        const typeText = document.createElementNS("http://www.w3.org/2000/svg", "text");
        typeText.setAttribute("x", 60);
        typeText.setAttribute("y", 45);
        typeText.setAttribute("text-anchor", "middle");
        typeText.textContent = `(${node.type.toUpperCase()})`;

        g.appendChild(rect);
        g.appendChild(title);
        g.appendChild(typeText);

        g.addEventListener('click', () => this.onNodeClick(node));
        return g;
    }

    createEdgeElement(source, target) {
        const line = document.createElementNS("http://www.w3.org/2000/svg", "line");
        line.setAttribute("x1", source.x);
        line.setAttribute("y1", source.y);
        line.setAttribute("x2", target.x);
        line.setAttribute("y2", target.y);
        line.setAttribute("stroke", "#6c757d");
        line.setAttribute("stroke-width", 2);
        line.setAttribute("marker-end", "url(#arrowhead)");
        return line;
    }

    async onNodeClick(node) {
        const details = await this.ormService.call(
            "report.toc.dashboard",
            "get_node_details",
            [node.id]
        );
        this.state.selectedNode = details;
        this.state.isSidePanelOpen = true;
    }

    closeSidePanel() {
        this.state.isSidePanelOpen = false;
        this.state.selectedNode = null;
    }

    onBackToManufacturing() {
        this.actionService.doAction("mrp.mrp_production_action");
    }

    get activeId() {
        return this.props.action.context.active_id;
    }
}

registry.category("actions").add("toc_dashboard", TocDashboard);