<?xml version="1.0" encoding="UTF-8"?>
<templates xml:space="preserve">

    <div t-name="odoo_toc_integration.TocDashboard" class="o_action" t-ref="root">
        <Layout display="{ controlPanel: {} }">
            <t t-set-slot="control-panel-create-button">
                <button class="btn btn-primary" t-on-click="onBackToManufacturing">Back to Manufacturing</button>
            </t>
            <div class="overflow-auto border-bottom bg-view container-fluid p-4">
                <h1 class="mb-4">TOC Dashboard</h1>

                <div class="row mb-4">
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Total Work Centers</h5>
                                <p class="card-text fs-2"><t t-esc="state.data.summary.total_workcenters"/></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Bottlenecks Identified</h5>
                                <p class="card-text fs-2 text-danger"><t t-esc="state.data.summary.bottleneck_count"/></p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">Average Utilization</h5>
                                <p class="card-text fs-2"><t t-esc="state.data.summary.avg_utilization"/>%</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <h2 class="mt-4">Work Centers Overview</h2>
                <div class="table-responsive mb-4">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="width: 40%;">Work Center Name</th>
                                <th style="width: 25%;">Working Time</th>
                                <th style="width: 20%;">Utilization</th>
                                <th style="width: 15%;">Bottleneck</th>
                            </tr>
                        </thead>
                        <tbody>
                            <t t-if="state.data.workcenter_overview and state.data.workcenter_overview.length > 0">
                                <t t-foreach="state.data.workcenter_overview" t-as="wc" t-key="wc.id">
                                    <tr t-att-class="wc.is_bottleneck ? 'table-danger' : 'table-success'">
                                        <td><t t-esc="wc.name"/></td>
                                        <td><t t-esc="wc.working_time"/></td>
                                        <td><t t-esc="wc.utilization"/>%</td>
                                        <td>
                                            <span t-if="wc.is_bottleneck" class="badge bg-danger">Yes</span>
                                            <span t-else="" class="badge bg-success">No</span>
                                        </td>
                                    </tr>
                                </t>
                            </t>
                            <t t-else="">
                                <tr>
                                    <td colspan="4" class="text-center">No work center data available.</td>
                                </tr>
                            </t>
                        </tbody>
                    </table>
                </div>

                <h2 class="mt-4">Process Flow</h2>
                <div class="toc-graph-container border bg-light" style="height: 600px; position: relative;">
                    <svg class="w-100 h-100">
                        <defs>
                            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="0" refY="3.5" orient="auto">
                                <polygon points="0 0, 10 3.5, 0 7" />
                            </marker>
                        </defs>
                        <g class="graph-content">
                            <g class="graph-edges">
                                <!-- Edges will be rendered here by JS -->
                            </g>
                            <g class="graph-nodes">
                                <!-- Nodes will be rendered here by JS -->
                            </g>
                        </g>
                    </svg>
                </div>

                <!-- Side Panel for Node Details -->
                <div t-if="state.isSidePanelOpen" class="side-panel-backdrop" t-on-click="closeSidePanel"/>
                <div t-if="state.isSidePanelOpen" class="side-panel">
                    <div class="side-panel-header">
                        <h4>Node Details</h4>
                        <button class="btn-close" t-on-click="closeSidePanel"/>
                    </div>
                    <div class="side-panel-body">
                        <t t-if="state.selectedNode">
                            <p><strong>Name:</strong> <t t-esc="state.selectedNode.name"/></p>
                            <p><strong>Model:</strong> <t t-esc="state.selectedNode.model"/></p>
                            <p><strong>ID:</strong> <t t-esc="state.selectedNode.id"/></p>
                            <p><t t-esc="state.selectedNode.details"/></p>
                        </t>
                        <t t-else="">
                            <p>Loading...</p>
                        </t>
                    </div>
                </div>
            </div>
        </Layout>
    </div>

</templates>