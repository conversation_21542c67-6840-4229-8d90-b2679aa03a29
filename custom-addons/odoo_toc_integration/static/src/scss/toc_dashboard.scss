/* Main Dashboard Container */
.o_toc_dashboard {
    display: flex;
    flex-direction: column;
    height: 100%;
    background-color: #f0f4f7;
    color: #333;
}

/* Header */
.o_toc_dashboard_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 20px;
    background-color: #fff;
    border-bottom: 1px solid #dee2e6;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.o_toc_dashboard_header h1 {
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
    color: #4c4c4c;
}

/* Controls */
.o_toc_dashboard_controls {
    display: flex;
    gap: 10px;
}

/* Main Content Area */
.o_toc_dashboard_content {
    flex-grow: 1;
    position: relative;
    overflow: hidden;
}

/* Loading Indicator */
.o_toc_loading {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.8);
    z-index: 10;
}

.o_toc_loading span {
    margin-left: 15px;
    font-size: 1.2rem;
    color: #555;
}

/* SVG Graph */
.o_toc_svg {
    width: 100%;
    height: 100%;
}

.workcenter-bar {
    transition: opacity 0.2s ease-in-out;
}

.workcenter-bar:hover {
    opacity: 0.8;
}

/* Legend */
.o_toc_legend {
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 250px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease-in-out;
    transform: translateY(120%);
    z-index: 5;
}

.o_toc_legend_visible {
    transform: translateY(0);
}

.o_toc_legend_header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    border-bottom: 1px solid #dee2e6;
}

.o_toc_legend_header h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.o_toc_close_legend {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
}

.o_toc_legend_content {
    padding: 15px;
}

.o_toc_legend_item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.o_toc_legend_item:last-child {
    margin-bottom: 0;
}

.o_toc_legend_color {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    margin-right: 10px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .o_toc_dashboard_header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .o_toc_dashboard_controls {
        width: 100%;
        justify-content: space-between;
    }

    .o_toc_legend {
        width: 100%;
        bottom: 0;
        right: 0;
        border-radius: 0;
    }
}

/* Side Panel for Node Details */
.side-panel-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1050;
}

.side-panel {
    position: fixed;
    top: 0;
    right: 0;
    width: 400px;
    height: 100%;
    background-color: white;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
    z-index: 1051;
    display: flex;
    flex-direction: column;
    padding: 1rem;
    transform: translateX(100%);
    transition: transform 0.3s ease-in-out;
}

.side-panel.is-open {
    transform: translateX(0);
}

.side-panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #dee2e6;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
}

.side-panel-body {
    flex-grow: 1;
    overflow-y: auto;
}

/* Graph Node Styling */
.graph-node {
    cursor: pointer;
    transition: opacity 0.2s ease-in-out;
}

.graph-node:hover {
    opacity: 0.7;
}
