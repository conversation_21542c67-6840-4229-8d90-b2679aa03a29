# -*- coding: utf-8 -*-

from odoo import api, models, _


class ResGroups(models.Model):
    _inherit = 'res.groups'
    
    @api.model
    def set_group_hierarchy(self, args):
        """
        Set up group hierarchy by adding implied groups.
        
        :param args: Tuple containing (implied_group_ids, group_vals)
                     where implied_group_ids is a list of group IDs to be added as implied groups
                     and group_vals is a dictionary containing the ID of the group to update
        :return: True
        """
        if not args or len(args) != 2:
            return True
            
        implied_group_ids = []
        group_id = False
        
        # Handle implied_group_ids (first argument)
        if isinstance(args[0], list):
            # Handle case where we have [(4, id)] format
            implied_group_ids = [x[1] for x in args[0] if isinstance(x, (list, tuple)) and len(x) > 1 and x[0] == 4]
        
        # Handle group_id (second argument)
        if isinstance(args[1], dict) and 'id' in args[1]:
            group_id = args[1]['id']
        
        if group_id and implied_group_ids:
            group = self.browse(group_id)
            if group:
                group.write({'implied_ids': [(6, 0, implied_group_ids)]})
        return True
