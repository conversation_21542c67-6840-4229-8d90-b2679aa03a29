# -*- coding: utf-8 -*-
from odoo import models, fields, api, tools
from datetime import datetime, time

class Mr<PERSON><PERSON><PERSON><PERSON>center(models.Model):
    """Extend mrp.workcenter to add TOC specific fields and methods."""
    _inherit = 'mrp.workcenter'
    
    # Add database index for performance
    _sql_constraints = [
        ('name_company_uniq', 'unique (name, company_id)', 'The name of the work center must be unique per company!'),
    ]
    
    def _auto_init(self):
        # Create indexes when the module is installed or updated
        res = super()._auto_init()
        tools.create_index(
            self._cr, 'mrp_workcenter_company_idx',
            self._table, ['company_id']
        )
        tools.create_index(
            self._cr, 'mrp_workcenter_active_idx',
            self._table, ['active']
        )
        return res

    # TOC Metrics
    x_work_order_queue_time = fields.Float(
        compute='_compute_x_toc_metrics',
        string='Queue Time (hours)',
        store=True,
        help="Total estimated hours of work orders in queue for this work center"
    )
    
    x_actual_load_hours_daily = fields.Float(
        compute='_compute_x_toc_metrics',
        string='Daily Load (hours)',
        store=True,
        help="Actual hours worked at this work center today"
    )
    
    x_utilization_percentage = fields.Float(
        compute='_compute_x_toc_metrics',
        string='Utilization %',
        store=True,
        help="Percentage of work center capacity utilized today"
    )
    
    x_average_processing_time = fields.Float(
        compute='_compute_x_toc_metrics',
        string='Avg. Processing Time (hours)',
        store=True,
        help="Average time taken to complete work orders at this work center"
    )
    
    x_wip_value = fields.Monetary(
        compute='_compute_x_toc_metrics',
        string='WIP Value',
        currency_field='company_currency_id',
        store=True,
        help="Estimated value of work in progress at this work center"
    )
    
    x_scrap_rate_percentage = fields.Float(
        compute='_compute_x_toc_metrics',
        string='Scrap Rate %',
        store=True,
        help="Percentage of scrapped items at this work center"
    )

    x_is_bottleneck = fields.Boolean(
        compute='_compute_x_toc_metrics',
        string='Is Bottleneck',
        store=True,
        help="True if the work center is a bottleneck"
    )

    x_utilization_color = fields.Char(
        compute='_compute_x_toc_metrics',
        string='Utilization Color',
        store=True,
        help="Color representation of the utilization percentage"
    )
    
    company_currency_id = fields.Many2one(
        'res.currency',
        related='company_id.currency_id',
        string='Company Currency',
        readonly=True
    )

    department_id = fields.Many2one(
        'hr.department',
        string='Department',
        help="Department responsible for this work center."
    )

    def button_recalculate_toc_metrics(self):
        """
        Button to manually trigger the recalculation of TOC metrics for the selected work centers.
        """
        self._compute_x_toc_metrics()
        return True

    @api.depends('order_ids.state')
    def _compute_x_toc_metrics(self):
        """
        Compute TOC metrics for each work center with optimized queries.
        This method now focuses on the current, active workload.
        """
        if not self:
            return

        # Get all work center IDs
        wc_ids = self.ids
        
        # Batch 1: Get all active (ready, progress) work orders for queue time and utilization
        active_wo_domain = [
            ('workcenter_id', 'in', wc_ids),
            ('state', 'in', ['ready', 'progress'])
        ]
        active_work_data = self.env['mrp.workorder'].read_group(
            active_wo_domain,
            ['workcenter_id', 'duration_expected:sum'],
            ['workcenter_id']
        )
        active_work_map = {res['workcenter_id'][0]: res['duration_expected'] for res in active_work_data}

        # Batch 2: Get average processing times (last 50 completed orders per work center)
        avg_processing_times = {}
        for wc_id in wc_ids:
            recent_done_wo = self.env['mrp.workorder'].search([
                ('workcenter_id', '=', wc_id),
                ('state', '=', 'done'),
                ('duration', '>', 0)
            ], order='date_finished desc', limit=50)
            
            if recent_done_wo:
                avg_processing_times[wc_id] = sum(wo.duration for wo in recent_done_wo) / len(recent_done_wo)
            else:
                avg_processing_times[wc_id] = 0.0

        # Update all work centers in a single pass
        for wc in self:
            # Get the total expected duration of the active workload (in minutes)
            active_load_duration_minutes = active_work_map.get(wc.id, 0.0)
            
            # Convert minutes to hours for all calculations
            active_load_duration_hours = active_load_duration_minutes / 60.0
            
            # Queue time is the total expected time of work not yet started
            wc.x_work_order_queue_time = active_load_duration_hours
            
            # Daily load is the active load, as we are now focused on the present
            wc.x_actual_load_hours_daily = active_load_duration_hours
            
            # Calculate utilization based on the active workload (in hours) against the calendar (in hours)
            calendar_hours = wc.resource_calendar_id.hours_per_day or 8
            # The 'percentage' widget in the view multiplies by 100, so we just need to provide the ratio.
            wc.x_utilization_percentage = (
                active_load_duration_hours / calendar_hours
                if calendar_hours > 0 else 0
            )
            
            # Set average processing time
            wc.x_average_processing_time = avg_processing_times.get(wc.id, 0.0)
            
            # Calculate WIP value (simplified - value of active MOs at this WC)
            active_mos = self.env['mrp.production'].search([
                ('workorder_ids.workcenter_id', '=', wc.id),
                ('state', 'in', ['confirmed', 'progress'])
            ])
            wc.x_wip_value = sum(
                mo.product_qty * mo.product_id.standard_price 
                for mo in active_mos
                if mo.product_id and mo.product_id.standard_price > 0
            )
            
            # Calculate scrap rate (simplified - all time)
            total_scrapped_qty = 0
            if 'mrp.scrap' in self.env:
                scrap_records = self.env['mrp.scrap'].search([('workcenter_id', '=', wc.id)])
                total_scrapped_qty = sum(scrap_records.mapped('scrap_qty'))
            
            completed_wo_at_wc = self.env['mrp.workorder'].search([
                ('workcenter_id', '=', wc.id),
                ('state', '=', 'done')
            ])
            total_produced_qty = sum(wo.qty_produced for wo in completed_wo_at_wc if wo.qty_produced > 0)
            
            if total_produced_qty + total_scrapped_qty > 0:
                wc.x_scrap_rate_percentage = (
                    (total_scrapped_qty / (total_produced_qty + total_scrapped_qty)) * 100
                )
            else:
                wc.x_scrap_rate_percentage = 0.0

            # Set bottleneck status
            threshold = self.env['ir.config_parameter'].sudo().get_param('odoo_toc_integration.high_utilization_threshold', 90.0)
            wc.x_is_bottleneck = wc.x_utilization_percentage >= float(threshold)

            # Set utilization color
            if wc.x_utilization_percentage >= float(threshold):
                wc.x_utilization_color = 'danger'
            elif wc.x_utilization_percentage >= float(self.env['ir.config_parameter'].sudo().get_param('odoo_toc_integration.medium_utilization_threshold', 70.0)):
                wc.x_utilization_color = 'warning'
            else:
                wc.x_utilization_color = 'success'

            # Send notification to the dashboard
            self.env['bus.bus']._sendone(
                'toc_dashboard',
                'toc_update',
                {
                    'workcenter_id': wc.id,
                    'metrics': {
                        'x_utilization_percentage': wc.x_utilization_percentage,
                        'x_utilization_color': wc.x_utilization_color,
                        'x_work_order_queue_time': wc.x_work_order_queue_time,
                        'x_average_processing_time': wc.x_average_processing_time,
                        'x_wip_value': wc.x_wip_value,
                        'x_scrap_rate_percentage': wc.x_scrap_rate_percentage,
                    }
                }
            )
            
            # Check for bottlenecks and create alerts
            self._check_for_bottleneck_alerts(wc)

    def _check_for_bottleneck_alerts(self, workcenter):
        """Check for bottlenecks and create alerts if necessary."""
        threshold = self.env['ir.config_parameter'].sudo().get_param('odoo_toc_integration.high_utilization_threshold', 90.0)
        if workcenter.x_utilization_percentage >= float(self.env['ir.config_parameter'].sudo().get_param('odoo_toc_integration.high_utilization_threshold', 90.0)):
            # Check if an alert already exists for this workcenter today
            today_start = fields.Datetime.to_string(datetime.combine(fields.Date.today(), time.min))
            existing_alert = self.env['mrp.alert'].search([
                ('workcenter_id', '=', workcenter.id),
                ('create_date', '>=', today_start),
                ('state', '=', 'open')
            ], limit=1)
            
            if not existing_alert:
                self.env['mrp.alert'].create({
                    'name': f"Bottleneck detected at {workcenter.name}",
                    'workcenter_id': workcenter.id,
                    'message': f"Workcenter {workcenter.name} has reached {workcenter.x_utilization_percentage:.2f}% utilization.",
                    'state': 'open',
                })
