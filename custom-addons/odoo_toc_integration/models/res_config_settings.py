# -*- coding: utf-8 -*-
from odoo import models, fields, api

class ResConfigSettings(models.TransientModel):
    """Extend res.config.settings to add TOC configuration options."""
    _inherit = 'res.config.settings'

    # TOC Configuration
    x_toc_default_shift_hours = fields.Float(
        string='Default Shift Hours',
        default=8.0,
        config_parameter='odoo_toc_integration.default_shift_hours',
        help="Default number of working hours per shift for utilization calculations"
    )
    
    x_toc_high_utilization_threshold = fields.Float(
        string='High Utilization Threshold (%)',
        default=90.0,
        config_parameter='odoo_toc_integration.high_utilization_threshold',
        help="Utilization percentage above which a work center is considered a bottleneck"
    )
    
    x_toc_medium_utilization_threshold = fields.Float(
        string='Medium Utilization Threshold (%)',
        default=70.0,
        config_parameter='odoo_toc_integration.medium_utilization_threshold',
        help="Utilization percentage above which a work center is at risk of becoming a bottleneck"
    )
    
    x_toc_high_wait_time_multiplier = fields.Float(
        string='High Wait Time Multiplier',
        default=2.0,
        config_parameter='odoo_toc_integration.high_wait_time_multiplier',
        help="Multiplier of average processing time to determine high wait time"
    )
    
    x_toc_medium_wait_time_multiplier = fields.Float(
        string='Medium Wait Time Multiplier',
        default=1.0,
        config_parameter='odoo_toc_integration.medium_wait_time_multiplier',
        help="Multiplier of average processing time to determine medium wait time"
    )
    
    def set_values(self):
        res = super(ResConfigSettings, self).set_values()
        set_param = self.env['ir.config_parameter'].sudo().set_param
        set_param('odoo_toc_integration.default_shift_hours', self.x_toc_default_shift_hours)
        set_param('odoo_toc_integration.high_utilization_threshold', self.x_toc_high_utilization_threshold)
        set_param('odoo_toc_integration.medium_utilization_threshold', self.x_toc_medium_utilization_threshold)
        set_param('odoo_toc_integration.high_wait_time_multiplier', self.x_toc_high_wait_time_multiplier)
        set_param('odoo_toc_integration.medium_wait_time_multiplier', self.x_toc_medium_wait_time_multiplier)
        return res

    @api.model
    def get_values(self):
        """Get the current configuration values."""
        res = super(ResConfigSettings, self).get_values()
        get_param = self.env['ir.config_parameter'].sudo().get_param
        
        res.update(
            x_toc_default_shift_hours=float(get_param('odoo_toc_integration.default_shift_hours', 8.0)),
            x_toc_high_utilization_threshold=float(get_param('odoo_toc_integration.high_utilization_threshold', 90.0)),
            x_toc_medium_utilization_threshold=float(get_param('odoo_toc_integration.medium_utilization_threshold', 70.0)),
            x_toc_high_wait_time_multiplier=float(get_param('odoo_toc_integration.high_wait_time_multiplier', 2.0)),
            x_toc_medium_wait_time_multiplier=float(get_param('odoo_toc_integration.medium_wait_time_multiplier', 1.0)),
        )
        return res
    
