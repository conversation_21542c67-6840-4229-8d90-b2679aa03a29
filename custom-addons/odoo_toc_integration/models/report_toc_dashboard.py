# -*- coding: utf-8 -*-

from odoo import models, api

class ReportTocDashboard(models.AbstractModel):
    _name = 'report.toc.dashboard'
    _description = 'TOC Dashboard Report'

    @api.model
    def get_report_values(self, docids=None, data=None):
        """
        This function is called from the JS widget. It returns the data to render the dashboard,
        building a comprehensive graph of the manufacturing process.
        """
        node_map = {}
        edges = []

        def _add_node(node_id, node_type, label, level, data=None):
            if node_id not in node_map:
                node_map[node_id] = {
                    'id': node_id,
                    'type': node_type,
                    'label': label,
                    'level': level,
                    'data': data or {}
                }

        def _add_edge(source_id, target_id):
            edges.append({'source': source_id, 'target': target_id})

        def _build_graph_from_mo(mo, is_root=False, root_node_id=None, level_offset=0):
            mo_node_id = f'mo_{mo.id}'
            level = level_offset if is_root else level_offset + 1
            _add_node(mo_node_id, 'mo', mo.name, level, {'state': mo.state, 'product': mo.product_id.display_name})
            if root_node_id:
                _add_edge(root_node_id, mo_node_id)

            for wo in mo.workorder_ids.filtered(lambda w: w.state not in ('done', 'cancel')):
                wc = wo.workcenter_id
                wc_node_id = f'wc_{wc.id}'
                _add_node(wc_node_id, 'wc', wc.name, level + 1, {
                    'utilization': wc.x_utilization_percentage,
                    'is_bottleneck': wc.x_is_bottleneck,
                    'color': wc.x_utilization_color,
                })
                _add_edge(mo_node_id, wc_node_id)

            # Find POs created from this MO's needs by traversing stock moves
            # Find POs created from this MO's needs using the standard Odoo method
            for po in mo._get_purchase_orders().filtered(lambda p: p.state not in ['done', 'cancel']):
                po_node_id = f'po_{po.id}'
                _add_node(po_node_id, 'po', po.name, level - 1 if level > 0 else 0, {'state': po.state, 'amount': po.amount_total})
                _add_edge(po_node_id, mo_node_id)

        def _build_graph_from_so(so):
            # An SO is ongoing if it has outgoing pickings that are not done/cancelled
            # or if it's not fully invoiced.
            ongoing_pickings = so.picking_ids.filtered(
                lambda p: p.picking_type_code == 'outgoing' and p.state not in ('done', 'cancel')
            )
            if not ongoing_pickings and so.invoice_status == 'invoiced':
                return False # Skip this completed SO

            so_node_id = f'so_{so.id}'
            _add_node(so_node_id, 'so', so.name, 0, {'state': so.state, 'amount': so.amount_total})

            productions = self.env['mrp.production'].search([
                ('origin', '=', so.name),
                ('state', 'not in', ['done', 'cancel'])
            ])
            for mo in productions:
                _build_graph_from_mo(mo, root_node_id=so_node_id, level_offset=0)

            # Use the already filtered ongoing_pickings
            for do in ongoing_pickings:
                do_node_id = f'do_{do.id}'
                _add_node(do_node_id, 'do', do.name, 1, {'state': do.state})
                _add_edge(so_node_id, do_node_id)

                invoices = so.invoice_ids.filtered(lambda i: i.state not in ('posted', 'cancel'))
                for inv in invoices:
                    inv_node_id = f'inv_{inv.id}'
                    _add_node(inv_node_id, 'inv', inv.name, 2, {'state': inv.state, 'amount': inv.amount_total})
                    _add_edge(do_node_id, inv_node_id)
            
            return True # SO was processed

        # Process flows starting from Sales Orders
        sales_orders = self.env['sale.order'].search(
            [('state', '=', 'sale'), ('picking_ids', '!=', False)],
            limit=50,  # Fetch more to find enough ongoing orders
            order='date_order desc'
        )
        
        processed_so_count = 0
        for so in sales_orders:
            if processed_so_count >= 10:
                break
            if _build_graph_from_so(so):
                processed_so_count += 1

        # Process flows for standalone Manufacturing Orders (not linked to SOs)
        linked_mo_origins = [so.name for so in sales_orders if so.name]
        standalone_mos = self.env['mrp.production'].search([
            ('origin', 'not in', linked_mo_origins),
            ('state', 'not in', ['done', 'cancel']),
        ], limit=10, order='create_date desc')
        for mo in standalone_mos:
            _build_graph_from_mo(mo, is_root=True, level_offset=0)

        nodes = list(node_map.values())
        
        # Summary calculations
        workcenters = self.env['mrp.workcenter'].search([])
        bottleneck_count = sum(1 for wc in workcenters if wc.x_is_bottleneck)
        total_utilization = sum(wc.x_utilization_percentage for wc in workcenters)
        avg_utilization = total_utilization / len(workcenters) if workcenters else 0

        workcenter_overview = []
        for wc in workcenters:
            workcenter_overview.append({
                'id': wc.id,
                'name': wc.name,
                'working_time': wc.resource_calendar_id.name or 'N/A',
                'utilization': round(wc.x_utilization_percentage, 2),
                'is_bottleneck': wc.x_is_bottleneck,
            })

        return {
            'graph_data': {
                'nodes': nodes,
                'edges': edges,
            },
            'summary': {
                'total_workcenters': len(workcenters),
                'bottleneck_count': bottleneck_count,
                'avg_utilization': round(avg_utilization, 2),
            },
            'workcenter_overview': workcenter_overview,
        }

    @api.model
    def get_node_details(self, node_id):
        """
        Fetches detailed information for a specific node, including record details
        and its direct upstream and downstream dependencies.
        """
        if not node_id or '_' not in node_id:
            return {'error': 'Invalid node ID'}

        node_type, record_id_str = node_id.split('_', 1)
        try:
            record_id = int(record_id_str)
        except ValueError:
            return {'error': 'Invalid record ID'}

        model_mapping = {
            'so': 'sale.order',
            'mo': 'mrp.production',
            'wc': 'mrp.workcenter',
            'po': 'purchase.order',
            'do': 'stock.picking',
            'inv': 'account.move',
        }

        model_name = model_mapping.get(node_type)
        if not model_name or not self.env['ir.model']._get(model_name):
            return {'error': 'Invalid node type'}

        record = self.env[model_name].browse(record_id)
        if not record.exists():
            return {'error': 'Record not found'}

        # For simplicity, we'll just return the display_name for now.
        # A full implementation would fetch more details and dependencies.
        details = {
            'name': record.display_name,
            'model': model_name,
            'id': record.id,
            'details': f"Details for {model_name} record with ID {record.id}."
        }

        return details
