# -*- coding: utf-8 -*-
from odoo import models, fields, api, tools
from datetime import datetime, timedelta

class Mrp<PERSON>orkorder(models.Model):
    """Extend mrp.workorder to add TOC specific fields and methods."""
    _inherit = 'mrp.workorder'
    
    def _auto_init(self):
        # Create indexes when the module is installed or updated
        res = super()._auto_init()
        # Create indexes for performance
        tools.create_index(
            self._cr, 'mrp_workorder_workcenter_idx',
            self._table, ['workcenter_id']
        )
        tools.create_index(
            self._cr, 'mrp_workorder_production_idx',
            self._table, ['production_id']
        )
        tools.create_index(
            self._cr, 'mrp_workorder_state_idx',
            self._table, ['state']
        )
        tools.create_index(
            self._cr, 'mrp_workorder_date_finished_idx',
            self._table, ['date_finished']
        )
        tools.create_index(
            self._cr, 'mrp_workorder_sequence_idx',
            self._table, ['sequence']
        )
        return res

    # TOC Metrics
    x_end_time_at_previous_wc = fields.Datetime(
        string='End Time at Previous WC',
        copy=False,
        help="Timestamp when the previous work order in the sequence was completed"
    )
    
    x_start_time_at_current_wc = fields.Datetime(
        string='Start Time at Current WC',
        copy=False,
        help="Timestamp when this work order started at the current work center"
    )
    
    x_waiting_duration_minutes = fields.Float(
        compute='_compute_x_waiting_duration_minutes',
        string='Waiting Duration (minutes)',
        store=True,  # Store in database to allow searching
        help="Time spent waiting between the previous work order and starting this one"
    )
    
    # Workcenter metrics
    x_workcenter_utilization = fields.Float(
        compute='_compute_workcenter_utilization',
        string='WC Util %',
        store=True,
        help='Workcenter utilization percentage for this specific work order.'
    )
    
    x_workcenter_queue_time = fields.Float(
        related='workcenter_id.x_work_order_queue_time',
        string='Workcenter Queue Time (hours)',
        store=False,
        readonly=True,
        help='Current queue time for the assigned workcenter.'
    )
    
    @api.depends('date_start', 'date_finished', 'duration')
    def _compute_workcenter_utilization(self):
        """
        Compute the utilization for this specific work order against its workcenter's daily capacity.
        This is now a stored field and will be recalculated when the key date/duration fields change.
        """
        for wo in self:
            if not wo.workcenter_id:
                wo.x_workcenter_utilization = 0.0
                continue
            
            # Use real duration if available, otherwise fall back to expected duration
            duration_in_minutes = wo.duration if wo.date_finished else wo.duration_expected
            if not duration_in_minutes:
                wo.x_workcenter_utilization = 0.0
                continue

            calendar_hours = wo.workcenter_id.resource_calendar_id.hours_per_day or 8
            if calendar_hours > 0:
                duration_hours = duration_in_minutes / 60.0
                # The 'percentage' widget in the view multiplies by 100, so we provide the ratio.
                wo.x_workcenter_utilization = duration_hours / calendar_hours
            else:
                wo.x_workcenter_utilization = 0.0

    @api.depends('x_start_time_at_current_wc', 'x_end_time_at_previous_wc')
    def _compute_x_waiting_duration_minutes(self):
        """Calculate waiting duration in minutes between work centers."""
        for wo in self:
            if wo.x_start_time_at_current_wc and wo.x_end_time_at_previous_wc:
                try:
                    if isinstance(wo.x_start_time_at_current_wc, str):
                        start_dt = fields.Datetime.from_string(wo.x_start_time_at_current_wc)
                    else:
                        start_dt = wo.x_start_time_at_current_wc
                        
                    if isinstance(wo.x_end_time_at_previous_wc, str):
                        end_dt = fields.Datetime.from_string(wo.x_end_time_at_previous_wc)
                    else:
                        end_dt = wo.x_end_time_at_previous_wc
                        
                    if start_dt and end_dt and start_dt > end_dt:
                        wo.x_waiting_duration_minutes = (start_dt - end_dt).total_seconds() / 60.0
                    else:
                        wo.x_waiting_duration_minutes = 0.0
                except (TypeError, AttributeError, ValueError):
                    # Handle case where dates are not in the expected format
                    wo.x_waiting_duration_minutes = 0.0
            else:
                wo.x_waiting_duration_minutes = 0.0

    def button_start(self, **kwargs):
        """Override button_start to record start time at current work center."""
        res = super(MrpWorkorder, self).button_start(**kwargs)
        current_time = fields.Datetime.now()
        for wo in self.filtered(lambda w: not w.x_start_time_at_current_wc):
            wo.x_start_time_at_current_wc = current_time
            
            # Find previous work order in the sequence for the same MO
            if wo.production_id and len(wo.production_id.workorder_ids) > 1:
                # Get all work orders for this MO, ordered by sequence
                all_wos = wo.production_id.workorder_ids.sorted(key=lambda x: x.sequence)
                wo_index = all_wos.ids.index(wo.id)
                
                if wo_index > 0:
                    previous_wo = all_wos[wo_index - 1]
                    if previous_wo.state == 'done' and previous_wo.date_finished:
                        wo.x_end_time_at_previous_wc = previous_wo.date_finished
                        
            # Force recomputation of waiting duration
            wo._compute_x_waiting_duration_minutes()
        return res

    def record_production(self):
        """Override record_production to set end time for next work order."""
        res = super(MrpWorkorder, self).record_production()
        
        # If this work order is being completed, find the next one in sequence
        if self.state == 'done' and self.production_id:
            all_wos = self.production_id.workorder_ids.sorted(key=lambda x: x.sequence)
            wo_index = all_wos.ids.index(self.id)
            
            if wo_index < len(all_wos) - 1:  # If there's a next work order
                next_wo = all_wos[wo_index + 1]
                if not next_wo.x_end_time_at_previous_wc:
                    next_wo.x_end_time_at_previous_wc = fields.Datetime.now()
        
        return res
