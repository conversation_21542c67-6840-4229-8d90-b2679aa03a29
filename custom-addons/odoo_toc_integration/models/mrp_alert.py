# -*- coding: utf-8 -*-
from odoo import models, fields, api

class Mrp<PERSON>lert(models.Model):
    """Model to manage bottleneck alerts."""
    _name = 'mrp.alert'
    _description = 'MRP Alert'
    _inherit = ['mail.thread', 'mail.activity.mixin']

    name = fields.Char(required=True, index=True)
    workcenter_id = fields.Many2one(
        'mrp.workcenter',
        string='Work Center',
        required=True,
        ondelete='cascade'
    )
    message = fields.Text(required=True)
    state = fields.Selection([
        ('open', 'Open'),
        ('acknowledged', 'Acknowledged'),
        ('resolved', 'Resolved'),
    ], string='Status', default='open', tracking=True)
    active = fields.Boolean(default=True)

    def action_acknowledge(self):
        self.write({'state': 'acknowledged'})

    def action_resolve(self):
        self.write({'state': 'resolved'})