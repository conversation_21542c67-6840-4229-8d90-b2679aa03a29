# Copyright 2024 Your Company
# License LGPL-3.0 or later (https://www.gnu.org/licenses/lgpl-3.0).

import logging
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def post_init_hook(cr, registry):
    """Post initialization hook for the module.
    
    This hook is called after the module is installed or updated.
    It performs the following actions:
    1. Sets up security group hierarchy
    2. Updates work center views to include TOC metrics
    3. Sets up initial configuration parameters
    """
    env = api.Environment(cr, SUPERUSER_ID, {})
    
    try:
        # Set up group hierarchy
        _setup_group_hierarchy(env)
        
        # Update work center views
        _update_work_center_views(env)
        
        _logger.info("TOC Integration module post-install hook executed successfully.")
    except Exception as e:
        _logger.error("Error in TOC Integration post-install hook: %s", str(e))
        raise


def uninstall_hook(cr, registry):
    """Uninstallation hook for the module.
    
    This hook is called when the module is uninstalled.
    It performs cleanup of any data or settings that should be removed.
    """
    env = api.Environment(cr, SUPERUSER_ID, {})
    _logger.info("TOC Integration module uninstalled.")


def _setup_group_hierarchy(env):
    """Set up the security group hierarchy.
    
    This function establishes the following hierarchy:
    TOC Admin > TOC Manager > TOC User
    """
    try:
        # Get the groups
        admin_group = env.ref('odoo_toc_integration.group_toc_admin')
        manager_group = env.ref('odoo_toc_integration.group_toc_manager')
        user_group = env.ref('odoo_toc_integration.group_toc_user')
        
        # Set up the hierarchy
        if admin_group and manager_group and user_group:
            # Manager implies User
            if user_group not in manager_group.implied_ids:
                manager_group.write({'implied_ids': [(4, user_group.id)]})
            
            # Admin implies Manager (which in turn implies User)
            if manager_group not in admin_group.implied_ids:
                admin_group.write({'implied_ids': [(4, manager_group.id)]})
            
            _logger.info("TOC Integration security group hierarchy set up successfully.")
    except Exception as e:
        _logger.error("Error setting up TOC Integration security group hierarchy: %s", str(e))
        raise


def _update_work_center_views(env):
    """Update work center views to include TOC metrics.
    
    Args:
        env: Odoo environment
    """
    # Get the view to update
    view = env['ir.ui.view'].search([
        ('model', '=', 'mrp.workcenter'),
        ('type', '=', 'form'),
        ('mode', '=', 'primary')
    ], limit=1)
    
    if view and 'x_utilization_percentage' not in view.arch:
        # Add TOC fields to the work center form view
        arch = view.arch
        # Add TOC fields to the form view
        if '<page string="Accounting">' in arch:
            # Insert before the Accounting page
            new_arch = arch.replace(
                '<page string="Accounting">',
                '''<page string="TOC Metrics" name="toc_metrics">
                    <group>
                        <group string="Utilization">
                            <field name="x_utilization_percentage" widget="progressbar" options="{'edit': '0'}" class="oe_inline"/>
                            <field name="x_daily_workload" widget="float_time" options="{'edit': '0'}"/>
                            <field name="x_avg_processing_time" widget="float_time" options="{'edit': '0'}"/>
                        </group>
                        <group string="Bottleneck Analysis">
                            <field name="x_is_bottleneck" invisible="1"/>
                            <field name="x_work_order_queue_time" widget="float_time" options="{'edit': '0'}"/>
                            <field name="x_avg_waiting_time" widget="float_time" options="{'edit': '0'}"/>
                        </group>
                    </group>
                </page>
                <page string="Accounting">'''
            )
        else:
            # Add the TOC metrics page at the end if Accounting page not found
            new_arch = arch.replace(
                '</form>',
                '''<page string="TOC Metrics" name="toc_metrics">
                    <group>
                        <group string="Utilization">
                            <field name="x_utilization_percentage" widget="progressbar" options="{'edit': '0'}" class="oe_inline"/>
                            <field name="x_daily_workload" widget="float_time" options="{'edit': '0'}"/>
                            <field name="x_avg_processing_time" widget="float_time" options="{'edit': '0'}"/>
                        </group>
                        <group string="Bottleneck Analysis">
                            <field name="x_is_bottleneck" invisible="1"/>
                            <field name="x_work_order_queue_time" widget="float_time" options="{'edit': '0'}"/>
                            <field name="x_avg_waiting_time" widget="float_time" options="{'edit': '0'}"/>
                        </group>
                    </group>
                </page>
            </form>'''
            )
        
        # Update the view
        view.write({'arch': new_arch})
        _logger.info("Updated work center form view with TOC metrics.")
