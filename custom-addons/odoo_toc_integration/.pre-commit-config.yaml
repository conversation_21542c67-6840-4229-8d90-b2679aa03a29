# Pre-commit configuration for Odoo TOC Integration
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: debug-statements
      - id: requirements-txt-fixer
      - id: mixed-line-ending
        args: [--fix=lf]

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3.8
        args: [--line-length=88]

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort
        name: isort (python)
        args: [--profile=black, --multi-line=3, --trailing-comma, --force-grid-wrap=0, --line-width=88]

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-comprehensions, flake8-bugbear, flake8-builtins]
        args: [--max-line-length=88, --ignore=E203,W503]

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v3.0.0
    hooks:
      - id: prettier
        types_or: [javascript, css, scss, html, json, yaml, markdown, md, xml]
        args: [--write, --prose-wrap=always, --print-width=88, --tab-width=4, --use-tabs=false]

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.0.275
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]

  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.3.1
    hooks:
      - id: commitizen
        stages: [commit-msg]
        additional_dependencies: [commitizen==3.3.1]
