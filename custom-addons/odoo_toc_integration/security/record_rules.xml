<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Multi-Company Rules -->        
        <record id="mrp_workorder_toc_company_rule" model="ir.rule">
            <field name="name">TOC: Work Order multi-company</field>
            <field name="model_id" ref="mrp.model_mrp_workorder"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
        </record>
        
        <!-- TOC Admin Rules - Full access to all records -->
        <record id="mrp_workcenter_rule_admin" model="ir.rule">
            <field name="name">TOC: Full Access to Work Centers (Admin)</field>
            <field name="model_id" ref="mrp.model_mrp_workcenter"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('odoo_toc_integration.group_toc_admin'))]"/>
        </record>
        
        <record id="mrp_workorder_rule_admin" model="ir.rule">
            <field name="name">TOC: Full Access to Work Orders (Admin)</field>
            <field name="model_id" ref="mrp.model_mrp_workorder"/>
            <field name="domain_force">[(1, '=', 1)]</field>
            <field name="groups" eval="[(4, ref('odoo_toc_integration.group_toc_admin'))]"/>
        </record>
        
        <!-- TOC Manager Rules - Access to all records (no department restriction) -->
        <record id="mrp_workcenter_rule_manager" model="ir.rule">
            <field name="name">TOC: Access to Work Centers (Manager)</field>
            <field name="model_id" ref="mrp.model_mrp_workcenter"/>
            <field name="domain_force">[('department_id', 'in', user.employee_ids.department_id.ids)]</field>
            <field name="groups" eval="[(4, ref('odoo_toc_integration.group_toc_manager'))]"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="mrp_workorder_rule_manager" model="ir.rule">
            <field name="name">TOC: Access to Work Orders (Manager)</field>
            <field name="model_id" ref="mrp.model_mrp_workorder"/>
            <field name="domain_force">[('workcenter_id.department_id', 'in', user.employee_ids.department_id.ids)]</field>
            <field name="groups" eval="[(4, ref('odoo_toc_integration.group_toc_manager'))]"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- TOC User Rules - Read-only access to all records -->
        <record id="mrp_workcenter_rule_user" model="ir.rule">
            <field name="name">TOC: Read-Only Access to Work Centers (User)</field>
            <field name="model_id" ref="mrp.model_mrp_workcenter"/>
            <field name="domain_force">[('department_id', 'in', user.employee_ids.department_id.ids)]</field>
            <field name="groups" eval="[(4, ref('odoo_toc_integration.group_toc_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="True"/>
            <field name="perm_create" eval="True"/>
            <field name="perm_unlink" eval="True"/>
        </record>
        
        <record id="mrp_workorder_rule_user" model="ir.rule">
            <field name="name">TOC: Read-Only Access to Work Orders (User)</field>
            <field name="model_id" ref="mrp.model_mrp_workorder"/>
            <field name="domain_force">[('workcenter_id.department_id', 'in', user.employee_ids.department_id.ids)]</field>
            <field name="groups" eval="[(4, ref('odoo_toc_integration.group_toc_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <!-- Default rule for regular users - Read-only access to work centers -->
        <record id="mrp_workcenter_rule_base" model="ir.rule">
            <field name="name">TOC: Basic Access to Work Centers (Base)</field>
            <field name="model_id" ref="mrp.model_mrp_workcenter"/>
            <field name="domain_force">[('id', '=', -1)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
        
        <record id="mrp_workorder_rule_base" model="ir.rule">
            <field name="name">TOC: Basic Access to Work Orders (Base)</field>
            <field name="model_id" ref="mrp.model_mrp_workorder"/>
            <field name="domain_force">[('id', '=', -1)]</field>
            <field name="groups" eval="[(4, ref('base.group_user'))]"/>
            <field name="perm_read" eval="True"/>
            <field name="perm_write" eval="False"/>
            <field name="perm_create" eval="False"/>
            <field name="perm_unlink" eval="False"/>
        </record>
    </data>
</odoo>
