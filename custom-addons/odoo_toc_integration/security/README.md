# TOC Integration Module - Security Implementation

This document outlines the security model implemented for the TOC Integration module.

## Security Groups

The module defines three security groups with the following permissions:

### 1. TOC Administrator (`group_toc_admin`)
- **Access Level**: Full access
- **Permissions**:
  - Full CRUD (Create, Read, Update, Delete) access to all TOC-related models
  - Can configure TOC settings and parameters
  - Can manage all TOC records regardless of department

### 2. TOC Manager (`group_toc_manager`)
- **Access Level**: Department-level manager
- **Permissions**:
  - Create, Read, Update (no Delete) access to TOC records in their department
  - Can update work center and work order records
  - Cannot delete records or modify system configurations

### 3. TOC User (`group_toc_user`)
- **Access Level**: Read-only
- **Permissions**:
  - Read-only access to TOC records in their department
  - Can view dashboards and reports
  - Cannot create, update, or delete any records

## Record Rules

### Work Center Access Rules
- **Admin**: Full access to all work centers
- **Manager**: Access to work centers in their department
- **User**: Read-only access to work centers in their department
- **Others**: No access to TOC features

### Work Order Access Rules
- **Admin**: Full access to all work orders
- **Manager**: Access to work orders for work centers in their department
- **User**: Read-only access to work orders for work centers in their department
- **Others**: No access to TOC features

## Implementation Files

1. `ir.model.access.csv`
   - Defines the access control lists (ACLs) for each model and group
   - Specifies create, read, update, delete permissions

2. `security_groups.xml`
   - Defines the security groups and their hierarchy
   - Sets up menu items for each group

3. `record_rules.xml`
   - Implements row-level security rules
   - Restricts access based on department and user group

## Setup Instructions

1. Install the module
2. Go to Settings > Users & Companies > Users
3. For each user, assign the appropriate TOC security group:
   - TOC Administrators: Assign `TOC / Administrator`
   - Department Managers: Assign `TOC / Manager`
   - Regular Users: Assign `TOC / User`

## Best Practices

1. **Principle of Least Privilege**: Always assign the minimum necessary permissions
2. **Regular Audits**: Periodically review user access rights
3. **Department Structure**: Ensure departments are properly set up for access control
4. **Testing**: Thoroughly test all security rules before deployment

## Troubleshooting

- If users can't see TOC menu items, check their assigned groups
- If users can't access certain records, verify their department assignments
- Check Odoo logs for any security-related errors

## Security Considerations

- Always keep Odoo updated with the latest security patches
- Regularly review and update security rules as needed
- Consider implementing IP restrictions for sensitive operations
- Monitor user activity for any suspicious behavior
