<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="0">
        <!-- Security Groups for TOC Integration Module -->
        
        <!-- 1. First define all groups without implied_ids -->
        <record id="group_toc_user" model="res.groups">
            <field name="name">TOC User</field>
            <field name="category_id" ref="base.module_category_manufacturing_manufacturing"/>
            <field name="comment">Read-only access to TOC metrics and dashboards</field>
            <field name="implied_ids" eval="[(4, ref('mrp.group_mrp_user'))]"/>
        </record>
        
        <record id="group_toc_manager" model="res.groups">
            <field name="name">TOC Manager</field>
            <field name="category_id" ref="base.module_category_manufacturing_manufacturing"/>
            <field name="comment">Can view and update TOC data but cannot delete records</field>
            <field name="implied_ids" eval="[(4, ref('odoo_toc_integration.group_toc_user')), (4, ref('mrp.group_mrp_manager'))]"/>
        </record>
        
        <record id="group_toc_admin" model="res.groups">
            <field name="name">TOC Administrator</field>
            <field name="category_id" ref="base.module_category_manufacturing_manufacturing"/>
            <field name="comment">Full access to all TOC features and configuration</field>
            <field name="implied_ids" eval="[(4, ref('odoo_toc_integration.group_toc_manager'))]"/>
            <field name="users" eval="[(4, ref('base.user_root')), (4, ref('base.user_admin'))]"/>
        </record>
    </data>
</odoo>
