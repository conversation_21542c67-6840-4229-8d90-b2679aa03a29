# Part of Odoo. See LICENSE file for full copyright and licensing details.

from .common import TestTocMrpCommon
from odoo import fields
from odoo.tests import tagged

@tagged('-at_install', 'post_install')
class TestMrpWorkcenter(TestTocMrpCommon):

    def test_x_is_bottleneck_initial_value(self):
        """Check the initial value of the x_is_bottleneck field."""
        # The compute method should run upon creation.
        # With no activity, utilization is 0, so it should not be a bottleneck.
        self.assertFalse(self.workcenter_bottleneck.x_is_bottleneck, "A new workcenter should not be a bottleneck by default.")

    def test_toc_metrics_computation(self):
        """Verify that TOC metrics are computed correctly after processing a work order."""
        # Create a manufacturing order to generate a work order
        mo = self.env['mrp.production'].create({
            'product_id': self.product_4.id,
            'product_uom_id': self.uom_unit.id,
            'bom_id': self.bom_bottleneck.id,
        })
        mo.action_confirm()
        
        # Process the work order
        workorder = mo.workorder_ids[0]
        workorder.duration = 60
        workorder.state = 'done'
        workorder.date_finished = fields.Datetime.now()
        
        # Manually trigger the compute method to ensure metrics are updated
        self.workcenter_bottleneck._compute_x_toc_metrics()
        
        # The utilization should be updated
        self.assertGreater(self.workcenter_bottleneck.x_utilization_percentage, 0, "Workcenter utilization should be greater than 0.")