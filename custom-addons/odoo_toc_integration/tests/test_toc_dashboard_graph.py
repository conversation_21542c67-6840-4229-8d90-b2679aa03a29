# -*- coding: utf-8 -*-
from .common import TestTocMrpCommon

class TestTocDashboardGraph(TestTocMrpCommon):
    """
    Test suite for the graphical dashboard data generation.
    """

    def test_get_report_values_graph_data(self):
        """
        Test that the get_report_values method returns a valid graph structure.
        """
        # Create some sample data to ensure the graph is not empty
        self.so = self.env['sale.order'].create({
            'name': 'Test SO for Graph',
            'partner_id': self.env.ref('base.partner_demo').id,
        })
        self.mo = self.env['mrp.production'].create({
            'name': 'Test MO for Graph',
            'product_id': self.product.id,
            'product_uom_id': self.product.uom_id.id,
            'product_qty': 1,
            'origin': self.so.name,
        })
        self.env['mrp.workorder'].create({
            'name': 'Test WO for Graph',
            'production_id': self.mo.id,
            'workcenter_id': self.workcenter_bottleneck.id,
            'product_id': self.product_4.id,
            'product_uom_id': self.product_4.uom_id.id,
        })

        # Call the method to get the dashboard data
        report_values = self.env['report.toc.dashboard'].get_report_values()

        # Check for the presence of graph_data
        self.assertIn('graph_data', report_values)
        graph_data = report_values['graph_data']

        # Check for nodes and edges
        self.assertIn('nodes', graph_data)
        self.assertIn('edges', graph_data)
        self.assertIsInstance(graph_data['nodes'], list)
        self.assertIsInstance(graph_data['edges'], list)

        # Check the structure of the first node (if any)
        if graph_data['nodes']:
            node = graph_data['nodes'][0]
            self.assertIn('id', node)
            self.assertIn('type', node)
            self.assertIn('label', node)
            self.assertIn('data', node)

        # Check the structure of the first edge (if any)
        if graph_data['edges']:
            edge = graph_data['edges'][0]
            self.assertIn('source', edge)
            self.assertIn('target', edge)
