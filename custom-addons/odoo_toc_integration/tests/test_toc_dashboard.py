# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.tests import common, tagged

@tagged('-at_install', 'post_install')
class TestTocDashboard(common.TransactionCase):
    def setUp(self, *args, **kwargs):
        super(TestTocDashboard, self).setUp(*args, **kwargs)
        self.user_demo = self.env.ref('base.user_demo')
        self.user_demo.groups_id |= self.env.ref('odoo_toc_integration.group_toc_user')

    def test_dashboard_action(self):
        """Check that the dashboard action can be called without errors."""
        action = self.env['ir.actions.client'].with_user(self.user_demo)._for_xml_id('odoo_toc_integration.action_toc_dashboard')
        self.assertTrue(action, "Dashboard action should exist.")
        
        # This is a simple check to ensure the action is valid
        # More advanced tests could be added to check the dashboard content
        self.assertEqual(action['tag'], 'toc_dashboard')