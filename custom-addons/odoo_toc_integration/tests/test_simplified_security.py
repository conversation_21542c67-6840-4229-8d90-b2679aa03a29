from odoo.tests import common, tagged

@tagged('post_install', '-at_install', 'simplified_security')
class TestSimplifiedSecurity(common.TransactionCase):
    def setUp(self):
        super(TestSimplifiedSecurity, self).setUp()
        self.dept_a = self.env['hr.department'].create({'name': 'Department A'})
        self.user_user = self.env['res.users'].create({
            'name': 'Simplified Test User',
            'login': 'simplified_user',
            'password': 'simplified_user',
            'groups_id': [(6, 0, [self.env.ref('mrp.group_mrp_user').id, self.env.ref('odoo_toc_integration.group_toc_user').id])],
        })
        self.env['hr.employee'].create({
            'name': 'Simplified Test Employee',
            'user_id': self.user_user.id,
            'department_id': self.dept_a.id,
        })
        self.user_user = self.user_user.with_context({})

    def test_user_can_read_workcenter_in_department(self):
        # Create workcenter as admin/superuser to ensure it exists
        wc = self.env['mrp.workcenter'].create({'name': 'Test WC', 'department_id': self.dept_a.id})
        # Now, check if the restricted user can read it
        self.assertTrue(wc.with_user(self.user_user).read(['name']), "User should be able to read workcenter in their department")