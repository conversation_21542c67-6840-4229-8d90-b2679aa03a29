# Part of Odoo. See LICENSE file for full copyright and licensing details.

from .common import TestTocMrpCommon
from odoo import fields
from odoo.tests import tagged

@tagged('-at_install', 'post_install')
class TestMrpAlert(TestTocMrpCommon):

    def test_bottleneck_alert_creation(self):
        """Verify that an alert is created when a workcenter becomes a bottleneck."""
        # Set a low threshold for testing
        self.env['ir.config_parameter'].sudo().set_param('odoo_toc_integration.high_utilization_threshold', '1')
        
        # Create a manufacturing order to generate a work order
        mo = self.env['mrp.production'].create({
            'product_id': self.product_4.id,
            'product_uom_id': self.uom_unit.id,
            'bom_id': self.bom_bottleneck.id,
        })
        mo.action_confirm()
        
        # Process the work order
        workorder = mo.workorder_ids[0]
        workorder.duration = 60
        workorder.state = 'done'
        workorder.date_finished = fields.Datetime.now()
        
        # Manually trigger the compute method to ensure metrics are updated
        self.workcenter_bottleneck._compute_x_toc_metrics()
        
        # Check if an alert was created
        alert = self.env['mrp.alert'].search([('workcenter_id', '=', self.workcenter_bottleneck.id)])
        self.assertTrue(alert, "An alert should have been created for the bottleneck.")