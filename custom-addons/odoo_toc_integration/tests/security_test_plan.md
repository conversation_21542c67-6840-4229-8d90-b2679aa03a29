# TOC Integration Module - Security Test Plan

## Test Environment Setup

1. **Test Users**
   - Create test users for each security group:
     - TOC Admin User
     - TOC Manager User
     - TOC User
     - Regular User (no TOC access)

2. **Test Data**
   - Create test work centers in different departments
   - Create test work orders for each work center
   - Assign test users to appropriate departments

## Test Cases

### 1. TOC Administrator Access

**Objective**: Verify that TOC Administrators have full access to all TOC features.

**Test Steps**:
1. Log in as TOC Admin User
2. Navigate to TOC Dashboard
3. Verify access to all TOC menus and features
4. Attempt to create, read, update, and delete work centers and work orders
5. Verify access to configuration settings

**Expected Results**:
- Full access to all TOC features
- Ability to perform all CRUD operations on all TOC models
- Access to all configuration options

### 2. TOC Manager Access

**Objective**: Verify that TOC Managers can manage but not delete records in their department.

**Test Steps**:
1. Log in as TOC Manager User
2. Navigate to TOC Dashboard
3. Verify access to TOC menus (no configuration access)
4. Attempt to create and update work centers and work orders in their department
5. Attempt to delete records
6. Attempt to access records in other departments

**Expected Results**:
- Can create and update records in their department
- Cannot delete records
- Cannot access records in other departments
- No access to configuration settings

### 3. TOC User Access

**Objective**: Verify that TOC Users have read-only access to records in their department.

**Test Steps**:
1. Log in as TOC User
2. Navigate to TOC Dashboard
3. Verify read-only access to TOC data in their department
4. Attempt to create, update, or delete records
5. Attempt to access records in other departments

**Expected Results**:
- Read-only access to records in their department
- Cannot create, update, or delete any records
- Cannot access records in other departments
- No access to configuration settings

### 4. Regular User Access

**Objective**: Verify that regular users have no access to TOC features.

**Test Steps**:
1. Log in as Regular User (no TOC groups)
2. Verify TOC menus are not visible
3. Attempt to access TOC URLs directly

**Expected Results**:
- No TOC menus visible
- Access denied when trying to access TOC features directly

### 5. Department-Based Access Control

**Objective**: Verify that users can only access records in their assigned department.

**Test Steps**:
1. Create two departments (Dept A and Dept B)
2. Assign users to different departments
3. Create work centers and work orders in each department
4. Log in as users from each department and verify access

**Expected Results**:
- Users can only see records in their assigned department
- Users cannot see records in other departments

## Test Execution

1. Execute each test case
2. Document any issues found
3. Retest after fixes

## Test Sign-Off

- [ ] All test cases passed
- [ ] All issues resolved
- [ ] Security documentation updated
- [ ] Final review completed

## Security Documentation

### Security Groups

1. **TOC Administrator**
   - Full access to all TOC features
   - Can configure system settings
   - Can manage all records

2. **TOC Manager**
   - Can manage records in their department
   - Cannot delete records
   - No access to configuration

3. **TOC User**
   - Read-only access to records in their department
   - No modification rights

### Access Control Lists (ACLs)

- Defined in `security/ir.model.access.csv`
- Controls CRUD permissions for each model and group

### Record Rules

- Defined in `security/record_rules.xml`
- Implements row-level security
- Restricts access based on department and user group
