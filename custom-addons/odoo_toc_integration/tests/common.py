# Part of Odoo. See LICENSE file for full copyright and licensing details.

from odoo.addons.mrp.tests.common import TestMrpCommon

class TestTocMrpCommon(TestMrpCommon):

    @classmethod
    def setUpClass(cls):
        super().setUpClass()
        cls.workcenter_bottleneck = cls.env['mrp.workcenter'].create({
            'name': 'Bottleneck Workcenter',
            'default_capacity': 1,
            'time_efficiency': 100,
            'oee_target': 90,
        })
        # Create a BOM for the bottleneck workcenter to ensure work orders are generated
        cls.bom_bottleneck = cls.env['mrp.bom'].create({
            'product_tmpl_id': cls.product_4.product_tmpl_id.id,
            'product_qty': 1.0,
            'operation_ids': [
                (0, 0, {
                    'name': 'Bottleneck Operation',
                    'workcenter_id': cls.workcenter_bottleneck.id,
                    'time_cycle': 60,
                })
            ]
        })