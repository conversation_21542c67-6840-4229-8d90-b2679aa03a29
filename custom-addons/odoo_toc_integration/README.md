# Odoo TOC Integration

![TOC Dashboard Banner](static/description/banner.png)

This module integrates the Theory of Constraints (TOC) principles into Odoo Manufacturing, providing real-time visibility into system bottlenecks and key performance metrics through an interactive, graphical dashboard.

## Features

- **Graphical Process Flow Dashboard**: A dynamic, interactive SVG-based dashboard that visualizes the entire manufacturing process, from Sales and Purchase Orders to Manufacturing Orders, Work Centers, and Invoices.
- **Hierarchical Layout**: The dashboard automatically arranges nodes in a clear, hierarchical layout, making complex process flows easy to understand.
- **Pan and Zoom**: The dashboard supports panning and zooming, allowing users to easily navigate large and complex manufacturing diagrams.
- **Drill-Down Details**: Click on any node in the graph to see detailed information about the corresponding Odoo record in a convenient side panel.
- **Work Centers Overview**: A clear tabular view of all work centers, showing their name, working time, utilization, and bottleneck status.
- **Work Center Metrics**: Track utilization, queue time, and WIP value for each work center.
- **Bottleneck Identification**: Visual indicators (color-coding) highlight potential bottlenecks in the manufacturing process.
- **Real-time Updates**: The dashboard updates in real-time to reflect the latest changes in the manufacturing process.
- **Configuration**: Customizable thresholds for bottleneck detection and alerting.

## Installation

1.  Copy the `odoo_toc_integration` directory to your Odoo addons directory.
2.  Update the addons path in your Odoo configuration file if needed.
3.  Install the module through the Odoo Apps interface or using the command line:

   ```bash
   ./odoo-bin -d your_database -i odoo_toc_integration --stop-after-init
   ```

## Configuration

1.  Go to **Manufacturing > Configuration > Settings**.
2.  Navigate to the **TOC Integration** section.
3.  Configure the thresholds for high and medium utilization to control bottleneck detection.

## Usage

### TOC Dashboard

Access the TOC Dashboard from the main Manufacturing menu. The dashboard provides a high-level summary, a detailed Work Centers Overview, and a visual representation of your manufacturing flow with color-coded work centers based on their utilization levels. Click on any node in the graph to drill down into the details.

### Work Center Views

Additional TOC metrics are available in the Work Center form and list views, including utilization percentage, queue time, WIP value, and scrap rate.

## Dependencies

- Odoo 18.0 Community Edition
- Manufacturing (`mrp`)
- Sales Management (`sale_management`)
- Inventory (`stock`)
- Accounting (`account`)
- Purchase (`purchase`)
- Purchase MRP (`purchase_mrp`)

## License

This module is licensed under the LGPL-3.0.

## Author

VPerfectCS

## Maintainer

Vinu [<EMAIL>]

## Security Implementation

The module implements a comprehensive security model with the following user roles:

### Security Groups

1.  **TOC Administrator**: Full access to all TOC features and configuration.
2.  **TOC Manager**: Can manage records in their assigned department.
3.  **TOC User**: Read-only access to TOC data in their department.

### Access Control

- **Model-level permissions** defined in `security/ir.model.access.csv`.
- **Record-level security** implemented with domain filters in `security/record_rules.xml`.
- **Department-based access control** to restrict data visibility.

### Testing

Automated tests are available in the `tests` directory to verify the module's functionality and security.

## Task Master Progress

### Completed Tasks
- [x] Implemented work order tracking with TOC metrics
- [x] Added work center utilization calculations (now real-time and accurate)
- [x] Created TOC dashboard with real-time metrics
- [x] Added waiting time tracking between work centers
- [x] Added manual recalculation button for TOC metrics on work centers
- [x] Added server action to recalculate metrics from work order list view
- [x] Implemented bottleneck detection algorithms
- [x] Fixed installation issues with mrp.scrap model dependency
- [x] Implemented security groups and access controls
- [x] Added department-based record rules
- [x] Created comprehensive security test cases
- [x] Implemented Mobile Responsiveness
- [x] Optimized Performance for large datasets
- [x] Implemented Graphical Process Flow Dashboard (US6)
- [x] Implemented Dashboard Drill-Down (US9)
- [x] Implemented WIP Value Tracking (US4)
- [x] Implemented Quality/Scrap Rate Tracking (US5)
- [x] Added Work Centers Overview to TOC Dashboard

### In Progress
- [ ] Add advanced reporting features
- [ ] Enhance drill-down to show upstream/downstream dependencies
- [ ] Resolve PO generation test failure (escalated)

## Credits

This module is based on the principles outlined in "The Goal" by Eliyahu M. Goldratt.
