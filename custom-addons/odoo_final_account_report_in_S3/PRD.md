# Product Requirements Document (PRD)
## Odoo 18 Indian Final Account Report - Schedule 3

### 1. Project Overview

**Project Name:** Odoo Final Account Report Schedule 3 (Indian Accounting Standards)  
**Module Name:** `odoo_final_account_report_in_s3`  
**Version:** 18.0.1.0.0  
**Target Platform:** Odoo 18 Community/Enterprise  
**Location:** `/Users/<USER>/workspace/18_Project/Odoo18Deployment/custom-addons/odoo_final_account_report_in_S3`

### 2. Business Requirements

#### 2.1 Primary Objective
Create a comprehensive Indian Final Account Report module that generates Balance Sheet and Profit & Loss statements according to Schedule 3 format for Private Limited Companies, utilizing Odoo's spreadsheet dashboard functionality for Excel export capabilities.

#### 2.2 Key Features Confirmed
- **Balance Sheet Report** following exact Schedule 3 Indian standard format
- **Profit & Loss Report** with Indian accounting standards
- **Dynamic Opening Balance** calculation from any specified start date
- **Posted/Unposted Entry Filtering** options
- **Flexible Date Range Selection** (any start date to end date)
- **Excel Export** using existing spreadsheet dashboard framework
- **Multi-Company Support** with separate chart of accounts per company
- **Company Currency Support** (multi-currency capability for future)
- **Manual/Automatic P&L Transfer** options to Balance Sheet
- **Scalable Design** for future Limited Company format

### 3. Technical Requirements

#### 3.1 Module Dependencies (✅ Validated)
```python
'depends': [
    'account',                          # ✅ Core accounting module
    'spreadsheet_dashboard',            # ✅ Dashboard framework available
    'spreadsheet_dashboard_account',    # ✅ Account dashboard integration
    'l10n_in',                         # Indian localization (if available)
]
```

#### 3.2 Database Validation Results
- **✅ Company**: "VERACIOUS PERFECT CS PRIVATE LIMITED" found
- **✅ Account Types**: All required account types validated
- **✅ Balance Fields**: `debit`, `credit`, `balance` fields confirmed
- **✅ Move States**: `posted`, `draft` states available
- **✅ Dashboard Framework**: 6 existing dashboards including Finance group
- **✅ Multi-Company**: Full support confirmed

#### 3.3 Account Type Mapping (✅ Database Validated)

**Balance Sheet Categories:**
- **Assets**
  - `asset_receivable` → Receivable
  - `asset_cash` → Bank and Cash
  - `asset_current` → Current Assets
  - `asset_non_current` → Non-current Assets
  - `asset_prepayments` → Prepayments
  - `asset_fixed` → Fixed Assets

- **Liabilities**
  - `liability_payable` → Payable
  - `liability_credit_card` → Credit Card
  - `liability_current` → Current Liabilities
  - `liability_non_current` → Non-current Liabilities

- **Equity**
  - `equity` → Equity
  - `equity_unaffected` → Current Year Earnings

**Profit & Loss Categories:**
- **Income**
  - `income` → Income
  - `income_other` → Other Income

- **Expenses**
  - `expense` → Expenses
  - `expense_depreciation` → Depreciation
  - `expense_direct_cost` → Cost of Revenue

#### 3.4 Core Functionality (✅ Technical Validation)

1. **Dynamic Date-Based Calculation Engine** (✅ Validated)
   - Opening balance calculation from any specified start date
   - Period balance calculation using existing Odoo methods
   - **✅ Confirmed**: `debit`, `credit`, `balance` fields exist in `account.move.line`
   - **✅ Confirmed**: Date filtering capabilities available
   - Utilize Odoo's built-in move line balance calculation methods

2. **Report Generation**
   - Balance Sheet in exact Schedule 3 format (Private Limited)
   - Profit & Loss statement following Indian standards
   - Manual/Automatic P&L transfer options
   - Excel template population using provided format

3. **Multi-Company Dashboard Integration** (✅ Validated)
   - **✅ Confirmed**: Multi-company framework available
   - **✅ Confirmed**: Company "VERACIOUS PERFECT CS PRIVATE LIMITED" exists
   - **✅ Confirmed**: Spreadsheet dashboard framework with 6 existing dashboards
   - **✅ Confirmed**: Finance dashboard group available
   - Excel export functionality with company filters

4. **Security & Permissions**
   - Use existing accounting user groups
   - No new permission groups required
   - Leverage Odoo's built-in accounting access controls

### 4. File Structure

```
odoo_final_account_report_in_s3/
├── __init__.py
├── __manifest__.py
├── models/
│   ├── __init__.py
│   ├── account_final_report.py          # Main report model
│   ├── account_final_report_line.py     # Report line details
│   └── res_company.py                   # Company extensions
├── wizard/
│   ├── __init__.py
│   └── final_account_report_wizard.py   # Report generation wizard
├── views/
│   ├── account_final_report_views.xml
│   ├── final_account_report_wizard_views.xml
│   └── menu_views.xml
├── data/
│   ├── dashboard_data.xml
│   ├── security_data.xml
│   └── files/
│       └── final_account_dashboard.json
├── report/
│   ├── __init__.py
│   ├── final_account_report.py          # Report logic
│   └── final_account_excel_export.py    # Excel export handler
├── static/
│   ├── description/
│   │   ├── icon.png
│   │   └── index.html
│   └── src/
│       └── js/
│           └── final_account_dashboard.js
├── security/
│   ├── ir.model.access.csv
│   └── security.xml
└── templates/
    ├── FinalAccountReportSchedule3_BS_ProfitAndLoss.xlsx  # Private Limited
    └── FinalAccountReportSchedule3_Limited.xlsx          # Future: Limited Company
```

### 5. Data Flow Architecture

```
User Input (Wizard)
    ↓
Company Selection + Date Range + Filters
    ↓
Dynamic Opening Balance Calculation (from any start date)
    ↓
Utilize Existing Odoo Balance Methods
    ↓
Period Movements Calculation (start to end date)
    ↓
Account Type Classification & Grouping
    ↓
Balance Sheet Data Structure (Schedule 3 Format)
    ↓
Profit & Loss Data Structure
    ↓
Manual/Automatic P&L Transfer Option
    ↓
Excel Template Population
    ↓
Dashboard/Excel Export
```

### 6. Database Validation Summary

#### 6.1 Confirmed Database Structure
```sql
-- ✅ Validated Models and Fields
Model: account.account
  - account_type (selection): asset_receivable, liability_payable, etc.
  - name, code, company_ids (confirmed)
  
Model: account.move.line  
  - debit, credit, balance (monetary fields)
  - date, company_id, account_id (confirmed)
  
Model: account.move
  - state (selection): posted, draft (confirmed)
  
Model: res.company
  - "VERACIOUS PERFECT CS PRIVATE LIMITED" (active company)
  
Model: spreadsheet.dashboard
  - 6 existing dashboards including Finance group
```

#### 6.2 Integration Points Validated
- **✅ Balance Calculations**: Native Odoo debit/credit/balance fields
- **✅ Date Filtering**: Built-in date range capabilities
- **✅ Posted/Unposted**: State-based filtering available
- **✅ Multi-Company**: Framework supports company isolation
- **✅ Dashboard Framework**: Spreadsheet dashboard ready for integration

### 7. Key Technical Specifications

#### 6.1 Leverage Existing Odoo Methods
- Use `account.move.line` existing balance calculation methods
- Utilize built-in date filtering capabilities
- Leverage existing `debit`, `credit`, `balance` fields
- Use Odoo's multi-company framework

#### 6.2 Excel Template Integration
- Follow exact format from `FinalAccountReportSchedule3_BS_ProfitAndLoss.xlsx`
- Use spreadsheet dashboard framework for data binding
- Maintain Schedule 3 Indian standard formatting
- Prepare for future Limited Company template scaling

#### 6.3 Multi-Company Support
- Handle separate chart of accounts per company
- Support branch companies with shared chart of accounts
- Company-specific report generation
- Multi-company dashboard filtering

#### 6.4 Currency Handling
- Primary focus on company currency
- Utilize existing `amount_currency` and `currency_id` fields
- Prepare foundation for future multi-currency support
- Use Odoo's built-in currency conversion methods

### 8. Success Criteria

1. **Functional Requirements**
   - ✅ Generate accurate Balance Sheet in exact Schedule 3 format
   - ✅ Generate accurate Profit & Loss statement
   - ✅ Dynamic opening balance calculation from any start date
   - ✅ Filter posted/unposted entries effectively
   - ✅ Export to Excel in provided template format
   - ✅ Multi-company support with proper data isolation

2. **Technical Requirements**
   - ✅ Seamless integration with existing Odoo accounting
   - ✅ Efficient use of existing balance calculation methods
   - ✅ Proper multi-company data handling
   - ✅ Scalable architecture for future enhancements

3. **User Experience**
   - ✅ Intuitive wizard interface with company selection
   - ✅ Flexible date range selection
   - ✅ Manual/Automatic P&L transfer options
   - ✅ Clear Schedule 3 formatted reports

### 9. Implementation Phases

#### Phase 1: Foundation & Core Models
- Module structure setup
- Core models with multi-company support
- Integration with existing Odoo balance methods

#### Phase 2: Report Logic & Calculations
- Dynamic opening balance calculation
- Account type classification and grouping
- Balance Sheet and P&L data structure

#### Phase 3: Excel Integration & Dashboard
- Excel template integration
- Spreadsheet dashboard setup
- Multi-company filtering

#### Phase 4: Testing & Optimization
- Multi-company testing
- Excel export validation
- Performance optimization

### 10. Future Scalability

1. **Limited Company Format**
   - Additional Excel template support
   - Extended account classifications
   - Enhanced reporting requirements

2. **Multi-Currency Enhancement**
   - Full multi-currency report support
   - Currency conversion options
   - Multi-currency dashboard views

3. **Advanced Features**
   - Comparative period reporting
   - Drill-down capabilities
   - Advanced filtering options

### 11. Risk Mitigation

| Risk | Mitigation Strategy |
|------|-------------------|
| Complex multi-company data handling | Use Odoo's built-in multi-company framework |
| Excel template complexity | Leverage existing spreadsheet dashboard capabilities |
| Performance with large datasets | Utilize Odoo's optimized balance calculation methods |
| Schedule 3 compliance | Follow exact template format and validate with experts |

---

## Final Confirmation

This PRD incorporates all your feedback:
- ✅ Exact Excel template format replication
- ✅ Confirmed account type mappings
- ✅ Dynamic start date for opening balance calculation
- ✅ Manual/Automatic P&L transfer options
- ✅ Multi-company support
- ✅ Company currency focus with multi-currency preparation
- ✅ Existing accounting group permissions
- ✅ Scalable design for Limited Company format

**Ready for implementation with confirmed requirements.**