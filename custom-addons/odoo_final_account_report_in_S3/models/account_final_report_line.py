# -*- coding: utf-8 -*-

from odoo import api, fields, models


class AccountFinalReportLine(models.Model):
    _name = 'account.final.report.line'
    _description = 'Final Account Report Line'
    _order = 'sequence, account_type, name'

    report_id = fields.Many2one('account.final.report', string='Report', required=True, ondelete='cascade')
    sequence = fields.Integer(string='Sequence', default=10)
    account_type = fields.Char(string='Account Type', required=True)
    name = fields.Char(string='Description', required=True)
    
    # Balance Fields
    opening_balance = fields.Monetary(string='Opening Balance', currency_field='currency_id')
    period_balance = fields.Monetary(string='Period Balance', currency_field='currency_id')
    balance = fields.Monetary(string='Closing Balance', currency_field='currency_id')
    
    # Related Fields
    company_id = fields.Many2one('res.company', related='report_id.company_id', store=True)
    currency_id = fields.Many2one('res.currency', related='company_id.currency_id', readonly=True)
    
    # Classification
    section = fields.Selection([
        ('assets', 'Assets'),
        ('liabilities', 'Liabilities'),
        ('equity', 'Equity'),
        ('income', 'Income'),
        ('expenses', 'Expenses'),
    ], string='Section', compute='_compute_section', store=True)

    @api.depends('account_type')
    def _compute_section(self):
        """Compute section based on account type"""
        for line in self:
            if line.account_type.startswith('asset'):
                line.section = 'assets'
            elif line.account_type.startswith('liability'):
                line.section = 'liabilities'
            elif line.account_type.startswith('equity'):
                line.section = 'equity'
            elif line.account_type.startswith('income'):
                line.section = 'income'
            elif line.account_type.startswith('expense'):
                line.section = 'expenses'
            else:
                line.section = 'assets'  # Default