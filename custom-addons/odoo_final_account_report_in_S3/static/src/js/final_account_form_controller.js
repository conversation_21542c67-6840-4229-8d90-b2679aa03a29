/** @odoo-module **/

import { FormController } from "@web/views/form/form_controller";
import { patch } from "@web/core/utils/patch";
import { useService } from "@web/core/utils/hooks";

patch(FormController.prototype, {
    setup() {
        super.setup();
        this.notification = useService("notification");
    },

    async onRecordSaved(record) {
        const result = await super.onRecordSaved(record);
        
        // Check if this is a final account report and show notification from context
        if (record.resModel === "account.final.report" && this.props.context) {
            this._showNotificationFromContext(this.props.context);
        }
        
        return result;
    },

    async load(params) {
        const result = await super.load(params);
        
        // Check for notification context when form loads
        if (this.props.resModel === "account.final.report" && this.props.context) {
            this._showNotificationFromContext(this.props.context);
        }
        
        return result;
    },

    _showNotificationFromContext(context) {
        // Show notification if context contains notification data
        if (context && context.show_notification && context.notification_message) {
            const notificationType = context.notification_type || 'info';
            this.notification.add(context.notification_message, {
                type: notificationType,
                sticky: notificationType === 'danger'
            });
        }
    }
});
