<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Indian Final Account Report - Schedule 3</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #875A7B; color: white; padding: 20px; text-align: center; }
        .content { padding: 20px; }
        .feature { margin: 10px 0; padding: 10px; background: #f9f9f9; border-left: 4px solid #875A7B; }
        .technical { background: #e8f4f8; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Indian Final Account Report - Schedule 3</h1>
        <p>Comprehensive financial reporting for Indian companies</p>
    </div>
    
    <div class="content">
        <h2>Features</h2>
        
        <div class="feature">
            <h3>📊 Schedule 3 Compliance</h3>
            <p>Generate Balance Sheet and Profit &amp; Loss reports following Indian Schedule 3 format for Private Limited companies</p>
        </div>
        
        <div class="feature">
            <h3>📅 Dynamic Date Ranges</h3>
            <p>Calculate opening balances from any specified start date with flexible period selection</p>
        </div>
        
        <div class="feature">
            <h3>🏢 Multi-Company Support</h3>
            <p>Handle multiple companies with separate chart of accounts and proper data isolation</p>
        </div>
        
        <div class="feature">
            <h3>📈 Excel Export</h3>
            <p>Export reports to Excel format using Odoo's spreadsheet dashboard framework</p>
        </div>
        
        <div class="feature">
            <h3>⚙️ Flexible Options</h3>
            <p>Include/exclude unposted entries, manual/automatic P&amp;L transfer options</p>
        </div>
        
        <h2>Technical Features</h2>
        
        <div class="feature technical">
            <h3>🔧 Native Integration</h3>
            <p>Seamlessly integrates with existing Odoo accounting framework using native balance calculations</p>
        </div>
        
        <div class="feature technical">
            <h3>🎯 Validated Database Structure</h3>
            <p>Built on validated Odoo 18 database structure with confirmed field availability</p>
        </div>
        
        <div class="feature technical">
            <h3>🚀 Scalable Design</h3>
            <p>Prepared for future enhancements including Limited Company format support</p>
        </div>
        
        <h2>Usage</h2>
        <ol>
            <li>Go to <strong>Accounting &gt; Reporting &gt; Final Account Reports</strong></li>
            <li>Click <strong>Generate Report</strong> to open the wizard</li>
            <li>Select company, date range, and options</li>
            <li>Generate and export your Schedule 3 compliant reports</li>
        </ol>
        
        <h2>Requirements</h2>
        <ul>
            <li>Odoo 18.0+</li>
            <li>Account module</li>
            <li>Spreadsheet Dashboard modules</li>
        </ul>
    </div>
</body>
</html>