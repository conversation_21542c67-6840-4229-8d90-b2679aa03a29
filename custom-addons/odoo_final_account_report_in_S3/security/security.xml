<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        
        <!-- Record Rules for Multi-Company Support -->
        <record id="final_account_report_company_rule" model="ir.rule">
            <field name="name">Final Account Report: Multi-Company</field>
            <field name="model_id" ref="model_account_final_report"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('account.group_account_user'))]"/>
        </record>
        
        <record id="final_account_report_line_company_rule" model="ir.rule">
            <field name="name">Final Account Report Line: Multi-Company</field>
            <field name="model_id" ref="model_account_final_report_line"/>
            <field name="domain_force">[('company_id', 'in', company_ids)]</field>
            <field name="groups" eval="[(4, ref('account.group_account_user'))]"/>
        </record>
        
    </data>
</odoo>