# -*- coding: utf-8 -*-

from odoo import api, fields, models, _
from odoo.exceptions import UserError, ValidationError


class FinalAccountReportWizard(models.TransientModel):
    _name = 'final.account.report.wizard'
    _description = 'Final Account Report Generation Wizard'

    company_id = fields.Many2one('res.company', string='Company', required=True,
                                default=lambda self: self.env.company)
    currency_id = fields.Many2one('res.currency', string='Currency',
                                 related='company_id.currency_id', readonly=True)
    
    # Preview fields
    preview_total_assets = fields.Monetary(string="Total Assets", readonly=True)
    preview_total_liabilities = fields.Monetary(string="Total Liabilities", readonly=True)
    preview_net_pl = fields.Monetary(string="Net Profit/Loss", readonly=True)
    preview_balance_check = fields.Char(string="Balance Check", readonly=True)
    date_from = fields.Date(string='Start Date', required=True,
                           default=lambda self: fields.Date.today().replace(month=4, day=1),
                           help="Opening balance will be calculated from this date")
    date_to = fields.Date(string='End Date', required=True,
                         default=lambda self: fields.Date.today(),
                         help="Report will include transactions up to this date")
    include_unposted = fields.Boolean(string='Include Unposted Entries', default=False,
                                     help="Include draft journal entries in calculations")
    pl_transfer_mode = fields.Selection([
        ('manual', 'Manual Transfer'),
        ('automatic', 'Automatic Transfer')
    ], string='P&L Transfer Mode', default='automatic', required=True,
       help="How to handle Profit & Loss transfer to Balance Sheet")
    
    report_name = fields.Char(string='Report Name',
                             default=lambda self: _('Final Account Report - %s') % fields.Date.today().strftime('%B %Y'))
    
    # Preview fields
    preview_company_name = fields.Char(string='Preview Company', readonly=True)
    preview_currency_symbol = fields.Char(string='Preview Currency', readonly=True)
    preview_total_assets = fields.Float(string='Preview Total Assets', readonly=True)
    preview_total_liabilities = fields.Float(string='Preview Total Liabilities', readonly=True)
    preview_net_profit_loss = fields.Float(string='Preview Net Profit/Loss', readonly=True)
    preview_balance_check = fields.Float(string='Preview Balance Check', readonly=True)

    @api.constrains('date_from', 'date_to')
    def _check_dates(self):
        for wizard in self:
            if wizard.date_from > wizard.date_to:
                raise UserError(_('Start Date cannot be later than End Date.'))

    @api.onchange('date_from', 'date_to')
    def _onchange_dates(self):
        if self.date_from and self.date_to:
            date_from = fields.Date.from_string(self.date_from)
            date_to = fields.Date.from_string(self.date_to)
            self.report_name = _('Final Account Report - %s to %s') % (
                date_from.strftime('%b %Y'),
                date_to.strftime('%b %Y')
            )

    def action_generate_report(self):
        """Generate the final account report"""
        self.ensure_one()
        
        # Create new report record
        report = self.env['account.final.report'].create({
            'name': self.report_name,
            'company_id': self.company_id.id,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'include_unposted': self.include_unposted,
            'pl_transfer_mode': self.pl_transfer_mode,
        })
        
        # Generate report data
        report.action_generate_report()
        
        # Return action to view the generated report
        return {
            'type': 'ir.actions.act_window',
            'name': _('Final Account Report'),
            'res_model': 'account.final.report',
            'res_id': report.id,
            'view_mode': 'form',
            'target': 'current',
        }

    def action_preview_report(self):
        """Preview report data without creating permanent record"""
        self.ensure_one()
        
        # Validate parameters first
        self._validate_parameters()
        
        # Get preview data
        preview_data = self._get_preview_data()
        
        # Update preview fields
        self.write({
            'preview_company_name': preview_data.get('company_name', ''),
            'preview_currency_symbol': preview_data.get('currency_symbol', ''),
            'currency_id': self.company_id.currency_id.id,
            'preview_total_assets': preview_data['totals'].get('total_assets', 0.0),
            'preview_total_liabilities': preview_data['totals'].get('total_liabilities', 0.0),
            'preview_net_profit_loss': preview_data['totals'].get('net_profit_loss', 0.0),
            'preview_balance_check': preview_data['totals'].get('balance_check', 0.0)
        })
        
        return {
            'type': 'ir.actions.act_window',
            'name': _('Report Preview - %s') % self.report_name,
            'res_model': 'final.account.report.wizard',
            'res_id': self.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'preview_mode': True,
                'preview_data': preview_data,
                'dialog_size': 'extra-large'
            }
        }
    
    def _validate_parameters(self):
        """Validate wizard parameters"""
        if self.date_from > self.date_to:
            raise UserError(_('Start Date cannot be later than End Date.'))
        
        # Check if company has accounts
        account_count = self.env['account.account'].search_count([
            ('company_ids', 'in', self.company_id.ids)
        ])
        
        if account_count == 0:
            raise UserError(_('No accounts found for company %s. Please set up chart of accounts first.') % self.company_id.name)
    
    def _get_preview_data(self):
        """Get preview data for the report with proper sign handling"""
        # Create temporary report instance
        temp_report = self.env['account.final.report'].new({
            'name': self.report_name,
            'company_id': self.company_id.id,
            'date_from': self.date_from,
            'date_to': self.date_to,
            'include_unposted': self.include_unposted,
            'pl_transfer_mode': self.pl_transfer_mode,
        })

        # Calculate preview balances with proper sign handling
        account_types = {
            'asset_receivable': 'Receivable',
            'asset_cash': 'Bank and Cash',
            'asset_current': 'Current Assets',
            'asset_non_current': 'Non-current Assets',
            'asset_prepayments': 'Prepayments',
            'asset_fixed': 'Fixed Assets',
            'liability_payable': 'Payable',
            'liability_current': 'Current Liabilities',
            'liability_non_current': 'Non-current Liabilities',
            'liability_credit_card': 'Credit Card',
            'equity': 'Equity',
            'equity_unaffected': 'Current Year Earnings',
            'income': 'Income',
            'income_other': 'Other Income',
            'expense': 'Expenses',
            'expense_depreciation': 'Depreciation',
            'expense_direct_cost': 'Cost of Revenue',
        }

        preview_lines = []
        total_assets = 0.0
        total_liabilities = 0.0
        total_equity = 0.0
        total_income = 0.0
        total_expenses = 0.0

        for account_type, display_name in account_types.items():
            accounts = self.env['account.account'].search([
                ('account_type', '=', account_type),
                ('company_ids', 'in', self.company_id.ids)
            ])

            if accounts:
                # Calculate raw balances
                raw_opening = temp_report._calculate_opening_balance(accounts.ids)
                raw_period = temp_report._calculate_period_balance(accounts.ids)
                raw_closing = raw_opening + raw_period

                # Apply sign logic for display
                opening_balance = temp_report._apply_sign_logic(raw_opening, account_type)
                period_balance = temp_report._apply_sign_logic(raw_period, account_type)
                closing_balance = temp_report._apply_sign_logic(raw_closing, account_type)

                preview_lines.append({
                    'account_type': account_type,
                    'name': display_name,
                    'opening_balance': opening_balance,
                    'period_balance': period_balance,
                    'closing_balance': closing_balance,
                    'account_count': len(accounts)
                })

                # Accumulate totals with proper signs
                if account_type.startswith('asset'):
                    total_assets += closing_balance
                elif account_type.startswith('liability'):
                    total_liabilities += closing_balance
                elif account_type.startswith('equity'):
                    total_equity += closing_balance
                elif account_type.startswith('income'):
                    total_income += closing_balance
                elif account_type.startswith('expense'):
                    total_expenses += closing_balance

        return {
            'lines': preview_lines,
            'totals': {
                'total_assets': total_assets,
                'total_liabilities': total_liabilities,
                'total_equity': total_equity,
                'net_profit_loss': total_income - total_expenses,
                'balance_check': total_assets - (total_liabilities + total_equity)
            },
            'company_name': self.company_id.name,
            'currency_symbol': self.company_id.currency_id.symbol
        }