<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        
        <!-- Final Account Report Wizard Form View -->
        <record id="view_final_account_report_wizard_form" model="ir.ui.view">
            <field name="name">final.account.report.wizard.form</field>
            <field name="model">final.account.report.wizard</field>
            <field name="arch" type="xml">
                <form string="Generate Final Account Report">
                    <sheet>
                        <div class="oe_title">
                            <h1>
                                <field name="report_name" placeholder="Enter report name..."/>
                            </h1>
                        </div>
                        
                        <group>
                            <group string="Report Parameters">
                                <field name="company_id" options="{'no_create': True}"/>
                                <field name="date_from" help="Opening balance will be calculated from this date"/>
                                <field name="date_to" help="Report will include transactions up to this date"/>
                            </group>
                            <group string="Options">
                                <field name="include_unposted" help="Include draft journal entries in calculations"/>
                                <field name="pl_transfer_mode" help="How to handle Profit &amp; Loss transfer to Balance Sheet"/>
                            </group>
                        </group>
                        
                        <div class="alert alert-info" role="status">
                            <strong>Note:</strong> This report follows Indian Schedule 3 format for Private Limited companies.
                            The opening balance will be calculated dynamically from your specified start date.
                        </div>
                        
                        <!-- Preview Section -->
                        <div invisible="not context.get('preview_mode')">
                            <separator string="Report Preview"/>
                            <div class="alert alert-success" role="status">
                                <strong>Preview Generated Successfully</strong><br/>
                                Company: <span><field name="company_id" readonly="1"/></span><br/>
                                Currency: <span><field name="currency_id" readonly="1"/></span>
                            </div>
                            
                            <group string="Summary Totals">
                                <group>
                                    <label for="preview_total_assets" string="Total Assets"/>
                                    <div name="preview_total_assets">
                                        <field name="preview_total_assets" readonly="1"/>
                                    </div>
                                    <label for="preview_total_liabilities" string="Total Liabilities"/>
                                    <div name="preview_total_liabilities">
                                        <field name="preview_total_liabilities" readonly="1"/>
                                    </div>
                                </group>
                                <group>
                                    <label for="preview_net_profit_loss" string="Net Profit/Loss"/>
                                    <div name="preview_net_profit_loss">
                                        <field name="preview_net_profit_loss" readonly="1"/>
                                    </div>
                                    <label for="preview_balance_check" string="Balance Check"/>
                                    <div name="preview_balance_check">
                                        <field name="preview_balance_check" readonly="1"/>
                                    </div>
                                </group>
                            </group>
                        </div>
                        
                    </sheet>
                    <footer>
                        <button name="action_generate_report" type="object" string="Generate Report" 
                                class="btn-primary" invisible="context.get('preview_mode')"/>
                        <button name="action_preview_report" type="object" string="Preview" 
                                class="btn-secondary" invisible="context.get('preview_mode')"/>
                        <button name="action_generate_report" type="object" string="Generate Full Report" 
                                class="btn-primary" invisible="not context.get('preview_mode')"/>
                        <button string="Close" class="btn-secondary" special="cancel"/>
                    </footer>
                </form>
            </field>
        </record>
        
    </data>
</odoo>