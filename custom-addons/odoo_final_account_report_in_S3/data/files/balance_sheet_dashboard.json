{"version": 21, "sheets": [{"id": "final-account-balance-sheet", "name": "Balance Sheet", "colNumber": 4, "rowNumber": 50, "rows": {"0": {"size": 30}, "1": {"size": 25}, "2": {"size": 20}, "4": {"size": 25}}, "cols": {"0": {"size": 250}, "1": {"size": 150}, "2": {"size": 150}, "3": {"size": 150}}, "merges": ["A1:D1", "A2:D2", "A3:D3"], "cells": {"A1": {"content": "=ODOO.COMPANY.NAME", "style": 1}, "A2": {"content": "BALANCE SHEET", "style": 1}, "A3": {"content": "=CONCATENATE(\"As at \", TEXT(ODOO.FILTER.VALUE(\"date_filter\").endDate, \"D MMMM YYYY\"))", "style": 2}, "A5": {"content": "Particulars", "style": 2}, "B5": {"content": "Opening Balance", "style": 2}, "C5": {"content": "Period Movement", "style": 2}, "D5": {"content": "Closing Balance", "style": 2}, "A7": {"content": "ASSETS", "style": 3}, "B7": {"style": 3}, "C7": {"style": 3}, "D7": {"style": 3}, "A8": {"content": "Current Assets:", "style": 3}, "B8": {"style": 3}, "C8": {"style": 3}, "D8": {"style": 3}, "A9": {"content": "  Receivable", "style": 5}, "B9": {"content": "=ODOO.LIST(1,1,\"opening_balance\")", "style": 4}, "C9": {"content": "=ODOO.LIST(1,1,\"period_balance\")", "style": 4}, "D9": {"content": "=ODOO.LIST(1,1,\"balance\")", "style": 4}, "A10": {"content": "  Bank and Cash", "style": 5}, "B10": {"content": "=ODOO.LIST(1,2,\"opening_balance\")", "style": 4}, "C10": {"content": "=ODOO.LIST(1,2,\"period_balance\")", "style": 4}, "D10": {"content": "=ODOO.LIST(1,2,\"balance\")", "style": 4}, "A11": {"content": "  Current Assets", "style": 5}, "B11": {"content": "=ODOO.LIST(1,3,\"opening_balance\")", "style": 4}, "C11": {"content": "=ODOO.LIST(1,3,\"period_balance\")", "style": 4}, "D11": {"content": "=ODOO.LIST(1,3,\"balance\")", "style": 4}, "A13": {"content": "Non-Current Assets:", "style": 3}, "B13": {"style": 3}, "C13": {"style": 3}, "D13": {"style": 3}, "A14": {"content": "  Fixed Assets", "style": 5}, "B14": {"content": "=ODOO.LIST(1,4,\"opening_balance\")", "style": 4}, "C14": {"content": "=ODOO.LIST(1,4,\"period_balance\")", "style": 4}, "D14": {"content": "=ODOO.LIST(1,4,\"balance\")", "style": 4}, "A15": {"content": "  Non-current Assets", "style": 5}, "B15": {"content": "=ODOO.LIST(1,5,\"opening_balance\")", "style": 4}, "C15": {"content": "=ODOO.LIST(1,5,\"period_balance\")", "style": 4}, "D15": {"content": "=ODOO.LIST(1,5,\"balance\")", "style": 4}, "A17": {"content": "TOTAL ASSETS", "style": 3}, "B17": {"style": 3}, "C17": {"style": 3}, "D17": {"content": "=PIVOT.VALUE(1,\"total_assets\")", "style": 6}, "A20": {"content": "LIABILITIES AND EQUITY", "style": 3}, "B20": {"style": 3}, "C20": {"style": 3}, "D20": {"style": 3}, "A21": {"content": "Current Liabilities:", "style": 3}, "B21": {"style": 3}, "C21": {"style": 3}, "D21": {"style": 3}, "A22": {"content": "  Payable", "style": 5}, "B22": {"content": "=ODOO.LIST(2,1,\"opening_balance\")", "style": 4}, "C22": {"content": "=ODOO.LIST(2,1,\"period_balance\")", "style": 4}, "D22": {"content": "=ODOO.LIST(2,1,\"balance\")", "style": 4}, "A23": {"content": "  Current Liabilities", "style": 5}, "B23": {"content": "=ODOO.LIST(2,2,\"opening_balance\")", "style": 4}, "C23": {"content": "=ODOO.LIST(2,2,\"period_balance\")", "style": 4}, "D23": {"content": "=ODOO.LIST(2,2,\"balance\")", "style": 4}, "A25": {"content": "Equity:", "style": 3}, "B25": {"style": 3}, "C25": {"style": 3}, "D25": {"style": 3}, "A26": {"content": "  Equity", "style": 5}, "B26": {"content": "=ODOO.LIST(3,1,\"opening_balance\")", "style": 4}, "C26": {"content": "=ODOO.LIST(3,1,\"period_balance\")", "style": 4}, "D26": {"content": "=ODOO.LIST(3,1,\"balance\")", "style": 4}, "A27": {"content": "  Current Year Earnings", "style": 5}, "B27": {"content": "=ODOO.LIST(3,2,\"opening_balance\")", "style": 4}, "C27": {"content": "=ODOO.LIST(3,2,\"period_balance\")", "style": 4}, "D27": {"content": "=ODOO.LIST(3,2,\"balance\")", "style": 4}, "A29": {"content": "TOTAL LIABILITIES AND EQUITY", "style": 3}, "B29": {"style": 3}, "C29": {"style": 3}, "D29": {"content": "=PIVOT.VALUE(1,\"total_liabilities_equity\")", "style": 6}}}], "styles": {"1": {"bold": true, "fontSize": 16, "align": "center", "fillColor": "#D9E1F2"}, "2": {"bold": true, "fontSize": 12, "align": "center", "fillColor": "#B4C6E7"}, "3": {"bold": true, "fontSize": 11, "fillColor": "#E2EFDA"}, "4": {"format": "data_format"}, "5": {}, "6": {"bold": true, "fontSize": 11, "fillColor": "#E2EFDA", "format": "data_format"}}, "formats": {"data_format": "#,##0.00"}, "borders": [{"cells": "A1:D29", "border": {"top": {"style": "thin"}, "bottom": {"style": "thin"}, "left": {"style": "thin"}, "right": {"style": "thin"}}}], "globalFilters": [{"id": "date_filter", "type": "date", "label": "Period", "defaultValue": "this_fiscal_year", "rangeType": "relative"}], "lists": {"1": {"id": "1", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "assets"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "fieldMatching": {"date_filter": {"chain": "report_id.date_to", "type": "date"}}}, "2": {"id": "2", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "liabilities"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "fieldMatching": {"date_filter": {"chain": "report_id.date_to", "type": "date"}}}, "3": {"id": "3", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "equity"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "fieldMatching": {"date_filter": {"chain": "report_id.date_to", "type": "date"}}}}, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {"1": {"id": "1", "type": "ODOO", "name": "Final Account Totals", "formulaId": "1", "model": "account.final.report", "domain": [], "measures": [{"id": "total_assets", "fieldName": "total_assets"}, {"id": "total_liabilities_equity", "fieldName": "total_liabilities_equity"}], "rows": [], "columns": [], "context": {}, "fieldMatching": {"date_filter": {"chain": "date_to", "type": "date"}}}}}