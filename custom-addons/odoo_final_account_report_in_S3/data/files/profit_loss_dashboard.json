{"version": 21, "sheets": [{"id": "final-account-profit-loss", "name": "Profit & Loss", "colNumber": 4, "rowNumber": 40, "rows": {"0": {"size": 30}, "1": {"size": 25}, "2": {"size": 20}}, "cols": {"0": {"size": 250}, "1": {"size": 150}, "2": {"size": 150}, "3": {"size": 150}}, "merges": ["A1:D1", "A2:D2", "A3:D3"], "cells": {"A1": {"content": "=ODOO.COMPANY.NAME", "style": 1}, "A2": {"content": "PROFIT & LOSS STATEMENT", "style": 1}, "A3": {"content": "=CONCATENATE(\"For the period \", TEXT(ODOO.FILTER.VALUE(\"date_filter\").startDate, \"D MMMM YYYY\"), \" to \", TEXT(ODOO.FILTER.VALUE(\"date_filter\").endDate, \"D MMMM YYYY\"))", "style": 2}, "A5": {"content": "Particulars", "style": 2}, "B5": {"content": "Opening Balance", "style": 2}, "C5": {"content": "Period Movement", "style": 2}, "D5": {"content": "Closing Balance", "style": 2}, "A7": {"content": "REVENUE", "style": 3}, "B7": {"style": 3}, "C7": {"style": 3}, "D7": {"style": 3}, "A8": {"content": "  Income", "style": 5}, "B8": {"content": "=ODOO.LIST(1,1,\"opening_balance\")", "style": 4}, "C8": {"content": "=ODOO.LIST(1,1,\"period_balance\")", "style": 4}, "D8": {"content": "=ODOO.LIST(1,1,\"balance\")", "style": 4}, "A9": {"content": "  Other Income", "style": 5}, "B9": {"content": "=ODOO.LIST(1,2,\"opening_balance\")", "style": 4}, "C9": {"content": "=ODOO.LIST(1,2,\"period_balance\")", "style": 4}, "D9": {"content": "=ODOO.LIST(1,2,\"balance\")", "style": 4}, "A11": {"content": "TOTAL REVENUE", "style": 3}, "B11": {"style": 3}, "C11": {"style": 3}, "D11": {"content": "=PIVOT.VALUE(1,\"total_income\")", "style": 6}, "A14": {"content": "EXPENSES", "style": 3}, "B14": {"style": 3}, "C14": {"style": 3}, "D14": {"style": 3}, "A15": {"content": "  Expenses", "style": 5}, "B15": {"content": "=ODOO.LIST(2,1,\"opening_balance\")", "style": 4}, "C15": {"content": "=ODOO.LIST(2,1,\"period_balance\")", "style": 4}, "D15": {"content": "=ODOO.LIST(2,1,\"balance\")", "style": 4}, "A16": {"content": "  Depreciation", "style": 5}, "B16": {"content": "=ODOO.LIST(2,2,\"opening_balance\")", "style": 4}, "C16": {"content": "=ODOO.LIST(2,2,\"period_balance\")", "style": 4}, "D16": {"content": "=ODOO.LIST(2,2,\"balance\")", "style": 4}, "A17": {"content": "  Cost of Revenue", "style": 5}, "B17": {"content": "=ODOO.LIST(2,3,\"opening_balance\")", "style": 4}, "C17": {"content": "=ODOO.LIST(2,3,\"period_balance\")", "style": 4}, "D17": {"content": "=ODOO.LIST(2,3,\"balance\")", "style": 4}, "A19": {"content": "TOTAL EXPENSES", "style": 3}, "B19": {"style": 3}, "C19": {"style": 3}, "D19": {"content": "=PIVOT.VALUE(1,\"total_expenses\")", "style": 6}, "A22": {"content": "NET PROFIT/(LOSS)", "style": 3}, "B22": {"style": 3}, "C22": {"style": 3}, "D22": {"content": "=PIVOT.VALUE(1,\"net_profit_loss\")", "style": 6}}}], "styles": {"1": {"bold": true, "fontSize": 16, "align": "center", "fillColor": "#D9E1F2"}, "2": {"bold": true, "fontSize": 12, "align": "center", "fillColor": "#B4C6E7"}, "3": {"bold": true, "fontSize": 11, "fillColor": "#E2EFDA"}, "4": {"format": "data_format"}, "5": {}, "6": {"bold": true, "fontSize": 11, "fillColor": "#E2EFDA", "format": "data_format"}}, "formats": {"data_format": "#,##0.00"}, "borders": [{"cells": "A1:D22", "border": {"top": {"style": "thin"}, "bottom": {"style": "thin"}, "left": {"style": "thin"}, "right": {"style": "thin"}}}], "globalFilters": [{"id": "date_filter", "type": "date", "label": "Period", "defaultValue": "this_fiscal_year", "rangeType": "relative"}], "lists": {"1": {"id": "1", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "income"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "fieldMatching": {"date_filter": {"chain": "report_id.date_to", "type": "date"}}}, "2": {"id": "2", "columns": ["name", "opening_balance", "period_balance", "balance"], "domain": [["section", "=", "expenses"]], "model": "account.final.report.line", "orderBy": [{"name": "sequence", "asc": true}], "fieldMatching": {"date_filter": {"chain": "report_id.date_to", "type": "date"}}}}, "settings": {"locale": {"name": "English (US)", "code": "en_US", "thousandsSeparator": ",", "decimalSeparator": ".", "dateFormat": "mm/dd/yyyy", "timeFormat": "hh:mm:ss", "formulaArgSeparator": ",", "weekStart": 7}}, "pivots": {"1": {"id": "1", "type": "ODOO", "name": "Final Account Totals", "formulaId": "1", "model": "account.final.report", "domain": [], "measures": [{"id": "total_income", "fieldName": "total_income"}, {"id": "total_expenses", "fieldName": "total_expenses"}, {"id": "net_profit_loss", "fieldName": "net_profit_loss"}], "rows": [], "columns": [], "context": {}, "fieldMatching": {"date_filter": {"chain": "date_to", "type": "date"}}}}}