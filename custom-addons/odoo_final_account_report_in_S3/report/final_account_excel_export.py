# -*- coding: utf-8 -*-

from odoo import api, models, _
from odoo.exceptions import UserError
import json
import base64
import io
try:
    import xlsxwriter
except ImportError:
    xlsxwriter = None


class FinalAccountExcelExport(models.TransientModel):
    _name = 'final.account.excel.export'
    _description = 'Final Account Excel Export Handler'

    @api.model
    def export_to_excel(self, report_id):
        """Export final account report to Excel format"""
        if not xlsxwriter:
            raise UserError(_('Python xlsxwriter library is required for Excel export.'))
        
        report = self.env['account.final.report'].browse(report_id)
        if not report.exists():
            raise UserError(_('Report not found.'))
        
        if report.state != 'generated':
            raise UserError(_('Please generate the report first.'))
        
        # Create Excel file in memory
        output = io.BytesIO()
        workbook = xlsxwriter.Workbook(output, {'in_memory': True})
        
        # Create worksheets
        self._create_balance_sheet(workbook, report)
        self._create_profit_loss(workbook, report)
        
        workbook.close()
        output.seek(0)
        
        # Create attachment
        filename = f"Final_Account_Report_{report.company_id.name}_{report.date_from}_{report.date_to}.xlsx"
        attachment = self.env['ir.attachment'].create({
            'name': filename,
            'type': 'binary',
            'datas': base64.b64encode(output.read()),
            'res_model': 'account.final.report',
            'res_id': report.id,
            'mimetype': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        
        # Update report state
        report.state = 'exported'
        
        return {
            'type': 'ir.actions.act_url',
            'url': f'/web/content/{attachment.id}?download=true',
            'target': 'self',
        }

    def _get_value_format(self, value, data_format, negative_format):
        """Get appropriate format based on value sign"""
        return negative_format if value < 0 else data_format

    def _write_currency_cell(self, worksheet, row, col, value, data_format, negative_format):
        """Write currency value with appropriate formatting"""
        format_to_use = self._get_value_format(value, data_format, negative_format)
        worksheet.write(row, col, value, format_to_use)

    def _create_balance_sheet(self, workbook, report):
        """Create Balance Sheet worksheet with proper sign handling"""
        worksheet = workbook.add_worksheet('Balance Sheet')
        
        # Define formats with proper number formatting for signs
        title_format = workbook.add_format({
            'bold': True, 'font_size': 16, 'align': 'center',
            'bg_color': '#D9E1F2', 'border': 1
        })
        header_format = workbook.add_format({
            'bold': True, 'font_size': 12, 'align': 'center',
            'bg_color': '#B4C6E7', 'border': 1
        })
        section_format = workbook.add_format({
            'bold': True, 'font_size': 11, 'bg_color': '#E2EFDA', 'border': 1
        })
        # Positive numbers format
        data_format = workbook.add_format({'border': 1, 'num_format': '#,##0.00'})
        # Negative numbers format (red color)
        negative_format = workbook.add_format({'border': 1, 'num_format': '#,##0.00_);[Red](#,##0.00)'})
        text_format = workbook.add_format({'border': 1})
        
        # Set column widths
        worksheet.set_column('A:A', 30)
        worksheet.set_column('B:D', 15)
        
        row = 0
        
        # Title
        worksheet.merge_range(row, 0, row, 3, f"{report.company_id.name}", title_format)
        row += 1
        worksheet.merge_range(row, 0, row, 3, "BALANCE SHEET", title_format)
        row += 1
        worksheet.merge_range(row, 0, row, 3, f"As at {report.date_to.strftime('%d %B %Y')}", header_format)
        row += 2
        
        # Headers
        worksheet.write(row, 0, "Particulars", header_format)
        worksheet.write(row, 1, "Opening Balance", header_format)
        worksheet.write(row, 2, "Period Movement", header_format)
        worksheet.write(row, 3, "Closing Balance", header_format)
        row += 1
        
        # Get Balance Sheet data
        bs_data = json.loads(report.balance_sheet_data) if report.balance_sheet_data else {}
        
        # Assets Section
        worksheet.write(row, 0, "ASSETS", section_format)
        worksheet.write(row, 1, "", section_format)
        worksheet.write(row, 2, "", section_format)
        worksheet.write(row, 3, "", section_format)
        row += 1
        
        # Current Assets
        current_assets = bs_data.get('assets', {}).get('current_assets', {}).get('items', [])
        if current_assets:
            worksheet.write(row, 0, "Current Assets:", section_format)
            row += 1
            for item in current_assets:
                worksheet.write(row, 0, f"  {item['name']}", text_format)
                self._write_currency_cell(worksheet, row, 1, item['opening_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 2, item['period_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 3, item['closing_balance'], data_format, negative_format)
                row += 1
        
        # Non-Current Assets
        non_current_assets = bs_data.get('assets', {}).get('non_current_assets', {}).get('items', [])
        if non_current_assets:
            worksheet.write(row, 0, "Non-Current Assets:", section_format)
            row += 1
            for item in non_current_assets:
                worksheet.write(row, 0, f"  {item['name']}", text_format)
                self._write_currency_cell(worksheet, row, 1, item['opening_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 2, item['period_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 3, item['closing_balance'], data_format, negative_format)
                row += 1

        # Total Assets
        row += 1
        worksheet.write(row, 0, "TOTAL ASSETS", section_format)
        self._write_currency_cell(worksheet, row, 3, float(report.total_assets), data_format, negative_format)
        row += 2
        
        # Liabilities & Equity Section
        worksheet.write(row, 0, "LIABILITIES AND EQUITY", section_format)
        row += 1
        
        # Current Liabilities
        current_liab = bs_data.get('liabilities_and_equity', {}).get('current_liabilities', {}).get('items', [])
        if current_liab:
            worksheet.write(row, 0, "Current Liabilities:", section_format)
            row += 1
            for item in current_liab:
                worksheet.write(row, 0, f"  {item['name']}", text_format)
                self._write_currency_cell(worksheet, row, 1, item['opening_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 2, item['period_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 3, item['closing_balance'], data_format, negative_format)
                row += 1

        # Equity
        equity_items = bs_data.get('liabilities_and_equity', {}).get('equity', {}).get('items', [])
        if equity_items:
            worksheet.write(row, 0, "Equity:", section_format)
            row += 1
            for item in equity_items:
                worksheet.write(row, 0, f"  {item['name']}", text_format)
                self._write_currency_cell(worksheet, row, 1, item['opening_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 2, item['period_balance'], data_format, negative_format)
                self._write_currency_cell(worksheet, row, 3, item['closing_balance'], data_format, negative_format)
                row += 1

        # Total Liabilities & Equity
        row += 1
        worksheet.write(row, 0, "TOTAL LIABILITIES AND EQUITY", section_format)
        self._write_currency_cell(worksheet, row, 3, float(report.total_liabilities + report.total_equity), data_format, negative_format)

    def _create_profit_loss(self, workbook, report):
        """Create Profit & Loss worksheet with proper sign handling"""
        worksheet = workbook.add_worksheet('Profit & Loss')

        # Define formats with proper number formatting for signs
        title_format = workbook.add_format({
            'bold': True, 'font_size': 16, 'align': 'center',
            'bg_color': '#D9E1F2', 'border': 1
        })
        header_format = workbook.add_format({
            'bold': True, 'font_size': 12, 'align': 'center',
            'bg_color': '#B4C6E7', 'border': 1
        })
        section_format = workbook.add_format({
            'bold': True, 'font_size': 11, 'bg_color': '#E2EFDA', 'border': 1
        })
        # Positive numbers format
        data_format = workbook.add_format({'border': 1, 'num_format': '#,##0.00'})
        # Negative numbers format (red color)
        negative_format = workbook.add_format({'border': 1, 'num_format': '#,##0.00_);[Red](#,##0.00)'})
        text_format = workbook.add_format({'border': 1})
        
        # Set column widths
        worksheet.set_column('A:A', 30)
        worksheet.set_column('B:D', 15)
        
        row = 0
        
        # Title
        worksheet.merge_range(row, 0, row, 3, f"{report.company_id.name}", title_format)
        row += 1
        worksheet.merge_range(row, 0, row, 3, "PROFIT & LOSS STATEMENT", title_format)
        row += 1
        worksheet.merge_range(row, 0, row, 3, 
                            f"For the period {report.date_from.strftime('%d %B %Y')} to {report.date_to.strftime('%d %B %Y')}", 
                            header_format)
        row += 2
        
        # Headers
        worksheet.write(row, 0, "Particulars", header_format)
        worksheet.write(row, 1, "Opening Balance", header_format)
        worksheet.write(row, 2, "Period Movement", header_format)
        worksheet.write(row, 3, "Closing Balance", header_format)
        row += 1
        
        # Get P&L data
        pl_data = json.loads(report.profit_loss_data) if report.profit_loss_data else {}
        
        # Revenue Section
        worksheet.write(row, 0, "REVENUE", section_format)
        row += 1
        
        revenue_items = pl_data.get('revenue', {}).get('items', [])
        for item in revenue_items:
            worksheet.write(row, 0, f"  {item['name']}", text_format)
            self._write_currency_cell(worksheet, row, 1, item['opening_balance'], data_format, negative_format)
            self._write_currency_cell(worksheet, row, 2, item['period_balance'], data_format, negative_format)
            self._write_currency_cell(worksheet, row, 3, item['closing_balance'], data_format, negative_format)
            row += 1

        # Total Revenue
        total_revenue = pl_data.get('revenue', {}).get('total', 0)
        worksheet.write(row, 0, "TOTAL REVENUE", section_format)
        self._write_currency_cell(worksheet, row, 3, total_revenue, data_format, negative_format)
        row += 2

        # Expenses Section
        worksheet.write(row, 0, "EXPENSES", section_format)
        row += 1

        expense_items = pl_data.get('expenses', {}).get('items', [])
        for item in expense_items:
            worksheet.write(row, 0, f"  {item['name']}", text_format)
            self._write_currency_cell(worksheet, row, 1, item['opening_balance'], data_format, negative_format)
            self._write_currency_cell(worksheet, row, 2, item['period_balance'], data_format, negative_format)
            self._write_currency_cell(worksheet, row, 3, item['closing_balance'], data_format, negative_format)
            row += 1

        # Total Expenses
        total_expenses = pl_data.get('expenses', {}).get('total', 0)
        worksheet.write(row, 0, "TOTAL EXPENSES", section_format)
        self._write_currency_cell(worksheet, row, 3, total_expenses, data_format, negative_format)
        row += 2

        # Net Profit/Loss
        worksheet.write(row, 0, "NET PROFIT/(LOSS)", section_format)
        self._write_currency_cell(worksheet, row, 3, float(report.net_profit_loss), data_format, negative_format)