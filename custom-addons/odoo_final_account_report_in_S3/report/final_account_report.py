# -*- coding: utf-8 -*-

from odoo import api, models, _
import json


class FinalAccountReportExport(models.AbstractModel):
    _name = 'report.odoo_final_account_report_in_s3.final_account_report'
    _description = 'Final Account Report Export Handler'

    @api.model
    def _get_report_values(self, docids, data=None):
        """Get report values for PDF/Excel export"""
        reports = self.env['account.final.report'].browse(docids)
        
        return {
            'doc_ids': docids,
            'doc_model': 'account.final.report',
            'docs': reports,
            'data': data,
            'get_balance_sheet_data': self._get_balance_sheet_data,
            'get_profit_loss_data': self._get_profit_loss_data,
            'format_currency': self._format_currency,
        }

    def _get_balance_sheet_data(self, report):
        """Get formatted Balance Sheet data"""
        if not report.balance_sheet_data:
            return {}
        
        try:
            return json.loads(report.balance_sheet_data)
        except (json.JSONDecodeError, TypeError):
            return {}

    def _get_profit_loss_data(self, report):
        """Get formatted Profit & Loss data"""
        if not report.profit_loss_data:
            return {}
        
        try:
            return json.loads(report.profit_loss_data)
        except (json.JSONDecodeError, TypeError):
            return {}

    def _format_currency(self, amount, currency):
        """Format currency amount"""
        if not currency:
            return str(amount)
        
        return currency._format(amount)