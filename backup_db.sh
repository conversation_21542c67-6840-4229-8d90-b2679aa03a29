#!/bin/bash
# Database backup script for Docker environment

DB_NAME=${1:-llmdb18}
BACKUP_DIR="/tmp/backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="${DB_NAME}_${TIMESTAMP}.sql"

echo "Creating backup of database: $DB_NAME"

# Create backup directory
docker exec odoo18-stack mkdir -p $BACKUP_DIR

# Create backup using docker exec with proper environment
docker exec -e PGPASSWORD=odoo odoo18-stack pg_dump \
    -h database18-stack \
    -U odoo \
    -d $DB_NAME \
    --no-owner \
    --no-privileges \
    --clean \
    --if-exists \
    > "${DB_NAME}_${TIMESTAMP}.sql"

if [ $? -eq 0 ]; then
    echo "✅ Backup created successfully: ${DB_NAME}_${TIMESTAMP}.sql"
    echo "File size: $(ls -lh ${DB_NAME}_${TIMESTAMP}.sql | awk '{print $5}')"
else
    echo "❌ Backup failed"
    exit 1
fi